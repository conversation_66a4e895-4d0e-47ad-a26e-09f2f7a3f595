package main

import (
	"testing"

	uuid "github.com/iris-contrib/go.uuid"
	"github.com/stretchr/testify/assert"
)

var opr *Operator

func init() {
	opr = NewOperator()
}

func TestOperator_Extra(t *testing.T) {
	assert.NotNil(t, opr.Extra())
}

func TestOperator_Token(t *testing.T) {
	assert.GreaterOrEqual(t, len(opr.Token()), 0)
}

func TestOperator_TraceID(t *testing.T) {
	assert.NotEmpty(t, opr.TraceID())

	traceID := uuid.Must(uuid.NewV4()).String()
	opr.Extra()["traceID"] = traceID
	assert.Equal(t, traceID, opr.TraceID())
}
