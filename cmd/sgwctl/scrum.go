package main

import (
	"os"

	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/rest"
)

type ScrumOperator struct {
	Operator

	*rest.RestyClient
}

func NewScrumOperator() *ScrumOperator {
	return &ScrumOperator{}
}

func (o *ScrumOperator) newRestyClient(_ *cli.Context) (string, error) {
	client := rest.NewRestyClient(o.TraceID())

	client = client.SetAuthToken(os.Getenv("JIRA_API_TOKEN"))

	o.RestyClient = client

	return "", nil
}

func (o *ScrumOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:   "scrum",
		Usage:  "scrum related operation",
		Before: acts.Before(o.newRestyClient),
		Subcommands: []*cli.Command{
			o.sprintCommand(acts),
		},
	}

	return cmd
}
