package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/jinzhu/now"
	"github.com/pkg/errors"
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/go-shopeelib/json"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

type createSprintReq struct {
	Name      string `json:"name"`
	Goal      string `json:"goal"`
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
}

func (o *ScrumOperator) doCreateSprint(scrumID int, req createSprintReq) (*Sprint, error) {
	url := fmt.Sprintf("%s/rest/greenhopper/1.0/sprint/%d", configs.Mgmt.Jira.Host, scrumID)
	resp, err := o.RestyClient.PostRaw(url, req)
	if err != nil {
		return nil, errors.WithMessage(err, "create_sprint_failed")
	}

	if !resp.IsSuccess() {
		return nil, errors.Errorf("create_sprint_failed: %s", resp.String())
	}

	var data Sprint
	if err = json.Unmarshal(resp.Body(), &data); err != nil {
		return nil, errors.Wrap(err, "unmarshal_sprint_failed")
	}

	return &data, nil
}

func (o *ScrumOperator) createSprint(ctx *cli.Context) (string, error) {
	req := createSprintReq{
		Name:      ctx.String("name"),
		Goal:      ctx.String("goal"),
		StartDate: ctx.String("start"),
		EndDate:   ctx.String("end"),
	}

	if req.StartDate == "" {
		req.StartDate = now.BeginningOfWeek().Format("2006-01-02 15:04")
	}

	if req.EndDate == "" {
		req.EndDate = now.EndOfWeek().Format("2006-01-02 15:04")
	}

	sprint, err := o.doCreateSprint(ctx.Int("scrum-id"), req)
	if err != nil {
		return "", errors.WithMessage(err, "create_sprint_failed")
	}

	writer := sprint.TableWriter()

	return writer.Render(), nil
}

type Sprint struct {
	ID                 int      `json:"id"`
	Sequence           int      `json:"sequence"`
	Name               string   `json:"name"`
	State              string   `json:"state"` // ACTIVE, FUTURE
	Goal               string   `json:"goal"`
	AutoStartStop      bool     `json:"autoStartStop"`
	StartDate          string   `json:"startDate"`
	EndDate            string   `json:"endDate"`
	ActivatedDate      string   `json:"activatedDate"`
	CompleteDate       string   `json:"completeDate"`
	CanUpdateSprint    bool     `json:"canUpdateSprint"`
	CanStartStopSprint bool     `json:"canStartStopSprint"`
	CanUpdateDates     bool     `json:"canUpdateDates"`
	RemoteLinks        []string `json:"remoteLinks"`
	DaysRemaining      int      `json:"daysRemaining"`
	IssuesIDs          []int    `json:"issuesIds"`
}

func (s *Sprint) IsActive() bool {
	return s.State == "ACTIVE"
}

func (s *Sprint) TableWriter() table.Writer {
	writer := table.NewWriter()
	writer.AppendRows([]table.Row{
		{"ID", s.ID},
		{"Name", s.Name},
		{"Goal", s.Goal},
		{"StartDate", s.StartDate},
		{"EndDate", s.EndDate},
		{"State", s.State},
	})

	return writer
}

type StartSprintReq struct {
	SprintID    int    `json:"sprintId"`
	StartDate   string `json:"startDate"`
	EndDate     string `json:"endDate"`
	RapidViewID int    `json:"rapidViewId"`
	Name        string `json:"name"`
	Goal        string `json:"goal"`
}

func (s *Sprint) ToStartReq() *StartSprintReq {
	sd, _ := time.Parse(time.RFC3339, s.StartDate)
	ed, _ := time.Parse(time.RFC3339, s.EndDate)

	return &StartSprintReq{
		SprintID:  s.ID,
		StartDate: sd.Format("2006-01-02 15:04"),
		EndDate:   ed.Format("2006-01-02 15:04"),
		Name:      s.Name,
		Goal:      s.Goal,
	}
}

type listSprintsResp struct {
	SprintsData struct {
		Sprints []*Sprint `json:"sprints"`
	} `json:"sprintsData"`
}

func (o *ScrumOperator) doListSprints(scrumID int, project string) ([]*Sprint, error) {
	url := fmt.Sprintf("%s/rest/greenhopper/1.0/xboard/work/allData.json", configs.Mgmt.Jira.Host)
	resp, err := o.RestyClient.GetRaw(url, map[string]string{
		"rapidViewId":        fmt.Sprintf("%d", scrumID),
		"selectedProjectKey": project,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "list_sprints_failed")
	}

	if !resp.IsSuccess() {
		return nil, errors.Errorf("list_sprints_failed: %s", resp.String())
	}

	var data listSprintsResp
	if err = json.Unmarshal(resp.Body(), &data); err != nil {
		return nil, errors.Wrap(err, "unmarshal_sprints_failed")
	}

	return data.SprintsData.Sprints, nil
}

func (o *ScrumOperator) listSprints(ctx *cli.Context) (string, error) {
	sprints, err := o.doListSprints(ctx.Int("scrum-id"), ctx.String("project"))
	if err != nil {
		return "", errors.WithMessage(err, "list_sprints_failed")
	}

	writer := table.NewWriter()
	writer.AppendHeader(table.Row{"ID", "Name", "Goal", "Start", "End", "State"})

	for _, sprint := range sprints {
		writer.AppendRows([]table.Row{
			{sprint.ID, sprint.Name, sprint.Goal, sprint.StartDate, sprint.EndDate, sprint.State},
		})
	}

	return writer.Render(), nil
}

type backlogResp struct {
	Sprints []*Sprint `json:"sprints"`
}

func (o *ScrumOperator) sprintsFromBacklog(scrumID int, project string) ([]*Sprint, error) {
	url := fmt.Sprintf("%s/rest/greenhopper/1.0/xboard/plan/backlog/data.json", configs.Mgmt.Jira.Host)
	resp, err := o.RestyClient.GetRaw(url, map[string]string{
		"rapidViewId":        fmt.Sprintf("%d", scrumID),
		"selectedProjectKey": project,
		"issueLimit":         fmt.Sprintf("%d", 0),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "backlog_sprints_failed")
	}

	if !resp.IsSuccess() {
		return nil, errors.Errorf("backlog_sprints_failed: %s", resp.String())
	}

	var data backlogResp
	if err = json.Unmarshal(resp.Body(), &data); err != nil {
		return nil, errors.Wrap(err, "unmarshal_sprints_failed")
	}

	return data.Sprints, nil
}

func (o *ScrumOperator) backlogSprints(ctx *cli.Context) (string, error) {
	sprints, err := o.sprintsFromBacklog(ctx.Int("scrum-id"), ctx.String("project"))
	if err != nil {
		return "", errors.WithMessage(err, "backlog_sprints_failed")
	}

	writer := table.NewWriter()
	writer.AppendHeader(table.Row{"ID", "Name", "Goal", "Start", "End", "State"})

	for _, sprint := range sprints {
		writer.AppendRows([]table.Row{
			{sprint.ID, sprint.Name, sprint.Goal, sprint.StartDate, sprint.EndDate, sprint.State},
		})
	}

	return writer.Render(), nil
}

func (o *ScrumOperator) sprint(id int) (*Sprint, error) {
	url := fmt.Sprintf("%s/rest/agile/1.0/sprint/%d", configs.Mgmt.Jira.Host, id)
	resp, err := o.RestyClient.GetRaw(url, nil)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_sprint_failed")
	}

	if !resp.IsSuccess() {
		return nil, errors.Errorf("fetch_sprint_failed: %s", resp.String())
	}

	var data Sprint
	if err = json.Unmarshal(resp.Body(), &data); err != nil {
		return nil, errors.Wrap(err, "unmarshal_sprint_failed")
	}

	return &data, nil
}

func (o *ScrumOperator) showSprint(ctx *cli.Context) (string, error) {
	sprint, err := o.sprint(ctx.Int("sprint-id"))
	if err != nil {
		return "", errors.WithMessage(err, "show_sprint_failed")
	}

	writer := sprint.TableWriter()

	return writer.Render(), nil
}

func (o *ScrumOperator) doStartSprint(sprintID, scrumID int) (bool, error) {
	sprint, err := o.sprint(sprintID)
	if err != nil {
		return false, errors.WithMessage(err, "start_sprint_failed")
	}

	req := sprint.ToStartReq()
	req.RapidViewID = scrumID

	url := fmt.Sprintf("%s/rest/greenhopper/1.0/sprint/%d/start", configs.Mgmt.Jira.Host, sprintID)
	resp, err := o.RestyClient.PutRaw(url, req)
	if err != nil {
		return false, errors.WithMessage(err, "start_sprint_failed")
	}

	if !resp.IsSuccess() {
		return false, errors.Errorf("start_sprint_failed: %s", resp.String())
	}

	return true, nil
}

func (o *ScrumOperator) startSprint(ctx *cli.Context) (string, error) {
	ok, err := o.doStartSprint(ctx.Int("sprint-id"), ctx.Int("scrum-id"))
	if err != nil {
		return "", errors.WithMessage(err, "start_sprint_failed")
	}
	if !ok {
		return "", errors.New("start_sprint_failed")
	}

	return "Sprint successfully started", nil
}

type finishSprintReq struct {
	RapidViewID                 int    `json:"rapidViewId"`
	SprintID                    int    `json:"sprintId"`
	IncompleteIssuesDestination string `json:"incompleteIssuesDestination"`
}

func (o *ScrumOperator) doFinishSprint(sprintID, scrumID int, nextSprintID string) (bool, error) {
	url := fmt.Sprintf("%s/rest/greenhopper/1.0/sprint/%d/complete", configs.Mgmt.Jira.Host, sprintID)
	resp, err := o.RestyClient.PutRaw(url, finishSprintReq{
		RapidViewID:                 scrumID,
		SprintID:                    sprintID,
		IncompleteIssuesDestination: nextSprintID,
	})
	if err != nil {
		return false, errors.WithMessage(err, "finish_sprint_failed")
	}

	if !resp.IsSuccess() {
		return false, errors.Errorf("finish_sprint_failed: %s", resp.String())
	}

	return true, nil
}

func (o *ScrumOperator) finishSprint(ctx *cli.Context) (string, error) {
	ok, err := o.doFinishSprint(ctx.Int("sprint-id"), ctx.Int("scrum-id"), ctx.String("next-sprint-id"))
	if err != nil {
		return "", errors.WithMessage(err, "finish_sprint_failed")
	}
	if !ok {
		return "", errors.New("finish_sprint_failed")
	}

	return "Sprint successfully completed", nil
}

func (o *ScrumOperator) doCreateSprintByName(username string, scrumID int, project string) (*Sprint, error) {
	startDate := now.BeginningOfWeek()
	endDate := now.EndOfWeek()
	namePostfix := fmt.Sprintf("%d/%d-%d/%d", startDate.Month(), startDate.Day(), endDate.Month(), endDate.Day())
	newSprintName := fmt.Sprintf("%s %s", username, namePostfix)

	sprints, err := o.sprintsFromBacklog(scrumID, project)
	if err != nil {
		return nil, errors.WithMessage(err, "sprints_from_backlog_failed")
	}

	sprint, ok := slice.FindBy(sprints, func(_ int, sprint *Sprint) bool {
		return strings.EqualFold(sprint.Name, username)
	})
	if !ok {
		// create a new sprint
		req := createSprintReq{
			Name:      newSprintName,
			Goal:      "",
			StartDate: now.BeginningOfWeek().Format("2006-01-02 15:04"),
			EndDate:   now.EndOfWeek().Format("2006-01-02 15:04"),
		}
		sprint, err = o.doCreateSprint(scrumID, req)
		if err != nil {
			return nil, errors.WithMessage(err, "create_sprint_failed")
		}
	}

	return sprint, nil
}

func (o *ScrumOperator) doFinishActiveSprint(username string, scrumID int, project string, next *Sprint) error {
	sprints, err := o.doListSprints(scrumID, project)
	if err != nil {
		return errors.WithMessage(err, "list_sprints_failed")
	}
	sprint, ok := slice.FindBy(sprints, func(_ int, sprint *Sprint) bool {
		return sprint.IsActive() && strings.HasPrefix(sprint.Name, username)
	})
	if !ok {
		// active_sprint_not_found
		return nil
	}

	ok, err = o.doFinishSprint(sprint.ID, scrumID, fmt.Sprintf("%d", next.ID))
	if err != nil {
		return errors.WithMessage(err, "finish_sprint_failed")
	}
	if !ok {
		return errors.New("finish_sprint_failed")
	}

	return nil
}

/*
1. create a new sprint by username when no the same name sprint
2. fetch unfinished sprint by username then finish it
3. start the new sprint
*/
func (o *ScrumOperator) rotateSprint(ctx *cli.Context) (string, error) {
	sprint, err := o.doCreateSprintByName(ctx.String("username"), ctx.Int("scrum-id"), ctx.String("project"))
	if err != nil {
		return "", errors.WithMessage(err, "create_sprint_failed")
	}

	err = o.doFinishActiveSprint(ctx.String("username"), ctx.Int("scrum-id"), ctx.String("project"), sprint)
	if err != nil {
		return "", errors.WithMessage(err, "finish_sprint_failed")
	}

	ok, err := o.doStartSprint(sprint.ID, ctx.Int("scrum-id"))
	if err != nil {
		return "", errors.WithMessage(err, "start_sprint_failed")
	}
	if !ok {
		return "", errors.New("start_sprint_failed")
	}

	return fmt.Sprintf("Rotate successfully: %s", sprint.Name), nil
}

func (o *ScrumOperator) sprintCommand(acts *botapp.ActionFuncs) *cli.Command {
	cmd := &cli.Command{
		Name:  "sprint",
		Usage: "sprint related operation",
		Subcommands: []*cli.Command{
			{
				Name: "create",
				Flags: []cli.Flag{
					&cli.StringFlag{Name: "name", Aliases: []string{"n"}, Usage: "sprint name", Required: true},
					&cli.StringFlag{Name: "goal", Aliases: []string{"g"}, Usage: "sprint goal"},
					//nolint:lll
					&cli.StringFlag{Name: "start", Aliases: []string{"s"}, Usage: "sprint start date, format: YYYY-MM-DD, default: first day of the week"},
					//nolint:lll
					&cli.StringFlag{Name: "end", Aliases: []string{"e"}, Usage: "sprint end date, format: YYYY-MM-DD, default: last day of the week"},
					//nolint:lll
					&cli.IntFlag{Name: "scrum-id", Aliases: []string{"id", "scrum"}, Usage: "sprint scrum id, default: AZSRE SGW SCRUM", Value: 968935},
				},
				Action: acts.Action(o.createSprint),
			},
			{
				Name:    "list",
				Aliases: []string{"ls"},
				Flags: []cli.Flag{
					//nolint:lll
					&cli.IntFlag{Name: "scrum-id", Aliases: []string{"id", "scrum"}, Usage: "sprint scrum id, default: AZSRE SGW SCRUM", Value: 968935},
					&cli.StringFlag{Name: "project", Aliases: []string{"p"}, Usage: "sprint project key", Value: "SATOS"},
				},
				Action: acts.Action(o.listSprints),
			},
			{
				Name: "backlog",
				Flags: []cli.Flag{
					//nolint:lll
					&cli.IntFlag{Name: "scrum-id", Aliases: []string{"id", "scrum"}, Usage: "sprint scrum id, default: AZSRE SGW SCRUM", Value: 968935},
					&cli.StringFlag{Name: "project", Aliases: []string{"p"}, Usage: "sprint project key", Value: "SATOS"},
				},
				Action: acts.Action(o.backlogSprints),
			},
			{
				Name: "show",
				Flags: []cli.Flag{
					&cli.IntFlag{Name: "sprint-id", Aliases: []string{"id"}, Usage: "sprint id", Required: true},
				},
				Action: acts.Action(o.showSprint),
			},
			{
				Name: "start",
				Flags: []cli.Flag{
					&cli.IntFlag{Name: "sprint-id", Aliases: []string{"id"}, Usage: "sprint id", Required: true},
					//nolint:lll
					&cli.IntFlag{Name: "scrum-id", Aliases: []string{"scrum"}, Usage: "sprint scrum id, default: AZSRE SGW SCRUM", Value: 968935},
				},
				Action: acts.Action(o.startSprint),
			},
			{
				Name: "finish",
				Flags: []cli.Flag{
					&cli.IntFlag{Name: "sprint-id", Aliases: []string{"id"}, Usage: "sprint id", Required: true},
					//nolint:lll
					&cli.IntFlag{Name: "scrum-id", Aliases: []string{"scrum"}, Usage: "sprint scrum id, default: AZSRE SGW SCRUM", Value: 968935},
					&cli.IntFlag{Name: "next-sprint-id", Aliases: []string{"next"}, Usage: "next sprint id", Required: true},
				},
				Action: acts.Action(o.finishSprint),
			},
			{
				Name: "rotate",
				Flags: []cli.Flag{
					//nolint:lll
					&cli.IntFlag{Name: "scrum-id", Aliases: []string{"id", "scrum"}, Usage: "sprint scrum id, default: AZSRE SGW SCRUM", Value: 968935},
					&cli.StringFlag{Name: "project", Aliases: []string{"p"}, Usage: "sprint project key", Value: "SATOS"},
					&cli.StringFlag{Name: "username", Aliases: []string{"u"}, Usage: "username", Required: true},
				},
				Action: acts.Action(o.rotateSprint),
			},
		},
	}

	return cmd
}
