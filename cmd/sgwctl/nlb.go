package main

import (
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

type NLBOperator struct {
	Operator

	nlb *botapp.NLBOperatorApp
}

func NewNLBOperator(app *botapp.OperatorApp) *NLBOperator {
	nlb := botapp.NewNLBOperatorApp(app)

	return &NLBOperator{
		nlb: nlb,
	}
}

func (o *NLBOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "nlb",
		Usage: "nlb(network load balancer) related operation",
		Subcommands: []*cli.Command{
			o.nlb.WikiCommand(acts.Action),
			o.nlb.NodeCommand(acts),
			o.nlb.VIPCommand(acts),
			o.nlb.InstanceCommand(acts),
			o.nlb.ReplicaCommand(acts),
		},
	}

	return cmd
}
