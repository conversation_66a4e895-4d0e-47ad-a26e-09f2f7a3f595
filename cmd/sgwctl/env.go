package main

import (
	"os"
	"strings"

	"github.com/urfave/cli/v2"
)

type EnvOperator struct {
	Operator
}

func NewEnvOperator() *EnvOperator {
	return &EnvOperator{}
}

func (o *EnvOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "env",
		Usage: "env related operation",
		Action: acts.Action(func(ctx *cli.Context) (string, error) {
			return strings.Join(os.Environ(), "\n"), nil
		}),
	}

	return cmd
}
