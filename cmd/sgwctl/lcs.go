package main

import (
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

type LCSOperator struct {
	Operator

	lcs *botapp.LCSOperatorApp
}

func NewLCSOperator(app *botapp.OperatorApp) *LCSOperator {
	return &LCSOperator{
		lcs: botapp.NewLCSOperatorApp(app),
	}
}

func (o *LCSOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "lcs",
		Usage: "lcs related operation",
		Subcommands: []*cli.Command{
			o.lcs.OpsCommand(acts.Action),
			o.lcs.WikiCommand(acts.Action),
			o.lcs.ClusterCommand(acts),
			o.lcs.InstanceCommand(acts),
			o.lcs.ListenerCommand(acts),
			o.lcs.LabelCommand(acts),
			o.lcs.NodeCommand(acts),
		},
	}

	return cmd
}
