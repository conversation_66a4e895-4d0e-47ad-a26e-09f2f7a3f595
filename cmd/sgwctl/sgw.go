package main

import (
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

type SGWOperator struct {
	Operator

	sgw *botapp.SGWOperatorApp
	Opr *botapp.OperatorApp
}

func NewSGWOperator(app *botapp.OperatorApp) *SGWOperator {
	sgw := botapp.NewSGWOperatorApp(app)

	return &SGWOperator{
		sgw: sgw,
		Opr: sgw.Opr,
	}
}

func (o *SGWOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "sgw",
		Usage: "sgw related operation",
		Subcommands: []*cli.Command{
			o.sgw.NodeCommand(acts),
			o.sgw.AlertCommand(acts),
			o.sgw.EventCommand(acts),
			o.Opr.AZ<PERSON>ommand(acts),
			o.Opr.SegmentCommand(acts),
			o.Opr.RemedyCommand(acts),
		},
	}

	return cmd
}
