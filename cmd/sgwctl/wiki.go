package main

import (
	"os"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/pkg/errors"
	"github.com/urfave/cli/v2"
	goconfluence "github.com/virtomize/confluence-go-api"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

type WikiOperator struct {
	Operator

	wiki *goconfluence.API
}

func NewWikiOperator() *WikiOperator {
	return &WikiOperator{}
}

func (o *WikiOperator) newWiki(_ *cli.Context) (string, error) {
	wiki, err := goconfluence.NewAPI("https://confluence.shopee.io/rest/api", "", os.Getenv("CONFLUENCE_API_TOKEN"))
	if err != nil {
		return "", errors.WithMessage(err, "new_confluence_api_failed")
	}

	o.wiki = wiki

	return "", nil
}

func (o *WikiOperator) pageCommand(acts *botapp.ActionFuncs) *cli.Command {
	cmd := &cli.Command{
		Name:  "page",
		Usage: "page related operation",
		Flags: []cli.Flag{
			&cli.StringFlag{Name: "id", Aliases: []string{"i"}, Usage: "wiki page id", Required: true},
			&cli.BoolFlag{Name: "debug", Aliases: []string{"d"}, Value: false, Hidden: true, Action: o.setDebug},
		},
		Action: acts.Action(func(ctx *cli.Context) (string, error) {
			page, err := o.wiki.GetContentByID(ctx.String("id"), goconfluence.ContentQuery{
				SpaceKey: ctx.String("space"),
				Expand:   []string{"body.storage", "version"},
			})
			if err != nil {
				return "", errors.WithMessage(err, "fetch_page_failed")
			}

			return page.Body.Storage.Value, nil
		}),
	}

	return cmd
}

func (o *WikiOperator) userCommand(acts *botapp.ActionFuncs) *cli.Command {
	cmd := &cli.Command{
		Name:  "user",
		Usage: "user related operation",
		Action: acts.Action(func(ctx *cli.Context) (string, error) {
			user, err := o.wiki.CurrentUser()
			if err != nil {
				return "", errors.WithMessage(err, "fetch_user_failed")
			}

			writer := table.NewWriter()
			writer.AppendRows([]table.Row{
				{"ID", user.AccountID},
				{"DisplayName", user.DisplayName},
				{"Username", user.Username},
				{"UserKey", user.UserKey},
				{"Type", user.Type},
			})

			return writer.Render(), nil
		}),
	}

	return cmd
}

func (o *WikiOperator) setDebug(ctx *cli.Context, _ bool) error {
	if ctx.IsSet("debug") {
		goconfluence.DebugFlag = true
	}

	return nil
}

func (o *WikiOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "wiki",
		Usage: "wiki related operation",
		Flags: []cli.Flag{
			&cli.StringFlag{Name: "space", Aliases: []string{"s"}, Usage: "space key", Value: "STPS"},
		},
		Before: acts.Before(o.newWiki),
		Subcommands: []*cli.Command{
			o.userCommand(acts),
			o.pageCommand(acts),
			o.projectCommand(acts),
		},
	}

	return cmd
}
