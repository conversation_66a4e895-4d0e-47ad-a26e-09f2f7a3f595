package main

import (
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

type ALBOperator struct {
	Operator

	alb *botapp.ALBOperatorApp
}

func NewALBOperator(app *botapp.OperatorApp) *ALBOperator {
	return &ALBOperator{
		alb: botapp.NewALBOperatorApp(app),
	}
}

func (o *ALBOperator) NodeCommands(acts *botapp.ActionFuncs) *cli.Command {
	nodeCmds := o.alb.NodeCommand(acts)
	nodeCmds.Subcommands = append(nodeCmds.Subcommands, o.alb.QATCommand(acts))

	return nodeCmds
}

func (o *ALBOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "alb",
		Usage: "alb related operation",
		Subcommands: []*cli.Command{
			o.alb.OpsCommand(acts.Action),
			o.alb.WikiCommand(acts.Action),
			o.alb.ClusterCommands(acts),
			o.alb.LabelCommand(acts),
			o.alb.InstanceCommand(acts),
			o.alb.ListenerCommand(acts),
			o.alb.TargetCommand(acts),
			o.alb.DeployConfigCommand(acts),
			o.alb.EKSCommands(acts),
			o.NodeCommands(acts),
		},
	}

	return cmd
}
