package main

import (
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

// TOCOperator toc operator
type TOCOperator struct {
	Operator

	toc *botapp.TOCOperatorApp
	Opr *botapp.OperatorApp
}

// NewTOCOperator toc operator
func NewTOCOperator(app *botapp.OperatorApp) *TOCOperator {
	toc := botapp.NewTOCOperatorApp(app)

	return &TOCOperator{
		toc: toc,
		Opr: toc.Opr,
	}
}

// Commands toc operator commands
func (o *TOCOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "toc",
		Usage: "toc related operation",
		Subcommands: []*cli.Command{
			o.toc.NodeCommand(acts),
			o.Opr.AZCommand(acts),
			o.Opr.SegmentCommand(acts),
		},
	}

	return cmd
}
