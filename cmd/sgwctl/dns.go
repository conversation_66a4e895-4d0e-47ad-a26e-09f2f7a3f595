package main

import (
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

type DNSOperator struct {
	Operator

	dns *botapp.DNSOperatorApp
}

func NewDNSOperator(app *botapp.OperatorApp) *DNSOperator {
	return &DNSOperator{
		dns: botapp.NewDNSOperatorApp(app),
	}
}

// Commands dns commands
func (o *DNSOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "dns",
		Usage: "dns related operation",
		Subcommands: []*cli.Command{
			o.dns.WikiCommand(o.doAction),
			o.dns.DomainCommand(o.doAction),
			o.dns.RecordCommand(acts),
			o.dns.NodeCommand(acts),
			o.dns.ClusterCommand(acts),
		},
	}

	return cmd
}
