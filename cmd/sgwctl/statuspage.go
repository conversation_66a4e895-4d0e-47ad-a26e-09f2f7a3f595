package main

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/meta"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/rest"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/meta/metavo"
)

type StatusPageOperator struct {
	Operator

	cluster   meta.ClusterAdapter
	l7Cluster sgw.L7ClusterAdapter
	client    *rest.RestyClient
}

func NewStatusPageOperator() *StatusPageOperator {
	return &StatusPageOperator{}
}

func (o *StatusPageOperator) init(_ *cli.Context) (string, error) {
	traceID := o.TraceID()
	o.cluster = meta.NewClusterAdapter(traceID)
	o.client = rest.NewRestyClient(traceID)

	return "", nil
}

func (o *StatusPageOperator) initL7Cluster(componentName string) error {
	traceID := o.TraceID()

	switch strings.ToLower(componentName) {
	case consts.ALB:
		o.l7Cluster = sgw.NewALBClusterAdapter(traceID)
	case consts.LCS:
		o.l7Cluster = sgw.NewLCSClusterAdapter(traceID)
	case consts.Ext2:
		o.l7Cluster = sgw.NewExt2ClusterAdapter(traceID)
	case consts.KeyLess:
		o.l7Cluster = sgw.NewKLSClusterAdapter(traceID)
	default:
		return errors.Errorf("unsupported_component_type: %s", componentName)
	}

	return nil
}

func (o *StatusPageOperator) addCluster(ctx *cli.Context) (string, error) {
	clusterName := ctx.Args().First()
	if clusterName == "" {
		return "", errors.New("cluster_name_required")
	}

	componentName := ctx.String("component")
	if componentName == "" {
		return "", errors.New("component_name_required")
	}

	// Get environment from command line
	environment := ctx.String("env")

	// Check if cluster already exists
	exists, err := o.cluster.CheckClusterExists(componentName, clusterName)
	if err != nil {
		return "", errors.WithMessage(err, "check_cluster_exists_failed")
	}

	if exists {
		return "", errors.Errorf("cluster_already_exists: %s", clusterName)
	}

	// Initialize L7ClusterAdapter for the specific component
	if err := o.initL7Cluster(componentName); err != nil {
		return "", errors.WithMessage(err, "init_l7_cluster_failed")
	}

	// Get cluster metadata to extract AZ and segment
	cluster, err := o.l7Cluster.GetByName(clusterName)
	if err != nil {
		return "", errors.WithMessage(err, "get_cluster_meta_failed")
	}

	// Extract segment from cluster metadata
	segment := cluster.Segment

	// Use MetaAdapter to get AZ information and IDC
	az := cluster.AZ
	if az == "" {
		return "", errors.Errorf("az_not_found_for_cluster: %s", clusterName)
	}

	resourceZone := cluster.RZ
	if resourceZone == "" {
		return "", errors.New("resource_zone_not_found")
	}

	// Create labels with user comment
	labels := ""
	if ctx.String("labels") != "" {
		labels = ctx.String("labels")
	}

	// Prepare request data
	req := &metavo.ClusterStatusPageRequest{
		Name:          clusterName,
		Availability:  ctx.String("availability"),
		Status:        ctx.String("status"),
		ComponentName: componentName,
		Environment:   environment,
		ResourceZone:  resourceZone,
		AZ:            az,
		Segment:       segment,
		Labels:        labels,
	}

	// Create cluster using ClusterAdapter
	err = o.cluster.CreateCluster(req)
	if err != nil {
		return "", errors.WithMessage(err, "create_cluster_failed")
	}

	return fmt.Sprintf("cluster_added_successfully: %s\naz: %s\nsegment: %s\nenvironment: %s\nresource_zone: %s",
		clusterName, az, segment, environment, resourceZone), nil
}

func (o *StatusPageOperator) checkCluster(ctx *cli.Context) (string, error) {
	clusterName := ctx.Args().First()
	if clusterName == "" {
		return "", errors.New("cluster_name_required")
	}

	// Get component name for fallback logic when cluster name doesn't match slug pattern
	componentName := ctx.String("component")

	// Get cluster information from status page
	cluster, err := o.cluster.GetClusterByName(componentName, clusterName)
	if err != nil {
		return "", errors.WithMessage(err, "get_cluster_failed")
	}

	// Format the cluster information
	result := fmt.Sprintf("cluster_information: %s\n", clusterName)
	result += fmt.Sprintf("id: %s\n", cluster.ID)
	result += fmt.Sprintf("name: %s\n", cluster.Name)
	result += fmt.Sprintf("availability: %s\n", cluster.Availability)
	result += fmt.Sprintf("status: %s\n", cluster.Status)
	result += fmt.Sprintf("component_name: %s\n", cluster.ComponentName)
	result += fmt.Sprintf("environment: %s\n", cluster.Environment)
	result += fmt.Sprintf("resource_zone: %s\n", cluster.ResourceZone)
	result += fmt.Sprintf("az: %s\n", cluster.AZ)
	result += fmt.Sprintf("segment: %s\n", cluster.Segment)
	result += fmt.Sprintf("labels: %s", cluster.Labels)

	return result, nil
}

func (o *StatusPageOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:    "statuspage",
		Aliases: []string{"sp"},
		Usage:   "status page related operations",
		Before:  acts.Before(o.init),
		Subcommands: []*cli.Command{
			{
				Name:      "add-cluster",
				Usage:     "add cluster to status page",
				ArgsUsage: "<cluster-name>",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:     "component",
						Aliases:  []string{"c"},
						Usage:    "component name (ALB, NLB, DNS, etc.)",
						Required: true,
					},
					&cli.StringFlag{
						Name:  "availability",
						Usage: "cluster availability status",
						Value: "available",
					},
					&cli.StringFlag{
						Name:  "status",
						Usage: "cluster status",
						Value: "Online",
					},
					&cli.StringFlag{
						Name:  "user",
						Usage: "user name for comment",
						Value: "sgwctl",
					},
					&cli.StringFlag{
						Name:  "labels",
						Usage: "custom labels JSON string",
					},
					&cli.StringFlag{
						Name:     "env",
						Aliases:  []string{"e"},
						Usage:    "environment (live, liveish, etc.)",
						Required: true,
					},
				},
				Action: acts.Action(o.addCluster),
			},
			{
				Name:      "check-cluster",
				Usage:     "check cluster information from status page",
				ArgsUsage: "<cluster-name>",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:    "component",
						Aliases: []string{"c"},
						Usage:   "component name (ALB, NLB, DNS, etc.) - required for fallback when cluster name contains special characters",
						Value:   "ALB",
					},
				},
				Action: acts.Action(o.checkCluster),
			},
		},
	}

	return cmd
}
