package main

import (
	"github.com/pkg/errors"
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/bot"
)

// BotOperator bot operator
type BotOperator struct {
	Operator
}

// NewBotOperator new bot operator
func NewBotOperator() *BotOperator {
	return &BotOperator{}
}

// ChatCommands chat related operations
func (o *BotOperator) ChatCommands() *cli.Command {
	cmd := &cli.Command{
		Name:    "chat",
		Aliases: []string{"c"},
		Usage:   "chat related operations",
		Flags: []cli.Flag{
			&cli.StringFlag{Name: "query", Aliases: []string{"q", "msg", "m"}, Usage: "query message"},
			&cli.BoolFlag{Name: "trace", Aliases: []string{"t", "s", "stack"}, Usage: "show response traceback"},
		},
		Action: func(ctx *cli.Context) error {
			o.ctx = ctx

			iBot := bot.NewBotAdapter(o.TraceID())
			resp, err := iBot.Chat(ctx.String("query"))
			if err != nil {
				return errors.Wrap(err, "bot_chat_failed")
			}

			if !resp.Success {
				o.Println(resp.Error.Message)
			} else {
				o.Println(resp.Result)
			}

			return nil
		},
	}

	return cmd
}

// Commands bot subcommands
func (o *BotOperator) Commands() *cli.Command {
	cmd := &cli.Command{
		Name:  "bot",
		Usage: "bot related operation",
		Subcommands: []*cli.Command{
			o.ChatCommands(),
		},
	}

	return cmd
}
