package main

import (
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

type SDNOperator struct {
	Operator

	sdn *botapp.SDNOperatorApp
}

func NewSDNOperator(app *botapp.OperatorApp) *SDNOperator {
	sdn := botapp.NewSDNOperatorApp(app)

	return &SDNOperator{
		sdn: sdn,
	}
}

func (o *SDNOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "sdn",
		Usage: "sdn related operation",
		Subcommands: []*cli.Command{
			o.sdn.ClusterCommand(acts),
		},
	}

	return cmd
}
