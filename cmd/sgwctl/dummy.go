package main

import (
	"github.com/pkg/errors"
	"github.com/urfave/cli/v2"

	infrauic "git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/uic"
)

type DummyOperator struct {
	Operator
}

func NewDummyOperator() *DummyOperator {
	return &DummyOperator{}
}

func (o *DummyOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:  "dummy",
		Usage: "dummy related operation",
		Subcommands: []*cli.Command{
			{
				Name: "auth",
				Flags: []cli.Flag{
					&cli.BoolFlag{Name: "skip-auth", Usage: "skip authentication", Aliases: []string{"S"}, Hidden: true},
				},
				Before: acts.BeforeWithAuth(o.doNil),
				Action: func(ctx *cli.Context) error {
					return nil
				},
			},
			{
				Name: "token",
				Action: acts.Action(func(ctx *cli.Context) (string, error) {
					token, err := infrauic.NewSessionAdapter().BotToken()
					if err != nil {
						return "", errors.WithMessage(err, "failed_to_get_token")
					}

					return token, nil
				}),
			},
		},
	}

	return cmd
}
