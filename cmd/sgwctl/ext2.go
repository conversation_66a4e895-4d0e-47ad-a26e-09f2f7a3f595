package main

import (
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

type EXTOperator struct {
	Operator

	ext *botapp.Ext2OperatorApp
}

func NewExt2Operator(app *botapp.OperatorApp) *EXTOperator {
	ext := botapp.NewExt2OperatorApp(app)

	return &EXTOperator{
		ext: ext,
	}
}

func (o *EXTOperator) Commands() *cli.Command {
	acts := o.NewActionFuncs()

	cmd := &cli.Command{
		Name:    "ext2",
		Aliases: []string{"ext"},
		Usage:   "ext(external2) related operation",
		Subcommands: []*cli.Command{
			o.ext.OpsCommand(acts.Action),
			o.ext.WikiCommand(acts.Action),
			o.ext.ClusterCommands(acts),
			o.ext.NodeCommand(acts),
		},
	}

	return cmd
}
