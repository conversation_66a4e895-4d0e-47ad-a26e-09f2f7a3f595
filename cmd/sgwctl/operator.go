package main

import (
	"fmt"
	"io"
	"os"
	"runtime"
	"strings"

	"github.com/c-bata/go-prompt"
	uuid "github.com/iris-contrib/go.uuid"
	"github.com/pkg/errors"
	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/go-shopeelib/byteutils"

	"git.garena.com/shopee/devops/uic-api/pkg"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	infrauic "git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/uic"
)

// Operator sgwctl operators
type Operator struct {
	ctx *cli.Context

	extra *botapp.Extra
}

// NewOperator operator instance, include all commands (basic, service, server, timeout and daemon)
func NewOperator() *Operator {
	return &Operator{
		extra: botapp.NewExtra(),
	}
}

type Writer struct {
	StdOut io.Writer
	StdErr io.Writer
}

// Commands return supported commands
func (o *Operator) Commands(writer Writer) []*cli.Command {
	var cmds []*cli.Command
	// register commands here
	app := botapp.NewOperatorApp()
	app.SetWriter(writer.StdOut)
	app.SetErrWriter(writer.StdErr)
	app.SetExtra(o.extra)
	app.Extra()["traceID"] = uuid.Must(uuid.NewV4()).String()

	sdn := NewSDNOperator(app)
	sgw := NewSGWOperator(app)
	nlb := NewNLBOperator(app)
	alb := NewALBOperator(app)
	lcs := NewLCSOperator(app)
	ext := NewExt2Operator(app)
	dns := NewDNSOperator(app)
	ops := NewOpsOperator(app)
	toc := NewTOCOperator(app)
	bot := NewBotOperator()
	env := NewEnvOperator()
	wiki := NewWikiOperator()
	scrum := NewScrumOperator()
	dummy := NewDummyOperator()
	statuspage := NewStatusPageOperator()

	cmds = append(cmds, sgw.Commands(), nlb.Commands(),
		alb.Commands(), lcs.Commands(), ext.Commands(),
		dns.Commands(), ops.Commands(), bot.Commands(), sdn.Commands(), env.Commands(),
		dummy.Commands(), toc.Commands(), wiki.Commands(), scrum.Commands(), statuspage.Commands())

	return cmds
}

const sgwctl = `
   _____   ______ _       __   ______  ______    __
  / ___/  / ____/| |     / /  / ____/ /_  __/   / /
  \__ \  / / __  | | /| / /  / /       / /     / /
 ___/ / / /_/ /  | |/ |/ /  / /___    / /     / /___
/____/  \____/   |__/|__/   \____/   /_/     /_____/

`

// ShowVersion print version information, including commit-id, built-time etc.
func (o *Operator) ShowVersion() func(c *cli.Context) {
	return func(c *cli.Context) {
		o.ctx = c

		ver := consts.NewVersion(c.App.Version)
		o.Printf(sgwctl)
		o.Printf("%s %s commit id %s\n", c.App.Name, c.App.Version, ver.CommitID)
		o.Printf("Built by %s %s/%s compiler %s at %s\n",
			runtime.Version(), runtime.GOOS, runtime.GOARCH, runtime.Compiler, ver.Built)
	}
}

// Printf output message into STDOUT
func (o *Operator) Printf(format string, a ...interface{}) {
	_, _ = fmt.Fprintf(o.ctx.App.Writer, format, a...)
}

// Println output message into STDOUT with linebreak
func (o *Operator) Println(a ...interface{}) {
	_, _ = fmt.Fprintln(o.ctx.App.Writer, a...)
}

func (o *Operator) NewActionFuncs() *botapp.ActionFuncs {
	return &botapp.ActionFuncs{
		Before:         o.doBefore,
		BeforeWithAuth: o.doBeforeWithAuth,
		Action:         o.doAction,
		After:          o.doAfter,
	}
}

//nolint:nolintlint,gosec
const (
	spaceFileDir       = "$HOME/.space"
	spaceTokenFilepath = "$HOME/.space/token"
)

func (o *Operator) Token() string {
	tokens, _ := os.ReadFile(os.ExpandEnv(spaceTokenFilepath))

	return byteutils.ToString(tokens)
}

func (o *Operator) TraceID() string {
	if o.extra != nil && o.extra.Get("traceID") != "" {
		return o.extra.Get("traceID")
	}

	return uuid.Must(uuid.NewV4()).String()
}

func (o *Operator) Extra() map[string]string {
	if o.extra == nil {
		o.extra = botapp.NewExtra()
	}

	return o.extra.Map()
}

func (o *Operator) promptToken() (*pkg.AuthResponse, error) {
	token := prompt.Input("Input Space Token:", func(doc prompt.Document) []prompt.Suggest {
		return prompt.FilterFuzzy([]prompt.Suggest{}, doc.GetWordBeforeCursor(), true)
	})
	token = strings.TrimSpace(token)
	if token == "" {
		return nil, fmt.Errorf("token_not_found")
	}

	resp, err := pkg.ValidateToken(token)
	if err != nil {
		return nil, errors.WithMessage(err, "validate_token")
	}

	if _, err := os.Stat(os.ExpandEnv(spaceFileDir)); os.IsNotExist(err) {
		_ = os.MkdirAll(os.ExpandEnv(spaceFileDir), consts.FileRWX)
	}

	filename := os.ExpandEnv(spaceTokenFilepath)
	if err := os.WriteFile(filename, byteutils.ToBytes(token), consts.FileRWRR); err != nil {
		return nil, errors.Wrap(err, "write_token_to_file")
	}

	return resp, nil
}

func (o *Operator) authByToken() (*pkg.AuthResponse, error) {
	var token string
	filename := os.ExpandEnv(spaceTokenFilepath)
	var resp *pkg.AuthResponse

	tokens, err := os.ReadFile(filename)
	token = strings.TrimSpace(byteutils.ToString(tokens))

	if os.IsNotExist(err) || token == "" {
		resp, err = o.promptToken()
	} else {
		resp, err = pkg.ValidateToken(token)
	}

	if err == nil {
		return resp, nil
	}

	if strings.Contains(err.Error(), "token is expired") {
		if token != "" {
			err = os.WriteFile(filename, []byte{}, consts.FileRWRR)
			if err != nil && !os.IsNotExist(err) {
				return nil, errors.Wrap(err, "reset_token_file_failed")
			}
		}

		return o.authByToken()
	}

	return nil, errors.Wrap(err, "uic_validate_token")
}

func (o *Operator) doNil(_ *cli.Context) (string, error) {
	return "", nil
}

func (o *Operator) doBefore(action botapp.Action) cli.BeforeFunc {
	return func(ctx *cli.Context) error {
		o.ctx = ctx

		if action == nil {
			return fmt.Errorf("before func not found")
		}

		ret, err := action(ctx)
		if err != nil {
			return err
		}

		o.Println(ret)

		return nil
	}
}

func (o *Operator) doBeforeWithAuth(action botapp.Action) cli.BeforeFunc {
	return func(ctx *cli.Context) error {
		o.ctx = ctx

		if action == nil {
			return fmt.Errorf("before func not found")
		}

		if !ctx.IsSet("skip-auth") {
			if _, err := o.authenticate(ctx); err != nil {
				return err
			}
		}

		ret, err := action(ctx)
		if err != nil {
			return err
		}

		o.Println(ret)

		return nil
	}
}

func (o *Operator) doAction(action botapp.Action) cli.ActionFunc {
	return func(ctx *cli.Context) error {
		o.ctx = ctx

		if action == nil {
			return fmt.Errorf("action func not found")
		}

		ret, err := action(ctx)
		if err != nil {
			return err
		}

		o.Println(ret)

		return nil
	}
}

func (o *Operator) doAfter(action botapp.Action) cli.AfterFunc {
	return func(ctx *cli.Context) error {
		o.ctx = ctx

		if action == nil {
			return fmt.Errorf("after func not found")
		}

		ret, err := action(ctx)
		if err != nil {
			return err
		}

		o.Println(ret)

		return nil
	}
}

func (o *Operator) authenticate(ctx *cli.Context) (bool, error) {
	resp, err := o.authByToken()
	if err != nil {
		return false, errors.Wrap(err, "auth_token_failed")
	}

	client, err := infrauic.NewUserAdapter(o.TraceID())
	if err != nil {
		return false, errors.Wrap(err, "new_uic_service")
	}
	user, err := client.UserByEmail(resp.User.Email)
	if err != nil {
		return false, errors.Wrap(err, "fetch_user_by_email")
	}

	var groups []string
	for _, group := range user.Groups {
		groups = append(groups, group.GroupName)
	}

	if !configs.Mgmt.IsUserAllow(user.Username) && !configs.Mgmt.IsGroupAllow(groups) {
		return false, fmt.Errorf("you aren't in the allow groups")
	}

	if !ctx.IsSet("email") {
		_ = ctx.Set("email", resp.User.Email)
	}

	return true, nil
}
