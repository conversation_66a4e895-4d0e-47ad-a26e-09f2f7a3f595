package main

import (
	"log"
	"os"
	"strings"

	"github.com/urfave/cli/v2"

	slog "git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsinf/opscah"

	_ "embed"
)

// Version sgwctl version
//
//go:embed version
var Version string

func init() {
	if err := configs.Init(consts.SGW); err != nil {
		log.Fatalln(err)
	}

	if os.Getenv("ENV") == "" {
		err := os.Setenv("ENV", "live")
		if err != nil {
			log.Fatalln(err)
		}
	}

	opscah.InitElasticRedis()
	slog.InitConsole()
}

func main() {
	app := cli.NewApp()
	app.Usage = "sgw controller as a commandline application"
	app.Version = strings.TrimSpace(Version)
	app.Description = "A Shopee Gateway Controller"

	opr := NewOperator()
	cli.VersionPrinter = opr.ShowVersion()
	app.Authors = configs.BOT.Authors()
	app.Commands = opr.Commands(Writer{
		StdOut: app.Writer,
		StdErr: app.ErrWriter,
	})
	app.ExtraInfo = opr.Extra

	app.ExtraInfo()["args"] = strings.Join(os.Args, " ")
	if err := app.Run(os.Args); err != nil {
		log.Fatalln(err)
	}
}
