package main

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/jinzhu/now"
	"github.com/pkg/errors"
	"github.com/urfave/cli/v2"
	goconfluence "github.com/virtomize/confluence-go-api"
	"github.com/xlab/treeprint"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/bot/botapp"
)

func (o *WikiOperator) verifyIssueID(_ *cli.Context, val string) error {
	reg := regexp.MustCompile(`^SATOS-\d*$`)
	if !reg.MatchString(val) {
		return errors.New("invalid_epic")
	}

	return nil
}

func (o *WikiOperator) listProject(ctx *cli.Context) (string, error) {
	page, err := o.wiki.GetContentByID(ctx.String("page-id"), goconfluence.ContentQuery{
		SpaceKey: ctx.String("space"),
	})
	if err != nil {
		return "", errors.WithMessage(err, "fetch_page_failed")
	}

	search, err := o.wiki.GetChildPages(ctx.String("page-id"))
	if err != nil {
		return "", errors.WithMessage(err, "fetch_project_failed")
	}

	tree := treeprint.NewWithRoot(page.Title)
	slice.ForEach(search.Results, func(_ int, result goconfluence.Results) {
		tree = tree.AddNode(result.Title)
	})

	return tree.String(), nil
}

func (o *WikiOperator) fetchProjectPages(ctx *cli.Context) (*goconfluence.Results, *goconfluence.Search, error) {
	// find project page
	search, err := o.wiki.GetChildPages(ctx.String("parent"))
	if err != nil {
		return nil, nil, errors.WithMessage(err, "fetch_projects_failed")
	}

	result, ok := slice.FindBy(search.Results, func(_ int, result goconfluence.Results) bool {
		return strings.Contains(result.Title, ctx.String("epic"))
	})
	if !ok {
		return nil, nil, errors.New("project_page_not_found")
	}

	search, err = o.wiki.GetChildPages(result.ID)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "fetch_project_pages_failed")
	}

	return &result, search, nil
}

func (o *WikiOperator) listProjectUpdates(ctx *cli.Context) (string, error) {
	project, childPages, err := o.fetchProjectPages(ctx)
	if err != nil {
		return "", errors.WithMessage(err, "fetch_project_pages_failed")
	}

	// updates page
	result, ok := slice.FindBy(childPages.Results, func(_ int, result goconfluence.Results) bool {
		return strings.HasSuffix(result.Title, "Updates")
	})
	if !ok {
		return "", errors.New("project_updates_page_not_found")
	}

	search, err := o.wiki.GetChildPages(result.ID)
	if err != nil {
		return "", errors.WithMessage(err, "fetch_project_updates_pages_failed")
	}

	tree := treeprint.NewWithRoot(project.Title)
	updates := tree.AddBranch(result.Title)

	slice.ForEach(search.Results, func(_ int, result goconfluence.Results) {
		updates = updates.AddNode(result.Title)
	})

	return tree.String(), nil
}

func (o *WikiOperator) rotateOneProjectUpdates(ctx *cli.Context, project *goconfluence.Results) {
	o.Println(fmt.Sprintf("starts to rotate project: %s", project.Title))

	search, err := o.wiki.GetChildPages(project.ID)
	if err != nil {
		o.Println(errors.WithMessage(err, "fetch_project_pages_failed"))

		return
	}

	// updates page
	updates, ok := slice.FindBy(search.Results, func(_ int, result goconfluence.Results) bool {
		return strings.HasSuffix(result.Title, "Updates")
	})
	if !ok {
		o.Println("project_updates_page_not_found")

		return
	}

	// replace date
	from := ctx.String("from")
	if from == "" {
		// Monday
		from = now.Monday().Format("2006-01-02")
	}
	to := ctx.String("to")
	if to == "" {
		// Friday
		to = now.Monday().AddDate(0, 0, _thisWeekFridayDates).Format("2006-01-02")
	}

	title := strings.ReplaceAll(updates.Title, "Updates", fmt.Sprintf("Update %s", to))

	search2, err := o.wiki.GetContent(goconfluence.ContentQuery{
		Title:    title,
		SpaceKey: ctx.String("space"),
	})
	if err != nil {
		o.Println(errors.WithMessage(err, "search_project_update_page_failed"))

		return
	}
	if len(search2.Results) != 0 {
		o.Println(fmt.Sprintf("%s %s already created", project.Title, title))

		return
	}

	search, err = o.wiki.GetChildPages(updates.ID)
	if err != nil {
		o.Println(errors.WithMessage(err, "fetch_project_updates_pages_failed"))

		return
	}

	// fetch last update page
	_ = slice.SortByField(search.Results, "ID", "desc")
	last := search.Results[0]

	// copy content
	o.Println(fmt.Sprintf("copy from %s PageID %s", last.Title, last.ID))
	content, err := o.wiki.GetContentByID(last.ID, goconfluence.ContentQuery{
		SpaceKey: ctx.String("space"),
		Expand:   []string{"body.storage", "version"},
	})
	if err != nil {
		o.Println(errors.WithMessage(err, "fetch_project_updates_pages_failed"))

		return
	}

	body := content.Body.Storage.Value

	reg := regexp.MustCompile(`(\d{4}-\d{2}-\d{2})`)
	dates := reg.FindAllString(body, -1)
	if len(dates) >= _matchedDatesLeast {
		body = strings.ReplaceAll(body, dates[0], from)
		body = strings.ReplaceAll(body, dates[1], to)
	}

	// update content
	updateContent := o.content(pageContent{
		Title:        title,
		Body:         body,
		Space:        ctx.String("space"),
		ParentPageID: updates.ID,
	})

	if content, err = o.wiki.CreateContent(updateContent); err != nil {
		o.Println(errors.WithMessage(err, "create_project_update_page_failed"))

		return
	}

	o.Println(fmt.Sprintf("create update page %s PageID %s", title, content.ID))

	o.Println(fmt.Sprintf("rotate project %s updates success", project.Title))
}

// 1. create a new update page
// 2. copy last update page content and replace Date from and to
func (o *WikiOperator) rotateProjectUpdates(ctx *cli.Context) (string, error) {
	search, err := o.wiki.GetChildPages(ctx.String("parent"))
	if err != nil {
		return "", errors.WithMessage(err, "fetch_projects_failed")
	}

	var results []goconfluence.Results
	if ctx.String("epic") != "" {
		results = slice.Filter(search.Results, func(_ int, result goconfluence.Results) bool {
			return strings.Contains(result.Title, ctx.String("epic"))
		})
	} else {
		// fetch projects matched with prefix
		results = slice.Filter(search.Results, func(_ int, result goconfluence.Results) bool {
			return slice.ContainBy(ctx.StringSlice("prefix"), func(prefix string) bool {
				return strings.HasPrefix(result.Title, prefix)
			})
		})
	}

	if len(results) == 0 {
		o.Println("projects not found")

		return "", nil
	}

	// foreach project
	slice.ForEach(results, func(_ int, result goconfluence.Results) {
		o.rotateOneProjectUpdates(ctx, &result)
	})

	return "", nil
}

func (o *WikiOperator) briefProject(ctx *cli.Context) (string, error) {
	project, childPages, err := o.fetchProjectPages(ctx)
	if err != nil {
		return "", errors.WithMessage(err, "fetch_project_pages_failed")
	}

	tree := treeprint.NewWithRoot(project.Title)
	slice.ForEach(childPages.Results, func(_ int, result goconfluence.Results) {
		tree = tree.AddNode(result.Title)
	})

	return tree.String(), nil
}

type pageContent struct {
	Title        string
	Body         string
	Space        string
	ParentPageID string
}

func (o *WikiOperator) content(con pageContent) *goconfluence.Content {
	content := goconfluence.Content{
		Title: con.Title,
		Type:  "page",
		Ancestors: []goconfluence.Ancestor{
			{
				ID: con.ParentPageID,
			},
		},
		Body: goconfluence.Body{
			Storage: goconfluence.Storage{
				Value:          con.Body,
				Representation: "storage",
			},
		},
		Version: &goconfluence.Version{
			Number: 1,
		},
		Space: &goconfluence.Space{
			Key: con.Space,
		},
	}

	return &content
}

const (
	_ProjectDescriptionTemplatePageID = "2293783580"
	_ProjectUpdatesTemplatePageID     = "2173240328"
	_ProjectEmptyPageContent          = ""

	_AZProjectsPageID = "350551437"

	_thisWeekFridayDates = 4
	_nextWeekFridayDates = 7
	_matchedDatesLeast   = 2
)

func (o *WikiOperator) freshProjectUpdates(ctx *cli.Context, pageID string) error {
	search, err := o.wiki.GetChildPages(pageID) // project page
	if err != nil {
		return errors.WithMessage(err, "fetch_project_pages_failed")
	}

	// `Project Updates`
	result, ok := slice.FindBy(search.Results, func(_ int, result goconfluence.Results) bool {
		return strings.HasSuffix(result.Title, "Updates")
	})
	if !ok {
		return errors.New("project_updates_page_not_found")
	}

	search, err = o.wiki.GetChildPages(result.ID) // project updates page
	if err != nil {
		return errors.WithMessage(err, "fetch_project_updates_pages_failed")
	}
	// this week Fri page title like `[Epic] Update YYYY-MM-DD`
	friday := now.Monday().AddDate(0, 0, _thisWeekFridayDates) // Fri
	// `YYYY-MM-DD`
	_, ok = slice.FindBy(search.Results, func(_ int, result goconfluence.Results) bool {
		return strings.HasSuffix(result.Title, friday.Format("2006-01-02"))
	})
	if ok {
		return nil
	}

	title := fmt.Sprintf("[%s] Update %s", ctx.String("epic"), friday.Format("2006-01-02"))
	search2, err := o.wiki.GetContent(goconfluence.ContentQuery{
		Title:    title,
		SpaceKey: ctx.String("space"),
	})
	if err != nil {
		return errors.WithMessage(err, "fetch_page_by_title_failed")
	}
	if search2 != nil && len(search2.Results) != 0 {
		return nil
	}

	// copy body from template
	page, err := o.wiki.GetContentByID(_ProjectUpdatesTemplatePageID, goconfluence.ContentQuery{
		SpaceKey: ctx.String("space"),
		Expand:   []string{"space", "body.storage", "version", "container"},
	})
	if err != nil {
		return errors.WithMessage(err, "fetch_page_failed")
	}

	// replace Date From To
	body := strings.ReplaceAll(page.Body.Storage.Value, "2024-5-20", now.Monday().Format("2006-01-02"))
	body = strings.ReplaceAll(body, "2024-5-24", friday.Format("2006-01-02"))
	body = strings.ReplaceAll(body, "2024-05-31", friday.AddDate(0, 0, _nextWeekFridayDates).Format("2006-01-02"))
	con := pageContent{
		Title:        title,
		Body:         body,
		Space:        ctx.String("space"),
		ParentPageID: result.ID,
	}
	content := o.content(con)
	_, err = o.wiki.CreateContent(content)
	if err != nil {
		return errors.WithMessage(err, "create_page_failed")
	}

	return nil
}

func (o *WikiOperator) freshProjectSubpages(ctx *cli.Context, pageID string) (string, error) {
	search, err := o.wiki.GetChildPages(pageID)
	if err != nil {
		return "", errors.WithMessage(err, "fetch_project_pages_failed")
	}

	// `Project Description`
	_, ok := slice.FindBy(search.Results, func(_ int, result goconfluence.Results) bool {
		return strings.HasSuffix(result.Title, "Project Description")
	})
	if !ok {
		// copy body from template
		page, err := o.wiki.GetContentByID(_ProjectDescriptionTemplatePageID, goconfluence.ContentQuery{
			SpaceKey: ctx.String("space"),
			Expand:   []string{"body.storage", "version"},
		})
		if err != nil {
			return "", errors.WithMessage(err, "fetch_page_failed")
		}

		con := pageContent{
			Title:        fmt.Sprintf("[%s] Project Description", ctx.String("epic")),
			Body:         page.Body.Storage.Value,
			Space:        ctx.String("space"),
			ParentPageID: pageID,
		}
		content := o.content(con)
		_, err = o.wiki.CreateContent(content)
		if err != nil {
			return "", errors.WithMessage(err, "create_page_project_description_failed")
		}
	}

	for _, suffix := range []string{"Technical Designs", "Updates", "Useful Links"} {
		_, ok := slice.FindBy(search.Results, func(_ int, result goconfluence.Results) bool {
			return strings.HasSuffix(result.Title, suffix)
		})
		if !ok { // not exists
			con := pageContent{
				Title:        fmt.Sprintf("[%s] %s", ctx.String("epic"), suffix),
				Space:        ctx.String("space"),
				ParentPageID: pageID,
				Body:         _ProjectEmptyPageContent,
			}
			content := o.content(con)
			_, err = o.wiki.CreateContent(content)
			if err != nil {
				return "", errors.WithMessage(err, fmt.Sprintf("create_page_%s_failed", suffix))
			}

			o.Println(fmt.Sprintf("create_page_%s_success", suffix))
		}
	}

	err = o.freshProjectUpdates(ctx, pageID)
	if err != nil {
		return "", errors.WithMessage(err, "fresh_project_updates_failed")
	}

	return "fresh_project_pages_success", nil
}

func (o *WikiOperator) createProject(ctx *cli.Context) (string, error) {
	// whether already exists
	search, err := o.wiki.GetChildPages(ctx.String("parent"))
	if err != nil {
		return "", errors.WithMessage(err, "fetch_projects_failed")
	}

	result, ok := slice.FindBy(search.Results, func(_ int, result goconfluence.Results) bool {
		return strings.Contains(result.Title, ctx.String("epic"))
	})
	if ok {
		if ctx.Bool("fix") {
			return o.freshProjectSubpages(ctx, result.ID)
		}

		url := result.URL
		if url == "" {
			url = fmt.Sprintf("https://confluence.shopee.io/pages/viewpage.action?pageId=%s", result.ID)
		}

		return fmt.Sprintf("project_page_already_created,link %s", url), nil
	}

	// create project pages
	title := ctx.String("title")
	if !strings.HasSuffix(title, fmt.Sprintf("[%s]", ctx.String("epic"))) {
		title = fmt.Sprintf("%s [%s]", title, ctx.String("epic"))
	}

	con := pageContent{
		Title:        title,
		Body:         _ProjectEmptyPageContent,
		Space:        ctx.String("space"),
		ParentPageID: ctx.String("parent"),
	}
	content := o.content(con)
	page, err := o.wiki.CreateContent(content)
	if err != nil {
		return "", errors.WithMessage(err, "create_project_page_failed")
	}

	return o.freshProjectSubpages(ctx, page.ID)
}

func (o *WikiOperator) projectCommand(acts *botapp.ActionFuncs) *cli.Command {
	cmd := &cli.Command{
		Name:  "project",
		Usage: "project related operation",
		Subcommands: []*cli.Command{
			{
				Name:    "list",
				Usage:   "list project",
				Aliases: []string{"ls"},
				Flags: []cli.Flag{
					&cli.StringFlag{Name: "page-id", Aliases: []string{"p", "id"}, Value: _AZProjectsPageID},
				},
				Action: acts.Action(o.listProject),
			},
			{
				Name:    "brief",
				Aliases: []string{"b"},
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name: "epic", Aliases: []string{"e"},
						Usage:    "project's epic like SATOS-XXX",
						Required: true,
						Action:   o.verifyIssueID,
					},
					&cli.StringFlag{Name: "parent", Aliases: []string{"p"}, Usage: "project's parent page id", Value: _AZProjectsPageID},
				},
				Action: acts.Action(o.briefProject),
			},
			{
				Name:    "create",
				Aliases: []string{"c"},
				Flags: []cli.Flag{
					&cli.StringFlag{Name: "title", Aliases: []string{"t"}, Required: true},
					&cli.StringFlag{
						Name: "epic", Aliases: []string{"e"},
						Usage:    "project's epic like SATOS-XXX",
						Required: true,
						Action:   o.verifyIssueID,
					},
					&cli.StringFlag{Name: "parent", Aliases: []string{"p"}, Usage: "project's parent page id", Value: _AZProjectsPageID},
					&cli.BoolFlag{Name: "fix", Aliases: []string{"f", "repair", "r"}, Value: false},
					&cli.BoolFlag{Name: "debug", Aliases: []string{"d"}, Value: false, Hidden: true, Action: o.setDebug},
				},
				Action: acts.Action(o.createProject),
				After:  acts.After(o.briefProject),
			},
			{
				Name: "updates",
				Subcommands: []*cli.Command{
					{
						Name:    "list",
						Aliases: []string{"ls"},
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name: "epic", Aliases: []string{"e"},
								Usage:    "project's epic like SATOS-XXX",
								Required: true,
								Action:   o.verifyIssueID,
							},
							&cli.StringFlag{Name: "parent", Aliases: []string{"p"}, Usage: "project's parent page id", Value: _AZProjectsPageID},
						},
						Action: acts.Action(o.listProjectUpdates),
					},
					{
						Name: "rotate",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "parent", Aliases: []string{"p"}, Usage: "project's parent page id", Value: _AZProjectsPageID},
							&cli.StringSliceFlag{
								Name: "prefix", Aliases: []string{"pre"}, Usage: "project's prefix",
								Action: func(ctx *cli.Context, prefixes []string) error {
									if len(prefixes) == 0 {
										return errors.New("project's prefix is required")
									}

									return nil
								},
							},
							&cli.StringFlag{Name: "from", Aliases: []string{"f"}, Usage: "project's updates date from, default: Monday"},
							&cli.StringFlag{Name: "to", Aliases: []string{"t"}, Usage: "project's updates date to, default: Friday"},
							&cli.StringFlag{
								Name: "epic", Aliases: []string{"e"},
								Usage:  "project's epic like SATOS-XXX",
								Action: o.verifyIssueID,
								Hidden: true,
							},
						},
						Action: acts.Action(o.rotateProjectUpdates),
					},
				},
			},
		},
	}

	return cmd
}
