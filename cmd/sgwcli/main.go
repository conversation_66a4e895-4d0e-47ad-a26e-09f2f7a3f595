// Package main implements the command-line interface for SGW-cli.
package main

import (
	"log"
	"os"
	"strings"

	"github.com/urfave/cli/v2"
)

var Version = "unknown"

func main() {
	app := cli.NewApp()
	app.Usage = "sgw command-line interface"
	app.Version = strings.TrimSpace(Version)
	app.Description = "SGW command-line interface for node-local operations"

	node := NewNodeOperator()
	cli.VersionPrinter = node.ShowVersion()
	app.Commands = node.Commands()

	if err := app.Run(os.Args); err != nil {
		log.Fatalln(err)
	}
}
