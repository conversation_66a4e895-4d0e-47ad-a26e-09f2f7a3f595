package main

import (
	"fmt"
	"runtime"

	"github.com/urfave/cli/v2"
)

type NodeOperator struct {
	ctx *cli.Context
}

func NewNodeOperator() *NodeOperator {
	return &NodeOperator{}
}

func (n *NodeOperator) Commands() []*cli.Command {
	return []*cli.Command{
		n.dockerCommand(),
		n.keepalivedCommand(),
	}
}

const sgwCLI = `
   _____   ______ _       __   ______ __     ____
  / ___/  / ____/| |     / /  / ____// /    /  _/
  \__ \  / / __  | | /| / /  / /    / /     / /
 ___/ / / /_/ /  | |/ |/ /  / /___ / /___ _/ /
/____/  \____/   |__/|__/   \____//_____//___/`

func (n *NodeOperator) ShowVersion() func(c *cli.Context) {
	return func(c *cli.Context) {
		n.ctx = c

		n.Println(sgwCLI)
		n.Printf("%s %s\n", c.App.Name, c.App.Version)
		n.Printf("Built by %s %s/%s compiler %s\n",
			runtime.Version(), runtime.GOOS, runtime.GOARCH, runtime.Compiler)
	}
}

func (n *NodeOperator) Printf(format string, a ...interface{}) {
	_, _ = fmt.Fprintf(n.ctx.App.Writer, format, a...)
}

func (n *NodeOperator) Println(a ...interface{}) {
	_, _ = fmt.Fprintln(n.ctx.App.Writer, a...)
}
