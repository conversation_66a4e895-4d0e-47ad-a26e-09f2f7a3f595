package main

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"regexp"
	"strconv"
	"strings"

	"github.com/urfave/cli/v2"
)

// Instance represents a keepalived vrrp_instance
type Instance struct {
	Interface       string `json:"interface"`
	State           string `json:"state"`
	VirtualRouterID int    `json:"virtual_router_id"`
	Priority        int    `json:"priority"`
	VIP             string `json:"vip"`
	WithoutAuth     bool   `json:"without_auth"`
}

// KeepalivedConfig is the final JSON structure
type KeepalivedConfig struct {
	Instances map[string]Instance `json:"keepalived_instances"`
}

func (n *NodeOperator) keepalivedCommand() *cli.Command {
	return &cli.Command{
		Name:  "keepalived",
		Usage: "Keepalived configuration utilities",
		Subcommands: []*cli.Command{
			{
				Name:  "parse",
				Usage: "Parse keepalived configuration file",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:    "config",
						Aliases: []string{"c"},
						Value:   "/etc/keepalived/keepalived.conf",
						Usage:   "path to keepalived configuration file (use '-' for stdin)",
					},
				},
				Action: n.parseKeepalivedConfig,
			},
		},
	}
}

func (n *NodeOperator) parseKeepalivedConfig(c *cli.Context) error {
	n.ctx = c
	configPath := c.String("config")

	var config string
	var err error

	if configPath == "-" {
		// Read from stdin
		data, err := io.ReadAll(os.Stdin)
		if err != nil {
			return fmt.Errorf("error reading from stdin: %w", err)
		}
		config = string(data)
	} else {
		data, err := os.ReadFile(configPath)
		if err != nil {
			return fmt.Errorf("error reading file: %w", err)
		}
		config = string(data)
	}

	result := parseKeepalivedConfig(config)

	// Convert to JSON and print
	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %w", err)
	}

	n.Println(string(jsonData))

	return nil
}

func parseKeepalivedConfig(content string) KeepalivedConfig {
	instances := make(map[string]Instance)

	// Find all vrrp_instance blocks
	instanceBlockRegex := regexp.MustCompile(`vrrp_instance\s+(\w+)\s+\{((?:.|\n)*?)\n\}`)
	instanceMatches := instanceBlockRegex.FindAllStringSubmatch(content, -1)

	authRegex := regexp.MustCompile(`authentication\s*\{`)
	authMatches := authRegex.FindAllStringSubmatch(content, -1)

	for _, match := range instanceMatches {
		instanceName := match[1]
		instanceContent := match[2]

		// Extract fields from instance content
		interfaceRegex := regexp.MustCompile(`interface\s+(\S+)`)
		stateRegex := regexp.MustCompile(`state\s+(\S+)`)
		virtualRouterIDRegex := regexp.MustCompile(`virtual_router_id\s+(\d+)`)
		priorityRegex := regexp.MustCompile(`priority\s+(\d+)`)
		vipRegex := regexp.MustCompile(`virtual_ipaddress\s*\{\s*([^}]*?)\s*\}`)

		var instance Instance

		if interfaceMatch := interfaceRegex.FindStringSubmatch(instanceContent); len(interfaceMatch) > 1 {
			instance.Interface = interfaceMatch[1]
		}

		if stateMatch := stateRegex.FindStringSubmatch(instanceContent); len(stateMatch) > 1 {
			instance.State = stateMatch[1]
		}

		if vrIDMatch := virtualRouterIDRegex.FindStringSubmatch(instanceContent); len(vrIDMatch) > 1 {
			if vrID, err := strconv.Atoi(vrIDMatch[1]); err == nil {
				instance.VirtualRouterID = vrID
			}
		}

		if priorityMatch := priorityRegex.FindStringSubmatch(instanceContent); len(priorityMatch) > 1 {
			if priority, err := strconv.Atoi(priorityMatch[1]); err == nil {
				instance.Priority = priority
			}
		}

		if vipMatch := vipRegex.FindStringSubmatch(instanceContent); len(vipMatch) > 1 {
			vipContent := strings.TrimSpace(vipMatch[1])
			if lines := strings.Split(vipContent, "\n"); len(lines) > 0 {
				instance.VIP = strings.TrimSpace(lines[0])
			}
		}

		if len(authMatches) == 0 {
			// No authentication block found
			instance.WithoutAuth = true
		}

		instances[instanceName] = instance
	}

	return KeepalivedConfig{
		Instances: instances,
	}
}
