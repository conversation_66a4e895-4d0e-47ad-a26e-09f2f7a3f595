package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/urfave/cli/v2"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

const bindPartNumber = 2

type ContainerInfo struct {
	Name       string `json:"Name"`
	HostConfig struct {
		Binds         []string `json:"Binds"`
		NetworkMode   string   `json:"NetworkMode"`
		Memory        int64    `json:"Memory"`
		MemorySwap    int64    `json:"MemorySwap"`
		CpusetCpus    string   `json:"CpusetCpus"`
		Privileged    bool     `json:"Privileged"`
		PidMode       string   `json:"PidMode"`
		SecurityOpt   []string `json:"SecurityOpt"`
		RestartPolicy struct {
			Name string `json:"Name"`
		} `json:"RestartPolicy"`
		LogConfig struct {
			Type   string `json:"Type"`
			Config struct {
				MaxSize string `json:"max-size"`
				MaxFile string `json:"max-file"`
			} `json:"Config"`
		} `json:"LogConfig"`
	} `json:"HostConfig"`
	Config struct {
		Image      string   `json:"Image"`
		Env        []string `json:"Env"`
		Cmd        []string `json:"Cmd"`
		Entrypoint []string `json:"Entrypoint"`
	} `json:"Config"`
	Mounts []struct {
		Type        string `json:"Type"`
		Source      string `json:"Source"`
		Destination string `json:"Destination"`
		Mode        string `json:"Mode"`
		RW          bool   `json:"RW"`
	} `json:"Mounts"`
}

func (n *NodeOperator) dockerCommand() *cli.Command {
	return &cli.Command{
		Name:  "docker",
		Usage: "Docker container management utilities",
		Subcommands: []*cli.Command{
			{
				Name:      "generate-run",
				Usage:     "Generate docker run command from running container",
				ArgsUsage: "<container-name>",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:    "file",
						Aliases: []string{"f"},
						Usage:   "Generate from JSON file instead of running container",
					},
				},
				Action: n.handleGenerateRun,
			},
			{
				Name:      "recreate",
				Usage:     "Stop, remove, and recreate container",
				ArgsUsage: "<container-name>",
				Action:    n.handleRecreate,
			},
		},
	}
}

func (n *NodeOperator) handleGenerateRun(ctx *cli.Context) error {
	n.ctx = ctx
	var containerInfo ContainerInfo
	var err error

	if file := ctx.String("file"); file != "" {
		containerInfo, err = n.loadFromFile(file)
	} else {
		if ctx.NArg() == 0 {
			return errors.New("generate-run requires a container name or -f flag")
		}
		containerName := ctx.Args().First()
		containerInfo, err = n.inspectContainer(containerName)
	}

	if err != nil {
		return err
	}

	dockerRunCmd := n.generateDockerRunCommand(containerInfo)
	n.Println(dockerRunCmd)

	return nil
}

func (n *NodeOperator) handleRecreate(ctx *cli.Context) error {
	n.ctx = ctx
	if ctx.NArg() == 0 {
		return errors.New("recreate requires a container name")
	}

	containerName := ctx.Args().First()

	cmd := exec.Command("docker", "inspect", containerName)
	inspectOutput, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("error inspecting container: %w", err)
	}

	inspectFile := fmt.Sprintf("/tmp/%s.json", containerName)
	if err := os.WriteFile(inspectFile, inspectOutput, consts.FilePermissionRW); err != nil {
		n.Printf("Warning: failed to save inspect output to %s: %v\n", inspectFile, err)
	} else {
		n.Printf("Container inspect output saved to %s\n", inspectFile)
	}

	containerInfo, err := n.inspectContainer(containerName)
	if err != nil {
		return fmt.Errorf("error parsing container info: %w", err)
	}

	dockerRunCmd := n.generateDockerRunCommand(containerInfo)

	commandFile := fmt.Sprintf("/tmp/%s.sh", containerName)
	dateOutput, _ := exec.Command("date").Output()
	commandContent := fmt.Sprintf("#!/bin/bash\n# Generated docker run command for container: %s\n# Generated at: %s\n\n%s\n",
		containerName,
		strings.TrimSpace(string(dateOutput)),
		dockerRunCmd)
	if err := os.WriteFile(commandFile, []byte(commandContent), consts.FilePermissionRW); err != nil {
		n.Printf("Warning: failed to save command to %s: %v\n", commandFile, err)
	} else {
		n.Printf("Generated command saved to %s\n", commandFile)
	}

	n.Printf("Stopping container '%s'...\n", containerName)
	if err := n.runCommand("docker", "stop", containerName); err != nil {
		n.Printf("Error stopping container: %v\n", err)
		n.Printf("Inspect output saved at: %s\n", inspectFile)
		n.Printf("Generated command saved at: %s\n", commandFile)

		return err
	}

	n.Printf("Removing container '%s'...\n", containerName)
	if err := n.runCommand("docker", "rm", containerName); err != nil {
		n.Printf("Error removing container: %v\n", err)
		n.Printf("Inspect output saved at: %s\n", inspectFile)
		n.Printf("Generated command saved at: %s\n", commandFile)

		return err
	}

	n.Printf("Recreating container '%s'...\n", containerName)

	const maxRetries = 10
	const retryDelay = 3 * time.Second

	var lastErr error
	for i := 1; i <= maxRetries; i++ {
		if i > 1 {
			n.Printf("Retrying... (attempt %d/%d)\n", i, maxRetries)
		}

		cmdParts := strings.Fields(dockerRunCmd)
		if err := n.runCommand(cmdParts[0], cmdParts[1:]...); err != nil {
			lastErr = err
			n.Printf("Error recreating container (attempt %d/%d): %v\n", i, maxRetries, err)

			if i < maxRetries {
				n.Printf("Waiting %v before retry...\n", retryDelay)
				time.Sleep(retryDelay)

				continue
			}

			n.Printf("Failed to recreate container after %d attempts\n", maxRetries)
			n.Printf("Inspect output saved at: %s\n", inspectFile)
			n.Printf("Generated command saved at: %s\n", commandFile)
			n.Printf("You can manually run: bash %s\n", commandFile)

			return lastErr
		}

		n.Printf("Container '%s' recreated successfully!\n", containerName)
		n.Printf("Backup files: %s, %s\n", inspectFile, commandFile)

		return nil
	}

	return lastErr
}

func (n *NodeOperator) inspectContainer(containerName string) (ContainerInfo, error) {
	cmd := exec.Command("docker", "inspect", containerName)
	output, err := cmd.Output()
	if err != nil {
		return ContainerInfo{}, fmt.Errorf("failed to inspect container '%s': %w", containerName, err)
	}

	var containers []ContainerInfo
	if err := json.Unmarshal(output, &containers); err != nil {
		return ContainerInfo{}, fmt.Errorf("failed to parse inspect output: %w", err)
	}

	if len(containers) == 0 {
		return ContainerInfo{}, fmt.Errorf("no container information found for '%s'", containerName)
	}

	return containers[0], nil
}

func (n *NodeOperator) loadFromFile(filename string) (ContainerInfo, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return ContainerInfo{}, fmt.Errorf("failed to read file '%s': %w", filename, err)
	}

	var containers []ContainerInfo
	if err := json.Unmarshal(data, &containers); err != nil {
		return ContainerInfo{}, fmt.Errorf("failed to parse JSON: %w", err)
	}

	if len(containers) == 0 {
		return ContainerInfo{}, errors.New("no container information found in file")
	}

	return containers[0], nil
}

func (n *NodeOperator) runCommand(name string, args ...string) error {
	cmd := exec.Command(name, args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("command '%s %s' failed: %w", name, strings.Join(args, " "), err)
	}

	return nil
}

func (n *NodeOperator) generateDockerRunCommand(container ContainerInfo) string {
	var cmd []string
	cmd = append(cmd, "docker run -d")

	cmd = n.addBasicOptions(cmd, container)
	cmd = n.addResourceOptions(cmd, container)
	cmd = n.addSecurityOptions(cmd, container)
	cmd = n.addLogOptions(cmd, container)
	cmd = n.addVolumeOptions(cmd, container)
	cmd = n.addEnvironmentOptions(cmd, container)
	cmd = n.addImageAndCommand(cmd, container)

	return strings.Join(cmd, " \\\n  ")
}

func (n *NodeOperator) addBasicOptions(cmd []string, container ContainerInfo) []string {
	containerName := strings.TrimPrefix(container.Name, "/")
	if containerName != "" {
		cmd = append(cmd, fmt.Sprintf("--name \"%s\"", containerName))
	}

	if container.HostConfig.NetworkMode != "" {
		cmd = append(cmd, fmt.Sprintf("--network \"%s\"", container.HostConfig.NetworkMode))
	}

	if container.HostConfig.PidMode != "" {
		cmd = append(cmd, fmt.Sprintf("--pid \"%s\"", container.HostConfig.PidMode))
	}

	if container.HostConfig.Privileged {
		cmd = append(cmd, "--privileged")
	}

	if container.HostConfig.RestartPolicy.Name != "" {
		cmd = append(cmd, fmt.Sprintf("--restart \"%s\"", container.HostConfig.RestartPolicy.Name))
	}

	return cmd
}

func (n *NodeOperator) addResourceOptions(cmd []string, container ContainerInfo) []string {
	if container.HostConfig.Memory > 0 {
		cmd = append(cmd, fmt.Sprintf("--memory %d", container.HostConfig.Memory))
	}

	if container.HostConfig.MemorySwap > 0 {
		cmd = append(cmd, fmt.Sprintf("--memory-swap %d", container.HostConfig.MemorySwap))
	}

	if container.HostConfig.CpusetCpus != "" {
		cmd = append(cmd, fmt.Sprintf("--cpuset-cpus \"%s\"", container.HostConfig.CpusetCpus))
	}

	return cmd
}

func (n *NodeOperator) addSecurityOptions(cmd []string, container ContainerInfo) []string {
	for _, secOpt := range container.HostConfig.SecurityOpt {
		cmd = append(cmd, fmt.Sprintf("--security-opt \"%s\"", secOpt))
	}

	return cmd
}

func (n *NodeOperator) addLogOptions(cmd []string, container ContainerInfo) []string {
	if container.HostConfig.LogConfig.Config.MaxSize != "" {
		cmd = append(cmd, fmt.Sprintf("--log-opt \"max-size=%s\"", container.HostConfig.LogConfig.Config.MaxSize))
	}
	if container.HostConfig.LogConfig.Config.MaxFile != "" {
		cmd = append(cmd, fmt.Sprintf("--log-opt \"max-file=%s\"", container.HostConfig.LogConfig.Config.MaxFile))
	}

	return cmd
}

func (n *NodeOperator) addVolumeOptions(cmd []string, container ContainerInfo) []string {
	volumeMap := n.buildVolumeMap(container)
	for _, volume := range volumeMap {
		cmd = append(cmd, fmt.Sprintf("-v \"%s\"", volume))
	}

	return cmd
}

func (n *NodeOperator) buildVolumeMap(container ContainerInfo) map[string]string {
	volumeMap := make(map[string]string)

	// Add mounts
	for _, mount := range container.Mounts {
		if mount.Type == "bind" {
			mode := ""
			if !mount.RW {
				mode = ":ro"
			}
			volumeMap[mount.Source] = fmt.Sprintf("%s:%s%s", mount.Source, mount.Destination, mode)
		}
	}

	// Add binds
	for _, bind := range container.HostConfig.Binds {
		if volume := n.parseBindVolume(bind); volume != "" {
			parts := strings.Split(bind, ":")
			if len(parts) >= bindPartNumber {
				volumeMap[parts[0]] = volume
			}
		}
	}

	return volumeMap
}

func (n *NodeOperator) parseBindVolume(bind string) string {
	parts := strings.Split(bind, ":")
	if len(parts) < bindPartNumber {
		return ""
	}

	source := parts[0]
	dest := parts[1]
	mode := ""
	if len(parts) > bindPartNumber {
		mode = ":" + parts[bindPartNumber]
	}

	return fmt.Sprintf("%s:%s%s", source, dest, mode)
}

func (n *NodeOperator) addEnvironmentOptions(cmd []string, container ContainerInfo) []string {
	for _, env := range container.Config.Env {
		cmd = append(cmd, fmt.Sprintf("-e \"%s\"", env))
	}

	return cmd
}

func (n *NodeOperator) addImageAndCommand(cmd []string, container ContainerInfo) []string {
	cmd = append(cmd, fmt.Sprintf("\"%s\"", container.Config.Image))

	for _, arg := range container.Config.Entrypoint {
		cmd = append(cmd, fmt.Sprintf("\"%s\"", arg))
	}

	for _, arg := range container.Config.Cmd {
		cmd = append(cmd, fmt.Sprintf("\"%s\"", arg))
	}

	return cmd
}
