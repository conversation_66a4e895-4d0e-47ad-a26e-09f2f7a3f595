package albvo

import (
	"fmt"
	"reflect"
	"strings"
	"sync"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/duke-git/lancet/v2/convertor"

	ticket "git.garena.com/shopee/platform/space-sdk/swp/defs/ticket"

	"git.garena.com/shopee/go-shopeelib/byteutils"
	"git.garena.com/shopee/go-shopeelib/json"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/nlb/nlbvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsent"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/swp/swpvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

// Add mutex for version protection
var versionMutex sync.RWMutex

// NodeResponse node response
type NodeResponse struct {
	core.BaseResponse

	Data struct {
		Errors []*core.ItemError `json:"errors,omitempty"`
	} `json:"data"`
}

// NodeComponentVersion node component version
type NodeComponentVersion struct {
	ALBMetrics string `json:"alb_metrics,omitempty"`
	ALBSD      string `json:"alb_sd,omitempty"`
	ALBWAF     string `json:"alb_waf,omitempty"`
	NginxLB    string `json:"mesos_nginx_lb,omitempty"`
	SGWAgent   string `json:"sgw_agent,omitempty"`

	Nginx string `json:"nginx,omitempty"`
}

// IsUnknownFound check if a version has unknown
func (v *NodeComponentVersion) IsUnknownFound() bool {
	values := reflect.ValueOf(*v)
	for i := 0; i < values.NumField(); i++ {
		if values.Field(i).String() == consts.UnknownVersion {
			return true
		}
	}

	return false
}

func (v *NodeComponentVersion) UnknownComponentVersion() (string, string) {
	values := reflect.ValueOf(*v)
	for i := 0; i < values.NumField(); i++ {
		vs := values.Field(i).String()
		if vs == consts.UnknownVersion {
			cmpName := values.Type().Field(i).Name

			return cmpName, vs
		}
	}

	return consts.UnknownVersion, consts.UnknownVersion
}

// PrettyJSON pretty print json
func (v *NodeComponentVersion) PrettyJSON() string {
	ret, _ := json.MarshalIndent(v, "", "    ")

	return byteutils.ToString(ret)
}

func (v *NodeComponentVersion) Version(name string) string {
	switch name {
	case cmp.NginxComponent.Name:
		return v.Nginx
	case cmp.SGWAgentComponent.Name:
		return v.SGWAgent
	case cmp.MesosNginxLBComponent.Name:
		return v.NginxLB
	case cmp.ALBSDComponent.Name:
		return v.ALBSD
	case cmp.ALBMetricsComponent.Name:
		return v.ALBMetrics
	}

	return consts.UnknownVersion
}

// NewNodeComponentVersion returns a new NodeComponentVersion
func NewNodeComponentVersion(name, version string) *NodeComponentVersion {
	ver := &NodeComponentVersion{}
	switch name {
	case cmp.ALBMetricsComponent.Name:
		ver.ALBMetrics = version
	case cmp.MesosNginxLBComponent.Name:
		ver.NginxLB = version
	case cmp.SGWAgentComponent.Name:
		ver.SGWAgent = version
	case cmp.ALBSDComponent.Name:
		ver.ALBSD = version
	case cmp.NginxComponent.Name:
		ver.Nginx = version
	}

	return ver
}

// ClusterNodeTicketRequest cluster add node request
type ClusterNodeTicketRequest struct {
	IPs     []string `json:"ips" binding:"gt=0,dive,ip,required"`
	SDU     string   `json:"sdu"`
	UUID    string   `json:"uuid" binding:"required"` // cluster uuid
	Cluster string   `json:"cluster"`
	State   string   `json:"state"`
	Token   string   `json:"token"`

	TicketID      int    `json:"id"`
	ApplicantUser string `json:"applicant_user"`

	Version *NodeComponentVersion `json:"version"`
}

func (r *ClusterNodeTicketRequest) ToVerifyReq() *ClusterNodeVerifyRequest {
	return &ClusterNodeVerifyRequest{
		IPs:  r.IPs,
		UUID: r.UUID,
	}
}

// GenALBNode generate alb node from request
func (r *ClusterNodeTicketRequest) GenALBNode(meta *sgwvo.ALBClusterConfigMeta) *v1alpha1.ALBNode {
	node := &v1alpha1.ALBNode{
		Zone:    meta.AZ,
		IDC:     meta.RZ,
		Segment: meta.Segment,
		Env:     meta.Env,

		SDU:         r.SDU,
		ALBName:     r.Cluster,
		ClusterName: r.Cluster,
		ClusterUUID: r.UUID,
		AZBusiness:  r.SDU,

		MetricsImageTag:  r.Version.ALBMetrics,
		NginxLbImageTag:  r.Version.NginxLB,
		ALBAgentImageTag: r.Version.SGWAgent,
		ALBSdImageTag:    r.Version.ALBSD,
		NginxVersion:     r.Version.Nginx,

		TocAttributes: map[string]string{
			"az_business": r.SDU,
		},

		SWPTicket: r.TicketID,
		Options: map[string]string{
			"ticket_id":      fmt.Sprintf("%d", r.TicketID),
			"applicant_user": r.ApplicantUser,
		},
	}

	return node
}

// ToSWPTicketAddNodeRequest generate swp ticket request of adding nodes
func (r *ClusterNodeTicketRequest) ToSWPTicketAddNodeRequest() *swpvo.TicketRequest {
	request := swpvo.TicketRequest{
		Title: fmt.Sprintf("Add Node into ALB Clusters, IP %s...", r.IPs[0]),
		Token: r.Token,
		FormData: &ClusterAddNodeFormData{
			ClusterNodeFormData: ClusterNodeFormData{
				IPs:     r.IPs,
				IPsTxt:  strings.Join(r.IPs, "\n"),
				UUID:    r.UUID,
				Cluster: r.Cluster,
			},

			Version:   r.Version.PrettyJSON(),
			Component: r.Version,
		},
		TemplateName: configs.ALB.SWP.Template.ClusterAddNode,
		AutoSubmit:   true,
	}

	return &request
}

// ToSWPTicketRemoveNodeRequest generate swp ticket request of removing nodes
func (r *ClusterNodeTicketRequest) ToSWPTicketRemoveNodeRequest() *swpvo.TicketRequest {
	request := swpvo.TicketRequest{
		Title: fmt.Sprintf("Remove Node from ALB Clusters, IP %s...", r.IPs[0]),
		Token: r.Token,
		FormData: &ClusterNodeFormData{
			IPs:     r.IPs,
			IPsTxt:  strings.Join(r.IPs, "\n"),
			UUID:    r.UUID,
			Cluster: r.Cluster,
		},
		TemplateName: configs.ALB.SWP.Template.ClusterRemoveNode,
		AutoSubmit:   true,
	}

	return &request
}

type ClusterNodeVerifyRequest struct {
	IPs  []string
	UUID string
}

// IsNodeCRNeedToUpdate check if alb node needs to update
func IsNodeCRNeedToUpdate(alb *v1alpha1.ALB, albCR *v1alpha1.ALB) bool {
	if alb.Spec.ALBNode.ClusterName != albCR.Spec.ALBNode.ClusterName {
		return true
	}
	if alb.Spec.ALBNode.SDU != albCR.Spec.ALBNode.SDU {
		return true
	}

	if alb.Spec.ALBNode.ALBSdImageTag != albCR.Spec.ALBNode.ALBSdImageTag {
		return true
	}

	if alb.Spec.ALBNode.ALBAgentImageTag != albCR.Spec.ALBNode.ALBAgentImageTag {
		return true
	}

	if alb.Spec.ALBNode.NginxLbImageTag != albCR.Spec.ALBNode.NginxLbImageTag {
		return true
	}
	if alb.Spec.ALBNode.MetricsImageTag != albCR.Spec.ALBNode.MetricsImageTag {
		return true
	}
	if alb.Spec.ALBNode.NginxVersion != albCR.Spec.ALBNode.NginxVersion {
		return true
	}

	if alb.Spec.ALBNode.TOCCluster != albCR.Spec.ALBNode.TOCCluster {
		return true
	}

	return false
}

// UpdateALBNodeVersion update alb node version
func UpdateALBNodeVersion(node *v1alpha1.ALBNode, version *NodeComponentVersion) {
	node.ALBSdImageTag = version.ALBSD
	node.ALBAgentImageTag = version.SGWAgent
	node.MetricsImageTag = version.ALBMetrics
	node.NginxLbImageTag = version.NginxLB
	node.NginxVersion = version.Nginx
}

// UpdateALBComponentVersion update alb component version
func UpdateALBComponentVersion(node *v1alpha1.ALBNode, component, version string) {
	versionMutex.Lock()
	defer versionMutex.Unlock()

	switch component {
	case cmp.ALBMetricsComponent.Name:
		node.MetricsImageTag = version
	case cmp.ALBSDComponent.Name:
		node.ALBSdImageTag = version
	case cmp.MesosNginxLBComponent.Name:
		node.NginxLbImageTag = version
	case cmp.NginxComponent.Name:
		node.NginxVersion = version
	case cmp.SGWAgentComponent.Name:
		node.ALBAgentImageTag = version
	case cmp.ScertmsComponent.Name:
		node.ScertmsVersion = version
	}
}

// ToNodeComponentVersion generate node component version
func ToNodeComponentVersion(node *v1alpha1.ALBNode) *NodeComponentVersion {
	return &NodeComponentVersion{
		ALBMetrics: node.MetricsImageTag,
		NginxLB:    node.NginxLbImageTag,
		SGWAgent:   node.ALBAgentImageTag,
		ALBSD:      node.ALBSdImageTag,
		Nginx:      node.NginxVersion,
	}
}

// ALBNodeComponentVersion return node component version
func ALBNodeComponentVersion(component string, node *v1alpha1.ALBNode) string {
	versionMutex.RLock()
	defer versionMutex.RUnlock()

	switch component {
	case cmp.ALBMetricsComponent.Name:
		return node.MetricsImageTag
	case cmp.ALBSDComponent.Name:
		return node.ALBSdImageTag
	case cmp.MesosNginxLBComponent.Name:
		return node.NginxLbImageTag
	case cmp.SGWAgentComponent.Name:
		return node.ALBAgentImageTag
	case cmp.NginxComponent.Name:
		return node.NginxVersion
	case cmp.ScertmsComponent.Name:
		return node.ScertmsVersion
	default:
		return ""
	}
}

// NodeIPRequest node ip request
type NodeIPRequest struct {
	IP string `form:"ip" json:"ip" binding:"required,ip"`
}

// NodePagerRequest node ip request with paging
type NodePagerRequest struct {
	IP    string `form:"ip" json:"ip" binding:"required,ip"`
	Page  int    `form:"page" json:"page" binding:"required,gte=1"`
	Limit int    `form:"limit" json:"limit" binding:"required,gte=10"`
}

// ToEventNodeRequest `EventNodeRequest` from opsvo
func (r *NodePagerRequest) ToEventNodeRequest() *opsvo.EventNodeRequest {
	return &opsvo.EventNodeRequest{
		Limit:   r.Limit,
		Page:    r.Page,
		IP:      r.IP,
		BizType: consts.BizALBType,
	}
}

// UpdateNodeStateRequest update node state request
type UpdateNodeStateRequest struct {
	NodeIPRequest

	State  string `form:"state" json:"state" binding:"required,state"`
	Reason string `form:"reason" json:"reason" binding:"required"`
}

func (r *UpdateNodeStateRequest) SetIP(ip string) {
	r.IP = ip
}

func (r *UpdateNodeStateRequest) SetState(state string) {
	r.State = state
}

func (r *UpdateNodeStateRequest) SetReason(reason string) {
	r.Reason = reason
}

// ReentrantNodeStateRequest reentrant node state request
type ReentrantNodeStateRequest struct {
	NodeIPRequest

	State string `form:"state" json:"state" binding:"required,state"`
}

// NodeState node state
type NodeState struct {
	IP          string      `json:"ip"`
	SDU         string      `json:"sdu"`
	Application string      `json:"application"`
	Function    string      `json:"function"`
	ALBName     string      `json:"alb_name"`
	State       string      `json:"state"`
	Status      string      `json:"status"`
	UpdatedAt   metav1.Time `json:"updated_at"`
}

// ToNodeState generate node state
func ToNodeState(alb *v1alpha1.ALB) *NodeState {
	node := NodeState{}

	node.SDU = alb.Spec.ALBNode.SDU
	node.Application = alb.Spec.ALBNode.TocAttributes["func_main"]
	node.Function = alb.Spec.ALBNode.TocAttributes["func_sub"]
	node.State = alb.Status.State
	node.ALBName = alb.Spec.ALBNode.ALBName
	node.IP = alb.Spec.LanIP
	node.UpdatedAt = alb.Status.LastTransitionTime

	if !consts.RunningState.Equals(alb.Status) && !consts.PreOfflineState.Equals(alb.Status) {
		node.Status = consts.NodeStatusNotReady
	} else {
		node.Status = consts.NodeStatusReady
	}

	return &node
}

// GetNodeResponse get node response
type GetNodeResponse struct {
	core.BaseResponse

	Data *NodeState `json:"data"`
}

// NodeRequest struct for node request
type NodeRequest struct {
	IPs []string `json:"ips" form:"ips" binding:"required,dive,ip,required"`
}

// ToNodeCRName convert to node CR name
func (req *NodeRequest) ToNodeCRName() {
	for i := 0; i < len(req.IPs); i++ {
		req.IPs[i] = CRName(req.IPs[i])
	}
}

func (req *NodeRequest) IPMap() map[string]struct{} {
	ipMap := make(map[string]struct{})
	for _, ip := range req.IPs {
		ipMap[ip] = struct{}{}
	}

	return ipMap
}

type NodeInfo struct {
	sgwvo.NodeInfo

	ALBName string `json:"alb_name"`
}

// ToInfo convert to NodeInfo
func ToInfo(node *sgwvo.Node, albCR *v1alpha1.ALB) *NodeInfo {
	message := ""
	var tick *ticket.Ticket
	// swp ticket number
	if albCR.Spec.ALBNode.SWPTicket != 0 {
		message = fmt.Sprintf("swp-%d", albCR.Spec.ALBNode.SWPTicket)
		tick = &ticket.Ticket{ID: uint64(albCR.Spec.ALBNode.SWPTicket)}
	}

	if albCR.Status.State != consts.RunningState.Status {
		updateBy := albCR.Spec.ALBNode.Options["applicant_user"]
		if updateBy != "" {
			node.UpdatedBy = updateBy
		}
	}

	info := &sgwvo.NodeInfo{
		SDU:         albCR.Spec.ALBNode.SDU,
		Application: albCR.Spec.ALBNode.TocAttributes["func_main"],
		Function:    albCR.Spec.ALBNode.TocAttributes["func_sub"],
		State:       albCR.Status.State,
		Ticket:      tick,
		Details: &sgwvo.DetailInfo{
			Message: message,
			Reason:  albCR.Status.Reason,
		},
		Components: map[string]string{
			cmp.ALBSDComponent.Name:        albCR.Spec.ALBNode.ALBSdImageTag,
			cmp.MesosNginxLBComponent.Name: albCR.Spec.ALBNode.NginxLbImageTag,
			cmp.ALBMetricsComponent.Name:   albCR.Spec.ALBNode.MetricsImageTag,
			cmp.SGWAgentComponent.Name:     albCR.Spec.ALBNode.ALBAgentImageTag,
			cmp.NginxComponent.Name:        albCR.Spec.ALBNode.NginxVersion,
		},
	}

	info.Node = *node

	return &NodeInfo{
		NodeInfo: *info,
		ALBName:  albCR.Name,
	}
}

// ToNodeInfo convert to NodeInfo with clusterID
func ToNodeInfo(albCR *v1alpha1.ALB) *NodeInfo {
	message := ""
	var tick *ticket.Ticket
	if albCR.Spec.ALBNode.SWPTicket != 0 {
		message = fmt.Sprintf("swp_ticket_%d", albCR.Spec.ALBNode.SWPTicket)
		tick = &ticket.Ticket{ID: uint64(albCR.Spec.ALBNode.SWPTicket)}
	}

	info := &sgwvo.NodeInfo{
		SDU:         albCR.Spec.ALBNode.SDU,
		Application: albCR.Spec.ALBNode.TocAttributes["func_main"],
		Function:    albCR.Spec.ALBNode.TocAttributes["func_sub"],
		State:       albCR.Status.State,
		Ticket:      tick,
		Details: &sgwvo.DetailInfo{
			Message: message,
			Reason:  albCR.Status.Reason,
		},
	}

	// behind preinitial state, can show the components
	if state, _ := consts.NewState(albCR.Status.State); state != nil {
		if state.Code > consts.InitialisingState.Code {
			info.Components = map[string]string{
				cmp.ALBSDComponent.Name:        albCR.Spec.ALBNode.ALBSdImageTag,
				cmp.MesosNginxLBComponent.Name: albCR.Spec.ALBNode.NginxLbImageTag,
				cmp.ALBMetricsComponent.Name:   albCR.Spec.ALBNode.MetricsImageTag,
				cmp.SGWAgentComponent.Name:     albCR.Spec.ALBNode.ALBAgentImageTag,
				cmp.NginxComponent.Name:        albCR.Spec.ALBNode.NginxVersion,
				cmp.ScertmsComponent.Name:      albCR.Spec.ALBNode.ScertmsVersion,
			}
		}
	}

	updateBy := albCR.Spec.ALBNode.Options["applicant_user"]
	if updateBy == "" {
		updateBy = configs.Mgmt.Ops.Bot.Email
	}

	info.ClusterUUID = albCR.Spec.ALBNode.ClusterUUID
	info.IP = albCR.Spec.LanIP
	info.NodeStatus = consts.NodeStatusNotReady
	info.UpdatedBy = updateBy
	info.UpdatedAt = int(albCR.Status.LastTransitionTime.Unix())

	return &NodeInfo{
		NodeInfo: *info,
		ALBName:  albCR.Name,
	}
}

// NodeInfosResponse node infos with detail result
type NodeInfosResponse struct {
	core.BaseResponse

	Data struct {
		Nodes []*NodeInfo `json:"nodes"`
	} `json:"data"`
}

// NodeInfoResponse single node info response from OpsPlatform API
type NodeInfoResponse struct {
	core.BaseResponse

	Data *NodeInfo `json:"data"`
}

// TargetState struct for target state
type TargetState struct {
	State     string `json:"state"`
	StateText string `json:"state_text"`
}

// ListNodeState struct for list node state
type ListNodeState struct {
	State        string        `json:"state,omitempty"`
	States       []string      `json:"states,omitempty"`
	TargetStates []TargetState `json:"target_states,omitempty"`
}

// ListNodeStateResponse struct for list node state
type ListNodeStateResponse struct {
	core.BaseResponse

	Data *ListNodeState `json:"data"`
}

// NodeRuntimeResponse struct for node runtime
type NodeRuntimeResponse struct {
	Code    string          `json:"code"`
	Message string          `json:"message"`
	Data    []*v1alpha1.ALB `json:"data"`
}

// NodeSpecResponse struct for node spec
type NodeSpecResponse struct {
	core.BaseResponse

	Data []*v1alpha1.ALBSpec `json:"data"`
}

// NodeProvisionResponse struct for node provision response
type NodeProvisionResponse struct {
	core.BaseResponse

	Data []*toclib.ProvisionNodeConfig `json:"data"`
}

// NodeProvisionConfigResponse struct for node provision config response
type NodeProvisionConfigResponse struct {
	core.BaseResponse

	Data *toclib.ProvisionNodeConfig `json:"data"`
}

// NodeHotUpdateProvisionRequest node provision component hot-update request
type NodeHotUpdateProvisionRequest struct {
	IPs           []string `json:"ips"  binding:"required,dive,ip"`
	Component     string   `json:"component" binding:"required,alb_core_component"`
	Version       string   `json:"version" binding:"required"`
	Env           string   `json:"env" binding:"required,oneof=test staging stable live"`
	OperationType string   `json:"-"`
	ApplicantUser string   `json:"applicant_user"`
	TicketID      uint64   `json:"ticket_id" binding:"required,gt=0"`
}

func (r *NodeHotUpdateProvisionRequest) ToVersionRequest() *ComponentVersionRequest {
	return &ComponentVersionRequest{
		Name:    r.Component,
		Env:     r.Env,
		Version: r.Version,
	}
}

// NodeProvisionComponentRequest struct for node provision component request
type NodeProvisionComponentRequest struct {
	IP            string `json:"ip" form:"ip" binding:"ip,required"`
	Component     string `json:"component" form:"component" binding:"required,alb_core_component"`
	Version       string `json:"version" form:"version" binding:"required"`
	OperationType string `json:"operation_type" form:"operation_type"`
}

// NodeProvisionTemplateRequest struct for node provision template request
type NodeProvisionTemplateRequest struct {
	IPs   []string `json:"ips" form:"ips" binding:"dive,ip,required"`
	Paths []string `json:"paths" binding:"dive,required"`
}

// NodeComponentRequest struct for node provision component request
type NodeComponentRequest struct {
	IPs        []string `json:"ips" form:"ips" binding:"dive,ip,required"`
	Components []string `json:"components" binding:"dive,alb_component,required"`
}

// NodeUpdateComponentRequest struct for node update provision component request
type NodeUpdateComponentRequest struct {
	IPs        []string `json:"ips" form:"ips" binding:"dive,ip,required"`
	Components []string `json:"components" binding:"dive,alb_component,required"`
	Disabled   bool     `json:"disabled" binding:"required"`
}

func (r *NodeUpdateComponentRequest) ComponentMap() map[string]struct{} {
	return convertor.ToMap(r.Components, func(com string) (string, struct{}) {
		return com, struct{}{}
	})
}

// NodeEventResponse struct for node event response
type NodeEventResponse struct {
	core.PagerResponse

	Data struct {
		Events []*opsent.EventLog `json:"events"`
	} `json:"data"`
}

// NodeTagResponse struct for node tag response
type NodeTagResponse struct {
	core.BaseResponse

	Data map[string]tocvo.Tags `json:"data"`
}

// NodeTagVariableResponse struct for node tag variable response
type NodeTagVariableResponse struct {
	core.BaseResponse

	Data map[string]*tocvo.ALBGroupVar `json:"data"`
}

// NodeNLBListenerResponse struct for node nlb listener response
type NodeNLBListenerResponse struct {
	core.BaseResponse

	Data map[string][]*nlbvo.ListenerTarget `json:"data"`
}

// ResetNodeRequest struct for reset node request
type ResetNodeRequest struct {
	IPs           []string `json:"ips" binding:"required"`
	Version       string   `json:"version"`
	ApplicantUser string   `json:"applicant_user"`
}
