package albvo

import (
	"fmt"
	"strings"
	"time"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// CRName return ALB CR name
func CRName(ip string) string {
	return fmt.Sprintf("hm-%s-alb", strings.ReplaceAll(ip, ".", "-"))
}

// ReloadRequest represents a request to reload nginx on ALB nodes
type ReloadRequest struct {
	ClusterID   string `json:"cluster_id" binding:"required,uuid4"`
	RequestedBy string `json:"requested_by,omitempty" binding:"omitempty,email"`
	Reason      string `json:"reason,omitempty" binding:"omitempty,max=500"`
}

// Validate performs additional validation on the reload request
func (r *ReloadRequest) Validate() error {
	if strings.TrimSpace(r.ClusterID) == "" {
		return fmt.Errorf("cluster_id is required")
	}

	if len(r.Reason) > 500 {
		return fmt.Errorf("reason must be 500 characters or less")
	}

	// Additional security checks
	if strings.Contains(r.Reason, "<script") || strings.Contains(r.Reason, "javascript:") {
		return fmt.Errorf("reason contains potentially malicious content")
	}

	return nil
}

// ReloadResponse represents the response from a reload operation
type ReloadResponse struct {
	core.BaseResponse
	Data ReloadResponseData `json:"data"`
}

// ReloadResponseData contains the detailed response data
type ReloadResponseData struct {
	RequestID   string     `json:"request_id"`
	Status      string     `json:"status"`
	Message     string     `json:"message"`
	ClusterID   string     `json:"cluster_id"`
	BU          string     `json:"bu"`
	TargetNodes []string   `json:"target_nodes"`
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time,omitempty"`
}

// UserBUMapping represents the mapping between users and business units
type UserBUMapping struct {
	Email string `json:"email" yaml:"email"`
	BU    string `json:"bu" yaml:"bu"`
}

// AuditLogEntry represents an audit log entry for reload operations
type AuditLogEntry struct {
	Timestamp   time.Time `json:"timestamp"`
	RequestID   string    `json:"request_id"`
	UserEmail   string    `json:"user_email"`
	BU          string    `json:"bu"`
	ClusterID   string    `json:"cluster_id"`
	TargetNodes []string  `json:"target_nodes"`
	Status      string    `json:"status"`
	ErrorMsg    string    `json:"error_msg,omitempty"`
	Reason      string    `json:"reason,omitempty"`
	Duration    int64     `json:"duration_ms"`
}

// RateLimitEntry represents a rate limiting entry
type RateLimitEntry struct {
	BU        string    `json:"bu"`
	LastUsed  time.Time `json:"last_used"`
	RequestID string    `json:"request_id"`
}

// ReloadStatus constants
const (
	ReloadStatusPending     = "pending"
	ReloadStatusInProgress  = "in_progress"
	ReloadStatusSuccess     = "success"
	ReloadStatusFailed      = "failed"
	ReloadStatusRateLimited = "rate_limited"
)

// Error codes for reload operations
const (
	ErrUnauthorized   = "ERR_UNAUTHORIZED"
	ErrForbidden      = "ERR_FORBIDDEN"
	ErrRateLimited    = "ERR_RATE_LIMITED"
	ErrInvalidCluster = "ERR_INVALID_CLUSTER"
	ErrInternalServer = "ERR_INTERNAL_SERVER"
	ErrReloadFailed   = "ERR_RELOAD_FAILED"
	ErrInvalidRequest = "ERR_INVALID_REQUEST"
)
