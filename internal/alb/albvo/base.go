package albvo

import (
	"fmt"
	"strings"
	"time"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// CRName return ALB CR name
func CRName(ip string) string {
	return fmt.Sprintf("hm-%s-alb", strings.ReplaceAll(ip, ".", "-"))
}

// ReloadRequest represents a request to reload nginx on ALB nodes
// @Description Request payload for triggering nginx reload operation on ALB cluster nodes
type ReloadRequest struct {
	ClusterID   string `json:"cluster_id" binding:"required,uuid4" example:"550e8400-e29b-41d4-a716-************" validate:"required,uuid4" description:"UUID of the ALB cluster to reload nginx on"`
	RequestedBy string `json:"requested_by,omitempty" binding:"omitempty,email" example:"<EMAIL>" description:"Email of the user requesting the reload (optional, will be set automatically if not provided)"`
	Reason      string `json:"reason,omitempty" binding:"omitempty,max=500" example:"Updated SSL certificates" description:"Reason for the reload operation (optional, max 500 characters)"`
}

// Validate performs additional validation on the reload request
func (r *ReloadRequest) Validate() error {
	if strings.TrimSpace(r.ClusterID) == "" {
		return fmt.Errorf("cluster_id is required")
	}

	if len(r.Reason) > 500 {
		return fmt.Errorf("reason must be 500 characters or less")
	}

	// Additional security checks
	if strings.Contains(r.Reason, "<script") || strings.Contains(r.Reason, "javascript:") {
		return fmt.Errorf("reason contains potentially malicious content")
	}

	return nil
}

// ReloadResponse represents the response from a reload operation
// @Description Response payload for nginx reload operation on ALB cluster nodes
type ReloadResponse struct {
	core.BaseResponse
	Data ReloadResponseData `json:"data" description:"Detailed response data for the reload operation"`
}

// ReloadResponseData contains the detailed response data
// @Description Detailed information about the nginx reload operation
type ReloadResponseData struct {
	RequestID   string     `json:"request_id" example:"123e4567-e89b-12d3-a456-************" description:"Unique identifier for this reload request"`
	Status      string     `json:"status" example:"success" enum:"pending,in_progress,success,failed,rate_limited" description:"Status of the reload operation"`
	Message     string     `json:"message" example:"Successfully reloaded nginx on 3 nodes" description:"Human-readable message describing the operation result"`
	ClusterID   string     `json:"cluster_id" example:"550e8400-e29b-41d4-a716-************" description:"UUID of the ALB cluster that was targeted"`
	BU          string     `json:"bu" example:"payments" description:"Business Unit of the requesting user"`
	TargetNodes []string   `json:"target_nodes" example:"[\"*********\",\"*********\",\"*********\"]" description:"List of node IP addresses that were targeted for reload"`
	StartTime   time.Time  `json:"start_time" example:"2025-01-08T10:30:00Z" description:"Timestamp when the reload operation started"`
	EndTime     *time.Time `json:"end_time,omitempty" example:"2025-01-08T10:30:15Z" description:"Timestamp when the reload operation completed (null if still in progress)"`
}

// UserBUMapping represents the mapping between users and business units
type UserBUMapping struct {
	Email string `json:"email" yaml:"email"`
	BU    string `json:"bu" yaml:"bu"`
}

// AuditLogEntry represents an audit log entry for reload operations
type AuditLogEntry struct {
	Timestamp   time.Time `json:"timestamp"`
	RequestID   string    `json:"request_id"`
	UserEmail   string    `json:"user_email"`
	BU          string    `json:"bu"`
	ClusterID   string    `json:"cluster_id"`
	TargetNodes []string  `json:"target_nodes"`
	Status      string    `json:"status"`
	ErrorMsg    string    `json:"error_msg,omitempty"`
	Reason      string    `json:"reason,omitempty"`
	Duration    int64     `json:"duration_ms"`
}

// RateLimitEntry represents a rate limiting entry
type RateLimitEntry struct {
	BU        string    `json:"bu"`
	LastUsed  time.Time `json:"last_used"`
	RequestID string    `json:"request_id"`
}

// ReloadStatus constants
const (
	ReloadStatusPending     = "pending"
	ReloadStatusInProgress  = "in_progress"
	ReloadStatusSuccess     = "success"
	ReloadStatusFailed      = "failed"
	ReloadStatusRateLimited = "rate_limited"
)

// Error codes for reload operations
const (
	ErrUnauthorized   = "ERR_UNAUTHORIZED"
	ErrForbidden      = "ERR_FORBIDDEN"
	ErrRateLimited    = "ERR_RATE_LIMITED"
	ErrInvalidCluster = "ERR_INVALID_CLUSTER"
	ErrInternalServer = "ERR_INTERNAL_SERVER"
	ErrReloadFailed   = "ERR_RELOAD_FAILED"
	ErrInvalidRequest = "ERR_INVALID_REQUEST"
)
