package albvo

import (
	"fmt"
	"math"
	"strings"

	"github.com/duke-git/lancet/v2/slice"
	uuid "github.com/iris-contrib/go.uuid"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/byteutils"
	"git.garena.com/shopee/go-shopeelib/json"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/nlb/nlbvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/swp/swpvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type FormData struct {
	Reason       string            `json:"reason"`
	Action       string            `json:"action"`
	UpdatedBy    string            `json:"updated_by"`
	IPs          []string          `json:"ips"`
	Nodes        []*sgwvo.Node     `json:"nodes"`
	NLBListeners []*nlbvo.Listener `json:"nlbListeners"`
	Cluster      *Cluster          `json:"cluster"`
	UUID         string            `json:"uuid"`
}

type TicketRequest struct {
	State string   `json:"state" binding:"omitempty,oneof=PreRunning PreOffline PreMA"`
	UUID  string   `json:"uuid" binding:"required"`
	IPs   []string `json:"ips" binding:"gt=0,dive,ip,required"`

	Token         string `json:"token"`
	Type          string `json:"type" binding:"omitempty,oneof=online offline ma"` // FE still not pass param
	Cluster       string `json:"cluster"`
	Reason        string `json:"reason" binding:"required"`
	ApplicantUser string `json:"applicant_user"`

	Title        string             `json:"title"`
	FormData     *FormData          `json:"form_data"`
	Label        *swpvo.TicketLabel `json:"label"`
	AutoSubmit   bool               `json:"auto_submit"`
	TemplateName string             `json:"template_name"`
}

func (r *TicketRequest) ResetTypeByState() error {
	switch r.State {
	case consts.PreRunningState.Status:
		r.Type = consts.TicketOnline
	case consts.PreOfflineState.Status:
		r.Type = consts.TicketOffline
	case consts.PreMaintenanceState.Status:
		r.Type = consts.TicketMA
	default:
		return fmt.Errorf("alb_state_%s_invalid", r.State)
	}

	return nil
}

func (r *TicketRequest) ToSWPTicket() *swpvo.TicketRequest {
	return &swpvo.TicketRequest{
		Title:        r.Title,
		FormData:     r.FormData,
		Label:        r.Label,
		TemplateName: r.TemplateName,
		AutoSubmit:   r.AutoSubmit,
		Token:        r.Token,
	}
}

func (r *TicketRequest) setBasicForm(info *Cluster, listeners []*nlbvo.Listener) {
	r.FormData = &FormData{
		UpdatedBy:    r.ApplicantUser,
		IPs:          r.IPs,
		NLBListeners: listeners,
	}
	r.Label = &swpvo.TicketLabel{
		FeData: &swpvo.TicketFEData{
			ServiceName: "sgw",
			ModuleName:  "cluster",
		},
	}
	r.FormData.Cluster = info
	r.FormData.UUID = info.UUID

	requestNodes := make(map[string]struct{})
	for _, val := range r.IPs {
		requestNodes[val] = struct{}{}
	}

	var nodes []*sgwvo.Node
	for _, node := range info.Nodes {
		if _, ok := requestNodes[node.IP]; !ok {
			continue
		}

		nodes = append(nodes, node)
	}

	r.FormData.Nodes = nodes
	r.AutoSubmit = true
}

func (r *TicketRequest) SetOnline(info *Cluster, listeners []*nlbvo.Listener) {
	r.setBasicForm(info, listeners)

	r.Title = fmt.Sprintf("ONLINE ALB Node - Cluster %s", info.Name)
	r.FormData.Reason = r.Reason
	r.FormData.Action = "ONLINE"
	r.TemplateName = consts.ALBOnlineNodeTemplate
	r.Label.TicketTemplateName = consts.ALBOnlineNodeTemplate
}

func (r *TicketRequest) SetOffline(info *Cluster, listeners []*nlbvo.Listener) {
	r.setBasicForm(info, listeners)

	r.Title = fmt.Sprintf("OFFLINE ALB Node - Cluster %s", info.Name)
	r.FormData.Reason = r.Reason
	r.FormData.Action = "OFFLINE"
	r.TemplateName = consts.ALBOfflineTemplate
	r.Label.TicketTemplateName = consts.ALBOfflineTemplate
}

type TicketData struct {
	Ticket *swpvo.Ticket    `json:"ticket"`
	Errors []core.ItemError `json:"errors,omitempty"`
}

func (t *TicketData) No() string {
	if t.Ticket == nil {
		return ""
	}

	return fmt.Sprintf("SWP-%d", t.Ticket.ID)
}

type TicketResponse struct {
	core.BaseResponse

	Data *TicketData `json:"data"`
}

type HotUpdateTicketFormData struct {
	Host string   `json:"host" binding:"required"`
	IPs  []string `json:"ips" binding:"dive,ip,required"` // must be the same ENV AZ
	// gray_ips 可以用户传入，也可以通过计算得到
	GrayIPs []string `json:"gray_ips"`
	// other_ips 通过计算得到
	OtherIPs []string `json:"other_ips"`
	// 如果gray_ips不是用户传入的， 那么就是必须填写的
	GrayScale int `json:"gray_scale" binding:"omitempty,min=1,max=99"`

	OperationDescription string `json:"operation_description" binding:"required"`
	OperationType        string `json:"operation_type" binding:"required,oneof=delete update add"`

	TargetConfigs   *tocvo.TocProvision `json:"-"` // target configs
	RollbackConfigs *tocvo.TocProvision `json:"-"` // rollback configs
}

func (d *HotUpdateTicketFormData) TargetConfig() (string, error) {
	ret, err := json.MarshalIndent(d.TargetConfigs, "", "    ")
	if err != nil {
		return "", errors.WithMessage(err, "json_marshal_indent_failed")
	}

	return byteutils.ToString(ret), nil
}

func (d *HotUpdateTicketFormData) RollbackConfig() (string, error) {
	ret, err := json.MarshalIndent(d.RollbackConfigs, "", "    ")
	if err != nil {
		return "", errors.WithMessage(err, "json_marshal_indent_failed")
	}

	return byteutils.ToString(ret), nil
}

func (d *HotUpdateTicketFormData) GrayIPMap() map[string]struct{} {
	ips := make(map[string]struct{})
	for _, ip := range d.GrayIPs {
		ips[ip] = struct{}{}
	}

	return ips
}

func (d *HotUpdateTicketFormData) SplitBatchByGrayIPs() {
	grayIPs := d.GrayIPMap()
	for _, ip := range d.IPs {
		if _, exist := grayIPs[ip]; !exist {
			d.OtherIPs = append(d.OtherIPs, ip)
		}
	}
}

func (d *HotUpdateTicketFormData) SplitBatch() {
	// 用户可以手动指定gray的机器
	if len(d.GrayIPs) != 0 {
		// reset gray scale value
		grayScaleNumFloat64 := float64(len(d.GrayIPs)*consts.HundredPercent) / float64(len(d.IPs))
		d.GrayScale = int(math.Ceil(grayScaleNumFloat64))
		// split batch
		d.SplitBatchByGrayIPs()
	} else {
		idx := int(math.Ceil(float64(len(d.IPs)*d.GrayScale) / consts.HundredPercent))
		d.GrayIPs = d.IPs[0:idx]
		d.OtherIPs = d.IPs[idx:]
	}
}

type HotUpdateTicketRequest struct {
	Token           string                   `json:"-"` // used at swp ticket
	IsOverall       bool                     `json:"is_overall"`
	UUID            string                   `json:"uuid" binding:"required"` // cluster's uuid
	Component       string                   `json:"component" binding:"required"`
	TargetVersion   string                   `json:"target_version" binding:"required"`
	RollbackVersion string                   `json:"rollback_version" binding:"required"`
	FormData        *HotUpdateTicketFormData `json:"form_data" binding:"required"`
}

func (r *HotUpdateTicketRequest) IsSupported() bool {
	return cmp.ALBComponent(r.Component) != nil
}

func (r *HotUpdateTicketRequest) ToNodeProvisionRequest() *NodeProvisionComponentRequest {
	return &NodeProvisionComponentRequest{
		IP:            r.FormData.IPs[0], // fetch by first
		Component:     r.Component,
		OperationType: r.FormData.OperationType,
	}
}

func (r *HotUpdateTicketRequest) ToTicketRequest() (*swpvo.TicketRequest, error) {
	rollbackConfig, err := r.FormData.RollbackConfig()
	if err != nil {
		log.Logger().WithError(err).WithField("rollback_config", r.FormData.RollbackConfigs).
			WithField("operation_type", r.FormData.OperationType).
			Error("create_tocex_ticket_error")

		return nil, err
	}

	targetConfig, err := r.FormData.TargetConfig()
	if err != nil {
		log.Logger().WithError(err).WithField("tocex_config", r.FormData.TargetConfigs).
			WithField("operation_type", r.FormData.OperationType).
			Error("create_tocex_ticket_error")

		return nil, err
	}

	formData := swpvo.HotUpdateSGWL7NodeTicketFormData{
		BusinessType:         consts.BizALBType,
		IPs:                  r.FormData.IPs,
		IPsTxt:               strings.Join(r.FormData.IPs, "\n"),
		GrayIPs:              r.FormData.GrayIPs,
		GrayIPsTxt:           strings.Join(r.FormData.GrayIPs, "\n"),
		OtherIPs:             r.FormData.OtherIPs,
		OtherIPsTxt:          strings.Join(r.FormData.OtherIPs, "\n"),
		GrayScale:            fmt.Sprintf("%d%%", r.FormData.GrayScale),
		OperationDescription: r.FormData.OperationDescription,
		OperationType:        r.FormData.OperationType,
		TargetConfigTxt:      targetConfig,
		TargetConfig:         r.FormData.TargetConfigs,
		RollbackConfigTxt:    rollbackConfig,
		RollbackConfig:       r.FormData.RollbackConfigs,
		Version:              uuid.Must(uuid.NewV4()).String(),
	}

	req := swpvo.TicketRequest{
		Title:        "[OpsPlatform] Hot Update ALB Node Component's configuration",
		FormData:     formData,
		TemplateName: configs.ALB.SWP.Template.HotUpdateNode,
		AutoSubmit:   true,
		Token:        r.Token,
	}

	return &req, nil
}

type HotUpdateStageRequest struct {
	Stage string `form:"stage" binding:"required,oneof=gray other all"`
}

type HotUpdateNodeBaseRequest struct {
	BusinessType  string   `json:"business_type" binding:"required"`
	IPs           []string `json:"ips" binding:"required,dive,ip"`        // total IP list
	Nodes         []string `json:"-"`                                     // hot-update IP list
	GrayIPs       []string `json:"gray_ips" binding:"required,dive,ip"`   // first batch
	OtherIPs      []string `json:"other_ips" binding:"omitempty,dive,ip"` // second batch
	Version       string   `json:"version" binding:"required"`
	ApplicantUser string   `json:"applicant_user"`
}

type HotUpdateOneNodeRequest struct {
	IP            string `json:"ip" binding:"required,ip"`
	Version       string `json:"version" binding:"required"`
	ApplicantUser string `json:"applicant_user"`
	OperationType string `json:"operation_type" binding:"required,oneof=update delete add"`
	// swpvo.HotUpdateSGWL7NodeTicketFormData
	HotUpdateConfig *tocvo.NodeProvision `json:"hot_update_config" binding:"required"`
}

func (r *HotUpdateOneNodeRequest) ToTocexConfig() *v1alpha1.TocexHotUpdateConfig {
	com := tocvo.NodeComponent(*r.HotUpdateConfig.Component)

	return &v1alpha1.TocexHotUpdateConfig{
		Type:       r.OperationType,
		Templates:  r.HotUpdateConfig.Templates.ToTocTemplates(),
		Components: com.ToTocComponents(),
		Version:    r.Version,
	}
}

func (r *HotUpdateOneNodeRequest) ToTocexRollbackConfig() *v1alpha1.TocexHotUpdateConfig {
	com := tocvo.NodeComponent(*r.HotUpdateConfig.Component)

	return &v1alpha1.TocexHotUpdateConfig{
		Type:               r.OperationType,
		RollbackTemplates:  r.HotUpdateConfig.Templates.ToTocTemplates(),
		RollbackComponents: com.ToTocComponents(),
		Version:            r.Version,
	}
}

type HotUpdateNodeRequest struct {
	HotUpdateNodeBaseRequest

	OperationType string `json:"operation_type" binding:"required,oneof=update delete add"`
	// swpvo.HotUpdateSGWL7NodeTicketFormData
	TargetConfig *v1alpha1.TocexHotUpdateConfig `json:"target_config" binding:"required"`
	// mapping to SWP template JSON schema
	RollbackConfig  *v1alpha1.TocexHotUpdateConfig `json:"rollback_config" binding:"required"`
	HotUpdateConfig *v1alpha1.TocexHotUpdateConfig `json:"-"`
}

// SetHotUpdateConfig set hot update config
// like update nginx need to restart sgw-agent
func (r *HotUpdateNodeRequest) SetHotUpdateConfig() {
	hotUpdateConfig := r.TargetConfig
	hotUpdateConfig.RollbackTemplates = r.RollbackConfig.Templates
	hotUpdateConfig.RollbackComponents = r.RollbackConfig.Components

	switch r.OperationType {
	case consts.DeleteTocexItem:
		slice.ForEach(hotUpdateConfig.Templates, func(i int, t v1alpha1.TocTemplate) {
			hotUpdateConfig.Templates[i].Content = "" // to decrease the size of template
		})
	}

	r.HotUpdateConfig = hotUpdateConfig
}

func (r *HotUpdateNodeRequest) ToTocexConfig() *v1alpha1.TocexHotUpdateConfig {
	return &v1alpha1.TocexHotUpdateConfig{
		Type:               r.OperationType,
		Templates:          r.HotUpdateConfig.Templates,
		Components:         r.HotUpdateConfig.Components,
		RollbackTemplates:  r.HotUpdateConfig.RollbackTemplates,
		RollbackComponents: r.HotUpdateConfig.RollbackComponents,
		Version:            r.Version,
	}
}

type ClusterTrafficRequest struct {
	Token       string   `json:"token"`
	Product     string   `json:"product" binding:"required"`
	RZ          string   `json:"rz" binding:"required"`
	NetworkType string   `json:"network_type" binding:"required,oneof=WAN LAN"`
	Domains     []string `json:"domains"`
}

type ControlTrafficFormRequest struct {
	Token       string   `json:"token"`
	Product     string   `json:"product" binding:"required"`
	RZ          string   `json:"rz" binding:"required"`
	IPs         []string `json:"ips" binding:"required,dive,ip"`
	Clusters    []string `json:"clusters" binding:"required"`
	NetworkType string   `json:"network_type" binding:"required,oneof=WAN LAN"`
	Domains     []string `json:"domains"`
}

func (r *ControlTrafficFormRequest) ToBlockTicketRequest() *swpvo.TicketRequest {
	req := swpvo.TicketRequest{
		Title: fmt.Sprintf("Block ALB Cluster traffic of %s", r.Clusters),
		Token: r.Token,
		FormData: map[string]interface{}{
			"rz":           r.RZ,
			"product":      r.Product,
			"network_type": r.NetworkType,
			"clusters":     r.Clusters,
			"clusters_txt": slice.Join(r.Clusters, "\n"),
			"ips":          r.IPs,
			"ips_txt":      slice.Join(r.IPs, "\n"),
		},
		TemplateName: configs.ALB.SWP.Template.ClusterBlockTraffic,
		AutoSubmit:   true,
	}

	return &req
}

func (r *ControlTrafficFormRequest) ToBlockTicketByDomainsRequest() *swpvo.TicketRequest {
	req := swpvo.TicketRequest{
		Title: fmt.Sprintf("Block ALB traffic by domains of %s", r.Clusters),
		Token: r.Token,
		FormData: map[string]interface{}{
			"rz":           r.RZ,
			"product":      r.Product,
			"network_type": r.NetworkType,
			"clusters":     r.Clusters,
			"clusters_txt": slice.Join(r.Clusters, "\n"),
			"ips":          r.IPs,
			"ips_txt":      slice.Join(r.IPs, "\n"),
			"domains":      r.Domains,
			"domains_txt":  slice.Join(r.Domains, "\n"),
		},
		TemplateName: configs.ALB.SWP.Template.BlockByDomains,
		AutoSubmit:   true,
	}

	return &req
}

func (r *ControlTrafficFormRequest) ToOpenTicketRequest() *swpvo.TicketRequest {
	req := swpvo.TicketRequest{
		Title: fmt.Sprintf("Open ALB Cluster traffic of %s", r.Clusters),
		Token: r.Token,
		FormData: map[string]interface{}{
			"rz":           r.RZ,
			"product":      r.Product,
			"clusters":     r.Clusters,
			"network_type": r.NetworkType,
			"clusters_txt": slice.Join(r.Clusters, "\n"),
			"ips":          r.IPs,
			"ips_txt":      slice.Join(r.IPs, "\n"),
		},
		TemplateName: configs.ALB.SWP.Template.ClusterOpenTraffic,
		AutoSubmit:   true,
	}

	return &req
}

func (r *ControlTrafficFormRequest) ToOpenTicketByDomainsRequest() *swpvo.TicketRequest {
	req := swpvo.TicketRequest{
		Title: fmt.Sprintf("Open ALB traffic by domains of %s", r.Clusters),
		Token: r.Token,
		FormData: map[string]interface{}{
			"rz":           r.RZ,
			"product":      r.Product,
			"clusters":     r.Clusters,
			"network_type": r.NetworkType,
			"clusters_txt": slice.Join(r.Clusters, "\n"),
			"ips":          r.IPs,
			"ips_txt":      slice.Join(r.IPs, "\n"),
		},
		TemplateName: configs.ALB.SWP.Template.OpenByDomains,
		AutoSubmit:   true,
	}

	return &req
}

type AutoMARequest struct {
	IPs    []string `json:"ips" binding:"required,dive,ip"`
	UUID   string   `json:"uuid" binding:"required"`
	Reason string   `json:"reason" binding:"required"`
}

func (r *AutoMARequest) SetIPs(ips []string) {
	r.IPs = ips
}

func (r *AutoMARequest) SetUUID(uuid string) {
	r.UUID = uuid
}

func (r *AutoMARequest) SetReason(reason string) {
	r.Reason = reason
}
