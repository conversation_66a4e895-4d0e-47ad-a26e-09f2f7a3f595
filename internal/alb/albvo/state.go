package albvo

import (
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

// GetStatesResponse state response
type GetStatesResponse struct {
	core.BaseResponse

	Data struct {
		States []consts.State `json:"states"`
	} `json:"data"`
}

// GetStateTasksResponse task response
type GetStateTasksResponse struct {
	core.BaseResponse

	Data struct {
		Tasks []string `json:"tasks"`
	} `json:"data"`
}

type GetStateTaskResultsRequest struct {
	IP    string `json:"ip" form:"ip" binding:"required,ip"`
	State string `json:"state" form:"state" binding:"required"`
}

// GetStateTaskResultsResponse task result response
type GetStateTaskResultsResponse struct {
	core.BaseResponse

	Data struct {
		Details *sgwvo.DetailInfo `json:"details"`
	} `json:"data"`
}

type ControlTrafficTaskResultsResponse struct {
	core.BaseResponse

	Data struct {
		Details []*sgwvo.NodeTaskResult `json:"details"`
	} `json:"data"`
}
