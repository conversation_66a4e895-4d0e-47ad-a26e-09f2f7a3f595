package albvo

import (
	"sync"
	"time"
)

// PreCheckTimeoutError represents a timeout error with detailed timing information
type PreCheckTimeoutError struct {
	Message       string                 `json:"message"`
	TotalElapsed  float64                `json:"total_elapsed_seconds"`
	StartedAt     time.Time              `json:"started_at"`
	TimeoutAt     time.Time              `json:"timeout_at"`
	NodeStats     *PreCheckNodeStats     `json:"node_stats"`
	ActiveNodes   map[string]*NodeStatus `json:"active_nodes,omitempty"`
	CompletedData interface{}            `json:"completed_data,omitempty"`
}

// Error implements the error interface
func (e *PreCheckTimeoutError) Error() string {
	return e.Message
}

// PreCheckNodeStats tracks overall node statistics
type PreCheckNodeStats struct {
	TotalNodes      int     `json:"total_nodes"`
	CompletedNodes  int     `json:"completed_nodes"`
	PendingNodes    int     `json:"pending_nodes"`
	FailedNodes     int     `json:"failed_nodes"`
	AvgNodeDuration float64 `json:"avg_node_duration_seconds,omitempty"`
}

// CheckerStatus tracks the status of a single checker
type CheckerStatus struct {
	Name        string `json:"name"`
	StartedAt   int64  `json:"started_at,omitempty"`
	CompletedAt int64  `json:"completed_at,omitempty"`
	ElapsedMs   int64  `json:"elapsed_ms,omitempty"`
	Success     bool   `json:"success,omitempty"`
	Reason      string `json:"reason,omitempty"`
	IsCompleted bool   `json:"is_completed"`
}

// NodeStatus tracks the status of a single node's precheck
type NodeStatus struct {
	IP               string                    `json:"ip"`
	StartedAt        time.Time                 `json:"started_at"`
	CurrentCheck     string                    `json:"current_check,omitempty"`
	CompletedChecks  int                       `json:"completed_checks"`
	TotalChecks      int                       `json:"total_checks"`
	ElapsedSeconds   float64                   `json:"elapsed_seconds"`
	LastCheckStarted time.Time                 `json:"last_check_started,omitempty"`
	CheckerStatuses  map[string]*CheckerStatus `json:"checker_statuses,omitempty"`
}

// PreCheckTracker tracks the overall precheck operation timing
type PreCheckTracker struct {
	mu              sync.RWMutex
	startTime       time.Time
	activeNodes     map[string]*NodeStatus
	completedNodes  map[string]bool
	failedNodes     map[string]bool
	totalNodes      int
	completedTiming []float64 // Track durations for averaging
}

// NewPreCheckTracker creates a new precheck tracker
func NewPreCheckTracker(totalNodes int) *PreCheckTracker {
	return &PreCheckTracker{
		startTime:       time.Now(),
		activeNodes:     make(map[string]*NodeStatus),
		completedNodes:  make(map[string]bool),
		failedNodes:     make(map[string]bool),
		totalNodes:      totalNodes,
		completedTiming: make([]float64, 0, totalNodes),
	}
}

// StartNode marks a node as started
func (t *PreCheckTracker) StartNode(ip string, totalChecks int) {
	t.mu.Lock()
	defer t.mu.Unlock()

	t.activeNodes[ip] = &NodeStatus{
		IP:              ip,
		StartedAt:       time.Now(),
		TotalChecks:     totalChecks,
		CheckerStatuses: make(map[string]*CheckerStatus),
	}
}

// UpdateNodeCheck updates the current check for a node
func (t *PreCheckTracker) UpdateNodeCheck(ip, checkName string) {
	t.mu.Lock()
	defer t.mu.Unlock()

	if node, ok := t.activeNodes[ip]; ok {
		node.CurrentCheck = checkName
		node.LastCheckStarted = time.Now()
		node.ElapsedSeconds = time.Since(node.StartedAt).Seconds()

		// Initialize checker status if not exists
		if node.CheckerStatuses[checkName] == nil {
			node.CheckerStatuses[checkName] = &CheckerStatus{
				Name:        checkName,
				StartedAt:   time.Now().UnixMilli(),
				IsCompleted: false,
			}
		}
	}
}

// CompleteNodeCheck marks a check as completed for a node
func (t *PreCheckTracker) CompleteNodeCheck(ip string) {
	t.mu.Lock()
	defer t.mu.Unlock()

	if node, ok := t.activeNodes[ip]; ok {
		node.CompletedChecks++
		node.ElapsedSeconds = time.Since(node.StartedAt).Seconds()
	}
}

// CompleteSpecificCheck marks a specific check as completed with result
func (t *PreCheckTracker) CompleteSpecificCheck(ip, checkName string, success bool, reason string) {
	t.mu.Lock()
	defer t.mu.Unlock()

	if node, ok := t.activeNodes[ip]; ok {
		if checker, ok := node.CheckerStatuses[checkName]; ok {
			now := time.Now().UnixMilli()
			checker.CompletedAt = now
			checker.ElapsedMs = now - checker.StartedAt
			checker.Success = success
			checker.Reason = reason
			checker.IsCompleted = true
		}
	}
}

// CompleteNode marks a node as completed
func (t *PreCheckTracker) CompleteNode(ip string, success bool) {
	t.mu.Lock()
	defer t.mu.Unlock()

	if node, ok := t.activeNodes[ip]; ok {
		duration := time.Since(node.StartedAt).Seconds()
		t.completedTiming = append(t.completedTiming, duration)

		if success {
			t.completedNodes[ip] = true
		} else {
			t.failedNodes[ip] = true
		}
		delete(t.activeNodes, ip)
	}
}

// GetStats returns current statistics
func (t *PreCheckTracker) GetStats() *PreCheckNodeStats {
	t.mu.RLock()
	defer t.mu.RUnlock()

	stats := &PreCheckNodeStats{
		TotalNodes:     t.totalNodes,
		CompletedNodes: len(t.completedNodes),
		FailedNodes:    len(t.failedNodes),
		PendingNodes:   t.totalNodes - len(t.completedNodes) - len(t.failedNodes),
	}

	// Calculate average duration
	if len(t.completedTiming) > 0 {
		var sum float64
		for _, d := range t.completedTiming {
			sum += d
		}
		stats.AvgNodeDuration = sum / float64(len(t.completedTiming))
	}

	return stats
}

// GetActiveNodes returns a copy of active nodes
func (t *PreCheckTracker) GetActiveNodes() map[string]*NodeStatus {
	t.mu.RLock()
	defer t.mu.RUnlock()

	// Deep copy to avoid race conditions
	result := make(map[string]*NodeStatus, len(t.activeNodes))
	for key, node := range t.activeNodes {
		nodeCopy := *node
		nodeCopy.ElapsedSeconds = time.Since(node.StartedAt).Seconds()

		// Deep copy checker statuses
		if node.CheckerStatuses != nil {
			nodeCopy.CheckerStatuses = make(map[string]*CheckerStatus, len(node.CheckerStatuses))
			for checkName, checker := range node.CheckerStatuses {
				checkerCopy := *checker
				nodeCopy.CheckerStatuses[checkName] = &checkerCopy
			}
		}

		result[key] = &nodeCopy
	}

	return result
}

// GetElapsedTime returns total elapsed time
func (t *PreCheckTracker) GetElapsedTime() float64 {
	return time.Since(t.startTime).Seconds()
}

// GetTimeoutError creates a timeout error with current state
func (t *PreCheckTracker) GetTimeoutError(message string) *PreCheckTimeoutError {
	return &PreCheckTimeoutError{
		Message:      message,
		TotalElapsed: t.GetElapsedTime(),
		StartedAt:    t.startTime,
		TimeoutAt:    time.Now(),
		NodeStats:    t.GetStats(),
		ActiveNodes:  t.GetActiveNodes(),
	}
}

// GetPendingNodeIPs returns list of nodes that haven't completed
func (t *PreCheckTracker) GetPendingNodeIPs() []string {
	t.mu.RLock()
	defer t.mu.RUnlock()

	pending := make([]string, 0, len(t.activeNodes))
	for ip := range t.activeNodes {
		pending = append(pending, ip)
	}

	return pending
}

// GetCheckerSummary returns a summary of checker statuses for all active nodes
func (t *PreCheckTracker) GetCheckerSummary() map[string]map[string]interface{} {
	t.mu.RLock()
	defer t.mu.RUnlock()

	summary := make(map[string]map[string]interface{})
	for ip, node := range t.activeNodes {
		nodeSummary := make(map[string]interface{})
		completed := make([]map[string]interface{}, 0)
		pending := make([]string, 0)

		for checkName, checker := range node.CheckerStatuses {
			if checker.IsCompleted {
				completed = append(completed, map[string]interface{}{
					"name":       checker.Name,
					"success":    checker.Success,
					"elapsed_ms": checker.ElapsedMs,
					"reason":     checker.Reason,
				})
			} else {
				pending = append(pending, checkName)
			}
		}

		nodeSummary["completed_checkers"] = completed
		nodeSummary["pending_checkers"] = pending
		nodeSummary["total_elapsed_seconds"] = time.Since(node.StartedAt).Seconds()
		summary[ip] = nodeSummary
	}

	return summary
}
