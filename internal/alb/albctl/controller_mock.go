// Code generated by MockGen. DO NOT EDIT.
// Source: controller.go

// Package albctl is a generated GoMock package.
package albctl

import (
	context "context"
	reflect "reflect"

	v1alpha1 "git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	albvo "git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	consts "git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	gomock "github.com/golang/mock/gomock"
)

// MockController is a mock of Controller interface.
type MockController struct {
	ctrl     *gomock.Controller
	recorder *MockControllerMockRecorder
}

// MockControllerMockRecorder is the mock recorder for MockController.
type MockControllerMockRecorder struct {
	mock *MockController
}

// NewMockController creates a new mock instance.
func NewMockController(ctrl *gomock.Controller) *MockController {
	mock := &MockController{ctrl: ctrl}
	mock.recorder = &MockControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockController) EXPECT() *MockControllerMockRecorder {
	return m.recorder
}

// CreateWithContext mocks base method.
func (m *MockController) CreateWithContext(ctx context.Context, alb *v1alpha1.ALB) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWithContext", ctx, alb)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateWithContext indicates an expected call of CreateWithContext.
func (mr *MockControllerMockRecorder) CreateWithContext(ctx, alb interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWithContext", reflect.TypeOf((*MockController)(nil).CreateWithContext), ctx, alb)
}

// DeleteWithContext mocks base method.
func (m *MockController) DeleteWithContext(ctx context.Context, alb *v1alpha1.ALB) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWithContext", ctx, alb)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteWithContext indicates an expected call of DeleteWithContext.
func (mr *MockControllerMockRecorder) DeleteWithContext(ctx, alb interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWithContext", reflect.TypeOf((*MockController)(nil).DeleteWithContext), ctx, alb)
}

// FreshStateRunner mocks base method.
func (m *MockController) FreshStateRunner(name, state string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "FreshStateRunner", name, state)
}

// FreshStateRunner indicates an expected call of FreshStateRunner.
func (mr *MockControllerMockRecorder) FreshStateRunner(name, state interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreshStateRunner", reflect.TypeOf((*MockController)(nil).FreshStateRunner), name, state)
}

// GetALBList mocks base method.
func (m *MockController) GetALBList(ctx context.Context) (*v1alpha1.ALBList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetALBList", ctx)
	ret0, _ := ret[0].(*v1alpha1.ALBList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetALBList indicates an expected call of GetALBList.
func (mr *MockControllerMockRecorder) GetALBList(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetALBList", reflect.TypeOf((*MockController)(nil).GetALBList), ctx)
}

// GetComponentVersion mocks base method.
func (m *MockController) GetComponentVersion(rz, env string) *albvo.NodeComponentVersion {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetComponentVersion", rz, env)
	ret0, _ := ret[0].(*albvo.NodeComponentVersion)
	return ret0
}

// GetComponentVersion indicates an expected call of GetComponentVersion.
func (mr *MockControllerMockRecorder) GetComponentVersion(rz, env interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetComponentVersion", reflect.TypeOf((*MockController)(nil).GetComponentVersion), rz, env)
}

// GetComponentVersionByAZ mocks base method.
func (m *MockController) GetComponentVersionByAZ(az string) *albvo.NodeComponentVersion {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetComponentVersionByAZ", az)
	ret0, _ := ret[0].(*albvo.NodeComponentVersion)
	return ret0
}

// GetComponentVersionByAZ indicates an expected call of GetComponentVersionByAZ.
func (mr *MockControllerMockRecorder) GetComponentVersionByAZ(az interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetComponentVersionByAZ", reflect.TypeOf((*MockController)(nil).GetComponentVersionByAZ), az)
}

// GetComponentVersionByGlobal mocks base method.
func (m *MockController) GetComponentVersionByGlobal() *albvo.NodeComponentVersion {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetComponentVersionByGlobal")
	ret0, _ := ret[0].(*albvo.NodeComponentVersion)
	return ret0
}

// GetComponentVersionByGlobal indicates an expected call of GetComponentVersionByGlobal.
func (mr *MockControllerMockRecorder) GetComponentVersionByGlobal() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetComponentVersionByGlobal", reflect.TypeOf((*MockController)(nil).GetComponentVersionByGlobal))
}

// GetStateRunnerTasks mocks base method.
func (m *MockController) GetStateRunnerTasks() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStateRunnerTasks")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetStateRunnerTasks indicates an expected call of GetStateRunnerTasks.
func (mr *MockControllerMockRecorder) GetStateRunnerTasks() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStateRunnerTasks", reflect.TypeOf((*MockController)(nil).GetStateRunnerTasks))
}

// GetWithContext mocks base method.
func (m *MockController) GetWithContext(ctx context.Context, name string) (*v1alpha1.ALB, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWithContext", ctx, name)
	ret0, _ := ret[0].(*v1alpha1.ALB)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWithContext indicates an expected call of GetWithContext.
func (mr *MockControllerMockRecorder) GetWithContext(ctx, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWithContext", reflect.TypeOf((*MockController)(nil).GetWithContext), ctx, name)
}

// UpdateMATicket mocks base method.
func (m *MockController) UpdateMATicket(ctx context.Context, name string, ticketID int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMATicket", ctx, name, ticketID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMATicket indicates an expected call of UpdateMATicket.
func (mr *MockControllerMockRecorder) UpdateMATicket(ctx, name, ticketID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMATicket", reflect.TypeOf((*MockController)(nil).UpdateMATicket), ctx, name, ticketID)
}

// UpdateState mocks base method.
func (m *MockController) UpdateState(ctx context.Context, name string, state consts.State) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateState", ctx, name, state)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateState indicates an expected call of UpdateState.
func (mr *MockControllerMockRecorder) UpdateState(ctx, name, state interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateState", reflect.TypeOf((*MockController)(nil).UpdateState), ctx, name, state)
}

// UpdateWithContext mocks base method.
func (m *MockController) UpdateWithContext(ctx context.Context, alb *v1alpha1.ALB) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWithContext", ctx, alb)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWithContext indicates an expected call of UpdateWithContext.
func (mr *MockControllerMockRecorder) UpdateWithContext(ctx, alb interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWithContext", reflect.TypeOf((*MockController)(nil).UpdateWithContext), ctx, alb)
}
