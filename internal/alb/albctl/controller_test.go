package albctl

import (
	"context"
	"os"
	"testing"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwctl"
)

func initController(t *testing.T) {
	t.Helper()

	t.Setenv("KUBECONFIG", "/Users/<USER>/.kube/config")

	quit := make(chan struct{})
	client, err := sgwctl.NewControllerClient(os.Getenv("KUBECONFIG"))
	assert.NoError(t, err)

	defer close(quit)

	ALBController = NewControllerApp(client)
	client.Start(quit)

	go func() {
		err = ALBController.Run(context.Background())
		assert.NoError(t, err)
	}()
}

func alb() *v1alpha1.ALB {
	fakeNodeIP := gofakeit.IPv4Address()
	alb := &v1alpha1.ALB{
		Spec: v1alpha1.ALBSpec{
			Type:  "ALB",
			LanIP: fakeNodeIP,
			ALBNode: v1alpha1.ALBNode{
				Zone:             "ap-sg-1-general-x",
				IDC:              "sg2",
				Env:              "test",
				SDU:              "alb-stdwan-test-sg2",
				AZBusiness:       "alb-stdwan-test-sg2",
				ALBName:          "alb.stdwan.sg2.test",
				MetricsImageTag:  gofakeit.AppVersion(),
				ALBSdImageTag:    gofakeit.AppVersion(),
				ALBAgentImageTag: gofakeit.AppVersion(),
				NginxLbImageTag:  gofakeit.AppVersion(),
				NginxVersion:     gofakeit.AppVersion(),
				ScertmsVersion:   gofakeit.AppVersion(),
			},
		},
		Status: v1alpha1.ALBStatus{
			State:  consts.InitialisedState.Status,
			Reason: consts.InitialisedState.Reason,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: albvo.CRName(fakeNodeIP),
		},
	}

	return alb
}

func TestControllerApp_UpdateState_Sunset(t *testing.T) {
	if ciEnv {
		t.SkipNow()
	}

	initController(t)

	alb := alb()
	err := ALBController.CreateWithContext(context.Background(), alb)
	assert.NoError(t, err)

	time.Sleep(5 * time.Second)

	err = ALBController.UpdateState(context.Background(), alb.Name, consts.SunsetState)
	assert.NoError(t, err)

	time.Sleep(5 * time.Second)
	alb, err = ALBController.GetWithContext(context.Background(), alb.Name)
	assert.NoError(t, err)
	assert.Equal(t, consts.SunsetState.Status, alb.Status.State)
}

func TestControllerApp_GetComponentVersion(t *testing.T) {
	if ciEnv {
		t.SkipNow()
	}

	initController(t)

	ver := ALBController.GetComponentVersion("", "")
	assert.Equal(t, consts.UnknownVersion, ver.ALBMetrics)

	alb := alb()

	err := ALBController.CreateWithContext(context.Background(), alb)
	assert.NoError(t, err)
	defer func() {
		err = ALBController.DeleteWithContext(context.Background(), alb)
		assert.NoError(t, err)
	}()

	time.Sleep(10 * time.Second)

	ver = ALBController.GetComponentVersion("sg2", "test")
	assert.NotEqual(t, consts.UnknownVersion, ver.ALBMetrics)
}
