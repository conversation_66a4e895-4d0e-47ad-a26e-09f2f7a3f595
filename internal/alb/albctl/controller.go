package albctl

import (
	"context"
	"fmt"
	"os"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/uuid"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	typedcorev1 "k8s.io/client-go/kubernetes/typed/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"k8s.io/client-go/tools/record"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	leap "git.garena.com/shopee/devops/leap-apis/pkg/generated/clientset/versioned"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwctl"
)

//go:generate mockgen -destination controller_mock.go -package albctl -source controller.go

// Controller controller
type Controller interface {
	GetComponentVersion(rz, env string) *albvo.NodeComponentVersion
	GetComponentVersionByAZ(az string) *albvo.NodeComponentVersion
	GetComponentVersionByGlobal() *albvo.NodeComponentVersion
	GetStateRunnerTasks() []string
	FreshStateRunner(name, state string)
	UpdateState(ctx context.Context, name string, state consts.State) error
	GetWithContext(ctx context.Context, name string) (*v1alpha1.ALB, error)
	CreateWithContext(ctx context.Context, alb *v1alpha1.ALB) error
	GetALBList(ctx context.Context) (*v1alpha1.ALBList, error)
	UpdateMATicket(ctx context.Context, name string, ticketID int) error
	UpdateWithContext(ctx context.Context, alb *v1alpha1.ALB) error
	DeleteWithContext(ctx context.Context, alb *v1alpha1.ALB) error
}

// ALBController alb controller
var ALBController *ControllerApp

// ControllerApp controller app
type ControllerApp struct {
	kubeClient kubernetes.Interface
	leapClient leap.Interface
	controller *albstate.Controller

	recorder record.EventRecorder
}

// NewControllerApp controller app
func NewControllerApp(client *sgwctl.ControllerClient) *ControllerApp {
	eventBroadcaster := record.NewBroadcaster()

	eventBroadcaster.StartLogging(func(format string, args ...interface{}) {
		log.Logger().Info(fmt.Sprintf(format, args...))
	})

	eventBroadcaster.StartRecordingToSink(&typedcorev1.EventSinkImpl{
		Interface: client.KubeClient.CoreV1().Events(""),
	})

	recorder := eventBroadcaster.NewRecorder(scheme.Scheme, v1.EventSource{
		Component: configs.ALB.Kube.Leader.Name,
	})

	informer := client.LeapInformer.Machine().V1alpha1().ALBs()

	return &ControllerApp{
		kubeClient: client.KubeClient,
		leapClient: client.LeapClient,
		recorder:   recorder,
		controller: albstate.NewController(
			client.KubeClient,
			client.LeapClient,
			informer,
			recorder,
		),
	}
}

func (c *ControllerApp) UpdateMATicket(ctx context.Context, name string, ticketID int) error {
	alb, err := c.GetWithContext(ctx, name)
	if err != nil {
		return errors.WithMessage(err, "fetch_alb_cr_failed")
	}

	if err = c.controller.UpdateALBMATicketWithContext(ctx, alb, ticketID); err != nil {
		return errors.WithMessage(err, "update_alb_ma_ticket_failed")
	}

	albstate.PurgeStateRunnerResult(alb)

	return nil
}

// UpdateWithContext update alb
func (c *ControllerApp) UpdateWithContext(ctx context.Context, alb *v1alpha1.ALB) error {
	albstate.PurgeStateRunnerResult(alb)

	return errors.WithMessage(c.controller.Update(ctx, alb), "update_cr_failed")
}

// CreateWithContext create alb
func (c *ControllerApp) CreateWithContext(ctx context.Context, alb *v1alpha1.ALB) error {
	return errors.WithMessage(c.controller.Create(ctx, alb), "create_cr_failed")
}

// DeleteWithContext delete alb
func (c *ControllerApp) DeleteWithContext(ctx context.Context, alb *v1alpha1.ALB) error {
	return errors.WithMessage(c.controller.Delete(ctx, alb), "delete_cr_failed")
}

// GetWithContext get alb
func (c *ControllerApp) GetWithContext(ctx context.Context, name string) (*v1alpha1.ALB, error) {
	alb, err := c.controller.Get(ctx, name)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cr_failed")
	}

	return alb, nil
}

// UpdateState update state
func (c *ControllerApp) UpdateState(ctx context.Context, name string, state consts.State) error {
	alb, err := c.GetWithContext(ctx, name)
	if err != nil {
		return errors.WithMessage(err, "fetch_cr_failed")
	}

	if err = c.controller.CompareAndUpdateStatusWithContext(ctx, alb, state); err != nil {
		return errors.WithMessage(err, "update_status_failed")
	}

	albstate.PurgeStateRunnerResult(alb)

	return nil
}

// GetStateRunnerTasks fetch state runner tasks
func (c *ControllerApp) GetStateRunnerTasks() []string {
	return albstate.FetchStateRunnerResult()
}

func (c *ControllerApp) FreshStateRunner(name, state string) {
	albstate.FreshStateRunnerResult(name, state)
}

// Run controller run
func (c *ControllerApp) Run(ctx context.Context) error {
	defer utilruntime.HandleCrash()

	if ok := cache.WaitForCacheSync(
		ctx.Done(),
		c.controller.InformerSynced(),
		c.controller.SyncNodes(),
	); !ok {
		err := errors.New("timeout_wait_for_cache_synced")
		log.Logger().WithError(err).Error("operator_run_failed")

		return errors.WithMessage(err, "operator_run_failed")
	}

	log.Logger().Info("wait_for_cache_sync_success")

	kube := configs.ALB.Kube

	go wait.Until(func() {
		defer utilruntime.HandleCrash()

		c.controller.SyncNodeMap(ctx)
	}, kube.Sync.SyncInterval(), ctx.Done())

	rl := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{
			Name:      kube.Lease.Name,
			Namespace: kube.Lease.Namespace,
		},
		Client: c.kubeClient.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity: func() string {
				id, _ := os.Hostname()
				id = id + "_" + string(uuid.NewUUID())

				return id
			}(),
		},
	}

	leaderelection.RunOrDie(ctx, leaderelection.LeaderElectionConfig{
		Lock:            rl,
		ReleaseOnCancel: true,
		LeaseDuration:   kube.Leader.LeaseDuration(),
		RenewDeadline:   kube.Leader.RenewDeadline(),
		RetryPeriod:     kube.Leader.RetryPeriod(),
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				c.controller.EnableLeader()

				for i := 0; i < kube.Leader.Concurrent; i++ {
					go wait.Until(c.controller.Run, kube.Leader.LaunchTimeout(), ctx.Done())
				}
			},
			OnStoppedLeading: func() {
				log.Logger().Fatal("operator_stop_leading")
			},
		},
		Name: kube.Leader.Name,
	})

	return nil
}

func (c *ControllerApp) GetALBList(ctx context.Context) (*v1alpha1.ALBList, error) {
	albs, err := c.controller.GetALBList(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "get_alb_list_failed")
	}

	return albs, nil
}
