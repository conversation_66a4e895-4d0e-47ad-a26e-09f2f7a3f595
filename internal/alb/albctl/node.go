package albctl

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// GetComponentVersion fetch component version
func (c *ControllerApp) GetComponentVersion(rz, env string) *albvo.NodeComponentVersion {
	return c.componentVersion(consts.ScopeRZ, rz, env)
}

// GetComponentVersionByAZ fetch component version by az
func (c *ControllerApp) GetComponentVersionByAZ(az string) *albvo.NodeComponentVersion {
	return c.componentVersion(consts.ScopeAZ, az, consts.EnvLive)
}

// GetComponentVersionByGlobal fetch component version by global
func (c *ControllerApp) GetComponentVersionByGlobal() *albvo.NodeComponentVersion {
	return c.componentVersion(consts.ScopeUnSpec, "", consts.EnvLive)
}

// componentVersion fetch component version
func (c *ControllerApp) componentVersion(scope consts.Scope, val, env string) *albvo.NodeComponentVersion {
	albMetricsVersion := make(map[string]uint)
	albSDVersion := make(map[string]uint)
	SGWAgentVersion := make(map[string]uint)
	NginxLBVersion := make(map[string]uint)
	NginxVersion := make(map[string]uint)

	albstate.NodeMap.Range(func(key, value interface{}) bool {
		alb, ok := value.(*v1alpha1.ALB)
		if !ok {
			return true
		}

		if alb.Status.State != consts.RunningState.Status && alb.Status.State != consts.InitialisedState.Status {
			return true
		}

		if scope.Code != consts.ScopeUnSpec.Code {
			var cmpValue string
			switch scope.Code {
			case consts.ScopeCluster.Code:
				cmpValue = alb.Spec.ALBNode.ClusterUUID
			case consts.ScopeRZ.Code:
				cmpValue = alb.Spec.ALBNode.IDC
			case consts.ScopeAZ.Code:
				cmpValue = alb.Spec.ALBNode.Zone
			}

			if scope.Code == consts.ScopeRZ.Code {
				if env != alb.Spec.ALBNode.Env {
					return true
				}
			}

			if cmpValue != val {
				return true
			}
		}

		albMetricsVersion[alb.Spec.ALBNode.MetricsImageTag]++
		albSDVersion[alb.Spec.ALBNode.ALBSdImageTag]++
		SGWAgentVersion[alb.Spec.ALBNode.ALBAgentImageTag]++
		NginxLBVersion[alb.Spec.ALBNode.NginxLbImageTag]++
		NginxVersion[alb.Spec.ALBNode.NginxVersion]++

		return true
	})

	return &albvo.NodeComponentVersion{
		ALBMetrics: core.NodePeakVersion(albMetricsVersion),
		ALBSD:      core.NodePeakVersion(albSDVersion),
		SGWAgent:   core.NodePeakVersion(SGWAgentVersion),
		NginxLB:    core.NodePeakVersion(NginxLBVersion),
		Nginx:      core.NodePeakVersion(NginxVersion),
	}
}
