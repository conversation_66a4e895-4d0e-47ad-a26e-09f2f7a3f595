package albapp

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
)

func TestNodeApp_Events(t *testing.T) {
	if ciEnv {
		t.Skip("Skipping test in CI")
	}

	app := NewNodeApp(t.Name())

	resp, err := app.Events(context.Background(), &albvo.NodePagerRequest{
		IP:    configs.E2E.ALB.Node2,
		Page:  1,
		Limit: 10,
	})
	assert.NoError(t, err)
	assert.True(t, resp.IsSuccess())
	assert.GreaterOrEqual(t, resp.Total, len(resp.Data.Events))
}
