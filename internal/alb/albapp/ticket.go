package albapp

import (
	"context"
	"fmt"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/json"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/im/imvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/im"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/meta"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/see"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/uic"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsdom/opsrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocdom/tocrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

// TicketApp ticket application
type TicketApp struct {
	traceID    string
	repo       albrepo.TicketRepo
	node       albrepo.NodeRepo
	cluster    albrepo.ClusterRepo
	verifier   albrepo.NodeVerifierRepo
	checker    albrepo.NodeCheckerRepo
	provision  albrepo.NodeProvisionRepo
	controller albctl.Controller
	meta       tocrepo.MetaRepo
	event      see.EventAdapter
	server     opsrepo.ServerRepo
	lock       sgw.LockAdapter
	sess       uic.SessionAdapter
	cluster2   meta.ClusterAdapter
	cluster3   sgw.L7ClusterAdapter
}

// NewTicketApp new ticket application
func NewTicketApp(traceID string) *TicketApp {
	return &TicketApp{
		traceID:    traceID,
		repo:       albrepo.NewTicketRepo(traceID),
		node:       albrepo.NewNodeRepo(traceID),
		cluster:    albrepo.NewClusterRepo(traceID),
		verifier:   albrepo.NewNodeVerifierRepo(traceID),
		checker:    albrepo.NewNodeCheckerRepo(traceID),
		provision:  albrepo.NewNodeProvisionRepo(traceID),
		controller: albctl.ALBController,
		meta:       tocrepo.NewMetaRepo(traceID),
		event:      see.NewEventAdapter(traceID),
		server:     opsrepo.NewServerRepo(traceID),
		lock:       sgw.NewLockAdapter(traceID),
		sess:       uic.NewSessionAdapter(),
		cluster2:   meta.NewClusterAdapter(traceID),
		cluster3:   sgw.NewALBClusterAdapter(traceID),
	}
}

// verifyCluster verify cluster sdu by az
func (a *TicketApp) verifyCluster(cluster *albvo.Cluster) error {
	az, err := a.meta.GetAZ(cluster.AZ)
	if err != nil {
		if !errors.Is(err, tocvo.NewOldAZError(cluster.AZ)) {
			return errors.WithMessage(err, "fetch_az_failed")
		}
	}

	if az != nil && az.IsPrivate() {
		return nil
	}

	if cluster.SDU == "" {
		return fmt.Errorf("cluster_%s_SDU_empty", cluster.Name)
	}

	return nil
}

func (a *TicketApp) verifyClusterNodeForAdd(cluster *albvo.Cluster, req *albvo.ClusterNodeTicketRequest) (bool, error) {
	var err error

	slice.ForEachWithBreak(req.IPs, func(i int, ip string) bool {
		if slice.Contain(cluster.NodeIPs(), ip) {
			err = fmt.Errorf("node_%s_in_cluster", ip)

			return false
		}

		return true
	})
	if err != nil {
		return false, errors.WithMessage(err, "check_node_exist_failed")
	}

	if err = a.verifyCluster(cluster); err != nil {
		return false, errors.WithMessage(err, "valid_cluster_sdu_failed")
	}

	if ok, err := a.server.VerifyExistence(req.IPs); !ok {
		if err != nil {
			return false, errors.WithMessage(err, "verify_server_in_platform_failed")
		}

		return false, errors.New("partial_servers_not_in_platform_pool")
	}

	if err = a.verifier.VerifyServerMeta(req.ToVerifyReq()); err != nil {
		return false, errors.WithMessage(err, "verify_server_failed")
	}

	return true, nil
}

// Add raise a ticket of adding nodes into cluster
func (a *TicketApp) Add(ctx context.Context, req *albvo.ClusterNodeTicketRequest) (*albvo.ClusterNodeTicketResponse, error) {
	cluster, err := a.cluster.GetByUUID(ctx, req.UUID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	if _, err = a.verifyClusterNodeForAdd(cluster, req); err != nil {
		return nil, errors.WithMessage(err, "verify_cluster_node_for_add_failed")
	}

	ver, err := a.provision.Version(req.IPs[0])
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_provision_version_failed")
	}

	req.Version = ver
	req.SDU = cluster.SDU
	req.Cluster = cluster.Name

	ticket, err := a.repo.Add(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "raise_add_node_ticket_failed")
	}

	resp := albvo.ClusterNodeTicketResponse{}
	resp.Success()
	resp.Data.Ticket = ticket

	return &resp, nil
}

func (a *TicketApp) verifyClusterNodeForRetirement(ctx context.Context, req *albvo.ClusterNodeTicketRequest) (bool, error) {
	cluster, err := a.cluster.GetByUUID(ctx, req.UUID)
	if err != nil {
		return false, errors.WithMessage(err, "fetch_cluster_failed")
	}

	slice.ForEachWithBreak(req.IPs, func(i int, ip string) bool {
		if !slice.Contain(cluster.NodeIPs(), ip) {
			err = fmt.Errorf("node_%s_not_in_cluster", ip)

			return false
		}

		if cluster.Node(ip).NodeStatus == consts.NodeStatusReady {
			err = fmt.Errorf("node_%s_is_still_ready", ip)

			return false
		}

		var state *albvo.NodeState
		state, err = a.node.State(ctx, ip)
		if err != nil {
			return false
		}

		var albState *consts.State
		albState, err = consts.NewState(state.State)
		if err != nil {
			err = errors.Wrapf(err, "fetch_node_%s_state_failed", ip)

			return false
		}

		if albState.Code > consts.InitialisedState.Code && albState.Code < consts.PreRetiringState.Code {
			err = fmt.Errorf("node_%s_under_%s_state_permit_to_remove", ip, albState.Status)

			return false
		}

		return true
	})
	if err != nil {
		return false, errors.WithMessage(err, "check_node_exist_failed")
	}

	if err = a.verifyCluster(cluster); err != nil {
		return false, errors.WithMessage(err, "valid_cluster_sdu_failed")
	}

	req.Cluster = cluster.Name

	return true, nil
}

// Retire raise a ticket of removing nodes from cluster
func (a *TicketApp) Retire(ctx context.Context, req *albvo.ClusterNodeTicketRequest) (*albvo.TicketResponse, error) {
	if _, err := a.verifyClusterNodeForRetirement(ctx, req); err != nil {
		return nil, errors.WithMessage(err, "verify_cluster_node_for_remove_failed")
	}

	ret, err := a.repo.Retire(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "raise_remove_node_ticket_failed")
	}

	resp := albvo.TicketResponse{}
	resp.Success()
	resp.Data = ret

	return &resp, nil
}

func (a *TicketApp) verifyALBCRStatus(ctx context.Context, req *albvo.TicketRequest) error {
	for _, ip := range req.IPs {
		alb, err := a.controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			return errors.WithMessage(err, "fetch_alb_cr_failed")
		}

		switch req.Type {
		case consts.TicketOnline:
			// TODO filter those READY nodes
			if !consts.InitialisedState.Equals(alb.Status) {
				err = fmt.Errorf("only_node_in_state_%s_can_create_online_ticket_current_state_%s",
					consts.InitialisedState.Status, alb.Status.State)

				return err
			}
		case consts.TicketOffline, consts.TicketMA:
			// offline will set nlb listeners weight zero
			if !consts.RunningState.Equals(alb.Status) && !consts.MaintenanceState.Equals(alb.Status) {
				err = fmt.Errorf("current_state_is_%s_forbid_create_offline_ticket",
					alb.Status.State)

				return err
			}
		default:
			if !consts.InitialisedState.Equals(alb.Status) {
				err = fmt.Errorf("current_state_is_%s_forbid", alb.Status.State)

				return err
			}
		}
	}

	return nil
}

// Offline raise a ticket of offline nodes
func (a *TicketApp) Offline(ctx context.Context, req *sgwvo.OfflineNodeRequest) (*albvo.TicketResponse, error) {
	if err := a.prerequire(ctx, req.IP); err != nil {
		return nil, errors.WithMessage(err, "prerequire_failed")
	}

	cluster, err := a.cluster.GetByNode(ctx, req.IP)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	token, err := a.sess.SgwDoctorBotToken()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_doctor_token_fail")
	}

	maReq := &albvo.TicketRequest{
		ApplicantUser: configs.SGW.Bot.Email,
		Token:         token,
		Reason:        req.Reason,
		IPs:           []string{req.IP},
		Type:          consts.TicketMA,
		AutoSubmit:    true,
		UUID:          cluster.UUID,
	}

	resp, err := a.OnlineOrOffline(ctx, maReq)
	if err != nil {
		return nil, errors.WithMessage(err, "offline_failed")
	}

	notifyInfo := &imvo.NodeNotifyInfo{
		Application:      consts.ALB,
		NodeIP:           req.IP,
		ClusterUUID:      cluster.UUID,
		Segment:          cluster.Segment,
		RZ:               cluster.RZ,
		AZ:               cluster.AZ,
		Reason:           req.Reason,
		DowntimeTicketID: req.DowntimeTicketID,
		OfflineTicketID:  resp.Data.Ticket.ID,
	}
	if err := a.SendNotifyMessage(ctx, notifyInfo); err != nil {
		log.Logger().WithError(err).WithField("ip", notifyInfo.NodeIP).Error("send_message_to_group_bot_failed")
	}

	return resp, nil
}

// 1. check sgw lock status
// 2. check now is office time
// 3. check cluster ready nodes num
func (a *TicketApp) prerequire(ctx context.Context, ip string) error {
	locked, err := a.lock.IsEnabled()
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", a.traceID).
			WithField("ip", ip).Error("fetch_sgw_lock_status_failed")

		return errors.WithMessage(err, "fetch_sgw_lock_status_failed")
	}

	if locked {
		log.Logger().WithField("trace_id", a.traceID).
			WithField("ip", ip).Error("sgw_lock_status_disabled")

		return errors.Errorf("sgw_lock_status_disabled")
	}

	workingHours, err := configs.Mgmt.Office.IsWorkingHours()
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", a.traceID).
			WithField("ip", ip).Error("check_working_hours_failed")

		return errors.WithMessage(err, "check_working_hours_failed")
	}
	if !workingHours {
		log.Logger().WithField("trace_id", a.traceID).
			WithField("ip", ip).Error("not_in_working_hours")

		return errors.Errorf("not_in_working_hours")
	}

	cluster, err := a.cluster.GetByNode(ctx, ip)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", a.traceID).
			WithField("ip", ip).Error("fetch_cluster_failed")

		return errors.WithMessage(err, "fetch_cluster_failed")
	}

	if (cluster.ReadyNodesNum()-1)*2 < len(cluster.Nodes) {
		return errors.Errorf("cluster_has_not_enough_ready_nodes")
	}

	return nil
}

func (a *TicketApp) SendNotifyMessage(_ context.Context, node *imvo.NodeNotifyInfo) error {
	seatalk := im.NewSeaTalkBotAdapter(a.traceID)
	message, err := node.AutoOfflineNotifyMessage()
	if err != nil {
		return errors.WithMessage(err, "generate_offline_notify_msg_failed")
	}
	err = seatalk.SendMessageToBot(message, configs.Mgmt.Seatalk.Bot.Sre)
	if err != nil {
		return errors.WithMessage(err, "send_message_to_group_bot_failed")
	}

	return nil
}

// OnlineOrOffline raise a ticket of online/offline nodes
func (a *TicketApp) OnlineOrOffline(ctx context.Context, req *albvo.TicketRequest) (*albvo.TicketResponse, error) {
	var resp albvo.TicketResponse

	if err := a.verifyALBCRStatus(ctx, req); err != nil {
		return nil, errors.WithMessage(err, "verify_alb_cr_status_failed")
	}

	cluster, err := a.cluster.GetByUUID(ctx, req.UUID)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", a.traceID).
			WithField("uuid", req.UUID).Error("fetch_cluster_failed")

		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	listeners, err := a.cluster.NLBListenersByUUID(ctx, req.UUID)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", a.traceID).
			WithField("uuid", req.UUID).Error("fetch_nlb_listeners_failed")

		return nil, errors.WithMessage(err, "fetch_nlb_listeners_failed")
	}

	log.Logger().WithFields(log.Fields{
		"trace_id": a.traceID,
		"uuid":     req.UUID,
		"token":    req.Token,
	}).Info("create_ticket")

	switch req.Type {
	case consts.TicketOnline:
		req.SetOnline(cluster, listeners)
		ticket, err := a.repo.Online(ctx, req)
		if err != nil {
			return nil, errors.WithMessage(err, "raise_online_ticket_failed")
		}

		event := &checkEvent{Event: consts.EventOnline, Ticket: ticket, Cluster: cluster, IPs: req.IPs}
		a.preCheckEvent(ctx, event)

		resp.Success()
		resp.Data = ticket
	case consts.TicketOffline, consts.TicketMA:
		req.SetOffline(cluster, listeners)
		ticket, err := a.repo.Offline(ctx, req)
		if err != nil {
			return nil, errors.WithMessage(err, "raise_offline_ticket_failed")
		}

		if req.Type == consts.TicketOffline {
			event := &checkEvent{Event: consts.EventOffline, Ticket: ticket, Cluster: cluster, IPs: req.IPs}
			a.preCheckEvent(ctx, event)
		}

		resp.Success()
		resp.Data = ticket
	}

	return &resp, nil
}

type checkEvent struct {
	Event   consts.Event
	Ticket  *albvo.TicketData
	Cluster *albvo.Cluster
	IPs     []string
}

func (a *TicketApp) preCheckEvent(ctx context.Context, ev *checkEvent) {
	ticket, cluster := ev.Ticket, ev.Cluster

	if ticket == nil || ticket.Ticket == nil {
		return
	}

	var results []*sgwvo.NodeTaskResult
	var err error

	switch ev.Event.Code {
	case consts.EventOffline.Code:
		results, err = a.checker.FetchPreCheckOfflineResult(ctx, ev.IPs)
	default:
		results, err = a.checker.FetchPreCheckResult(ctx, ev.IPs)
	}

	if err != nil {
		log.Logger().WithError(err).Error("fetch_pre_check_result_error")

		return
	}

	slice.ForEach(results, func(_ int, result *sgwvo.NodeTaskResult) {
		var title string
		switch ev.Event.Code {
		case consts.EventOffline.Code:
			title = fmt.Sprintf("%s:Offline ALB Node %s", ticket.No(), result.IP)
		default:
			title = fmt.Sprintf("%s:Online ALB Node %s", ticket.No(), result.IP)
		}

		precheck := a.event.ALBNodePreCheck()
		precheck.SetSourceGroup(ticket.No())
		precheck.SetOperator(ticket.Ticket.ApplicantUser)
		precheck.SetPriority(consts.HighPriority.Code)
		precheck.SetTitle(title)
		precheck.SetStartTime(result.FirstTime())
		precheck.SetEndTime(result.LastTime())
		precheck.AddExtra("eventContent", result.Tasks)
		precheck.AddExtra("ip", result.IP)

		precheck.AddTag("AZ", cluster.AZ)
		precheck.AddTag("Segment", cluster.Segment)
		precheck.AddTag("RZ", cluster.RZ)
		precheck.AddTag("Env", cluster.Env)

		precheck.Send()
	})
}

func (a *TicketApp) setHotUpdateConfig(ctx context.Context, req *albvo.HotUpdateTicketRequest) error {
	provReq := req.ToNodeProvisionRequest()
	provReq.Version = req.TargetVersion
	targetProvision, err := a.provision.GenerateConfig(ctx, provReq)
	if err != nil {
		return errors.WithMessage(err, "fetch_target_provision_config_failed")
	}

	if req.Component != cmp.NginxComponent.Name {
		targetProvision.Component.Version = req.TargetVersion
	}

	rollbackProvision := &tocvo.NodeProvision{}
	if req.Component == cmp.NginxComponent.Name {
		provReq.Version = req.RollbackVersion
		rollbackProvision, err = a.provision.GenerateConfig(ctx, provReq)
		if err != nil {
			return errors.WithMessage(err, "fetch_rollback_provision_config_failed")
		}
	} else {
		if err = json.DeepCopy(rollbackProvision, targetProvision); err != nil {
			return errors.WithMessage(err, "deep_copy_upgraded_provision_failed")
		}
		rollbackProvision.Component.Version = req.RollbackVersion
	}

	upgradedNodeComp := tocvo.NodeComponent(*targetProvision.Component)
	req.FormData.TargetConfigs = &tocvo.TocProvision{
		Templates:  targetProvision.Templates.ToTocTemplates(),
		Components: upgradedNodeComp.ToTocComponents(), // not all the attributes are set
	}
	rollbackNodeComp := tocvo.NodeComponent(*rollbackProvision.Component)
	req.FormData.RollbackConfigs = &tocvo.TocProvision{
		Templates:  rollbackProvision.Templates.ToTocTemplates(),
		Components: rollbackNodeComp.ToTocComponents(),
	}

	return nil
}

func (a *TicketApp) verifyHotUpdateALBCRStatus(ctx context.Context, req *albvo.HotUpdateTicketRequest) error {
	for _, ip := range req.FormData.IPs {
		albCR, err := a.controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			return errors.WithMessage(err, "fetch_alb_cr_failed")
		}

		// must be initialed or running can hot update
		switch albCR.Status.State {
		case consts.InitialisedState.Status, consts.RunningState.Status:
			return nil
		default:
			return fmt.Errorf("node_%s_in_%s_state_forbid_hot-update", ip, albCR.Status.State)
		}
	}

	return nil
}

// HotUpdate raise a ticket of hot update
func (a *TicketApp) HotUpdate(ctx context.Context, req *albvo.HotUpdateTicketRequest) (*albvo.TicketResponse, error) {
	if err := a.verifyHotUpdateALBCRStatus(ctx, req); err != nil {
		return nil, errors.WithMessage(err, "verify_alb_cr_status_failed")
	}

	if err := a.setHotUpdateConfig(ctx, req); err != nil {
		return nil, errors.WithMessage(err, "set_hot_update_config_failed")
	}

	req2, err := req.ToTicketRequest()
	if err != nil {
		return nil, errors.WithMessage(err, "to_ticket_req_failed")
	}

	ticket, err := a.repo.HotUpdate(ctx, req2)
	if err != nil {
		return nil, errors.WithMessage(err, "hot_update_failed")
	}

	resp := albvo.TicketResponse{}
	resp.Success()
	resp.Data = &albvo.TicketData{Ticket: ticket}

	return &resp, nil
}

func (a *TicketApp) Block(ctx context.Context, req *albvo.ClusterTrafficRequest) (*albvo.TicketResponse, error) {
	form, err := a.fetchTicketFormData(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "set_ticket_config_failed")
	}

	ticket, err := a.repo.Block(ctx, form.ToBlockTicketRequest())
	if err != nil {
		return nil, errors.WithMessage(err, "block_traffic_failed")
	}

	resp := albvo.TicketResponse{}
	resp.Success()
	resp.Data = &albvo.TicketData{Ticket: ticket}

	return &resp, nil
}

func (a *TicketApp) BlockByDomains(ctx context.Context, req *albvo.ClusterTrafficRequest) (*albvo.TicketResponse, error) {
	form, err := a.fetchTicketFormData(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "set_ticket_config_failed")
	}

	ticket, err := a.repo.BlockByDomains(ctx, form.ToBlockTicketByDomainsRequest())
	if err != nil {
		return nil, errors.WithMessage(err, "block_traffic_by_domains_failed")
	}

	resp := albvo.TicketResponse{}
	resp.Success()
	resp.Data = &albvo.TicketData{Ticket: ticket}

	return &resp, nil
}

func (a *TicketApp) Open(ctx context.Context, req *albvo.ClusterTrafficRequest) (*albvo.TicketResponse, error) {
	form, err := a.fetchTicketFormData(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "set_ticket_config_failed")
	}

	ticket, err := a.repo.Open(ctx, form.ToOpenTicketRequest())
	if err != nil {
		return nil, errors.WithMessage(err, "open_traffic_failed")
	}

	resp := albvo.TicketResponse{}
	resp.Success()
	resp.Data = &albvo.TicketData{Ticket: ticket}

	return &resp, nil
}

func (a *TicketApp) OpenByDomains(ctx context.Context, req *albvo.ClusterTrafficRequest) (*albvo.TicketResponse, error) {
	form, err := a.fetchTicketFormData(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "set_ticket_config_failed")
	}

	ticket, err := a.repo.OpenByDomains(ctx, form.ToOpenTicketByDomainsRequest())
	if err != nil {
		return nil, errors.WithMessage(err, "open_traffic_by_domains_failed")
	}

	resp := albvo.TicketResponse{}
	resp.Success()
	resp.Data = &albvo.TicketData{Ticket: ticket}

	return &resp, nil
}

func (a *TicketApp) fetchTicketFormData(ctx context.Context,
	req *albvo.ClusterTrafficRequest,
) (*albvo.ControlTrafficFormRequest, error) {
	var form albvo.ControlTrafficFormRequest

	clusters, err := a.cluster.GetProductBlockListByRZAndNetwork(ctx, req.RZ, req.NetworkType, req.Product)
	if err != nil {
		return nil, errors.WithMessage(err, "get_cluster_by_rz_failed")
	}

	var ips []string
	slice.ForEach(clusters, func(_ int, cluster *albvo.Cluster) {
		ips = append(ips, cluster.NodeIPs()...)
	})

	form.Clusters = slice.Map(clusters, func(_ int, cluster *albvo.Cluster) string {
		return cluster.Name
	})
	form.IPs = ips
	form.RZ = req.RZ
	form.Token = req.Token
	form.NetworkType = req.NetworkType
	form.Domains = req.Domains
	form.Product = req.Product

	return &form, nil
}
