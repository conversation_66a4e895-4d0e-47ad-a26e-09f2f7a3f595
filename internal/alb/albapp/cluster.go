package albapp

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/sourcegraph/conc/iter"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/swp/swpdom/swprepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/swp/swpvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocdom/tocrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

// ClusterApp ALB cluster application
type ClusterApp struct {
	traceID string

	repo     albrepo.ClusterRepo
	node     albrepo.NodeRepo
	checker  albrepo.NodeCheckerRepo
	ticket   swprepo.TicketRepo
	meta     tocrepo.MetaRepo
	serverV3 toc.ServerV3Adapter
	cluster  sgw.L7ClusterAdapter
}

// NewClusterApp return an ALB cluster app
func NewClusterApp(traceID string) *ClusterApp {
	return &ClusterApp{
		traceID: traceID,

		repo:     albrepo.NewClusterRepo(traceID),
		node:     albrepo.NewNodeRepo(traceID),
		checker:  albrepo.NewNodeCheckerRepo(traceID),
		ticket:   swprepo.NewTicketRepo(traceID),
		meta:     tocrepo.NewMetaRepo(traceID),
		serverV3: toc.NewServerV3Adapter(traceID),
		cluster:  sgw.NewALBClusterAdapter(traceID),
	}
}

func (a *ClusterApp) nonOnboardNodeIPs(cluster *albvo.Cluster) []string {
	var nonOnboardALBIPs []string              // check existing in runtime CustomResource, content is IP address
	onBoardALBMap := make(map[string]struct{}) // from alb-cluster mgmt

	for _, node := range cluster.Nodes {
		onBoardALBMap[node.ALBCRName()] = struct{}{}
	}

	albstate.NodeMap.Range(func(key, value interface{}) bool {
		albCR, ok := value.(*v1alpha1.ALB)
		if !ok {
			return true
		}

		if albCR.Spec.ALBNode.ALBName != cluster.Name {
			return true
		}

		// it's runtime CR key
		if _, ok = onBoardALBMap[key.(string)]; !ok {
			nonOnboardALBIPs = append(nonOnboardALBIPs, albCR.Spec.LanIP)
		}

		return true
	})

	log.Logger().WithFields(log.Fields{
		"onboard":    len(onBoardALBMap),
		"nonOnboard": len(nonOnboardALBIPs),
	}).Info("fetch_non-onboard_alb_ip")

	return nonOnboardALBIPs
}

// Node cluster node list
func (a *ClusterApp) Node(ctx context.Context, req *albvo.ClusterIDRequest) (*albvo.NodeInfosResponse, error) {
	cluster, err := a.repo.GetByUUID(ctx, req.UUID)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"uuid": req.UUID,
		}).Error("fetch_cluster_failed")

		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	nonOnboardNodeIPs := a.nonOnboardNodeIPs(cluster)

	var nodes []*albvo.NodeInfo
	nodes = append(nodes, a.node.Infos(ctx, cluster.Nodes)...)
	nodes = append(nodes, a.node.InfosInInitialising(ctx, nonOnboardNodeIPs)...)

	resp := albvo.NodeInfosResponse{}
	resp.Success()

	if len(nodes) == 0 {
		return &resp, nil
	}

	servers, err := a.serverV3.Servers(slice.Map(nodes, func(_ int, n *albvo.NodeInfo) string {
		return n.IP
	}))
	if err != nil {
		return nil, errors.WithMessage(err, "list_servers_failed")
	}

	iter.ForEach(nodes, func(n **albvo.NodeInfo) {
		node := *n
		if server, ok := servers[node.IP]; ok {
			node.Server = server
		}

		if node.Ticket == nil {
			return
		}

		rsp, err := a.ticket.Get(ctx, &swpvo.TicketReq{ID: node.Ticket.ID})
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"id":      node.Ticket.ID,
				"ip":      node.IP,
				"cluster": node.ALBName,
			}).Error("fetch swp ticket failed")

			return
		}

		node.Ticket = rsp.Ticket
	})

	resp.Data.Nodes = nodes

	return &resp, nil
}

// NodeIPs cluster node ip list from mgmt
func (a *ClusterApp) NodeIPs(uuid string) ([]string, error) {
	nodes, err := a.repo.Nodes(context.Background(), uuid)
	if err != nil {
		return nil, errors.WithMessage(err, "list_node_ip_failed")
	}

	var ips []string
	for _, n := range nodes {
		ips = append(ips, n.IP)
	}

	return ips, nil
}

// NodeNLBListeners cluster node NLB listeners
func (a *ClusterApp) NodeNLBListeners(ctx context.Context, uuid, nodeIP string) (
	*albvo.ClusterNodeNLBListenerResponse, error,
) {
	listeners, err := a.repo.NodeNLBListeners(ctx, uuid, nodeIP)
	if err != nil {
		return nil, errors.WithMessage(err, "list_nlb_listener_failed")
	}

	resp := albvo.ClusterNodeNLBListenerResponse{}
	resp.Data.Listeners = listeners
	resp.Success()

	return &resp, nil
}

// List cluster list
func (a *ClusterApp) List(ctx context.Context) (*albvo.ClusterListResponse, error) {
	rets, err := a.repo.List(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "list_cluster_failed")
	}

	for _, c := range rets {
		c.Managed = !c.IsECP
	}

	resp := albvo.ClusterListResponse{}
	resp.Success()
	resp.Data.Clusters = rets
	resp.Size = len(rets)

	return &resp, nil
}

// verify valid cluster sdu by az
func (a *ClusterApp) verify(cluster *albvo.Cluster) error {
	if cluster.IsECP {
		return errors.Errorf("cluster_%s_is_ecp", cluster.Name)
	}

	az, err := a.meta.GetAZ(cluster.AZ)
	if err != nil {
		if !errors.Is(err, tocvo.OldAZError{AZ: cluster.AZ}) {
			return errors.WithMessage(err, "fetch_az_failed")
		}
	}

	if az != nil && az.IsPrivate() {
		return nil
	}

	if cluster.IsLegacy && cluster.Labels.HasSeaMoney() {
		return nil
	}

	if cluster.SDU == "" {
		return fmt.Errorf("cluster_%s_SDU_empty", cluster.Name)
	}

	return nil
}

// Sync cluster node component sync
func (a *ClusterApp) Sync(ctx context.Context) (*core.BaseResponse, error) {
	clusters, err := a.cluster.Dump()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_clusters_failed")
	}

	slice.ForEach(clusters, func(index int, cluster *albvo.Cluster) {
		if err = a.verify(cluster); err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"cluster": cluster.Name,
			}).Error("verify_cluster_failed")

			return
		}

		var readyNodes, notReadyNodes atomic.Int64
		iter.ForEach(cluster.Nodes, func(nod **sgwvo.Node) {
			node := *nod
			// fetch components' version
			tocex := toc.NewTocexAdapter(cluster.ToTOCNode(node.IP))
			component := albrepo.NewNodeComponentRepo(a.traceID, tocex)
			comps, err := component.Dump(ctx)
			if err != nil {
				log.Logger().WithError(err).WithFields(log.Fields{
					"cluster": cluster.Name,
					"ip":      node.IP,
					"az":      cluster.AZ,
					"env":     cluster.Env,
				}).Error("fetch_component_version_failed")

				return
			}

			addNodeReq := &albvo.ClusterNodeTicketRequest{}
			addNodeReq.UUID = cluster.UUID
			addNodeReq.SDU = cluster.SDU
			addNodeReq.Cluster = cluster.Name
			addNodeReq.ApplicantUser = configs.Mgmt.Ops.Bot.Email
			addNodeReq.Version = comps

			addNodeReq.IPs = []string{node.IP}

			if node.Ready() {
				addNodeReq.State = consts.RunningState.Status
				readyNodes.Add(1)
			} else {
				addNodeReq.State = consts.InitialisedState.Status
				notReadyNodes.Add(1)
			}

			nodeSvc := albrepo.NewNodeRepo(a.traceID)
			_, err = nodeSvc.Add(ctx, addNodeReq)
			if err != nil {
				log.Logger().WithError(err).WithFields(log.Fields{
					"cluster": cluster.Name,
					"ip":      node.IP,
					"az":      cluster.AZ,
					"env":     cluster.Env,
				}).Error("sync_node_failed")
			}
		})

		log.Logger().WithFields(log.Fields{
			"cluster":       cluster.Name,
			"readyNodes":    readyNodes.Load(),
			"notReadyNodes": notReadyNodes.Load(),
		}).Info("sync_cluster_component")
	})

	log.Logger().WithFields(log.Fields{
		"clusters": len(clusters),
	}).Info("sync_cluster_end")

	resp := core.BaseResponse{}
	resp.Success()

	return &resp, nil
}

// SyncByUUID cluster node component sync
func (a *ClusterApp) SyncByUUID(ctx context.Context, req *albvo.ClusterSyncRequest) (*albvo.NodeResponse, error) {
	cluster, err := a.repo.GetByUUID(ctx, req.UUID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	if err = a.verify(cluster); err != nil {
		return nil, errors.WithMessage(err, "valid_sdu_failed")
	}

	nodes := cluster.Nodes
	if len(req.IPs) > 0 {
		ips := convertor.ToMap(req.IPs, func(ip string) (string, struct{}) {
			return ip, struct{}{}
		})

		nodes = slice.Filter(nodes, func(index int, node *sgwvo.Node) bool {
			_, ok := ips[node.IP]

			return ok
		})
	}

	servers, err := a.serverV3.Servers(slice.Map(nodes, func(_ int, node *sgwvo.Node) string {
		return node.IP
	}))
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_servers_failed")
	}

	var readyNodes, notReadyNodes atomic.Int64
	var failedNodes sync.Map
	iter.ForEach(nodes, func(nod **sgwvo.Node) {
		node := *nod

		node2 := &tocvo.Node{
			HostIP: node.IP, AZ: cluster.AZ, Env: cluster.Env, Segment: cluster.Segment,
		}
		if server, ok := servers[node.IP]; ok {
			node2.Cluster = server.Cluster
		}
		// fetch components' version
		tocex := toc.NewTocexAdapter(cluster.ToTOCNode(node.IP))
		component := albrepo.NewNodeComponentRepo(a.traceID, tocex)
		comps, err := component.Dump(ctx)
		if err != nil {
			failedNodes.Store(node.IP, err.Error())

			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":  node.IP,
				"az":  cluster.AZ,
				"env": cluster.Env,
			}).Error("fetch_component_version_failed")

			return
		}

		addNodeReq := &albvo.ClusterNodeTicketRequest{}
		addNodeReq.UUID = req.UUID
		addNodeReq.SDU = cluster.SDU
		addNodeReq.Cluster = cluster.Name
		addNodeReq.Token = req.Token
		addNodeReq.ApplicantUser = configs.Mgmt.Ops.Bot.Email
		addNodeReq.Version = comps

		addNodeReq.IPs = []string{node.IP}

		if node.Ready() {
			addNodeReq.State = consts.RunningState.Status
			readyNodes.Add(1)
		} else {
			addNodeReq.State = consts.InitialisedState.Status
			notReadyNodes.Add(1)
		}

		_, err = a.node.Add(ctx, addNodeReq)
		if err != nil {
			failedNodes.Store(node.IP, err.Error())
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":  node.IP,
				"az":  cluster.AZ,
				"env": cluster.Env,
			}).Error("sync_node_failed")
		}
	})

	var errItems []*core.ItemError
	failedNodes.Range(func(ip, msg interface{}) bool {
		nodeIP, _ := ip.(string)
		message, _ := msg.(string)

		errItems = append(errItems, &core.ItemError{
			IP:      nodeIP,
			Message: message,
		})

		return true
	})
	nodeFailedSize := len(errItems)

	resp := albvo.NodeResponse{}
	resp.Success()

	if nodeFailedSize > 0 {
		resp.Data.Errors = errItems
		resp.Code = consts.NodeSyncFailureCode
		resp.Message = consts.NodeSyncFailureMsg
	}

	log.Logger().WithFields(log.Fields{
		"uuid":          cluster.UUID,
		"cluster":       cluster.Name,
		"readyNodes":    readyNodes.Load(),
		"notReadyNodes": notReadyNodes.Load(),
		"failedNodes":   nodeFailedSize,
		"ips":           req.IPs,
	}).Info("sync_cluster_node_component")

	return &resp, nil
}

// Managed cluster is managed
func (a *ClusterApp) Managed(ctx context.Context, uuid string) (bool, error) {
	cluster, err := a.repo.GetByUUID(ctx, uuid)
	if err != nil {
		return false, errors.WithMessage(err, "fetch_cluster_failed")
	}

	return !cluster.IsECP, nil
}

// PreCheckNodes precheck nodes
func (a *ClusterApp) PreCheckNodes(ctx context.Context, req *albvo.ClusterNodeIPsRequest) (
	*albvo.ClusterNodePreCheckResponse, error,
) {
	results, err := a.checker.PreCheck(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "pre_check_nodes_failed")
	}

	resp := albvo.ClusterNodePreCheckResponse{}
	resp.Success()
	resp.Data.Results = results

	return &resp, nil
}

// PreCheckOfflineNodes precheck nodes before offline
func (a *ClusterApp) PreCheckOfflineNodes(_ context.Context, req *albvo.ClusterNodeIPsRequest) (
	*albvo.ClusterNodePreCheckResponse, error,
) {
	results, err := a.checker.PreCheckOffline(req)
	if err != nil {
		return nil, errors.WithMessage(err, "pre_check_nodes_failed")
	}

	resp := albvo.ClusterNodePreCheckResponse{}
	resp.Success()
	resp.Data.Results = results

	return &resp, nil
}

// AutoMAPreCheck check cluster ready node >= 50%, and no ready nodes weight is 0
func (a *ClusterApp) AutoMAPreCheck(ctx context.Context, ips []string, uuid string) error {
	nodes, err := a.repo.Nodes(ctx, uuid)
	if err != nil {
		return errors.WithMessage(err, "fetch_nodes_failed")
	}

	readyNodes := slice.Map(nodes, func(_ int, node *sgwvo.Node) *sgwvo.Node {
		if slice.Contain(ips, node.IP) {
			return nil
		}

		if node.NodeStatus == consts.NodeStatusReady {
			return node
		}

		return nil
	})
	readyNodes = slice.Filter(readyNodes, func(_ int, node *sgwvo.Node) bool {
		return node != nil
	})

	if len(readyNodes)*2 < len(nodes) {
		return errors.New("cluster_ready_node_less_than_50_percent")
	}

	nlbListenerTargetsMap, err := a.repo.NLBListenerTargetsMapByUUID(ctx, uuid)
	if err != nil {
		return errors.WithMessage(err, "fetch_nlb_listener_targets_failed")
	}

	badListenerTargets := make([]string, 0)
	for _, node := range readyNodes {
		listenerTargets, ok := nlbListenerTargetsMap[node.IP]
		if !ok {
			return errors.New("cluster_nlb_listener_target_not_found_" + node.IP)
		}

		for _, listenerTarget := range listenerTargets {
			if listenerTarget.Weight == 0 {
				badListenerTargets = append(badListenerTargets, fmt.Sprintf("%s:%s", node.IP, listenerTarget.Key()))
			}
		}
	}

	if len(badListenerTargets) > 0 {
		// unique badListenerTargets
		badListenerTargets = slice.Unique(badListenerTargets)

		return fmt.Errorf("cluster_nlb_listener_target_weight_is_0: %v", badListenerTargets)
	}

	return nil
}

func (a *ClusterApp) Config(ctx context.Context, req *albvo.ClusterIDRequest) (*albvo.ClusterConfigResponse, error) {
	meta, err := a.repo.Config(ctx, req.UUID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	resp := albvo.ClusterConfigResponse{}
	resp.Success()
	resp.Data.Config = &meta.L7ClusterConfig

	return &resp, nil
}

func (a *ClusterApp) FreshConfig(ctx context.Context, req *albvo.FreshClusterConfigRequest) (*core.BaseResponse, error) {
	if err := a.repo.FreshConfig(ctx, req); err != nil {
		return nil, errors.WithMessage(err, "fresh_cluster_config_failed")
	}

	resp := core.BaseResponse{}
	resp.Success()

	return &resp, nil
}

func (a *ClusterApp) SyncConfigFromTagVars(
	ctx context.Context,
	req *albvo.ClusterIDRequest,
) (*albvo.ClusterMetaSyncResponse, error) {
	err := a.repo.SyncConfigFromTagVars(ctx, req.UUID)

	resp := &albvo.ClusterMetaSyncResponse{}
	resp.Success()

	if err != nil {
		if errors.Is(err, consts.ErrClusterMetaSynced) {
			resp.Data.Status = "already_synced"
			resp.Data.Message = "already synced, no need to sync again"

			return resp, nil
		}
		if errors.Is(err, consts.ErrClusterMetaPartiallySynced) {
			resp.Data.Status = "partial_sync_skipped"
			resp.Data.Message = "only part of the cluster is synced, please check the logs for details"

			return resp, nil
		}

		return nil, errors.WithMessage(err, "sync_cluster_config_failed")
	}

	resp.Data.Status = "synced"
	resp.Data.Message = "sync successfully"

	return resp, nil
}
