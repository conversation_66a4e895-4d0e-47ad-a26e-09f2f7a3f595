package albapp

import (
	"context"
	serrors "errors"
	"fmt"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	uuid "github.com/iris-contrib/go.uuid"
	"github.com/pkg/errors"
	"github.com/sourcegraph/conc/iter"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	"git.garena.com/shopee/devops/toc-sdk/job"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/etcd/etcdvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/swp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/nlb/nlbvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsdom/opsrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocdom/tocrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

// NodeApp node application
type NodeApp struct {
	repo      albrepo.NodeRepo
	cluster   albrepo.ClusterRepo
	comp      albrepo.ComponentRepo
	verifier  albrepo.NodeVerifierRepo
	provision albrepo.NodeProvisionRepo

	controller albctl.Controller

	server2 opsrepo.ServerRepo
	event   opsrepo.EventRepo

	meta tocrepo.MetaRepo

	serverV3 toc.ServerV3Adapter
	ticket   swp.TicketAdapter

	traceID string
}

// NewNodeApp new node application
func NewNodeApp(traceID string) *NodeApp {
	return &NodeApp{
		repo:       albrepo.NewNodeRepo(traceID),
		cluster:    albrepo.NewClusterRepo(traceID),
		comp:       albrepo.NewComponentRepo(traceID),
		verifier:   albrepo.NewNodeVerifierRepo(traceID),
		provision:  albrepo.NewNodeProvisionRepo(traceID),
		controller: albctl.ALBController,
		server2:    opsrepo.NewServerRepo(traceID),
		event:      opsrepo.NewEventRepo(traceID),
		meta:       tocrepo.NewMetaRepo(traceID),
		serverV3:   toc.NewServerV3Adapter(traceID),
		ticket:     swp.NewTicketAdapter(traceID),

		traceID: traceID,
	}
}

func (a *NodeApp) List(ctx context.Context, req *albvo.NodeRequest) (*albvo.NodeInfosResponse, error) {
	nodes, err := a.cluster.NodesByIPs(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_nodes_failed")
	}

	var nonOnBoardNodeIPs []string
	if len(nodes) == 0 {
		nonOnBoardNodeIPs = req.IPs
	} else {
		nodeMap := convertor.ToMap(nodes, func(node *sgwvo.Node) (string, *sgwvo.Node) {
			return node.IP, node
		})
		nonOnBoardNodeIPs = slice.Filter(req.IPs, func(_ int, ip string) bool {
			_, ok := nodeMap[ip]

			return !ok
		})
	}

	var infos []*albvo.NodeInfo
	infos = append(infos, a.repo.Infos(ctx, nodes)...)
	infos = append(infos, a.repo.InfosInInitialising(ctx, nonOnBoardNodeIPs)...)

	resp := albvo.NodeInfosResponse{}
	resp.Success()

	resp.Data.Nodes = infos

	return &resp, nil
}

// verifyClusterForAdd verify cluster
func (a *NodeApp) verifyClusterForAdd(ctx context.Context, cluster *albvo.Cluster, ips []string) error {
	az, err := a.meta.GetAZ(cluster.AZ)
	if err != nil {
		if !errors.Is(err, tocvo.NewOldAZError(cluster.AZ)) {
			return errors.WithMessage(err, "fetch_az_failed")
		}
	}

	if az != nil && az.IsPrivate() {
		return nil
	}

	points, err := cluster.ToEtcdEndpoints()
	if err != nil {
		return errors.WithMessage(err, "to_etcd_points_failed")
	}

	if len(points) == 0 {
		return nil
	}

	server, err := a.serverV3.Server(ips[0])
	if err != nil {
		return errors.WithMessage(err, "fetch_server_failed")
	}

	client := toc.NewJobClientAdapter(server.Cluster, a.traceID)
	resps, err := client.DispatchTasks(ctx, etcdvo.EtcdMajorityHealthScript(points), ips)
	if err != nil {
		return errors.WithMessage(err, "check_etcd_failed")
	}

	for _, resp := range resps {
		if resp.ResultStatus != job.JobResultStatusSucceed {
			return fmt.Errorf("node:%s failed:%s", resp.ServerLanIP, resp.Output)
		}
	}

	return nil
}

// verifyVersion verifies the version of a component for a given environment.
//
// It takes in a NodeComponentVersion pointer and an environment string as parameters.
// It returns an error.
func (a *NodeApp) verifyVersion(version *albvo.NodeComponentVersion, env string) error {
	if version == nil {
		return fmt.Errorf("component_version_not_found")
	}

	for _, comp := range cmp.ALBCoreComponents {
		ver := version.Version(comp.Name)
		if ver == consts.UnknownVersion {
			return fmt.Errorf("component_version_%s_not_found", comp.Name)
		}

		if ok, err := a.comp.IsVersionExist(&albvo.ComponentVersionRequest{
			Name:    comp.Name,
			Version: ver,
			Env:     env,
		}); err != nil {
			return errors.WithMessagef(err, "verify_%s_version_failed", comp.Name)
		} else if !ok {
			return errors.Errorf("%s_version_%s_not_exist", comp.Name, ver)
		}
	}

	return nil
}

// Add nodes add into cluster
func (a *NodeApp) Add(ctx context.Context, req *albvo.ClusterNodeTicketRequest) (*albvo.NodeResponse, error) {
	cluster, err := a.cluster.GetByUUID(ctx, req.UUID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	if err = a.verifyClusterForAdd(ctx, cluster, req.IPs); err != nil {
		return nil, errors.WithMessage(err, "verify_cluster_failed")
	}

	req.SDU = cluster.SDU
	req.Cluster = cluster.Name

	if !configs.ALB.Verify.Disable {
		if err = a.verifier.VerifyServerMeta(req.ToVerifyReq()); err != nil {
			return nil, errors.WithMessage(err, "verify_tag_variable_failed")
		}

		if err = a.verifyVersion(req.Version, cluster.Env); err != nil {
			return nil, errors.WithMessage(err, "verify_version_failed")
		}
	}

	errs, err := a.repo.Add(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "add_node_into_cluster_failed")
	}

	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeAddFailureCode
		resp.Message = consts.NodeAddFailureMsg
	} else {
		resp.Success()
	}

	return &resp, nil
}

// Retire nodes remove into cluster
func (a *NodeApp) Retire(ctx context.Context, req *albvo.ClusterNodeTicketRequest) (*albvo.NodeResponse, error) {
	errs, err := a.repo.Retire(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "remove_node_from_cluster_failed")
	}

	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeRetireFailureCode
		resp.Message = consts.NodeRetireFailureMsg
	} else {
		resp.Success()
	}

	return &resp, nil
}

// State get node's state
func (a *NodeApp) State(ctx context.Context, req *albvo.NodeIPRequest) (*albvo.GetNodeResponse, error) {
	state, err := a.repo.State(ctx, req.IP)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_alb_state_failed")
	}

	resp := albvo.GetNodeResponse{}
	resp.Success()
	resp.Data = state

	return &resp, nil
}

// UpdateState update node's state
func (a *NodeApp) UpdateState(ctx context.Context, req *albvo.UpdateNodeStateRequest) (*core.BaseResponse, error) {
	state, err := consts.NewState(req.State)
	if err != nil {
		return nil, errors.WithMessage(err, "new_state_failed")
	}

	err = a.repo.UpdateState(ctx, req.IP, state)
	if err != nil {
		return nil, errors.WithMessage(err, "update_alb_state_failed")
	}
	resp := core.BaseResponse{}
	resp.Success()

	return &resp, nil
}

// ReentrantState reentrant node's state
func (a *NodeApp) ReentrantState(_ context.Context, req *albvo.ReentrantNodeStateRequest) (*core.BaseResponse, error) {
	a.controller.FreshStateRunner(albvo.CRName(req.IP), req.State)

	resp := core.BaseResponse{}
	resp.Success()

	return &resp, nil
}

// Spec fetch node's runtime spec
func (a *NodeApp) Spec(ctx context.Context, req *albvo.NodeRequest) (*albvo.NodeSpecResponse, error) {
	albs, err := a.repo.ListRuntime(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "list_alb_runtime_failed")
	}

	var specs []*v1alpha1.ALBSpec
	for _, a := range albs {
		specs = append(specs, &a.Spec)
	}

	resp := albvo.NodeSpecResponse{}
	resp.Success()
	resp.Data = specs

	return &resp, nil
}

// UpdateSpec update node's runtime spec
func (a *NodeApp) UpdateSpec(ctx context.Context, req *albvo.NodeRequest) (*albvo.NodeResponse, error) {
	errs, err := a.repo.UpdateSpec(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "update_alb_spec_failed")
	}

	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeUpdateSpecFailureCode
		resp.Message = consts.NodeUpdateSpecFailureMsg
	} else {
		resp.Success()
	}

	return &resp, nil
}

func (a *NodeApp) verifyStateForHotUpdate(ctx context.Context, ips []string, ops string) error {
	var errs []error
	slice.ForEach(ips, func(_ int, ip string) {
		alb, err := a.controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			errs = append(errs, errors.WithMessagef(err, "node:%s fetch_alb_failed", ip))

			return
		}

		state, _ := consts.NewState(alb.Status.State)
		switch ops {
		case consts.UpdateTocexItem:
			if !state.Equal(consts.InitialisedState) && !state.Equal(consts.RunningState) {
				errs = append(errs, fmt.Errorf("node:%s is_not_in_initialised_or_running_state", ip))
			}
		case consts.RollbackTocexItem:
			if state.Less(consts.InitialisedState) {
				errs = append(errs, fmt.Errorf("node:%s is_not_in_initialised_state", ip))
			}
			if state.Greater(consts.RunningRollbackState) {
				errs = append(errs, fmt.Errorf("node:%s is_not_in_running_state", ip))
			}
			if state.Equal(consts.PreRunningState) {
				errs = append(errs, fmt.Errorf("node:%s is_in_pre_running_state", ip))
			}
		}
	})

	return serrors.Join(errs...)
}

// UpgradeComponent upgrade component
func (a *NodeApp) UpgradeComponent(ctx context.Context, req *albvo.NodeHotUpdateProvisionRequest) (
	*albvo.NodeResponse, error,
) {
	req.OperationType = consts.UpdateTocexItem
	errs, err := a.hotUpdateComponent(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "upgrade_component_failed")
	}

	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeHotUpdateFailureCode
		resp.Message = consts.NodeHotUpdateFailureMsg

		return &resp, nil
	}

	resp.Success()

	return &resp, nil
}

func (a *NodeApp) hotUpdateComponent(ctx context.Context, req *albvo.NodeHotUpdateProvisionRequest) (
	[]*core.ItemError, error,
) {
	ticket, err := a.ticket.Get(req.TicketID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_ticket_failed")
	}

	if ticket.WorkflowPhaseType != consts.TicketEndSuccess {
		return nil, fmt.Errorf("ticket_%d_is_not_approved", req.TicketID)
	}

	ok, err := a.comp.IsVersionExist(req.ToVersionRequest())
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_versions_by_name_failed")
	}

	if !ok {
		return nil, fmt.Errorf("version_%s_unsupported", req.Version)
	}

	if err = a.verifyStateForHotUpdate(ctx, req.IPs, req.OperationType); err != nil {
		return nil, errors.WithMessage(err, "verify_state_for_hotupdate_failed")
	}

	// every node maybe general or private
	version := uuid.Must(uuid.NewV4()).String()
	errs := iter.Map(req.IPs, func(ipAddr *string) *core.ItemError {
		ip := *ipAddr
		provision, err := a.provision.GenerateConfig(ctx, &albvo.NodeProvisionComponentRequest{
			IP:        ip,
			Component: req.Component,
			Version:   req.Version,
		})
		if err != nil {
			return &core.ItemError{
				IP:      ip,
				Message: err.Error(),
			}
		}

		req2 := &albvo.HotUpdateOneNodeRequest{
			IP:              ip,
			Version:         version,
			HotUpdateConfig: provision,
			OperationType:   req.OperationType,
			ApplicantUser:   req.ApplicantUser,
		}

		if err = a.repo.HotUpdateOne(ctx, req2); err != nil {
			return &core.ItemError{
				IP:      ip,
				Message: err.Error(),
			}
		}

		return nil
	})

	errs = slice.Filter(errs, func(_ int, err *core.ItemError) bool {
		return err != nil
	})

	return errs, nil
}

// RollbackComponent rollback component
func (a *NodeApp) RollbackComponent(ctx context.Context, req *albvo.NodeHotUpdateProvisionRequest) (
	*albvo.NodeResponse, error,
) {
	req.OperationType = consts.RollbackTocexItem
	errs, err := a.hotUpdateComponent(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "rollback_component_failed")
	}

	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeRollbackFailureCode
		resp.Message = consts.NodeRollbackFailureMsg

		return &resp, nil
	}

	resp.Success()

	return &resp, nil
}

// AbortRollback abort rollback
func (a *NodeApp) AbortRollback(ctx context.Context, req *albvo.NodeRequest) (
	*albvo.NodeResponse, error,
) {
	errs, err := a.repo.Abort(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "abort_rollback_failed")
	}

	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeAbortFailureCode
		resp.Message = consts.NodeAbortFailureMsg

		return &resp, nil
	}

	resp.Success()

	return &resp, nil
}

// Recover recovers the NodeApp from an aborted request.
//
// It takes a context.Context and a *albvo.NodeRequest as parameters.
// It returns a *albvo.NodeResponse and an error.
func (a *NodeApp) Recover(ctx context.Context, req *albvo.NodeRequest) (
	*albvo.NodeResponse, error,
) {
	resp := albvo.NodeResponse{}

	errs, err := a.server2.VerifyMATicket(req.IPs)
	if err != nil {
		return nil, errors.WithMessage(err, "verify_ma_ticket_failed")
	}
	if len(errs) != 0 {
		resp.Data.Errors = errs

		resp.Code = consts.NodeRecoverFailureCode
		resp.Message = consts.NodeRecoverFailureMsg

		return &resp, nil
	}

	errs, err = a.repo.Recover(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "recover_failed")
	}

	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeRecoverFailureCode
		resp.Message = consts.NodeRecoverFailureMsg

		return &resp, nil
	}

	resp.Success()

	return &resp, nil
}

// ReInit initializes the NodeApp with the given context and NodeRequest.
//
// It returns a NodeResponse and an error.
func (a *NodeApp) ReInit(ctx context.Context, req *albvo.NodeRequest) (
	*albvo.NodeResponse, error,
) {
	errs, err := a.repo.ReInit(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "reinit_failed")
	}

	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeReInitFailureCode
		resp.Message = consts.NodeReInitFailureMsg

		return &resp, nil
	}

	resp.Success()

	return &resp, nil
}

// Events fetch events
func (a *NodeApp) Events(ctx context.Context, req *albvo.NodePagerRequest) (*albvo.NodeEventResponse, error) {
	count, events, err := a.event.ListByPaging(ctx, req.ToEventNodeRequest())
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_events_failed")
	}

	resp := albvo.NodeEventResponse{}
	resp.Success()
	resp.Data.Events = events
	resp.Size = req.Limit
	resp.Total = count

	return &resp, nil
}

// Tags get tags
func (a *NodeApp) Tags(ctx context.Context, req *albvo.NodeRequest) (*albvo.NodeTagResponse, error) {
	tags, err := a.repo.Tags(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_tags_failed")
	}

	resp := albvo.NodeTagResponse{}
	resp.Success()
	resp.Data = tags

	return &resp, nil
}

// TagVariables get tag variables
func (a *NodeApp) TagVariables(ctx context.Context, req *albvo.NodeRequest) (*albvo.NodeTagVariableResponse, error) {
	cfgs, err := a.repo.TagVariables(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_tag_variables_failed")
	}

	resp := albvo.NodeTagVariableResponse{}
	resp.Success()
	resp.Data = cfgs

	return &resp, nil
}

// NLBListeners get nlb listener targets
func (a *NodeApp) NLBListeners(ctx context.Context, req *albvo.NodeRequest) (*albvo.NodeNLBListenerResponse, error) {
	lis := make(map[string][]*nlbvo.ListenerTarget)
	for _, ip := range req.IPs {
		cluster, err := a.cluster.GetByNode(ctx, ip)
		if err != nil {
			log.Logger().WithError(err).Warn("fetch cluster failed")

			continue
		}

		targets, err := a.cluster.NodeNLBListenersByCluster(ctx, ip, cluster)
		if err != nil {
			log.Logger().WithError(err).Warn("fetch nlb listener target failed")

			continue
		}

		lis[ip] = targets
	}

	resp := albvo.NodeNLBListenerResponse{}
	resp.Success()
	resp.Data = lis

	return &resp, nil
}

// HotUpdate update nodes
func (a *NodeApp) HotUpdate(ctx context.Context, req *albvo.HotUpdateNodeRequest) (*albvo.NodeResponse, error) {
	resp := albvo.NodeResponse{}
	resp.Success()

	if len(req.Nodes) > 0 {
		errs, err := a.repo.HotUpdate(ctx, req)
		if err != nil {
			return nil, errors.WithMessage(err, "hot-update_failed")
		}

		if len(errs) > 0 {
			resp.Data.Errors = errs
			resp.Code = consts.NodeHotUpdateFailureCode
			resp.Message = consts.NodeHotUpdateFailureMsg
		}
	}

	return &resp, nil
}

// Rollback rollback nodes
func (a *NodeApp) Rollback(ctx context.Context, req *albvo.HotUpdateNodeBaseRequest) (*albvo.NodeResponse, error) {
	errs, _ := a.repo.Rollback(ctx, req)

	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeRollbackFailureCode
		resp.Message = consts.NodeRollbackFailureMsg
	} else {
		resp.Success()
	}

	return &resp, nil
}

// Reset reset node
func (a *NodeApp) Reset(ctx context.Context, req *albvo.ResetNodeRequest) (*albvo.NodeResponse, error) {
	errs, _ := a.repo.Reset(ctx, req)

	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeResetFailureCode
		resp.Message = consts.NodeResetFailureMsg
	} else {
		resp.Success()
	}

	return &resp, nil
}

// States get states
func (a *NodeApp) States() albvo.GetStatesResponse {
	resp := albvo.GetStatesResponse{}
	resp.Success()
	resp.Data.States = consts.States

	return resp
}

// StateTasks get tasks
func (a *NodeApp) StateTasks() albvo.GetStateTasksResponse {
	resp := albvo.GetStateTasksResponse{}
	resp.Success()
	resp.Data.Tasks = a.controller.GetStateRunnerTasks()

	return resp
}

// StateTaskResults get task results
func (a *NodeApp) StateTaskResults(
	ctx context.Context,
	req *albvo.GetStateTaskResultsRequest,
) (*albvo.GetStateTaskResultsResponse, error) {
	state, err := consts.NewState(req.State)
	if err != nil {
		return nil, errors.WithMessage(err, "new_state_failed")
	}

	info := &albvo.NodeInfo{
		ALBName: albvo.CRName(req.IP),
	}
	info.State = state.Status
	info.Details = &sgwvo.DetailInfo{Message: state.Status, Reason: state.Reason}

	if err = a.repo.FulfillTaskResults(ctx, info); err != nil {
		return nil, errors.WithMessage(err, "fulfill_task_results_failed")
	}

	resp := &albvo.GetStateTaskResultsResponse{}
	resp.Success()
	resp.Data.Details = info.Details

	return resp, nil
}

func (a *NodeApp) GetStateNodes(ctx context.Context, req *sgwvo.StateNodesRequest) (*sgwvo.StateNodesResponse, error) {
	nodeList := a.repo.GetStateNodes(ctx, req)

	resp := sgwvo.StateNodesResponse{}
	resp.Success()
	resp.Nodes = nodeList

	return &resp, nil
}

func (a *NodeApp) SetNodeMATicket(ctx context.Context, req *sgwvo.UpdateNodesRequest) (*albvo.NodeResponse, error) {
	errs, _ := a.repo.UpdateNodeTicket(ctx, req)
	resp := albvo.NodeResponse{}
	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeRollbackFailureCode
		resp.Message = consts.NodeRollbackFailureMsg
	} else {
		resp.Success()
	}

	return &resp, nil
}

func (a *NodeApp) ExpireNodes(ctx context.Context) (*albvo.NodeResponse, error) {
	errs, err := a.repo.ExpireNodes(ctx)

	resp := albvo.NodeResponse{}
	resp.Success()

	if err != nil {
		return nil, errors.WithMessage(err, "expire_node_failed")
	}

	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeExpireFailureCode
		resp.Message = consts.NodeExpireFailureMsg

		return &resp, nil
	}

	return &resp, nil
}
