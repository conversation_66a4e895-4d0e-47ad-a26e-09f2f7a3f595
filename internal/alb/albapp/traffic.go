package albapp

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
)

type TrafficApp struct {
	repo albrepo.TrafficRepo

	traceID string
}

func NewTrafficApp(traceID string) *TrafficApp {
	return &TrafficApp{
		repo:    albrepo.NewTrafficRepo(traceID),
		traceID: traceID,
	}
}

func (a *TrafficApp) Block(req *albvo.ControlTrafficFormRequest) (*albvo.ControlTrafficTaskResultsResponse, error) {
	results, err := a.repo.Block(req)
	if err != nil {
		return nil, errors.WithMessage(err, "block_traffic_failed")
	}

	resp := albvo.ControlTrafficTaskResultsResponse{}
	resp.Success()
	resp.Data.Details = results

	return &resp, nil
}

func (a *TrafficApp) BlockByDomains(req *albvo.ControlTrafficFormRequest) (*albvo.ControlTrafficTaskResultsResponse, error) {
	results, err := a.repo.BlockByDomains(req)
	if err != nil {
		return nil, errors.WithMessage(err, "block_traffic_failed")
	}

	resp := albvo.ControlTrafficTaskResultsResponse{}
	resp.Success()
	resp.Data.Details = results

	return &resp, nil
}

func (a *TrafficApp) Open(req *albvo.ControlTrafficFormRequest) (*albvo.ControlTrafficTaskResultsResponse, error) {
	results, err := a.repo.Open(req)
	if err != nil {
		return nil, errors.WithMessage(err, "open_traffic_failed")
	}

	resp := albvo.ControlTrafficTaskResultsResponse{}
	resp.Success()
	resp.Data.Details = results

	return &resp, nil
}

func (a *TrafficApp) OpenByDomains(req *albvo.ControlTrafficFormRequest) (*albvo.ControlTrafficTaskResultsResponse, error) {
	results, err := a.repo.OpenByDomains(req)
	if err != nil {
		return nil, errors.WithMessage(err, "open_traffic_failed")
	}

	resp := albvo.ControlTrafficTaskResultsResponse{}
	resp.Success()
	resp.Data.Details = results

	return &resp, nil
}
