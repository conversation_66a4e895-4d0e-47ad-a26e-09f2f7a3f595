package albapp

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
)

// AuditLogger handles audit logging for ALB reload operations
type AuditLogger struct {
	mu       sync.Mutex
	config   *configs.ALBConfig
	logFile  *os.File
	filePath string
}

// NewAuditLogger creates a new audit logger instance
func NewAuditLogger(config *configs.ALBConfig) (*AuditLogger, error) {
	if !config.IsAuditLogEnabled() {
		return &AuditLogger{config: config}, nil
	}

	logPath := config.GetAuditLogPath()
	
	// Ensure the directory exists
	dir := filepath.Dir(logPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, errors.WithMessage(err, "failed_to_create_audit_log_directory")
	}

	// Open the log file in append mode
	file, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, errors.WithMessage(err, "failed_to_open_audit_log_file")
	}

	return &AuditLogger{
		config:   config,
		logFile:  file,
		filePath: logPath,
	}, nil
}

// LogReloadRequest logs a reload request to the audit log
func (a *AuditLogger) LogReloadRequest(entry *albvo.AuditLogEntry) error {
	if !a.config.IsAuditLogEnabled() || a.logFile == nil {
		return nil
	}

	a.mu.Lock()
	defer a.mu.Unlock()

	// Convert the entry to JSON
	jsonData, err := json.Marshal(entry)
	if err != nil {
		log.Logger().WithError(err).Error("failed_to_marshal_audit_log_entry")
		return errors.WithMessage(err, "failed_to_marshal_audit_log_entry")
	}

	// Write to the log file with a newline
	logLine := fmt.Sprintf("%s\n", string(jsonData))
	if _, err := a.logFile.WriteString(logLine); err != nil {
		log.Logger().WithError(err).Error("failed_to_write_audit_log_entry")
		return errors.WithMessage(err, "failed_to_write_audit_log_entry")
	}

	// Flush to ensure the data is written
	if err := a.logFile.Sync(); err != nil {
		log.Logger().WithError(err).Error("failed_to_sync_audit_log_file")
		return errors.WithMessage(err, "failed_to_sync_audit_log_file")
	}

	return nil
}

// LogReloadStart logs the start of a reload operation
func (a *AuditLogger) LogReloadStart(requestID, userEmail, bu, clusterID string, targetNodes []string, reason string) error {
	entry := &albvo.AuditLogEntry{
		Timestamp:   time.Now(),
		RequestID:   requestID,
		UserEmail:   userEmail,
		BU:          bu,
		ClusterID:   clusterID,
		TargetNodes: targetNodes,
		Status:      albvo.ReloadStatusInProgress,
		Reason:      reason,
		Duration:    0,
	}

	return a.LogReloadRequest(entry)
}

// LogReloadSuccess logs a successful reload operation
func (a *AuditLogger) LogReloadSuccess(requestID, userEmail, bu, clusterID string, targetNodes []string, reason string, duration time.Duration) error {
	entry := &albvo.AuditLogEntry{
		Timestamp:   time.Now(),
		RequestID:   requestID,
		UserEmail:   userEmail,
		BU:          bu,
		ClusterID:   clusterID,
		TargetNodes: targetNodes,
		Status:      albvo.ReloadStatusSuccess,
		Reason:      reason,
		Duration:    duration.Milliseconds(),
	}

	return a.LogReloadRequest(entry)
}

// LogReloadFailure logs a failed reload operation
func (a *AuditLogger) LogReloadFailure(requestID, userEmail, bu, clusterID string, targetNodes []string, reason, errorMsg string, duration time.Duration) error {
	entry := &albvo.AuditLogEntry{
		Timestamp:   time.Now(),
		RequestID:   requestID,
		UserEmail:   userEmail,
		BU:          bu,
		ClusterID:   clusterID,
		TargetNodes: targetNodes,
		Status:      albvo.ReloadStatusFailed,
		ErrorMsg:    errorMsg,
		Reason:      reason,
		Duration:    duration.Milliseconds(),
	}

	return a.LogReloadRequest(entry)
}

// LogRateLimited logs a rate-limited reload request
func (a *AuditLogger) LogRateLimited(requestID, userEmail, bu, clusterID string, reason string) error {
	entry := &albvo.AuditLogEntry{
		Timestamp:   time.Now(),
		RequestID:   requestID,
		UserEmail:   userEmail,
		BU:          bu,
		ClusterID:   clusterID,
		TargetNodes: []string{},
		Status:      albvo.ReloadStatusRateLimited,
		ErrorMsg:    "Request rate limited",
		Reason:      reason,
		Duration:    0,
	}

	return a.LogReloadRequest(entry)
}

// Close closes the audit log file
func (a *AuditLogger) Close() error {
	if a.logFile != nil {
		a.mu.Lock()
		defer a.mu.Unlock()
		return a.logFile.Close()
	}
	return nil
}

// GetLogFilePath returns the path to the audit log file
func (a *AuditLogger) GetLogFilePath() string {
	return a.filePath
}

// IsEnabled returns whether audit logging is enabled
func (a *AuditLogger) IsEnabled() bool {
	return a.config.IsAuditLogEnabled()
}
