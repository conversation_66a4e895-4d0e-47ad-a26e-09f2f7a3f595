package albapp

import (
	"context"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
)

func TestTicketApp_Remove(t *testing.T) {
	t.Parallel()

	fakeit := gofakeit.New(time.Now().Unix())
	t.Run("not int cluster", func(t *testing.T) {
		t.Parallel()

		app := NewTicketApp(t.Name())
		req := &albvo.ClusterNodeTicketRequest{
			IPs:  []string{fakeit.IPv4Address()},
			UUID: configs.E2E.ALB.Shopee.UUID,
		}
		_, err := app.Retire(context.Background(), req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not_in_cluster")
	})
}
