package albapp

import (
	"context"
	"strings"
	"testing"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/nlb/nlbvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

func TestClusterApp_AutoMAPreCheck(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	mockRepo := albrepo.NewMockClusterRepo(ctrl)
	app := &ClusterApp{
		repo: mockRepo,
	}

	tests := []struct {
		name          string
		ips           []string
		uuid          string
		mockNodes     []*sgwvo.Node
		skipNLBMock   bool
		mockTargets   map[string][]*nlbvo.ListenerTarget
		expectedError string
	}{
		{
			name: "success - ready nodes >= 50% and no weight 0",
			ips:  []string{"********"},
			uuid: gofakeit.UUID(),
			mockNodes: []*sgwvo.Node{
				{IP: "********", NodeStatus: consts.NodeStatusReady},
				{IP: "********", NodeStatus: consts.NodeStatusReady},
				{IP: "********", NodeStatus: consts.NodeStatusNotReady},
				{IP: "********", NodeStatus: consts.NodeStatusReady},
			},
			mockTargets: map[string][]*nlbvo.ListenerTarget{
				"********": {{Weight: 100}},
				"********": {{Weight: 100}},
			},
		},
		{
			name: "error - ready nodes < 50%",
			ips:  []string{"********"},
			uuid: gofakeit.UUID(),
			mockNodes: []*sgwvo.Node{
				{IP: "********", NodeStatus: consts.NodeStatusReady},
				{IP: "********", NodeStatus: consts.NodeStatusNotReady},
				{IP: "********", NodeStatus: consts.NodeStatusNotReady},
			},
			skipNLBMock:   true,
			expectedError: "cluster_ready_node_less_than_50_percent",
		},
		{
			name: "error - NLB listener target not found",
			ips:  []string{"********"},
			uuid: gofakeit.UUID(),
			mockNodes: []*sgwvo.Node{
				{IP: "********", NodeStatus: consts.NodeStatusReady},
				{IP: "********", NodeStatus: consts.NodeStatusReady},
			},
			mockTargets:   map[string][]*nlbvo.ListenerTarget{},
			expectedError: "cluster_nlb_listener_target_not_found",
		},
		{
			name: "error - NLB listener target weight is 0",
			ips:  []string{"********"},
			uuid: gofakeit.UUID(),
			mockNodes: []*sgwvo.Node{
				{IP: "********", NodeStatus: consts.NodeStatusReady},
				{IP: "********", NodeStatus: consts.NodeStatusReady},
			},
			mockTargets: map[string][]*nlbvo.ListenerTarget{
				"********": {{Weight: 0}},
			},
			expectedError: "cluster_nlb_listener_target_weight_is_0: [********:0-0]",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockRepo.EXPECT().
				Nodes(gomock.Any(), tt.uuid).
				Return(tt.mockNodes, nil)

			if len(tt.mockNodes) > 0 && !tt.skipNLBMock {
				mockRepo.EXPECT().
					NLBListenerTargetsMapByUUID(gomock.Any(), tt.uuid).
					Return(tt.mockTargets, nil)
			}

			err := app.AutoMAPreCheck(context.Background(), tt.ips, tt.uuid)
			if tt.expectedError != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestClusterApp_AutoMAPreCheck_SG2(t *testing.T) {
	app := NewClusterApp(t.Name())
	err := app.AutoMAPreCheck(context.Background(), []string{configs.E2E.ALB.Shopee.Node},
		configs.E2E.ALB.Shopee.UUID)
	/*
		err := app.AutoMAPreCheck(context.Background(), []string{"*************"},
			"b3ec4b28-bda4-5cf9-b1f3-cfaba6d18d02")
		err := app.AutoMAPreCheck(context.Background(), []string{"************"},
			"da8f7350-bc16-58df-9cd2-7e959a756a77")
	*/

	assert.Condition(t, func() bool {
		if err != nil {
			return strings.Contains(err.Error(), "cluster_ready_node_less_than_50_percent") ||
				strings.Contains(err.Error(), "cluster_nlb_listener_target_weight_is_0")
		}

		return false
	})
}
