package albapp

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

// ReloadService handles ALB nginx reload operations
type ReloadService struct {
	traceID     string
	rateLimiter *RateLimiter
	auditLogger *AuditLogger
	reloader    *NginxReloader
	clusterApp  *ClusterApp
}

// NewReloadService creates a new reload service instance
func NewReloadService(traceID string) (*ReloadService, error) {
	config := configs.ALB

	// Check if reload functionality is enabled
	if !config.IsReloadEnabled() {
		return nil, errors.New("alb_reload_functionality_disabled")
	}

	// Initialize rate limiter
	rateLimiter := NewRateLimiter(config)

	// Initialize audit logger
	auditLogger, err := NewAuditLogger(config)
	if err != nil {
		return nil, errors.WithMessage(err, "failed_to_initialize_audit_logger")
	}

	// Initialize nginx reloader
	reloader := NewNginxReloader(config, traceID)

	// Initialize cluster app
	clusterApp := NewClusterApp(traceID)

	return &ReloadService{
		traceID:     traceID,
		rateLimiter: rateLimiter,
		auditLogger: auditLogger,
		reloader:    reloader,
		clusterApp:  clusterApp,
	}, nil
}

// ProcessReloadRequest processes a reload request with full validation and execution
func (s *ReloadService) ProcessReloadRequest(ctx context.Context, req *albvo.ReloadRequest, userEmail string) (*albvo.ReloadResponse, error) {
	startTime := time.Now()
	requestID := uuid.New().String()

	log.Logger().WithFields(log.Fields{
		"trace_id":   s.traceID,
		"request_id": requestID,
		"user_email": userEmail,
		"cluster_id": req.ClusterID,
		"reason":     req.Reason,
	}).Info("processing_alb_reload_request")

	// Step 1: Get user's BU
	bu, err := s.getUserBU(userEmail)
	if err != nil {
		return s.createErrorResponse(requestID, req.ClusterID, "", albvo.ErrForbidden, err.Error(), startTime), nil
	}

	// Step 2: Check rate limiting
	if !s.rateLimiter.IsAllowed(bu, requestID) {
		timeUntilNext := s.rateLimiter.GetTimeUntilNextAllowed(bu)
		errorMsg := fmt.Sprintf("Rate limit exceeded for BU %s. Next request allowed in %v", bu, timeUntilNext)

		// Log rate limited request
		if err := s.auditLogger.LogRateLimited(requestID, userEmail, bu, req.ClusterID, req.Reason); err != nil {
			log.Logger().WithError(err).Error("failed_to_log_rate_limited_request")
		}

		return s.createErrorResponse(requestID, req.ClusterID, bu, albvo.ErrRateLimited, errorMsg, startTime), nil
	}

	// Step 3: Get cluster information
	cluster, err := s.getClusterByID(ctx, req.ClusterID)
	if err != nil {
		errorMsg := fmt.Sprintf("Failed to get cluster %s: %v", req.ClusterID, err)
		return s.createErrorResponse(requestID, req.ClusterID, bu, albvo.ErrInvalidCluster, errorMsg, startTime), nil
	}

	// Step 4: Filter nodes by BU
	targetNodes := s.reloader.FilterNodesByBU(cluster.Nodes, bu)
	if len(targetNodes) == 0 {
		errorMsg := fmt.Sprintf("No nodes found for BU %s in cluster %s", bu, req.ClusterID)
		return s.createErrorResponse(requestID, req.ClusterID, bu, albvo.ErrForbidden, errorMsg, startTime), nil
	}

	targetNodeIPs := s.reloader.GetNodeIPs(targetNodes)

	// Step 5: Log reload start
	if err := s.auditLogger.LogReloadStart(requestID, userEmail, bu, req.ClusterID, targetNodeIPs, req.Reason); err != nil {
		log.Logger().WithError(err).Error("failed_to_log_reload_start")
	}

	// Step 6: Execute nginx reload
	reloadResults, err := s.reloader.ReloadNginxOnNodes(ctx, targetNodes)
	duration := time.Since(startTime)

	if err != nil {
		errorMsg := fmt.Sprintf("Failed to execute reload: %v", err)

		// Log reload failure
		if logErr := s.auditLogger.LogReloadFailure(requestID, userEmail, bu, req.ClusterID, targetNodeIPs, req.Reason, errorMsg, duration); logErr != nil {
			log.Logger().WithError(logErr).Error("failed_to_log_reload_failure")
		}

		return s.createErrorResponse(requestID, req.ClusterID, bu, albvo.ErrReloadFailed, errorMsg, startTime), nil
	}

	// Step 7: Check if reload was successful
	if !reloadResults.IsReloadSuccessful() {
		errorMsg := fmt.Sprintf("Reload failed on %d nodes: %s", reloadResults.FailureCount, reloadResults.GetErrorSummary())

		// Log reload failure
		if logErr := s.auditLogger.LogReloadFailure(requestID, userEmail, bu, req.ClusterID, targetNodeIPs, req.Reason, errorMsg, duration); logErr != nil {
			log.Logger().WithError(logErr).Error("failed_to_log_reload_failure")
		}

		return s.createErrorResponse(requestID, req.ClusterID, bu, albvo.ErrReloadFailed, errorMsg, startTime), nil
	}

	// Step 8: Log successful reload
	if err := s.auditLogger.LogReloadSuccess(requestID, userEmail, bu, req.ClusterID, targetNodeIPs, req.Reason, duration); err != nil {
		log.Logger().WithError(err).Error("failed_to_log_reload_success")
	}

	// Step 9: Create success response
	endTime := time.Now()
	response := &albvo.ReloadResponse{
		Data: albvo.ReloadResponseData{
			RequestID:   requestID,
			Status:      albvo.ReloadStatusSuccess,
			Message:     fmt.Sprintf("Successfully reloaded nginx on %d nodes", reloadResults.SuccessCount),
			ClusterID:   req.ClusterID,
			BU:          bu,
			TargetNodes: targetNodeIPs,
			StartTime:   startTime,
			EndTime:     &endTime,
		},
	}
	response.Success()

	log.Logger().WithFields(log.Fields{
		"trace_id":      s.traceID,
		"request_id":    requestID,
		"user_email":    userEmail,
		"bu":            bu,
		"cluster_id":    req.ClusterID,
		"success_count": reloadResults.SuccessCount,
		"duration":      duration,
	}).Info("alb_reload_request_completed_successfully")

	return response, nil
}

// getUserBU gets the BU for a user email
func (s *ReloadService) getUserBU(userEmail string) (string, error) {
	bu, found := configs.ALB.GetUserBU(userEmail)
	if !found {
		return "", fmt.Errorf("user %s is not authorized for ALB reload operations", userEmail)
	}
	return bu, nil
}

// getClusterByID gets cluster information by cluster ID
func (s *ReloadService) getClusterByID(ctx context.Context, clusterID string) (*albvo.Cluster, error) {
	// For now, we'll use the cluster UUID as the identifier
	// In a real implementation, you might need to handle different ID formats
	clusterReq := &albvo.ClusterIDRequest{UUID: clusterID}

	// Get cluster nodes
	nodeResp, err := s.clusterApp.Node(ctx, clusterReq)
	if err != nil {
		return nil, errors.WithMessage(err, "failed_to_get_cluster_nodes")
	}

	// Convert NodeInfo to sgwvo.Node
	var nodes []*sgwvo.Node
	for _, nodeInfo := range nodeResp.Data.Nodes {
		node := &sgwvo.Node{
			IP:          nodeInfo.IP,
			ClusterUUID: nodeInfo.ClusterUUID,
			NodeStatus:  nodeInfo.NodeStatus,
			UpdatedBy:   nodeInfo.UpdatedBy,
			UpdatedAt:   nodeInfo.UpdatedAt,
			// Copy other relevant fields from NodeInfo.Node
		}
		// Copy the embedded Node fields
		if nodeInfo.NodeInfo.Node.IP != "" {
			node.IP = nodeInfo.NodeInfo.Node.IP
		}
		nodes = append(nodes, node)
	}

	// Create a basic cluster object with the nodes
	cluster := &albvo.Cluster{
		UUID:  clusterID,
		Nodes: nodes,
	}

	return cluster, nil
}

// createErrorResponse creates an error response
func (s *ReloadService) createErrorResponse(requestID, clusterID, bu, errorCode, errorMsg string, startTime time.Time) *albvo.ReloadResponse {
	endTime := time.Now()
	response := &albvo.ReloadResponse{
		Data: albvo.ReloadResponseData{
			RequestID:   requestID,
			Status:      albvo.ReloadStatusFailed,
			Message:     errorMsg,
			ClusterID:   clusterID,
			BU:          bu,
			TargetNodes: []string{},
			StartTime:   startTime,
			EndTime:     &endTime,
		},
	}

	// Set appropriate error code and message
	switch errorCode {
	case albvo.ErrRateLimited:
		response.Code = 429
	case albvo.ErrForbidden:
		response.Code = 403
	case albvo.ErrInvalidCluster:
		response.Code = 400
	default:
		response.Code = 500
	}
	response.Message = errorMsg

	return response
}

// Close closes the reload service and its resources
func (s *ReloadService) Close() error {
	if s.auditLogger != nil {
		return s.auditLogger.Close()
	}
	return nil
}

// Helper functions for API layer

// GetUserBUFromConfig gets the BU for a user email from configuration
func GetUserBUFromConfig(userEmail string) (string, bool) {
	return configs.ALB.GetUserBU(userEmail)
}

// IsReloadEnabledFromConfig returns whether reload functionality is enabled
func IsReloadEnabledFromConfig() bool {
	return configs.ALB.IsReloadEnabled()
}

// GetRateLimitWindowFromConfig returns the rate limit window duration
func GetRateLimitWindowFromConfig() time.Duration {
	return configs.ALB.GetReloadRateLimitWindow()
}

// GetMaxRequestsFromConfig returns the maximum number of requests allowed
func GetMaxRequestsFromConfig() int {
	return configs.ALB.GetReloadMaxRequests()
}

// IsAuditLogEnabledFromConfig returns whether audit logging is enabled
func IsAuditLogEnabledFromConfig() bool {
	return configs.ALB.IsAuditLogEnabled()
}
