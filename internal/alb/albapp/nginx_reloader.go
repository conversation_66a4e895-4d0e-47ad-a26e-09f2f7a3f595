package albapp

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

// NginxReloader handles nginx reload operations on ALB nodes
type NginxReloader struct {
	config  *configs.ALBConfig
	traceID string
}

// NewNginxReloader creates a new nginx reloader instance
func NewNginxReloader(config *configs.ALBConfig, traceID string) *NginxReloader {
	return &NginxReloader{
		config:  config,
		traceID: traceID,
	}
}

// ReloadResult represents the result of a reload operation on a single node
type ReloadResult struct {
	NodeIP  string `json:"node_ip"`
	Success bool   `json:"success"`
	Output  string `json:"output"`
	Error   string `json:"error,omitempty"`
}

// ReloadResults represents the results of reload operations on multiple nodes
type ReloadResults struct {
	SuccessCount int            `json:"success_count"`
	FailureCount int            `json:"failure_count"`
	Results      []ReloadResult `json:"results"`
}

// ReloadNginxOnNodes executes nginx reload on the specified nodes
func (n *NginxReloader) ReloadNginxOnNodes(ctx context.Context, nodes []*sgwvo.Node) (*ReloadResults, error) {
	if len(nodes) == 0 {
		return nil, errors.New("no_nodes_specified")
	}

	command := n.config.GetNginxReloadCommand()
	timeout := n.config.GetNginxReloadTimeout()

	// Security check: validate the command to prevent injection
	if err := n.validateCommand(command); err != nil {
		return nil, errors.WithMessage(err, "invalid_nginx_command")
	}

	log.Logger().WithFields(log.Fields{
		"trace_id": n.traceID,
		"command":  command,
		"timeout":  timeout,
		"nodes":    len(nodes),
	}).Info("starting_nginx_reload_on_nodes")

	results := &ReloadResults{
		Results: make([]ReloadResult, 0, len(nodes)),
	}

	// Execute reload on each node
	for _, node := range nodes {
		result := n.reloadOnSingleNode(ctx, node, command, timeout)
		results.Results = append(results.Results, result)

		if result.Success {
			results.SuccessCount++
		} else {
			results.FailureCount++
		}
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":      n.traceID,
		"success_count": results.SuccessCount,
		"failure_count": results.FailureCount,
		"total_nodes":   len(nodes),
	}).Info("nginx_reload_completed")

	return results, nil
}

// reloadOnSingleNode executes nginx reload on a single node
func (n *NginxReloader) reloadOnSingleNode(_ context.Context, node *sgwvo.Node, command string, timeout time.Duration) ReloadResult {
	result := ReloadResult{
		NodeIP: node.IP,
	}

	log.Logger().WithFields(log.Fields{
		"trace_id": n.traceID,
		"node_ip":  node.IP,
		"command":  command,
	}).Info("executing_nginx_reload_on_node")

	// Create tocex adapter for the node
	tocexAdapter, err := toc.NewTocexAdapterWithIP(node.IP, n.traceID)
	if err != nil {
		result.Error = fmt.Sprintf("failed to create tocex adapter: %v", err)
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": n.traceID,
			"node_ip":  node.IP,
		}).Error("failed_to_create_tocex_adapter")
		return result
	}

	// Set timeout for the tocex adapter
	tocexAdapter = tocexAdapter.WithTimeout(timeout)

	// Execute the nginx reload command
	output, err := tocexAdapter.RunTask(command)
	if err != nil {
		result.Error = fmt.Sprintf("command execution failed: %v", err)
		result.Output = output
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": n.traceID,
			"node_ip":  node.IP,
			"output":   output,
		}).Error("nginx_reload_command_failed")
		return result
	}

	result.Success = true
	result.Output = output

	log.Logger().WithFields(log.Fields{
		"trace_id": n.traceID,
		"node_ip":  node.IP,
		"output":   output,
	}).Info("nginx_reload_successful_on_node")

	return result
}

// ValidateNginxConfig validates nginx configuration on the specified nodes before reload
func (n *NginxReloader) ValidateNginxConfig(ctx context.Context, nodes []*sgwvo.Node) (*ReloadResults, error) {
	if len(nodes) == 0 {
		return nil, errors.New("no_nodes_specified")
	}

	// Use nginx -t to test configuration
	testCommand := "nginx -t"
	timeout := n.config.GetNginxReloadTimeout()

	log.Logger().WithFields(log.Fields{
		"trace_id": n.traceID,
		"command":  testCommand,
		"nodes":    len(nodes),
	}).Info("validating_nginx_config_on_nodes")

	results := &ReloadResults{
		Results: make([]ReloadResult, 0, len(nodes)),
	}

	// Validate configuration on each node
	for _, node := range nodes {
		result := n.reloadOnSingleNode(ctx, node, testCommand, timeout)
		results.Results = append(results.Results, result)

		if result.Success {
			results.SuccessCount++
		} else {
			results.FailureCount++
		}
	}

	return results, nil
}

// FilterNodesByBU filters nodes that belong to the specified BU
// For the initial version, this is a placeholder that returns all nodes
// In a real implementation, this would check node metadata or tags to determine BU ownership
func (n *NginxReloader) FilterNodesByBU(nodes []*sgwvo.Node, bu string) []*sgwvo.Node {
	// For the initial version, we'll return all nodes
	// In a production implementation, you would filter based on:
	// - Node labels/tags indicating BU ownership
	// - Cluster metadata
	// - External service mapping

	log.Logger().WithFields(log.Fields{
		"trace_id":    n.traceID,
		"bu":          bu,
		"total_nodes": len(nodes),
	}).Info("filtering_nodes_by_bu")

	// Placeholder implementation - return all nodes for now
	// TODO: Implement actual BU-based filtering logic
	filteredNodes := make([]*sgwvo.Node, len(nodes))
	copy(filteredNodes, nodes)

	log.Logger().WithFields(log.Fields{
		"trace_id":       n.traceID,
		"bu":             bu,
		"filtered_nodes": len(filteredNodes),
	}).Info("nodes_filtered_by_bu")

	return filteredNodes
}

// GetNodeIPs extracts IP addresses from a list of nodes
func (n *NginxReloader) GetNodeIPs(nodes []*sgwvo.Node) []string {
	return slice.Map(nodes, func(_ int, node *sgwvo.Node) string {
		return node.IP
	})
}

// IsReloadSuccessful determines if the overall reload operation was successful
func (r *ReloadResults) IsReloadSuccessful() bool {
	return r.FailureCount == 0 && r.SuccessCount > 0
}

// GetErrorSummary returns a summary of errors from failed reload operations
func (r *ReloadResults) GetErrorSummary() string {
	if r.FailureCount == 0 {
		return ""
	}

	var errors []string
	for _, result := range r.Results {
		if !result.Success && result.Error != "" {
			errors = append(errors, fmt.Sprintf("%s: %s", result.NodeIP, result.Error))
		}
	}

	return strings.Join(errors, "; ")
}

// validateCommand validates the nginx command to prevent command injection
func (n *NginxReloader) validateCommand(command string) error {
	// Only allow systemctl reload nginx command
	if command != "systemctl reload nginx" {
		return fmt.Errorf("only 'systemctl reload nginx' command is allowed, got: %s", command)
	}

	return nil
}
