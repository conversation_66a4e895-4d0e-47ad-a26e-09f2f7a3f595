package albapp

import (
	"sync"
	"time"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
)

// RateLimiter manages rate limiting for ALB reload operations per BU
type RateLimiter struct {
	mu      sync.RWMutex
	entries map[string]*albvo.RateLimitEntry
	config  *configs.ALBConfig
}

// NewRateLimiter creates a new rate limiter instance
func NewRateLimiter(config *configs.ALBConfig) *RateLimiter {
	return &RateLimiter{
		entries: make(map[string]*albvo.RateLimitEntry),
		config:  config,
	}
}

// IsAllowed checks if a reload operation is allowed for the given BU
func (r *RateLimiter) IsAllowed(bu, requestID string) bool {
	r.mu.Lock()
	defer r.mu.Unlock()

	now := time.Now()
	window := r.config.GetReloadRateLimitWindow()

	// Check if there's an existing entry for this BU
	if entry, exists := r.entries[bu]; exists {
		// Check if the last request was within the rate limit window
		if now.Sub(entry.LastUsed) < window {
			return false
		}
	}

	// Allow the request and update the entry
	r.entries[bu] = &albvo.RateLimitEntry{
		BU:        bu,
		LastUsed:  now,
		RequestID: requestID,
	}

	return true
}

// GetLastRequest returns the last request information for a BU
func (r *RateLimiter) GetLastRequest(bu string) (*albvo.RateLimitEntry, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	entry, exists := r.entries[bu]
	if !exists {
		return nil, false
	}

	// Return a copy to avoid race conditions
	return &albvo.RateLimitEntry{
		BU:        entry.BU,
		LastUsed:  entry.LastUsed,
		RequestID: entry.RequestID,
	}, true
}

// GetTimeUntilNextAllowed returns the duration until the next request is allowed for a BU
func (r *RateLimiter) GetTimeUntilNextAllowed(bu string) time.Duration {
	r.mu.RLock()
	defer r.mu.RUnlock()

	entry, exists := r.entries[bu]
	if !exists {
		return 0 // No previous request, allowed immediately
	}

	window := r.config.GetReloadRateLimitWindow()
	elapsed := time.Since(entry.LastUsed)
	
	if elapsed >= window {
		return 0 // Window has passed, allowed immediately
	}

	return window - elapsed
}

// CleanupExpiredEntries removes expired entries from the rate limiter
func (r *RateLimiter) CleanupExpiredEntries() {
	r.mu.Lock()
	defer r.mu.Unlock()

	now := time.Now()
	window := r.config.GetReloadRateLimitWindow()

	for bu, entry := range r.entries {
		if now.Sub(entry.LastUsed) >= window {
			delete(r.entries, bu)
		}
	}
}

// GetAllEntries returns all current rate limit entries (for debugging/monitoring)
func (r *RateLimiter) GetAllEntries() map[string]*albvo.RateLimitEntry {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]*albvo.RateLimitEntry)
	for bu, entry := range r.entries {
		result[bu] = &albvo.RateLimitEntry{
			BU:        entry.BU,
			LastUsed:  entry.LastUsed,
			RequestID: entry.RequestID,
		}
	}

	return result
}
