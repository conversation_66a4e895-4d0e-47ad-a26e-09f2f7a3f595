package albapp

import (
	"context"
	"testing"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

func TestNodeApp_GenerateProvisionConfigs_failed(t *testing.T) {
	if ciEnv {
		t.Skip("Skipping test in CI")
	}

	app := NewNodeProvisionApp(t.Name())

	fakeNodeIP := gofakeit.IPv4Address()
	fakeComponent := gofakeit.Name()

	// node isn't found
	_, err := app.GenConfigs(context.Background(), &albvo.NodeProvisionComponentRequest{
		IP:        fakeNodeIP,
		Component: cmp.ScertmsComponent.Name,
		Version:   "1.0.0",
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "fetch_alb_node_server_failed")

	// component unsupported
	_, err = app.GenConfigs(context.Background(), &albvo.NodeProvisionComponentRequest{
		IP:        configs.E2E.ALB.Node2,
		Component: fakeComponent,
		Version:   "1.0.0",
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported_component_")
}

func TestNodeApp_GenerateProvisionConfigs(t *testing.T) {
	if ciEnv {
		t.Skip("Skipping test in CI")
	}

	app := NewNodeProvisionApp(t.Name())

	if ciEnv {
		mockNodeRepo := albrepo.NewMockNodeProvisionRepo(gomock.NewController(t))
		mockNodeRepo.EXPECT().GenerateConfig(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, req *albvo.NodeProvisionComponentRequest) (*tocvo.NodeProvision, error) {
				prov := tocvo.NodeProvision{}
				prov.Component = &toclib.ProvisionNodeComponent{}
				prov.Templates = []toclib.ProvisionNodeTemplate{
					{
						Path:    "/tmp/test.yaml",
						Content: "test",
					},
				}

				return &prov, nil
			}).AnyTimes()

		app.provision = mockNodeRepo
	}

	resp, err := app.GenConfigs(context.Background(), &albvo.NodeProvisionComponentRequest{
		IP:        configs.E2E.ALB.Node2,
		Component: cmp.ScertmsComponent.Name,
		Version:   "1.0.0",
	})
	assert.NoError(t, err)
	assert.True(t, resp.IsSuccess())
	assert.NotNil(t, resp.Data)
	assert.Greater(t, len(resp.Data.Templates), 0)
	assert.Equal(t, len(resp.Data.Components), 1)
}
