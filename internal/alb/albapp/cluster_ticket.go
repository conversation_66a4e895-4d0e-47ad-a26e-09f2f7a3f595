package albapp

import (
	"context"
	"strings"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

func (a *TicketApp) verifyClusterConfigFresh(cluster *albvo.Cluster, config *sgwvo.L7ClusterConfig) error {
	if cluster.HAName() == consts.HAMNLB {
		return errors.Errorf("ham_nlb_not_support_fresh_config")
	}

	// cluster should be in azmeta
	_, err := a.cluster2.GetByName(consts.ALB, cluster.Name)
	if err != nil {
		return errors.WithMessage(err, "cluster_not_in_azmeta")
	}

	switch cluster.HAName() {
	case consts.HAMECMP:
		if err := config.VerifyECMPConfigs(); err != nil {
			return errors.WithMessage(err, "verify_ecmp_configs_failed")
		}

		vips2 := &albvo.ClusterVIPs{
			WANVIPs:        config.WANVIPs(),
			LANVIPs:        config.LANVIPs(),
			PrivateWANVIPs: config.PrivateWANVIPs(),
		}

		if err = a.verifyClusterVips(cluster.UUID, vips2); err != nil {
			return errors.WithMessage(err, "verify_cluster_vips_failed")
		}
	case consts.HAMActiveStandby:
		if err := config.VerifyActStandbyConfigs(); err != nil {
			return errors.WithMessage(err, "verify_act_standby_configs_failed")
		}

		vips2 := &albvo.ClusterVIPs{
			WANVIPs:        config.KeepalivedWANVIPs(),
			LANVIPs:        config.KeepalivedLANVIPs(),
			PrivateWANVIPs: config.KeepalivedPrivateWANVIPs(),
		}

		if err = a.verifyClusterVips(cluster.UUID, vips2); err != nil {
			return errors.WithMessage(err, "verify_cluster_vips_failed")
		}
	}

	return nil
}

func (a *TicketApp) verifyClusterVips(uuid string, vips2 *albvo.ClusterVIPs) error {
	cls, err := a.cluster3.Dump()
	if err != nil {
		return errors.WithMessage(err, "fetch_clusters_failed")
	}

	cls = slice.Filter(cls, func(_ int, cluster *albvo.Cluster) bool {
		return !strings.EqualFold(uuid, cluster.UUID)
	})

	clusters := albvo.Clusters(cls)

	vips := clusters.VIPs()

	wans := vips2.WANVIPs
	if len(wans) > 0 {
		if slice.ContainSubSlice(vips.WANVIPs, wans) {
			return errors.Errorf("%s_already_exist_in_wan_vips", wans)
		}
	}

	lans := vips2.LANVIPs
	if len(lans) > 0 {
		if slice.ContainSubSlice(vips.LANVIPs, lans) {
			return errors.Errorf("%s_already_exist_in_lan_vips", lans)
		}
	}

	privateWans := vips2.PrivateWANVIPs
	if len(privateWans) > 0 {
		if slice.ContainSubSlice(vips.PrivateWANVIPs, privateWans) {
			return errors.Errorf("%s_already_exist_in_private_wan_vips", privateWans)
		}
	}

	return nil
}

// ClusterConfig raise a ticket of fresh cluster config
func (a *TicketApp) ClusterConfig(ctx context.Context, req *albvo.ClusterConfigTicketRequest) (
	*albvo.TicketResponse, error,
) {
	cluster, err := a.cluster.GetByUUID(ctx, req.UUID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	if err = a.verifyClusterConfigFresh(cluster, req.Config); err != nil {
		return nil, errors.WithMessage(err, "verify_cluster_config_fresh_failed")
	}

	req.Cluster = cluster.Name
	req.HA = cluster.HAName()

	req2, err := req.ToFreshConfigTicketRequest()
	if err != nil {
		return nil, errors.WithMessage(err, "to_fresh_config_ticket_req_failed")
	}

	ticket, err := a.repo.FreshClusterConfig(ctx, req2)
	if err != nil {
		return nil, errors.WithMessage(err, "raise_fresh_cluster_config_ticket_failed")
	}

	resp := albvo.TicketResponse{}
	resp.Success()
	resp.Data = &albvo.TicketData{Ticket: ticket}

	return &resp, nil
}
