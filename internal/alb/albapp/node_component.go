package albapp

import (
	"context"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

// Components get components
func (a *NodeApp) Components() albvo.ComponentListResponse {
	resp := albvo.ComponentListResponse{}
	resp.Success()
	resp.Data.Components = cmp.ALBLegacyCoreComponents

	return resp
}

// Versions get component versions
func (a *NodeApp) Versions(req albvo.ComponentRequest) (*albvo.ComponentVersionResponse, error) {
	versions, err := a.comp.Versions(&req)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_component_version_failed")
	}

	resp := albvo.ComponentVersionResponse{}
	resp.Success()
	resp.Data.Versions = versions

	return &resp, nil
}

// ListComponents list components
func (a *NodeApp) ListComponents(ctx context.Context, req *albvo.NodeIPRequest) (*albvo.NodeComponentResponse, error) {
	svr, err := a.serverV3.Get(req.IP)
	if err != nil {
		log.Logger().WithError(err).WithField("ip", req.IP).Error("get_from_toc_failed")

		return nil, errors.WithMessage(err, "list_component_failed")
	}

	server := tocvo.CMDBServer(*svr)
	tocex := toc.NewTocexAdapter(server.ToNode())
	component := albrepo.NewNodeComponentRepo(a.traceID, tocex)
	comps, err := component.Dump(ctx)
	if err != nil {
		log.Logger().WithError(err).Error("get_node_component_failed")

		return nil, errors.WithMessage(err, "fetch_component_failed")
	}

	resp := albvo.NodeComponentResponse{}
	resp.Success()
	resp.Data.Component = comps

	return &resp, nil
}

// UpdateComponents update components version
func (a *NodeApp) UpdateComponents(ctx context.Context, req *albvo.NodeIPRequest) (*core.BaseResponse, error) {
	alb, err := a.controller.GetWithContext(ctx, albvo.CRName(req.IP))
	if err != nil {
		log.Logger().WithError(err).WithField("ip", req.IP).Error("get_alb_cr_failed")

		return nil, errors.WithMessage(err, "fetch_alb_cr_failed")
	}

	svr, err := a.serverV3.Get(req.IP)
	if err != nil {
		log.Logger().WithError(err).WithField("ip", req.IP).Error("get_from_toc_failed")

		return nil, errors.WithMessage(err, "fetch_server_failed")
	}

	server := tocvo.CMDBServer(*svr)
	tocex := toc.NewTocexAdapter(server.ToNode())
	component := albrepo.NewNodeComponentRepo(a.traceID, tocex)
	comps, err := component.Dump(ctx)
	if err != nil {
		log.Logger().WithError(err).Error("get_node_component_failed")

		return nil, errors.WithMessage(err, "fetch_node_component_failed")
	}

	albvo.UpdateALBNodeVersion(&alb.Spec.ALBNode, comps)

	if err = a.controller.UpdateWithContext(ctx, alb); err != nil {
		return nil, errors.WithMessage(err, "update_alb_node_version_failed")
	}

	resp := core.BaseResponse{}
	resp.Success()

	return &resp, nil
}

// UpdateComponent update component version
func (a *NodeApp) UpdateComponent(ctx context.Context, req *albvo.NodeProvisionComponentRequest) (
	*core.BaseResponse, error,
) {
	alb, err := a.controller.GetWithContext(ctx, albvo.CRName(req.IP))
	if err != nil {
		log.Logger().WithError(err).WithField("ip", req.IP).Error("get_alb_cr_failed")

		return nil, errors.WithMessage(err, "fetch_alb_cr_failed")
	}

	albvo.UpdateALBComponentVersion(&alb.Spec.ALBNode, req.Component, req.Version)
	if err = a.controller.UpdateWithContext(ctx, alb); err != nil {
		log.Logger().WithError(err).WithField("ip", req.IP).Error("update_alb_cr_failed")

		return nil, errors.WithMessage(err, "update_alb_cr_failed")
	}

	resp := core.BaseResponse{}
	resp.Success()

	return &resp, nil
}
