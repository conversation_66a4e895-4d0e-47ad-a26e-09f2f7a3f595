package albapp

import (
	"context"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type NodeProvisionApp struct {
	provision albrepo.NodeProvisionRepo

	traceID string
}

func NewNodeProvisionApp(traceID string) *NodeProvisionApp {
	return &NodeProvisionApp{
		provision: albrepo.NewNodeProvisionRepo(traceID),
		traceID:   traceID,
	}
}

// GenConfigs generate upgraded provision configs
func (a *NodeProvisionApp) GenConfigs(ctx context.Context, req *albvo.NodeProvisionComponentRequest) (
	*albvo.NodeProvisionConfigResponse, error,
) {
	provision, err := a.provision.GenerateConfig(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_provision_config_failed")
	}

	resp := albvo.NodeProvisionConfigResponse{}
	resp.Success()
	resp.Data = provision.ToConfig()

	return &resp, nil
}

// Delete delete specified nodes' provision config
func (a *NodeProvisionApp) Delete(ctx context.Context, req []toclib.ProvisionNodeConfig) (*core.BaseResponse, error) {
	err := a.provision.DeleteConfigs(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "delete_provision_config_failed")
	}

	resp := core.BaseResponse{}
	resp.Success()

	return &resp, nil
}

// DeleteComponent delete specified nodes' component provision config
func (a *NodeProvisionApp) DeleteComponent(ctx context.Context, req *albvo.NodeComponentRequest) (
	*albvo.NodeResponse, error,
) {
	items, err := a.provision.DeleteComponent(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "delete_provision_component")
	}

	resp := albvo.NodeResponse{}
	resp.Success()
	resp.Data.Errors = items

	return &resp, nil
}

// UpdateComponent update specified nodes' component provision config
// Only on `disabled` attribute
func (a *NodeProvisionApp) UpdateComponent(ctx context.Context, req *albvo.NodeUpdateComponentRequest) (
	*albvo.NodeResponse, error,
) {
	errs, err := a.provision.UpdateComponent(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "disable_provision_component")
	}

	resp := albvo.NodeResponse{}

	if len(errs) > 0 {
		resp.Data.Errors = errs
		resp.Code = consts.NodeUpdateComponentFailureCode
		resp.Message = consts.NodeUpdateComponentFailureMsg

		return &resp, nil
	}

	resp.Success()

	return &resp, nil
}

// DeleteTemplate delete provision template
func (a *NodeProvisionApp) DeleteTemplate(ctx context.Context, req *albvo.NodeProvisionTemplateRequest) (
	*albvo.NodeResponse, error,
) {
	items, err := a.provision.DeleteTemplate(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "delete_provision_template_failed")
	}

	resp := albvo.NodeResponse{}
	resp.Success()
	resp.Data.Errors = items

	return &resp, nil
}

// PurgeContainers purge provision containers
func (a *NodeProvisionApp) PurgeContainers(ctx context.Context, req *albvo.NodeRequest) (*albvo.NodeResponse, error) {
	items, err := a.provision.PurgeContainers(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "purge_container_provision_failed")
	}

	resp := albvo.NodeResponse{}
	resp.Success()
	resp.Data.Errors = items

	return &resp, nil
}

// Purge purge provision
func (a *NodeProvisionApp) Purge(ctx context.Context, req *albvo.NodeRequest) (*albvo.NodeResponse, error) {
	items, err := a.provision.PurgeConfigs(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "purge_provision_failed")
	}

	resp := albvo.NodeResponse{}
	resp.Success()
	resp.Data.Errors = items

	return &resp, nil
}

// Configs fetch node's provision configs
func (a *NodeProvisionApp) Configs(ctx context.Context, req *albvo.NodeRequest) (*albvo.NodeProvisionResponse, error) {
	cfgs, err := a.provision.Configs(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_provision_configs_failed")
	}

	resp := albvo.NodeProvisionResponse{}
	resp.Success()
	resp.Data = cfgs

	return &resp, nil
}
