package albrepo

import (
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albsvc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

type TrafficRepo interface {
	Block(req *albvo.ControlTrafficFormRequest) ([]*sgwvo.NodeTaskResult, error)
	BlockByDomains(req *albvo.ControlTrafficFormRequest) ([]*sgwvo.NodeTaskResult, error)
	Open(req *albvo.ControlTrafficFormRequest) ([]*sgwvo.NodeTaskResult, error)
	OpenByDomains(req *albvo.ControlTrafficFormRequest) ([]*sgwvo.NodeTaskResult, error)
}

func NewTrafficRepo(traceID string) TrafficRepo {
	return &albsvc.TrafficService{
		TraceID: traceID,
	}
}
