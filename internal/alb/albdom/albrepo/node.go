package albrepo

import (
	"context"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albsvc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

//go:generate mockgen -destination node_mock.go -package albrepo -source node.go

type NodeRepo interface {
	Add(ctx context.Context, req *albvo.ClusterNodeTicketRequest) ([]*core.ItemError, error)
	Retire(ctx context.Context, req *albvo.ClusterNodeTicketRequest) ([]*core.ItemError, error)
	Infos(ctx context.Context, nodes []*sgwvo.Node) []*albvo.NodeInfo
	InfosInInitialising(ctx context.Context, ips []string) []*albvo.NodeInfo
	FulfillTaskResults(ctx context.Context, node *albvo.NodeInfo) error
	State(ctx context.Context, ip string) (*albvo.NodeState, error)
	UpdateState(ctx context.Context, ip string, state *consts.State) error
	Runtime(ctx context.Context, ip string) (*v1alpha1.ALB, error)
	ListRuntime(ctx context.Context, req *albvo.NodeRequest) ([]*v1alpha1.ALB, error)
	UpdateSpec(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error)
	Tags(ctx context.Context, req *albvo.NodeRequest) (map[string]tocvo.Tags, error)
	TagVariables(ctx context.Context, req *albvo.NodeRequest) (map[string]*tocvo.ALBGroupVar, error)
	HotUpdate(ctx context.Context, req *albvo.HotUpdateNodeRequest) ([]*core.ItemError, error)
	HotUpdateOne(ctx context.Context, req *albvo.HotUpdateOneNodeRequest) error
	Rollback(ctx context.Context, req *albvo.HotUpdateNodeBaseRequest) ([]*core.ItemError, error)
	Abort(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error)
	Recover(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error)
	ReInit(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error)
	Reset(ctx context.Context, req *albvo.ResetNodeRequest) ([]*core.ItemError, error)
	GetStateNodes(ctx context.Context, req *sgwvo.StateNodesRequest) []*sgwvo.StateNodeInfo
	UpdateNodeTicket(ctx context.Context, req *sgwvo.UpdateNodesRequest) ([]*core.ItemError, error)
	ExpireNodes(ctx context.Context) ([]*core.ItemError, error)
}

func NewNodeRepo(traceID string) NodeRepo {
	return albsvc.NewNodeService(traceID)
}

type NodeComponentRepo interface {
	Dump(_ context.Context) (*albvo.NodeComponentVersion, error)
}

func NewNodeComponentRepo(traceID string, tocex toc.TocexAdapter) NodeComponentRepo {
	return &albsvc.NodeComponentService{
		TraceID: traceID,
		Node:    tocex,
	}
}

type NodeVerifierRepo interface {
	VerifyServerMeta(req *albvo.ClusterNodeVerifyRequest) error
}

func NewNodeVerifierRepo(traceID string) NodeVerifierRepo {
	return albsvc.NewNodeVerifierService(traceID)
}

type NodeCheckerRepo interface {
	PreCheck(ctx context.Context, req *albvo.ClusterNodeIPsRequest) ([]*sgwvo.NodeTaskResult, error)
	PreCheckOffline(req *albvo.ClusterNodeIPsRequest) ([]*sgwvo.NodeTaskResult, error)
	FetchPreCheckResult(ctx context.Context, ips []string) ([]*sgwvo.NodeTaskResult, error)
	FetchPreCheckOfflineResult(ctx context.Context, ips []string) ([]*sgwvo.NodeTaskResult, error)
}

func NewNodeCheckerRepo(traceID string) NodeCheckerRepo {
	return &albsvc.NodeCheckerService{
		TraceID:     traceID,
		Meta:        toc.NewMetaAdapter(traceID),
		Server:      toc.NewServerAdapter(traceID),
		ServerV3:    toc.NewServerV3Adapter(traceID),
		Cluster:     sgw.NewALBClusterAdapter(traceID),
		NLBInstance: sgw.NewNLBInstanceAdapter(traceID),
	}
}

type NodeProvisionRepo interface {
	Configs(ctx context.Context, req *albvo.NodeRequest) ([]*toclib.ProvisionNodeConfig, error)
	GenerateConfig(ctx context.Context, req *albvo.NodeProvisionComponentRequest) (*tocvo.NodeProvision, error)
	DeleteConfigs(ctx context.Context, configs []toclib.ProvisionNodeConfig) error
	DeleteTemplate(ctx context.Context, req *albvo.NodeProvisionTemplateRequest) ([]*core.ItemError, error)
	DeleteComponent(ctx context.Context, req *albvo.NodeComponentRequest) ([]*core.ItemError, error)
	UpdateComponent(_ context.Context, req *albvo.NodeUpdateComponentRequest) ([]*core.ItemError, error)
	PurgeContainers(_ context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error)
	PurgeConfigs(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error)
	Version(ip string) (*albvo.NodeComponentVersion, error)
}

func NewNodeProvisionRepo(traceID string) NodeProvisionRepo {
	return albsvc.NewNodeProvisionService(traceID)
}
