package albrepo

import (
	"context"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albsvc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/nlb/nlbvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

//go:generate mockgen -destination=cluster_mock.go -package=albrepo -source=cluster.go ClusterRepo

type ClusterRepo interface {
	GetByUUID(ctx context.Context, uuid string) (*albvo.Cluster, error)
	GetByNode(ctx context.Context, node string) (*albvo.Cluster, error)
	List(ctx context.Context) ([]*albvo.ClusterOverview, error)
	Nodes(ctx context.Context, uuid string) ([]*sgwvo.Node, error)
	NodesByIPs(_ context.Context, req *albvo.NodeRequest) ([]*sgwvo.Node, error)
	NLBListenersByUUID(ctx context.Context, uuid string) ([]*nlbvo.Listener, error)
	NodeNLBListeners(ctx context.Context, uuid string, nodeIP string) ([]*nlbvo.ListenerTarget, error)
	NodeNLBListenersByCluster(ctx context.Context, nodeIP string, cluster *albvo.Cluster) ([]*nlbvo.ListenerTarget, error)
	GetProductBlockListByRZAndNetwork(_ context.Context, rz string, networkType string,
		product string) ([]*albvo.Cluster, error)
	NLBListenerTargetsMapByUUID(ctx context.Context, uuid string) (map[string][]*nlbvo.ListenerTarget, error)
	Config(ctx context.Context, uuid string) (*sgwvo.ALBClusterConfigMeta, error)
	FreshConfig(ctx context.Context, req *albvo.FreshClusterConfigRequest) error
	SyncConfigFromTagVars(_ context.Context, uuid string) error
}

func NewClusterRepo(traceID string) ClusterRepo {
	return albsvc.NewClusterService(traceID)
}
