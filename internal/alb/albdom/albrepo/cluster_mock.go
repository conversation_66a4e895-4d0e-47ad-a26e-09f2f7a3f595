// Code generated by MockGen. DO NOT EDIT.
// Source: cluster.go

// Package albrepo is a generated GoMock package.
package albrepo

import (
	context "context"
	reflect "reflect"

	albvo "git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	nlbvo "git.garena.com/shopee/devops/sgw-addon-operator/internal/nlb/nlbvo"
	sgwvo "git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	gomock "github.com/golang/mock/gomock"
)

// MockClusterRepo is a mock of ClusterRepo interface.
type MockClusterRepo struct {
	ctrl     *gomock.Controller
	recorder *MockClusterRepoMockRecorder
}

// MockClusterRepoMockRecorder is the mock recorder for MockClusterRepo.
type MockClusterRepoMockRecorder struct {
	mock *MockClusterRepo
}

// NewMockClusterRepo creates a new mock instance.
func NewMockClusterRepo(ctrl *gomock.Controller) *MockClusterRepo {
	mock := &MockClusterRepo{ctrl: ctrl}
	mock.recorder = &MockClusterRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClusterRepo) EXPECT() *MockClusterRepoMockRecorder {
	return m.recorder
}

// Config mocks base method.
func (m *MockClusterRepo) Config(ctx context.Context, uuid string) (*sgwvo.ALBClusterConfigMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Config", ctx, uuid)
	ret0, _ := ret[0].(*sgwvo.ALBClusterConfigMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Config indicates an expected call of Config.
func (mr *MockClusterRepoMockRecorder) Config(ctx, uuid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Config", reflect.TypeOf((*MockClusterRepo)(nil).Config), ctx, uuid)
}

// FreshConfig mocks base method.
func (m *MockClusterRepo) FreshConfig(ctx context.Context, req *albvo.FreshClusterConfigRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreshConfig", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// FreshConfig indicates an expected call of FreshConfig.
func (mr *MockClusterRepoMockRecorder) FreshConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreshConfig", reflect.TypeOf((*MockClusterRepo)(nil).FreshConfig), ctx, req)
}

// GetByNode mocks base method.
func (m *MockClusterRepo) GetByNode(ctx context.Context, node string) (*albvo.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByNode", ctx, node)
	ret0, _ := ret[0].(*albvo.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByNode indicates an expected call of GetByNode.
func (mr *MockClusterRepoMockRecorder) GetByNode(ctx, node interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByNode", reflect.TypeOf((*MockClusterRepo)(nil).GetByNode), ctx, node)
}

// GetByUUID mocks base method.
func (m *MockClusterRepo) GetByUUID(ctx context.Context, uuid string) (*albvo.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByUUID", ctx, uuid)
	ret0, _ := ret[0].(*albvo.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByUUID indicates an expected call of GetByUUID.
func (mr *MockClusterRepoMockRecorder) GetByUUID(ctx, uuid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByUUID", reflect.TypeOf((*MockClusterRepo)(nil).GetByUUID), ctx, uuid)
}

// GetProductBlockListByRZAndNetwork mocks base method.
func (m *MockClusterRepo) GetProductBlockListByRZAndNetwork(arg0 context.Context, rz, networkType, product string) ([]*albvo.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProductBlockListByRZAndNetwork", arg0, rz, networkType, product)
	ret0, _ := ret[0].([]*albvo.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProductBlockListByRZAndNetwork indicates an expected call of GetProductBlockListByRZAndNetwork.
func (mr *MockClusterRepoMockRecorder) GetProductBlockListByRZAndNetwork(arg0, rz, networkType, product interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductBlockListByRZAndNetwork", reflect.TypeOf((*MockClusterRepo)(nil).GetProductBlockListByRZAndNetwork), arg0, rz, networkType, product)
}

// List mocks base method.
func (m *MockClusterRepo) List(ctx context.Context) ([]*albvo.ClusterOverview, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx)
	ret0, _ := ret[0].([]*albvo.ClusterOverview)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockClusterRepoMockRecorder) List(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockClusterRepo)(nil).List), ctx)
}

// NLBListenerTargetsMapByUUID mocks base method.
func (m *MockClusterRepo) NLBListenerTargetsMapByUUID(ctx context.Context, uuid string) (map[string][]*nlbvo.ListenerTarget, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NLBListenerTargetsMapByUUID", ctx, uuid)
	ret0, _ := ret[0].(map[string][]*nlbvo.ListenerTarget)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NLBListenerTargetsMapByUUID indicates an expected call of NLBListenerTargetsMapByUUID.
func (mr *MockClusterRepoMockRecorder) NLBListenerTargetsMapByUUID(ctx, uuid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NLBListenerTargetsMapByUUID", reflect.TypeOf((*MockClusterRepo)(nil).NLBListenerTargetsMapByUUID), ctx, uuid)
}

// NLBListenersByUUID mocks base method.
func (m *MockClusterRepo) NLBListenersByUUID(ctx context.Context, uuid string) ([]*nlbvo.Listener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NLBListenersByUUID", ctx, uuid)
	ret0, _ := ret[0].([]*nlbvo.Listener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NLBListenersByUUID indicates an expected call of NLBListenersByUUID.
func (mr *MockClusterRepoMockRecorder) NLBListenersByUUID(ctx, uuid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NLBListenersByUUID", reflect.TypeOf((*MockClusterRepo)(nil).NLBListenersByUUID), ctx, uuid)
}

// NodeNLBListeners mocks base method.
func (m *MockClusterRepo) NodeNLBListeners(ctx context.Context, uuid, nodeIP string) ([]*nlbvo.ListenerTarget, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeNLBListeners", ctx, uuid, nodeIP)
	ret0, _ := ret[0].([]*nlbvo.ListenerTarget)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NodeNLBListeners indicates an expected call of NodeNLBListeners.
func (mr *MockClusterRepoMockRecorder) NodeNLBListeners(ctx, uuid, nodeIP interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeNLBListeners", reflect.TypeOf((*MockClusterRepo)(nil).NodeNLBListeners), ctx, uuid, nodeIP)
}

// NodeNLBListenersByCluster mocks base method.
func (m *MockClusterRepo) NodeNLBListenersByCluster(ctx context.Context, nodeIP string, cluster *albvo.Cluster) ([]*nlbvo.ListenerTarget, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeNLBListenersByCluster", ctx, nodeIP, cluster)
	ret0, _ := ret[0].([]*nlbvo.ListenerTarget)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NodeNLBListenersByCluster indicates an expected call of NodeNLBListenersByCluster.
func (mr *MockClusterRepoMockRecorder) NodeNLBListenersByCluster(ctx, nodeIP, cluster interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeNLBListenersByCluster", reflect.TypeOf((*MockClusterRepo)(nil).NodeNLBListenersByCluster), ctx, nodeIP, cluster)
}

// Nodes mocks base method.
func (m *MockClusterRepo) Nodes(ctx context.Context, uuid string) ([]*sgwvo.Node, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Nodes", ctx, uuid)
	ret0, _ := ret[0].([]*sgwvo.Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Nodes indicates an expected call of Nodes.
func (mr *MockClusterRepoMockRecorder) Nodes(ctx, uuid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Nodes", reflect.TypeOf((*MockClusterRepo)(nil).Nodes), ctx, uuid)
}

// NodesByIPs mocks base method.
func (m *MockClusterRepo) NodesByIPs(arg0 context.Context, req *albvo.NodeRequest) ([]*sgwvo.Node, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodesByIPs", arg0, req)
	ret0, _ := ret[0].([]*sgwvo.Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NodesByIPs indicates an expected call of NodesByIPs.
func (mr *MockClusterRepoMockRecorder) NodesByIPs(arg0, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodesByIPs", reflect.TypeOf((*MockClusterRepo)(nil).NodesByIPs), arg0, req)
}

// SyncConfigFromTagVars mocks base method.
func (m *MockClusterRepo) SyncConfigFromTagVars(arg0 context.Context, uuid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncConfigFromTagVars", arg0, uuid)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncConfigFromTagVars indicates an expected call of SyncConfigFromTagVars.
func (mr *MockClusterRepoMockRecorder) SyncConfigFromTagVars(arg0, uuid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncConfigFromTagVars", reflect.TypeOf((*MockClusterRepo)(nil).SyncConfigFromTagVars), arg0, uuid)
}
