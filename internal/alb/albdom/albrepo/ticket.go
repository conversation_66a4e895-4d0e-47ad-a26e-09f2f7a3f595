package albrepo

import (
	"context"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albsvc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/swp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/swp/swpvo"
)

// TicketRepo interface for ticket
type TicketRepo interface {
	Add(ctx context.Context, req *albvo.ClusterNodeTicketRequest) (*swpvo.Ticket, error)
	Retire(ctx context.Context, req *albvo.ClusterNodeTicketRequest) (*albvo.TicketData, error)
	Online(ctx context.Context, req *albvo.TicketRequest) (*albvo.TicketData, error)
	Offline(ctx context.Context, req *albvo.TicketRequest) (*albvo.TicketData, error)
	HotUpdate(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error)
	Block(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error)
	BlockByDomains(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error)
	Open(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error)
	OpenByDomains(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error)
	FreshClusterConfig(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error)
}

// NewTicketRepo new ticket repo
func NewTicketRepo(traceID string) TicketRepo {
	return &albsvc.TicketService{
		TraceID:    traceID,
		Ticket:     swp.NewTicketAdapter(traceID),
		Controller: albctl.ALBController,
	}
}
