package albrepo

import (
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albsvc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
)

type ComponentRepo interface {
	Versions(req *albvo.ComponentRequest) ([]*albvo.ComponentVersion, error)
	// IsVersionExist checks if a version exists.
	//
	// It takes a ComponentVersionRequest parameter and returns a boolean value and an error.
	IsVersionExist(req *albvo.ComponentVersionRequest) (bool, error)
}

func NewComponentRepo(traceID string) ComponentRepo {
	return albsvc.NewComponentService(traceID)
}
