// Code generated by MockGen. DO NOT EDIT.
// Source: node.go

// Package albrepo is a generated GoMock package.
package albrepo

import (
	context "context"
	reflect "reflect"

	v1alpha1 "git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	albvo "git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	consts "git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	core "git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	sgwvo "git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	tocvo "git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	toclib "git.garena.com/shopee/go-shopeelib/toclib"
	gomock "github.com/golang/mock/gomock"
)

// MockNodeRepo is a mock of NodeRepo interface.
type MockNodeRepo struct {
	ctrl     *gomock.Controller
	recorder *MockNodeRepoMockRecorder
}

// MockNodeRepoMockRecorder is the mock recorder for MockNodeRepo.
type MockNodeRepoMockRecorder struct {
	mock *MockNodeRepo
}

// NewMockNodeRepo creates a new mock instance.
func NewMockNodeRepo(ctrl *gomock.Controller) *MockNodeRepo {
	mock := &MockNodeRepo{ctrl: ctrl}
	mock.recorder = &MockNodeRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNodeRepo) EXPECT() *MockNodeRepoMockRecorder {
	return m.recorder
}

// Abort mocks base method.
func (m *MockNodeRepo) Abort(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Abort", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Abort indicates an expected call of Abort.
func (mr *MockNodeRepoMockRecorder) Abort(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Abort", reflect.TypeOf((*MockNodeRepo)(nil).Abort), ctx, req)
}

// Add mocks base method.
func (m *MockNodeRepo) Add(ctx context.Context, req *albvo.ClusterNodeTicketRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Add", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Add indicates an expected call of Add.
func (mr *MockNodeRepoMockRecorder) Add(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockNodeRepo)(nil).Add), ctx, req)
}

// ExpireNodes mocks base method.
func (m *MockNodeRepo) ExpireNodes(ctx context.Context) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireNodes", ctx)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExpireNodes indicates an expected call of ExpireNodes.
func (mr *MockNodeRepoMockRecorder) ExpireNodes(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireNodes", reflect.TypeOf((*MockNodeRepo)(nil).ExpireNodes), ctx)
}

// FulfillTaskResults mocks base method.
func (m *MockNodeRepo) FulfillTaskResults(ctx context.Context, node *albvo.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FulfillTaskResults", ctx, node)
	ret0, _ := ret[0].(error)
	return ret0
}

// FulfillTaskResults indicates an expected call of FulfillTaskResults.
func (mr *MockNodeRepoMockRecorder) FulfillTaskResults(ctx, node interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FulfillTaskResults", reflect.TypeOf((*MockNodeRepo)(nil).FulfillTaskResults), ctx, node)
}

// GetStateNodes mocks base method.
func (m *MockNodeRepo) GetStateNodes(ctx context.Context, req *sgwvo.StateNodesRequest) []*sgwvo.StateNodeInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStateNodes", ctx, req)
	ret0, _ := ret[0].([]*sgwvo.StateNodeInfo)
	return ret0
}

// GetStateNodes indicates an expected call of GetStateNodes.
func (mr *MockNodeRepoMockRecorder) GetStateNodes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStateNodes", reflect.TypeOf((*MockNodeRepo)(nil).GetStateNodes), ctx, req)
}

// HotUpdate mocks base method.
func (m *MockNodeRepo) HotUpdate(ctx context.Context, req *albvo.HotUpdateNodeRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HotUpdate", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HotUpdate indicates an expected call of HotUpdate.
func (mr *MockNodeRepoMockRecorder) HotUpdate(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HotUpdate", reflect.TypeOf((*MockNodeRepo)(nil).HotUpdate), ctx, req)
}

// HotUpdateOne mocks base method.
func (m *MockNodeRepo) HotUpdateOne(ctx context.Context, req *albvo.HotUpdateOneNodeRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HotUpdateOne", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// HotUpdateOne indicates an expected call of HotUpdateOne.
func (mr *MockNodeRepoMockRecorder) HotUpdateOne(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HotUpdateOne", reflect.TypeOf((*MockNodeRepo)(nil).HotUpdateOne), ctx, req)
}

// Infos mocks base method.
func (m *MockNodeRepo) Infos(ctx context.Context, nodes []*sgwvo.Node) []*albvo.NodeInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Infos", ctx, nodes)
	ret0, _ := ret[0].([]*albvo.NodeInfo)
	return ret0
}

// Infos indicates an expected call of Infos.
func (mr *MockNodeRepoMockRecorder) Infos(ctx, nodes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Infos", reflect.TypeOf((*MockNodeRepo)(nil).Infos), ctx, nodes)
}

// InfosInInitialising mocks base method.
func (m *MockNodeRepo) InfosInInitialising(ctx context.Context, ips []string) []*albvo.NodeInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InfosInInitialising", ctx, ips)
	ret0, _ := ret[0].([]*albvo.NodeInfo)
	return ret0
}

// InfosInInitialising indicates an expected call of InfosInInitialising.
func (mr *MockNodeRepoMockRecorder) InfosInInitialising(ctx, ips interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InfosInInitialising", reflect.TypeOf((*MockNodeRepo)(nil).InfosInInitialising), ctx, ips)
}

// ListRuntime mocks base method.
func (m *MockNodeRepo) ListRuntime(ctx context.Context, req *albvo.NodeRequest) ([]*v1alpha1.ALB, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRuntime", ctx, req)
	ret0, _ := ret[0].([]*v1alpha1.ALB)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRuntime indicates an expected call of ListRuntime.
func (mr *MockNodeRepoMockRecorder) ListRuntime(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRuntime", reflect.TypeOf((*MockNodeRepo)(nil).ListRuntime), ctx, req)
}

// ReInit mocks base method.
func (m *MockNodeRepo) ReInit(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReInit", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReInit indicates an expected call of ReInit.
func (mr *MockNodeRepoMockRecorder) ReInit(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReInit", reflect.TypeOf((*MockNodeRepo)(nil).ReInit), ctx, req)
}

// Recover mocks base method.
func (m *MockNodeRepo) Recover(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recover", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recover indicates an expected call of Recover.
func (mr *MockNodeRepoMockRecorder) Recover(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recover", reflect.TypeOf((*MockNodeRepo)(nil).Recover), ctx, req)
}

// Reset mocks base method.
func (m *MockNodeRepo) Reset(ctx context.Context, req *albvo.ResetNodeRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reset", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Reset indicates an expected call of Reset.
func (mr *MockNodeRepoMockRecorder) Reset(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reset", reflect.TypeOf((*MockNodeRepo)(nil).Reset), ctx, req)
}

// Retire mocks base method.
func (m *MockNodeRepo) Retire(ctx context.Context, req *albvo.ClusterNodeTicketRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Retire", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Retire indicates an expected call of Retire.
func (mr *MockNodeRepoMockRecorder) Retire(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Retire", reflect.TypeOf((*MockNodeRepo)(nil).Retire), ctx, req)
}

// Rollback mocks base method.
func (m *MockNodeRepo) Rollback(ctx context.Context, req *albvo.HotUpdateNodeBaseRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Rollback", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Rollback indicates an expected call of Rollback.
func (mr *MockNodeRepoMockRecorder) Rollback(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Rollback", reflect.TypeOf((*MockNodeRepo)(nil).Rollback), ctx, req)
}

// Runtime mocks base method.
func (m *MockNodeRepo) Runtime(ctx context.Context, ip string) (*v1alpha1.ALB, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Runtime", ctx, ip)
	ret0, _ := ret[0].(*v1alpha1.ALB)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Runtime indicates an expected call of Runtime.
func (mr *MockNodeRepoMockRecorder) Runtime(ctx, ip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Runtime", reflect.TypeOf((*MockNodeRepo)(nil).Runtime), ctx, ip)
}

// State mocks base method.
func (m *MockNodeRepo) State(ctx context.Context, ip string) (*albvo.NodeState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "State", ctx, ip)
	ret0, _ := ret[0].(*albvo.NodeState)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// State indicates an expected call of State.
func (mr *MockNodeRepoMockRecorder) State(ctx, ip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "State", reflect.TypeOf((*MockNodeRepo)(nil).State), ctx, ip)
}

// TagVariables mocks base method.
func (m *MockNodeRepo) TagVariables(ctx context.Context, req *albvo.NodeRequest) (map[string]*tocvo.ALBGroupVar, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TagVariables", ctx, req)
	ret0, _ := ret[0].(map[string]*tocvo.ALBGroupVar)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TagVariables indicates an expected call of TagVariables.
func (mr *MockNodeRepoMockRecorder) TagVariables(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TagVariables", reflect.TypeOf((*MockNodeRepo)(nil).TagVariables), ctx, req)
}

// Tags mocks base method.
func (m *MockNodeRepo) Tags(ctx context.Context, req *albvo.NodeRequest) (map[string]tocvo.Tags, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tags", ctx, req)
	ret0, _ := ret[0].(map[string]tocvo.Tags)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Tags indicates an expected call of Tags.
func (mr *MockNodeRepoMockRecorder) Tags(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tags", reflect.TypeOf((*MockNodeRepo)(nil).Tags), ctx, req)
}

// UpdateNodeTicket mocks base method.
func (m *MockNodeRepo) UpdateNodeTicket(ctx context.Context, req *sgwvo.UpdateNodesRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNodeTicket", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNodeTicket indicates an expected call of UpdateNodeTicket.
func (mr *MockNodeRepoMockRecorder) UpdateNodeTicket(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNodeTicket", reflect.TypeOf((*MockNodeRepo)(nil).UpdateNodeTicket), ctx, req)
}

// UpdateSpec mocks base method.
func (m *MockNodeRepo) UpdateSpec(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSpec", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSpec indicates an expected call of UpdateSpec.
func (mr *MockNodeRepoMockRecorder) UpdateSpec(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSpec", reflect.TypeOf((*MockNodeRepo)(nil).UpdateSpec), ctx, req)
}

// UpdateState mocks base method.
func (m *MockNodeRepo) UpdateState(ctx context.Context, ip string, state *consts.State) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateState", ctx, ip, state)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateState indicates an expected call of UpdateState.
func (mr *MockNodeRepoMockRecorder) UpdateState(ctx, ip, state interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateState", reflect.TypeOf((*MockNodeRepo)(nil).UpdateState), ctx, ip, state)
}

// MockNodeComponentRepo is a mock of NodeComponentRepo interface.
type MockNodeComponentRepo struct {
	ctrl     *gomock.Controller
	recorder *MockNodeComponentRepoMockRecorder
}

// MockNodeComponentRepoMockRecorder is the mock recorder for MockNodeComponentRepo.
type MockNodeComponentRepoMockRecorder struct {
	mock *MockNodeComponentRepo
}

// NewMockNodeComponentRepo creates a new mock instance.
func NewMockNodeComponentRepo(ctrl *gomock.Controller) *MockNodeComponentRepo {
	mock := &MockNodeComponentRepo{ctrl: ctrl}
	mock.recorder = &MockNodeComponentRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNodeComponentRepo) EXPECT() *MockNodeComponentRepoMockRecorder {
	return m.recorder
}

// Dump mocks base method.
func (m *MockNodeComponentRepo) Dump(arg0 context.Context) (*albvo.NodeComponentVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dump", arg0)
	ret0, _ := ret[0].(*albvo.NodeComponentVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Dump indicates an expected call of Dump.
func (mr *MockNodeComponentRepoMockRecorder) Dump(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dump", reflect.TypeOf((*MockNodeComponentRepo)(nil).Dump), arg0)
}

// MockNodeVerifierRepo is a mock of NodeVerifierRepo interface.
type MockNodeVerifierRepo struct {
	ctrl     *gomock.Controller
	recorder *MockNodeVerifierRepoMockRecorder
}

// MockNodeVerifierRepoMockRecorder is the mock recorder for MockNodeVerifierRepo.
type MockNodeVerifierRepoMockRecorder struct {
	mock *MockNodeVerifierRepo
}

// NewMockNodeVerifierRepo creates a new mock instance.
func NewMockNodeVerifierRepo(ctrl *gomock.Controller) *MockNodeVerifierRepo {
	mock := &MockNodeVerifierRepo{ctrl: ctrl}
	mock.recorder = &MockNodeVerifierRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNodeVerifierRepo) EXPECT() *MockNodeVerifierRepoMockRecorder {
	return m.recorder
}

// VerifyServerMeta mocks base method.
func (m *MockNodeVerifierRepo) VerifyServerMeta(req *albvo.ClusterNodeVerifyRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyServerMeta", req)
	ret0, _ := ret[0].(error)
	return ret0
}

// VerifyServerMeta indicates an expected call of VerifyServerMeta.
func (mr *MockNodeVerifierRepoMockRecorder) VerifyServerMeta(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyServerMeta", reflect.TypeOf((*MockNodeVerifierRepo)(nil).VerifyServerMeta), req)
}

// MockNodeCheckerRepo is a mock of NodeCheckerRepo interface.
type MockNodeCheckerRepo struct {
	ctrl     *gomock.Controller
	recorder *MockNodeCheckerRepoMockRecorder
}

// MockNodeCheckerRepoMockRecorder is the mock recorder for MockNodeCheckerRepo.
type MockNodeCheckerRepoMockRecorder struct {
	mock *MockNodeCheckerRepo
}

// NewMockNodeCheckerRepo creates a new mock instance.
func NewMockNodeCheckerRepo(ctrl *gomock.Controller) *MockNodeCheckerRepo {
	mock := &MockNodeCheckerRepo{ctrl: ctrl}
	mock.recorder = &MockNodeCheckerRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNodeCheckerRepo) EXPECT() *MockNodeCheckerRepoMockRecorder {
	return m.recorder
}

// FetchPreCheckOfflineResult mocks base method.
func (m *MockNodeCheckerRepo) FetchPreCheckOfflineResult(ctx context.Context, ips []string) ([]*sgwvo.NodeTaskResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchPreCheckOfflineResult", ctx, ips)
	ret0, _ := ret[0].([]*sgwvo.NodeTaskResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPreCheckOfflineResult indicates an expected call of FetchPreCheckOfflineResult.
func (mr *MockNodeCheckerRepoMockRecorder) FetchPreCheckOfflineResult(ctx, ips interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPreCheckOfflineResult", reflect.TypeOf((*MockNodeCheckerRepo)(nil).FetchPreCheckOfflineResult), ctx, ips)
}

// FetchPreCheckResult mocks base method.
func (m *MockNodeCheckerRepo) FetchPreCheckResult(ctx context.Context, ips []string) ([]*sgwvo.NodeTaskResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchPreCheckResult", ctx, ips)
	ret0, _ := ret[0].([]*sgwvo.NodeTaskResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPreCheckResult indicates an expected call of FetchPreCheckResult.
func (mr *MockNodeCheckerRepoMockRecorder) FetchPreCheckResult(ctx, ips interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPreCheckResult", reflect.TypeOf((*MockNodeCheckerRepo)(nil).FetchPreCheckResult), ctx, ips)
}

// PreCheck mocks base method.
func (m *MockNodeCheckerRepo) PreCheck(ctx context.Context, req *albvo.ClusterNodeIPsRequest) ([]*sgwvo.NodeTaskResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PreCheck", ctx, req)
	ret0, _ := ret[0].([]*sgwvo.NodeTaskResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PreCheck indicates an expected call of PreCheck.
func (mr *MockNodeCheckerRepoMockRecorder) PreCheck(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreCheck", reflect.TypeOf((*MockNodeCheckerRepo)(nil).PreCheck), ctx, req)
}

// PreCheckOffline mocks base method.
func (m *MockNodeCheckerRepo) PreCheckOffline(req *albvo.ClusterNodeIPsRequest) ([]*sgwvo.NodeTaskResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PreCheckOffline", req)
	ret0, _ := ret[0].([]*sgwvo.NodeTaskResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PreCheckOffline indicates an expected call of PreCheckOffline.
func (mr *MockNodeCheckerRepoMockRecorder) PreCheckOffline(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreCheckOffline", reflect.TypeOf((*MockNodeCheckerRepo)(nil).PreCheckOffline), req)
}

// MockNodeProvisionRepo is a mock of NodeProvisionRepo interface.
type MockNodeProvisionRepo struct {
	ctrl     *gomock.Controller
	recorder *MockNodeProvisionRepoMockRecorder
}

// MockNodeProvisionRepoMockRecorder is the mock recorder for MockNodeProvisionRepo.
type MockNodeProvisionRepoMockRecorder struct {
	mock *MockNodeProvisionRepo
}

// NewMockNodeProvisionRepo creates a new mock instance.
func NewMockNodeProvisionRepo(ctrl *gomock.Controller) *MockNodeProvisionRepo {
	mock := &MockNodeProvisionRepo{ctrl: ctrl}
	mock.recorder = &MockNodeProvisionRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNodeProvisionRepo) EXPECT() *MockNodeProvisionRepoMockRecorder {
	return m.recorder
}

// Configs mocks base method.
func (m *MockNodeProvisionRepo) Configs(ctx context.Context, req *albvo.NodeRequest) ([]*toclib.ProvisionNodeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Configs", ctx, req)
	ret0, _ := ret[0].([]*toclib.ProvisionNodeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Configs indicates an expected call of Configs.
func (mr *MockNodeProvisionRepoMockRecorder) Configs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Configs", reflect.TypeOf((*MockNodeProvisionRepo)(nil).Configs), ctx, req)
}

// DeleteComponent mocks base method.
func (m *MockNodeProvisionRepo) DeleteComponent(ctx context.Context, req *albvo.NodeComponentRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteComponent", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteComponent indicates an expected call of DeleteComponent.
func (mr *MockNodeProvisionRepoMockRecorder) DeleteComponent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteComponent", reflect.TypeOf((*MockNodeProvisionRepo)(nil).DeleteComponent), ctx, req)
}

// DeleteConfigs mocks base method.
func (m *MockNodeProvisionRepo) DeleteConfigs(ctx context.Context, configs []toclib.ProvisionNodeConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteConfigs", ctx, configs)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteConfigs indicates an expected call of DeleteConfigs.
func (mr *MockNodeProvisionRepoMockRecorder) DeleteConfigs(ctx, configs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConfigs", reflect.TypeOf((*MockNodeProvisionRepo)(nil).DeleteConfigs), ctx, configs)
}

// DeleteTemplate mocks base method.
func (m *MockNodeProvisionRepo) DeleteTemplate(ctx context.Context, req *albvo.NodeProvisionTemplateRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTemplate", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTemplate indicates an expected call of DeleteTemplate.
func (mr *MockNodeProvisionRepoMockRecorder) DeleteTemplate(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTemplate", reflect.TypeOf((*MockNodeProvisionRepo)(nil).DeleteTemplate), ctx, req)
}

// GenerateConfig mocks base method.
func (m *MockNodeProvisionRepo) GenerateConfig(ctx context.Context, req *albvo.NodeProvisionComponentRequest) (*tocvo.NodeProvision, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateConfig", ctx, req)
	ret0, _ := ret[0].(*tocvo.NodeProvision)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateConfig indicates an expected call of GenerateConfig.
func (mr *MockNodeProvisionRepoMockRecorder) GenerateConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateConfig", reflect.TypeOf((*MockNodeProvisionRepo)(nil).GenerateConfig), ctx, req)
}

// PurgeConfigs mocks base method.
func (m *MockNodeProvisionRepo) PurgeConfigs(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PurgeConfigs", ctx, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PurgeConfigs indicates an expected call of PurgeConfigs.
func (mr *MockNodeProvisionRepoMockRecorder) PurgeConfigs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PurgeConfigs", reflect.TypeOf((*MockNodeProvisionRepo)(nil).PurgeConfigs), ctx, req)
}

// PurgeContainers mocks base method.
func (m *MockNodeProvisionRepo) PurgeContainers(arg0 context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PurgeContainers", arg0, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PurgeContainers indicates an expected call of PurgeContainers.
func (mr *MockNodeProvisionRepoMockRecorder) PurgeContainers(arg0, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PurgeContainers", reflect.TypeOf((*MockNodeProvisionRepo)(nil).PurgeContainers), arg0, req)
}

// UpdateComponent mocks base method.
func (m *MockNodeProvisionRepo) UpdateComponent(arg0 context.Context, req *albvo.NodeUpdateComponentRequest) ([]*core.ItemError, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateComponent", arg0, req)
	ret0, _ := ret[0].([]*core.ItemError)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateComponent indicates an expected call of UpdateComponent.
func (mr *MockNodeProvisionRepoMockRecorder) UpdateComponent(arg0, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateComponent", reflect.TypeOf((*MockNodeProvisionRepo)(nil).UpdateComponent), arg0, req)
}

// Version mocks base method.
func (m *MockNodeProvisionRepo) Version(ip string) (*albvo.NodeComponentVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Version", ip)
	ret0, _ := ret[0].(*albvo.NodeComponentVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Version indicates an expected call of Version.
func (mr *MockNodeProvisionRepoMockRecorder) Version(ip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Version", reflect.TypeOf((*MockNodeProvisionRepo)(nil).Version), ip)
}
