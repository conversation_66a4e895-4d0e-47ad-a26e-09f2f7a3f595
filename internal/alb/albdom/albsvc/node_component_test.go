package albsvc

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

//nolint:nolintlint,lll
func TestNodeService_Components(t *testing.T) {
	t.<PERSON>llel()

	t.Run("local", func(t *testing.T) {
		if ciEnv {
			return
		}

		component := NodeComponentService{
			TraceID: t.Name(),
			Node: toc.NewTocexAdapter(&tocvo.Node{
				HostIP: configs.E2E.ALB.Node2,
				AZ:     "sg2",
				Env:    "test",
			}),
		}

		ver, err := component.Dump(context.Background())
		assert.NoError(t, err)
		assert.NotEmpty(t, ver)
	})

	t.Run("mock", func(t *testing.T) {
		t.<PERSON>llel()

		cmd := `docker ps --all --no-trunc --format "{{json .}}"`
		tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
		//nolint:lll
		tocex.EXPECT().RunTask(gomock.Eq(cmd)).Return(`
{"Command":"\"smb run --env=test --cid=sg --idc=sg2\"","CreatedAt":"2024-12-10 18:34:01 +0800 +08","ID":"2cf21f8f5a12f33fdc0f53d648b83dc11eed26a843f3465665adbf97d2e33aec","Image":"harbor.test.shopeemobile.com/shopee/alb-sd-test-sg:v1.1.2","Labels":"","LocalVolumes":"0","Mounts":"/etc/nginx/service_deps,/etc/resolv.conf,/etc/sgw,/data/log/alb-sd-test-sg,/data/tmp/alb-sd-test-sg,/etc/hosts,/etc/mesos,/etc/mesos-lb","Names":"alb-sd","Networks":"host","Ports":"","RunningFor":"4 weeks ago","Size":"1.07kB (virtual 2.87GB)","State":"running","Status":"Up 3 weeks"}
{"Command":"\"smb run --env=test --cid=sg --idc=sg2\"","CreatedAt":"2024-12-10 18:32:32 +0800 +08","ID":"a7dfb57d822bfe17a1827ad37e90cec5345fa8980a2bd475f016702bd69d4a7a","Image":"harbor.test.shopeemobile.com/shopee/alb-metrics-test-sg:v1.1.10","Labels":"","LocalVolumes":"0","Mounts":"/etc/mesos-lb,/data/tmp/alb-metrics-test-sg,/etc/hosts,/etc/mesos,/etc/nginx,/etc/nginx/service_deps,/etc/resolv.conf,/etc/sgw,/data/log/alb-metrics-test-sg","Names":"alb-metrics","Networks":"host","Ports":"","RunningFor":"4 weeks ago","Size":"1.12kB (virtual 4.09GB)","State":"running","Status":"Up 4 weeks"}
`, nil).AnyTimes()
		tocex.EXPECT().RunTask(gomock.Eq(`dpkg-query --list |grep -E 'docker-shopee|docker-shopee-dev|lxcfs-shopee|shopee-driver-manager' | awk '{print $2,$3}'`)).
			Return(`docker-shopee-dev 20.10.8-shopee-237
lxcfs-shopee 4.0.7-3
shopee-driver-manager 1.0.2`, nil).AnyTimes()
		tocex.EXPECT().RunTask(`nginx -v 2>&1 | awk -F '/' '{print $2}'`).Return("1.21.3-1.5.3", nil).AnyTimes()

		component := NodeComponentService{
			TraceID: t.Name(),
			Node:    tocex,
		}

		ver, err := component.Dump(context.Background())
		assert.NoError(t, err)
		assert.NotEmpty(t, ver)
	})
}
