package albsvc

import (
	"context"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	"git.garena.com/shopee/devops/toc-sdk/cmdb"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

func TestNodeService_GenALBCR(t *testing.T) {
	node := NodeService{TraceID: t.Name()}

	ip := gofakeit.IPv4Address()
	okUUID := gofakeit.UUID()

	cluster := sgw.NewMockALBClusterAdapter(gomock.NewController(t))
	cluster.EXPECT().Meta(gomock.Any()).DoAndReturn(func(uuid string) (*sgwvo.ALBClusterConfigMeta, error) {
		switch uuid {
		case okUUID:
			meta := &sgwvo.ALBClusterConfigMeta{}
			meta.RZ = "idc1"

			return meta, nil
		default:
			return nil, errors.New("fetch_cluster_meta_failed")
		}
	}).AnyTimes()

	controller := albctl.NewMockController(gomock.NewController(t))
	controller.EXPECT().GetComponentVersion(gomock.Any(), gomock.Any()).DoAndReturn(
		func(rz, _ string) (*albvo.NodeComponentVersion, error) {
			switch rz {
			case "idc1":
				return &albvo.NodeComponentVersion{
					ALBMetrics: consts.UnknownVersion,
				}, nil
			default:
				return fetchFakeNodeComponentVersion(), nil
			}
		}).AnyTimes()
	controller.EXPECT().GetComponentVersionByAZ(gomock.Any()).Return(fetchFakeNodeComponentVersion()).AnyTimes()

	serverV3 := toc.NewMockServerV3Adapter(gomock.NewController(t))
	serverV3.EXPECT().Get(gomock.Any()).DoAndReturn(func(ip string) (*cmdb.Server, error) {
		svr := &cmdb.Server{}
		assert.NoError(t, gofakeit.Struct(svr))

		return svr, nil
	}).AnyTimes()

	node.ALBCluster = cluster
	node.Controller = controller
	node.ServerV3 = serverV3

	_, err := node.GenALBCR(&albvo.ClusterNodeTicketRequest{UUID: gofakeit.UUID()}, ip)
	require.Error(t, err)
	assert.Contains(t, err.Error(), "fetch_alb_meta_failed")

	cr, err := node.GenALBCR(&albvo.ClusterNodeTicketRequest{UUID: okUUID}, ip)
	assert.NoError(t, err)
	assert.NotNil(t, cr)
}

func TestNode_UpdateSpec(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	t.Run("mock_ok", func(t *testing.T) {
		t.Parallel()

		node := newTestNodeService(t)

		fakeit := gofakeit.New(time.Now().Unix())
		ip := fakeit.IPv4Address()
		testALBCR := &v1alpha1.ALB{
			Spec: v1alpha1.ALBSpec{
				LanIP: ip,
			},
		}

		testCluster := &albvo.Cluster{
			UUID:    fakeit.UUID(),
			AZ:      fakeit.Word(),
			Segment: fakeit.Word(),
			RZ:      fakeit.Word(),
		}

		if ctl, ok := node.Controller.(*albctl.MockController); ok {
			ctl.EXPECT().GetWithContext(gomock.Any(), albvo.CRName(ip)).Return(testALBCR, nil).AnyTimes()
			ctl.EXPECT().UpdateWithContext(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		}

		if cls, ok := node.Cluster.(*sgw.MockL7ClusterAdapter); ok {
			cls.EXPECT().GetByNode(ip).Return(testCluster, nil).AnyTimes()
		}

		if meta, ok := node.Meta.(*toc.MockMetaAdapter); ok {
			meta.EXPECT().AZ(gomock.Eq(testCluster.AZ)).Return(&tocvo.AZ{
				AZType: fakeit.Word(),
			}, nil)
		}

		if svr, ok := node.ServerV3.(*toc.MockServerV3Adapter); ok {
			svr.EXPECT().Get(gomock.Eq(ip)).Return(&cmdb.Server{
				Cluster: "Shopee",
			}, nil)
		}

		errs, err := node.UpdateSpec(context.Background(), &albvo.NodeRequest{
			IPs: []string{ip},
		})

		assert.NoError(t, err)
		assert.Empty(t, errs)

		assert.Equal(t, testALBCR.Spec.LanIP, ip)
		assert.Equal(t, testALBCR.Spec.ALBNode.IDC, testCluster.RZ)
		assert.Equal(t, testALBCR.Spec.ALBNode.Segment, testCluster.Segment)
		assert.Equal(t, testALBCR.Spec.ALBNode.Zone, testCluster.AZ)
		assert.Equal(t, testALBCR.Spec.ALBNode.ClusterUUID, testCluster.UUID)
	})

	t.Run("test_env", func(t *testing.T) {
		t.Parallel()

		if ciEnv {
			t.Skip("skipping test in CI environment")
		}

		ip := configs.E2E.ALB.Node2
		testALBCR := &v1alpha1.ALB{
			Spec: v1alpha1.ALBSpec{
				LanIP: ip,
			},
		}

		albCtrl := albctl.NewMockController(ctrl)
		albCtrl.EXPECT().GetWithContext(gomock.Any(), albvo.CRName(ip)).Return(testALBCR, nil).AnyTimes()
		albCtrl.EXPECT().UpdateWithContext(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		node := NewNodeService(t.Name())
		// NB: mock the controller to avoid real API calls
		node.Controller = albCtrl

		errs, err := node.UpdateSpec(context.Background(), &albvo.NodeRequest{
			IPs: []string{ip},
		})

		assert.NoError(t, err)
		assert.Empty(t, errs)
	})
}

func newTestNodeService(t *testing.T) *NodeService {
	t.Helper()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	server := toc.NewMockServerAdapter(ctrl)
	controller := albctl.NewMockController(ctrl)
	cluster := sgw.NewMockL7ClusterAdapter(ctrl)
	meta := toc.NewMockMetaAdapter(ctrl)
	serverV3 := toc.NewMockServerV3Adapter(ctrl)

	node := NodeService{
		TraceID:    t.Name(),
		Meta:       meta,
		Server:     server,
		ServerV3:   serverV3,
		Controller: controller,
		Cluster:    cluster,
	}

	return &node
}
