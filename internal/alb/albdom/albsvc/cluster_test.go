package albsvc

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
)

func TestClusterService_NLBListenersByUUID(t *testing.T) {
	cls := ClusterService{L7Cluster: sgw.NewALBClusterAdapter(t.Name())}

	// fa4f3ea1-f562-52c8-a01d-2d8b04ab9911  alb.standard.az.8
	ret, err := cls.NLBListenersByUUID(context.Background(), "fa4f3ea1-f562-52c8-a01d-2d8b04ab9911")
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(ret), 2) // 2 80 & 443
}
