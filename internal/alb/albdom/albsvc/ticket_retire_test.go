package albsvc

import (
	"context"
	"fmt"
	"testing"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/swp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/swp/swpvo"
)

// TestTicketService_Retire_ValidationError tests that the error we're seeing in production
// is properly handled when the ALB CR update fails due to missing required fields
func TestTicketService_Retire_ValidationError(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	fakeit := gofakeit.New(time.Now().Unix())

	mockController := albctl.NewMockController(ctrl)
	mockTicket := swp.NewMockTicketAdapter(ctrl)

	service := &TicketService{
		TraceID:    fakeit.UUID(),
		Ticket:     mockTicket,
		Controller: mockController,
	}

	testIP := "************" // Keep the actual IP from the error message
	testTicketID := fakeit.Number(1000000, 9999999)

	// Create ALB CR with standard fields
	existingALB := &v1alpha1.ALB{
		ObjectMeta: metav1.ObjectMeta{
			Name: albvo.CRName(testIP),
		},
		Spec: v1alpha1.ALBSpec{
			LanIP: testIP,
			ALBNode: v1alpha1.ALBNode{
				ClusterUUID:      fakeit.UUID(),
				ClusterName:      fakeit.Word(),
				SDU:              fakeit.Word(),
				ALBSdImageTag:    fakeit.Word() + ":v" + fakeit.AppVersion(),
				ALBAgentImageTag: fakeit.Word() + ":v" + fakeit.AppVersion(),
				NginxLbImageTag:  fakeit.Word() + ":v" + fakeit.AppVersion(),
				MetricsImageTag:  fakeit.Word() + ":v" + fakeit.AppVersion(),
				NginxVersion:     fakeit.AppVersion(),
				Options:          make(map[string]string),
			},
		},
		Status: v1alpha1.ALBStatus{
			State:  consts.RunningState.Status,
			Reason: consts.RunningState.Reason,
		},
	}

	req := &albvo.ClusterNodeTicketRequest{
		IPs:           []string{testIP},
		UUID:          existingALB.Spec.ALBNode.ClusterUUID,
		Cluster:       existingALB.Spec.ALBNode.ClusterName,
		SDU:           existingALB.Spec.ALBNode.SDU,
		Token:         fakeit.Password(true, true, true, false, false, 32),
		ApplicantUser: fakeit.Email(),
	}

	swpTicketResp := &swpvo.Ticket{
		ID:            testTicketID,
		ApplicantUser: req.ApplicantUser,
	}

	// Setup mocks
	mockTicket.EXPECT().SetToken(req.Token).Times(1)
	mockTicket.EXPECT().Create(gomock.Any()).Return(swpTicketResp, nil).Times(1)
	mockController.EXPECT().GetWithContext(gomock.Any(), albvo.CRName(testIP)).Return(existingALB, nil).Times(1)

	// Simulate the exact validation error that occurs in production
	validationError := errors.New(
		"update_alb_info_error: ALB.machine.shopee.io \"hm-10-115-176-5-alb\" is invalid: " +
			"[spec.albNode.dockerVersion: Required value, spec.albNode.driverVersion: Required value, " +
			"spec.albNode.lxcfsVersion: Required value, spec.albNode.wafImageTag: Required value]",
	)
	mockController.EXPECT().UpdateWithContext(gomock.Any(), gomock.Any()).Return(validationError).Times(1)

	// Execute
	result, err := service.Retire(context.Background(), req)

	// Assert - the operation should complete but with errors reported
	assert.NoError(t, err, "Retire should not return error even when update fails")
	assert.NotNil(t, result)
	assert.Equal(t, swpTicketResp, result.Ticket, "Ticket should be created successfully")
	assert.Len(t, result.Errors, 1, "Should have one error for the failed node")
	assert.Contains(t, result.Errors[0].Message, "Required value")
	assert.Equal(t, testIP, result.Errors[0].IP)
}

// TestTicketService_Retire_WithNewFields tests that new required fields are properly handled
func TestTicketService_Retire_WithNewFields(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	fakeit := gofakeit.New(time.Now().Unix())

	mockController := albctl.NewMockController(ctrl)
	mockTicket := swp.NewMockTicketAdapter(ctrl)

	service := &TicketService{
		TraceID:    fakeit.UUID(),
		Ticket:     mockTicket,
		Controller: mockController,
	}

	testIP := fakeit.IPv4Address()
	testTicketID := fakeit.Number(1000000, 9999999)

	// Create ALB CR with new fields populated
	existingALB := &v1alpha1.ALB{
		ObjectMeta: metav1.ObjectMeta{
			Name: albvo.CRName(testIP),
		},
		Spec: v1alpha1.ALBSpec{
			LanIP: testIP,
			ALBNode: v1alpha1.ALBNode{
				ClusterUUID:      fakeit.UUID(),
				ClusterName:      fakeit.Word(),
				SDU:              fakeit.Word(),
				ALBSdImageTag:    fakeit.Word() + ":v" + fakeit.AppVersion(),
				ALBAgentImageTag: fakeit.Word() + ":v" + fakeit.AppVersion(),
				NginxLbImageTag:  fakeit.Word() + ":v" + fakeit.AppVersion(),
				MetricsImageTag:  fakeit.Word() + ":v" + fakeit.AppVersion(),
				NginxVersion:     fakeit.AppVersion(),
				Options:          make(map[string]string),
			},
		},
		Status: v1alpha1.ALBStatus{
			State:  consts.RunningState.Status,
			Reason: consts.RunningState.Reason,
		},
	}

	req := &albvo.ClusterNodeTicketRequest{
		IPs:           []string{testIP},
		UUID:          existingALB.Spec.ALBNode.ClusterUUID,
		Cluster:       existingALB.Spec.ALBNode.ClusterName,
		SDU:           existingALB.Spec.ALBNode.SDU,
		Token:         fakeit.Password(true, true, true, false, false, 32),
		ApplicantUser: fakeit.Email(),
	}

	swpTicketResp := &swpvo.Ticket{
		ID:            testTicketID,
		ApplicantUser: req.ApplicantUser,
	}

	// Setup mocks
	mockTicket.EXPECT().SetToken(req.Token).Times(1)
	mockTicket.EXPECT().Create(gomock.Any()).Return(swpTicketResp, nil).Times(1)
	mockController.EXPECT().GetWithContext(gomock.Any(), albvo.CRName(testIP)).Return(existingALB, nil).Times(1)

	// Verify that new fields are preserved during update
	mockController.EXPECT().UpdateWithContext(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, alb *v1alpha1.ALB) error {
			// Verify retirement fields are updated
			assert.Equal(t, consts.PreRetiringState.Status, alb.Status.State)
			assert.Equal(t, consts.PreRetiringState.Reason, alb.Status.Reason)
			assert.Equal(t, testTicketID, alb.Spec.ALBNode.SWPTicket)

			return nil
		},
	).Times(1)

	// Execute
	result, err := service.Retire(context.Background(), req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, swpTicketResp, result.Ticket)
	assert.Empty(t, result.Errors)
}

// TestTicketService_Retire_Success tests the happy path where all fields are properly preserved
func TestTicketService_Retire_Success(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	fakeit := gofakeit.New(time.Now().Unix())

	mockController := albctl.NewMockController(ctrl)
	mockTicket := swp.NewMockTicketAdapter(ctrl)

	service := &TicketService{
		TraceID:    fakeit.UUID(),
		Ticket:     mockTicket,
		Controller: mockController,
	}

	testIP := fakeit.IPv4Address()
	testTicketID := fakeit.Number(1000000, 9999999)
	testClusterUUID := fakeit.UUID()
	testClusterName := fakeit.Word()
	testSDU := fakeit.Word()
	testToken := fakeit.Password(true, true, true, false, false, 32)
	testApplicantUser := fakeit.Email()

	// Create ALB CR with all standard fields populated
	existingALB := &v1alpha1.ALB{
		ObjectMeta: metav1.ObjectMeta{
			Name: albvo.CRName(testIP),
		},
		Spec: v1alpha1.ALBSpec{
			LanIP: testIP,
			ALBNode: v1alpha1.ALBNode{
				ClusterUUID:      testClusterUUID,
				ClusterName:      testClusterName,
				SDU:              testSDU,
				ALBSdImageTag:    fakeit.Word() + ":v" + fakeit.AppVersion(),
				ALBAgentImageTag: fakeit.Word() + ":v" + fakeit.AppVersion(),
				NginxLbImageTag:  fakeit.Word() + ":v" + fakeit.AppVersion(),
				MetricsImageTag:  fakeit.Word() + ":v" + fakeit.AppVersion(),
				NginxVersion:     fakeit.AppVersion(),
				Options:          make(map[string]string),
			},
		},
		Status: v1alpha1.ALBStatus{
			State:  consts.RunningState.Status,
			Reason: consts.RunningState.Reason,
		},
	}

	req := &albvo.ClusterNodeTicketRequest{
		IPs:           []string{testIP},
		UUID:          testClusterUUID,
		Cluster:       testClusterName,
		SDU:           testSDU,
		Token:         testToken,
		ApplicantUser: testApplicantUser,
	}

	swpTicketResp := &swpvo.Ticket{
		ID:            testTicketID,
		ApplicantUser: req.ApplicantUser,
	}

	// Store original values for verification
	originalALBSDImageTag := existingALB.Spec.ALBNode.ALBSdImageTag
	originalALBAgentImageTag := existingALB.Spec.ALBNode.ALBAgentImageTag
	originalNginxLbImageTag := existingALB.Spec.ALBNode.NginxLbImageTag
	originalMetricsImageTag := existingALB.Spec.ALBNode.MetricsImageTag
	originalNginxVersion := existingALB.Spec.ALBNode.NginxVersion

	// Setup mocks
	mockTicket.EXPECT().SetToken(req.Token).Times(1)
	mockTicket.EXPECT().Create(gomock.Any()).Return(swpTicketResp, nil).Times(1)
	mockController.EXPECT().GetWithContext(gomock.Any(), albvo.CRName(testIP)).Return(existingALB, nil).Times(1)

	// Verify that all fields are preserved during update
	mockController.EXPECT().UpdateWithContext(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, alb *v1alpha1.ALB) error {
			// Verify existing fields are preserved
			assert.Equal(t, originalALBSDImageTag, alb.Spec.ALBNode.ALBSdImageTag)
			assert.Equal(t, originalALBAgentImageTag, alb.Spec.ALBNode.ALBAgentImageTag)
			assert.Equal(t, originalNginxLbImageTag, alb.Spec.ALBNode.NginxLbImageTag)
			assert.Equal(t, originalMetricsImageTag, alb.Spec.ALBNode.MetricsImageTag)
			assert.Equal(t, originalNginxVersion, alb.Spec.ALBNode.NginxVersion)

			// Verify retirement fields are updated
			assert.Equal(t, consts.PreRetiringState.Status, alb.Status.State)
			assert.Equal(t, consts.PreRetiringState.Reason, alb.Status.Reason)
			assert.Equal(t, testTicketID, alb.Spec.ALBNode.SWPTicket)
			assert.Equal(t, fmt.Sprintf("%d", testTicketID), alb.Spec.ALBNode.Options["ticket_id"])
			assert.Equal(t, testApplicantUser, alb.Spec.ALBNode.Options["applicant_user"])

			return nil
		},
	).Times(1)

	// Execute
	result, err := service.Retire(context.Background(), req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, swpTicketResp, result.Ticket)
	assert.Empty(t, result.Errors)
}
