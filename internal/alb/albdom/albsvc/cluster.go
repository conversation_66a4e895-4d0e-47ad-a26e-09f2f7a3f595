package albsvc

import (
	"context"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"git.garena.com/shopee/go-shopeelib/json"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/meta"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/meta/metavo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/nlb/nlbvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

// ClusterService cluster service
type ClusterService struct {
	TraceID string

	L7Cluster  sgw.L7ClusterAdapter
	NLBCluster sgw.NLBClusterAdapter
	Controller albctl.Controller
	MetaCls    meta.ClusterAdapter
	ALBCluster sgw.ALBClusterAdapter
	server     toc.ServerAdapter
}

func NewClusterService(traceID string) *ClusterService {
	cluster := sgw.NewALBClusterAdapter(traceID)
	nlbCluster := sgw.NewNLBClusterAdapter(traceID)

	return &ClusterService{
		TraceID:    traceID,
		L7Cluster:  cluster,
		MetaCls:    meta.NewClusterAdapter(traceID),
		ALBCluster: sgw.NewALBCluster(traceID),
		NLBCluster: nlbCluster,
		Controller: albctl.ALBController,
		server:     toc.NewServerAdapter(traceID),
	}
}

// List dump clusters
func (s *ClusterService) List(_ context.Context) ([]*albvo.ClusterOverview, error) {
	ret, err := s.L7Cluster.Dump()
	if err != nil {
		return nil, errors.WithMessage(err, "dump_clusters_failed")
	}

	var rets []*albvo.ClusterOverview
	for _, item := range ret {
		rets = append(rets, item.ToOverview())
	}

	return rets, nil
}

// GetByUUID get cluster by uuid
func (s *ClusterService) GetByUUID(_ context.Context, uuid string) (*albvo.Cluster, error) {
	cluster, err := s.L7Cluster.GetByUUID(uuid)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	return cluster, nil
}

// GetByNode node was IP addr
func (s *ClusterService) GetByNode(_ context.Context, node string) (*albvo.Cluster, error) {
	cluster, err := s.L7Cluster.GetByNode(node)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	return cluster, nil
}

// Nodes get nodes by uuid
func (s *ClusterService) Nodes(_ context.Context, uuid string) ([]*sgwvo.Node, error) {
	nodes, err := s.L7Cluster.GetNodesByUUID(uuid)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_nodes_failed")
	}

	return nodes, nil
}

// NodesByIPs get nodes by ip list
func (s *ClusterService) NodesByIPs(_ context.Context, req *albvo.NodeRequest) ([]*sgwvo.Node, error) {
	var nodes []*sgwvo.Node
	clusters, err := s.L7Cluster.Dump()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_list_failed")
	}

	ipmap := req.IPMap()
	slice.ForEach(clusters, func(_ int, cluster *albvo.Cluster) {
		ipNodes := slice.Map(cluster.Nodes, func(_ int, node *sgwvo.Node) *sgwvo.Node {
			if _, ok := ipmap[node.IP]; ok {
				return node
			}

			return nil
		})
		nodes = append(nodes, slice.Filter(ipNodes, func(_ int, node *sgwvo.Node) bool {
			return node != nil
		})...)
	})

	return nodes, nil
}

// NLBListenersByUUID get nlb listeners by uuid
func (s *ClusterService) NLBListenersByUUID(_ context.Context, uuid string) ([]*nlbvo.Listener, error) {
	listeners, err := s.L7Cluster.GetNLBListenersByUUID(uuid)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"uuid": uuid,
		}).Error("fetch_nlb_listeners_failed")
	}

	return listeners, nil
}

// NodeNLBListeners get nlb listeners by uuid and node's ip
func (s *ClusterService) NodeNLBListeners(_ context.Context, uuid string, nodeIP string) ([]*nlbvo.ListenerTarget, error) {
	cluster, err := s.L7Cluster.GetByUUID(uuid)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"uuid": uuid,
		}).Error("fetch cluster fail")

		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	listeners, err := s.fetchNLBListeners(cluster)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_nlb_listeners_failed")
	}

	return listeners[nodeIP], nil
}

// NodeNLBListenersByCluster get nlb listeners by cluster with node's ip
func (s *ClusterService) NodeNLBListenersByCluster(_ context.Context, nodeIP string, cluster *albvo.Cluster) (
	[]*nlbvo.ListenerTarget, error,
) {
	listeners, err := s.fetchNLBListeners(cluster)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_nlb_listeners_by_cluster_failed")
	}

	return listeners[nodeIP], nil
}

// NLBListenerTargetsMapByUUID get nlb listener targets by uuid
func (s *ClusterService) NLBListenerTargetsMapByUUID(
	_ context.Context,
	uuid string,
) (map[string][]*nlbvo.ListenerTarget, error) {
	cluster, err := s.L7Cluster.GetByUUID(uuid)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"uuid": uuid,
		}).Error("fetch cluster fail")

		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	listeners, err := s.fetchNLBListeners(cluster)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_nlb_listeners_failed")
	}

	return listeners, nil
}

// fetchNLBListeners if std cluster then fetch by az+env else if legacy then dump all
func (s *ClusterService) fetchNLBListeners(cluster *albvo.Cluster) (map[string][]*nlbvo.ListenerTarget, error) {
	targets := make(map[string][]*nlbvo.ListenerTarget) // key: target host IP addr
	nlbListener := sgw.NewNLBListenerAdapter(s.TraceID)

	nlbInstanceIDs, err := s.fetchNLBInstanceIDsByUUID(cluster.UUID)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"uuid": cluster.UUID,
		}).Error("fetch_nlb_instance_ids_failed")

		return nil, errors.WithMessage(err, "fetch_nlb_instance_ids_failed")
	}

	if !cluster.IsLegacy {
		listeners, err := nlbListener.DumpALBList(cluster.AZ, cluster.Env)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_nlb_listener_of_alb_failed")
		}

		listeners = s.filterNLBListenersByInstanceIDs(listeners, nlbInstanceIDs)

		targets = s.toListenerTargets(listeners)

		return targets, nil
	}

	clusters, err := s.NLBCluster.Dump()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_nlb_clusters_failed")
	}

	for _, c := range clusters {
		listeners, err := nlbListener.DumpByUUID(c.UUID)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_listeners_failed")
		}

		listeners = s.filterNLBListenersByInstanceIDs(listeners, nlbInstanceIDs)

		for host, lts := range s.toListenerTargets(listeners) {
			if hostTargets, ok := targets[host]; ok {
				hostTargets = append(hostTargets, lts...)
				// filter repeated elements
				dataMap := make(map[string]struct{})
				var lts2 []*nlbvo.ListenerTarget
				for _, data := range hostTargets {
					if _, ok = dataMap[data.Key()]; !ok {
						lts2 = append(lts2, data)
						dataMap[data.Key()] = struct{}{}
					}
				}

				targets[host] = lts2
			} else {
				targets[host] = lts
			}
		}
	}

	return targets, nil
}

func (s *ClusterService) toListenerTargets(listeners []*nlbvo.Listener) map[string][]*nlbvo.ListenerTarget {
	targets := make(map[string][]*nlbvo.ListenerTarget)

	for _, listener := range listeners {
		if !strings.EqualFold("FIXED", listener.TargetType) {
			// NLB-ALB must be fixed
			continue
		}

		for _, tar := range listener.FixedTargets {
			target := nlbvo.ListenerTarget{}
			target.ListenerCore = listener.ListenerCore
			target.Weight = tar.Weight
			target.TargetPort = tar.Port

			if data, ok := targets[tar.Host]; ok {
				data = append(data, &target)
				targets[tar.Host] = data
			} else {
				targets[tar.Host] = []*nlbvo.ListenerTarget{&target}
			}
		}
	}

	return targets
}

func (s *ClusterService) GetProductBlockListByRZAndNetwork(_ context.Context, rz string,
	networkType string, product string,
) ([]*albvo.Cluster, error) {
	clusters, err := s.L7Cluster.Dump()
	if err != nil {
		return nil, errors.WithMessage(err, "get_cluster_by_rz_failed")
	}

	clusters = slice.Filter(clusters, func(_ int, cluster *albvo.Cluster) bool {
		if strings.EqualFold(cluster.RZ, rz) {
			if strings.EqualFold(cluster.NetworkType, networkType) {
				return cluster.HasLabel(product)
			}
		}

		return false
	})

	return clusters, nil
}

func (s *ClusterService) Config(_ context.Context, uuid string) (*sgwvo.ALBClusterConfigMeta, error) {
	cfg, err := s.ALBCluster.Meta(uuid)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_config_failed")
	}

	return cfg, nil
}

func (s *ClusterService) FreshConfig(_ context.Context, req *albvo.FreshClusterConfigRequest) error {
	cluster, err := s.MetaCls.GetByName(consts.ALB, req.Cluster)
	if err != nil {
		return errors.WithMessage(err, "fetch_cluster_failed")
	}

	info, err := s.MetaCls.Info(cast.ToInt(cluster.ID))
	if err != nil {
		return errors.WithMessage(err, "fetch_cluster_info_failed")
	}

	var version string
	if info.Cluster != nil && info.Cluster.Meta != nil {
		version = info.Cluster.Meta.Version
	}

	version = s.MetaCls.IncrementVersion(version)

	cfg, err := json.Marshal(req.Config)
	if err != nil {
		return errors.WithMessage(err, "marshal_config_failed")
	}

	meta2 := metavo.Meta{}
	meta2.ClusterName = cluster.Name
	meta2.ClusterID = cluster.ID
	meta2.Version = version
	meta2.Remarks = req.Reason
	meta2.UpdatedBy = req.ApplicantUser
	meta2.UpdatedAt = cast.ToString(time.Now().Unix())
	meta2.Meta = cast.ToString(cfg)

	req2 := &metavo.UpsertMetaRequest{}
	req2.Meta = meta2

	ret, err := s.MetaCls.UpsertMeta(req2)
	if err != nil {
		return errors.WithMessage(err, "upsert_meta_failed")
	}

	if ret.Version != version {
		return errors.New("version_mismatch")
	}

	return nil
}

// SyncConfigFromTagVars sync config from tag vars
/*
1. fetch cluster by uuid
2. for ECMP cluster, pick up 1 node's tag vars then convert to config
3. for ActiveStandby cluster, foreach every node's tag vars then convert to config
*/
func (s *ClusterService) SyncConfigFromTagVars(ctx context.Context, uuid string) error {
	cluster, err := s.L7Cluster.GetByUUID(uuid)
	if err != nil {
		return errors.WithMessage(err, "fetch_cluster_failed")
	}

	switch cluster.HAName() {
	case consts.HAMNLB:
		return nil
	case consts.HAMActiveStandby:
		if err = s.syncConfigForHAMActiveStandby(ctx, cluster); err != nil {
			return errors.WithMessage(err, "sync_config_failed")
		}

		return nil
	case consts.HAMECMP:
		if err = s.syncConfigForHAMECMP(ctx, cluster); err != nil {
			return errors.WithMessage(err, "sync_config_failed")
		}

		return nil
	}

	return nil
}

func (s *ClusterService) getClusterConfig(clusterName string) (*sgwvo.L7ClusterConfig, error) {
	cls, err := s.MetaCls.GetByName(consts.ALB, clusterName)
	if err != nil {
		if !errors.Is(err, meta.ErrClusterNotFound) {
			return nil, errors.WithMessage(err, "fetch_cluster_failed")
		}

		return nil, consts.ErrClusterConfigEmpty
	}

	info, err := s.MetaCls.Info(cast.ToInt(cls.ID))
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_info_failed")
	}

	if info.Cluster != nil && info.Cluster.Meta != nil && !info.Cluster.Meta.IsEmpty() {
		return nil, consts.ErrClusterMetaSynced
	}

	if info.Cluster != nil && info.Cluster.Meta != nil {
		if config, err := info.Cluster.Meta.ToL7ClusterConfig(); err == nil {
			return config, nil
		}
	}

	return nil, consts.ErrClusterConfigEmpty
}

func (s *ClusterService) validateFieldsForSync(isEmpty1, isEmpty2 bool) error {
	if (!isEmpty1 && isEmpty2) || (isEmpty1 && !isEmpty2) {
		return consts.ErrClusterMetaPartiallySynced
	}

	if !isEmpty1 && !isEmpty2 {
		return consts.ErrClusterMetaSynced
	}

	return nil
}

func (s *ClusterService) createFreshConfigRequest(
	clusterName string,
	config *sgwvo.L7ClusterConfig,
) *albvo.FreshClusterConfigRequest {
	req := &albvo.FreshClusterConfigRequest{}
	req.Cluster = clusterName
	req.Config = config
	req.Reason = "sync_config_from_tag_vars"
	req.ApplicantUser = configs.SGW.Bot.Email

	return req
}

func (s *ClusterService) processHANodes(nodes []*sgwvo.Node) ([]*sgwvo.HAInstance, []string) {
	var haInstances []*sgwvo.HAInstance
	var wanVIPs []string

	slice.ForEach(nodes, func(_ int, node *sgwvo.Node) {
		vars, err := s.server.GetALBGroupVar(node.IP)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"node": node.IP,
			}).Error("fetch_alb_group_vars_failed")

			return
		}

		wanVIPs = append(wanVIPs, vars.KeepalivedWANVIPS...)

		var instances []*sgwvo.KeepalivedInstance
		maputil.ForEach(vars.HAInstances, func(key string, instance *tocvo.KeepalivedInstance) {
			instances = append(instances, &sgwvo.KeepalivedInstance{
				Name:            key,
				Priority:        instance.Priority,
				VirtualRouterID: instance.VirtualRouterID,
				Interface:       instance.Interface,
				State:           instance.State,
				VIP:             instance.VIP,
				WithoutAuth:     instance.WithoutAuth,
			})
		})

		haInstance := sgwvo.HAInstance{
			Node:      node.IP,
			Instances: instances,
		}
		haInstances = append(haInstances, &haInstance)
	})

	return haInstances, wanVIPs
}

func (s *ClusterService) syncConfigForHAMECMP(ctx context.Context, cluster *albvo.Cluster) error {
	var isECMPBGPWanVIPsEmpty, isECMPBGPVIPsEmpty bool

	currentConfig, err := s.getClusterConfig(cluster.Name)
	if err != nil {
		if errors.Is(err, consts.ErrClusterConfigEmpty) {
			isECMPBGPWanVIPsEmpty = true
			isECMPBGPVIPsEmpty = true
		} else {
			return err
		}
	} else {
		isECMPBGPWanVIPsEmpty = len(currentConfig.ECMPBGPWanVIPs) == 0
		isECMPBGPVIPsEmpty = len(currentConfig.ECMPBGPVIPs) == 0
	}

	if err := s.validateFieldsForSync(isECMPBGPWanVIPsEmpty, isECMPBGPVIPsEmpty); err != nil {
		return err
	}

	ips := cluster.ReadyNodeIPs()
	if len(ips) == 0 {
		return errors.New("no_ready_node_found")
	}

	vars, err := s.server.GetALBGroupVar(ips[0])
	if err != nil {
		return errors.WithMessage(err, "get_alb_group_vars_failed")
	}

	config := sgwvo.L7ClusterConfig{}
	if currentConfig != nil {
		config = *currentConfig
	}
	config.ECMPBGPWanVIPs = vars.ECMPBGPWanVIPs
	config.ECMPBGPVIPs = vars.ECMPBGPVIPs

	req := s.createFreshConfigRequest(cluster.Name, &config)

	return s.FreshConfig(ctx, req)
}

func (s *ClusterService) syncConfigForHAMActiveStandby(ctx context.Context, cluster *albvo.Cluster) error {
	currentConfig, err := s.getClusterConfig(cluster.Name)
	if err != nil {
		return err
	}

	var isHAInstancesEmpty, isHAWANVIPsEmpty bool
	if currentConfig != nil {
		isHAInstancesEmpty = len(currentConfig.HAInstances) == 0
		isHAWANVIPsEmpty = len(currentConfig.HAWANVIPs) == 0
	} else {
		isHAInstancesEmpty = true
		isHAWANVIPsEmpty = true
	}

	if err := s.validateFieldsForSync(isHAInstancesEmpty, isHAWANVIPsEmpty); err != nil {
		return err
	}

	config := sgwvo.L7ClusterConfig{}
	if currentConfig != nil {
		config = *currentConfig
	}

	haInstances, wanVIPs := s.processHANodes(cluster.Nodes)
	config.HAInstances = haInstances
	config.HAWANVIPs = slice.Unique(wanVIPs)

	req := s.createFreshConfigRequest(cluster.Name, &config)

	return s.FreshConfig(ctx, req)
}

func (s *ClusterService) fetchNLBInstanceIDsByUUID(uuid string) ([]string, error) {
	instances, err := s.L7Cluster.GetInstancesByUUID(uuid)
	if err != nil {
		return nil, errors.Wrap(err, "fetch_instances_failed")
	}

	nlbInstanceIDs := slice.FlatMap(instances, func(_ int, ins *albvo.Instance) []string {
		return slice.Map(ins.NLBParams, func(_ int, nlb *albvo.NLBParam) string {
			return nlb.InstanceID
		})
	})

	if len(nlbInstanceIDs) == 0 {
		return nil, errors.New("no_nlb_instance_found")
	}

	return nlbInstanceIDs, nil
}

func (s *ClusterService) filterNLBListenersByInstanceIDs(
	listeners []*nlbvo.Listener, nlbInstanceIDs []string,
) []*nlbvo.Listener {
	if len(nlbInstanceIDs) == 0 {
		return nil
	}

	return slice.Filter(listeners, func(_ int, listener *nlbvo.Listener) bool {
		return slice.Contain(nlbInstanceIDs, listener.InstanceID)
	})
}
