package albsvc

import (
	"fmt"
	"os"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

var ciEnv bool

const albNginxTestTimeout = 10.0

func init() {
	log.InitConsole()

	_ = os.Setenv("ENV", "dev")
	_ = os.Setenv("ALB_PRECHECK_NGINX_TEST_TIMEOUT", fmt.Sprint(albNginxTestTimeout))
	_ = configs.Init(consts.ALB)

	ciEnv = configs.CI()
}
