package albsvc

import (
	"context"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/json"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/nlb/nlbvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsinf/opscah"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

// PreCheckOffline precheck before node offline
// 1. At general AZ, check NLB instances advertise status which ALB instances integrated with NLB
func (s *NodeCheckerService) PreCheckOffline(req *albvo.ClusterNodeIPsRequest) ([]*sgwvo.NodeTaskResult, error) {
	cluster, err := s.Cluster.GetByUUID(req.UUID)
	if err != nil {
		return nil, errors.Wrap(err, "fetch_cluster_failed")
	}

	azInfo, err := s.Meta.AZ(cluster.AZ)
	if err != nil {
		return nil, errors.Wrap(err, "fetch_az_failed")
	}

	var rets []*sgwvo.NodeTaskResult

	if azInfo.IsGeneral() {
		rets, err = s.checkNLBInstance(cluster, req.IPs)
		if err != nil {
			return nil, errors.Wrap(err, "check_nlb_instance_failed")
		}
	}

	if len(rets) == 0 {
		rets = s.checkDummy(req.IPs)
	}

	s.storePreCheckOfflineResult(rets)

	return rets, nil
}

func (s *NodeCheckerService) storePreCheckOfflineResult(results []*sgwvo.NodeTaskResult) {
	results2 := convertor.ToMap(results, func(result *sgwvo.NodeTaskResult) (string, interface{}) {
		ret, err := json.Marshal(result)
		if err != nil {
			log.Logger().WithField("ip", result.IP).WithError(err).Error("precheck_node_marshal_failed")

			return result.IP, nil
		}

		return result.IP, ret
	})

	cmd := opscah.ElasticRedis.HSet(context.Background(), consts.ALBNodeOfflinePreCheckKey, results2)
	if cmd.Err() != nil {
		log.Logger().WithField("key", consts.ALBNodeOfflinePreCheckKey).WithError(cmd.Err()).Error("set_precheck_node_failed")
	}
}

func (s *NodeCheckerService) FetchPreCheckOfflineResult(ctx context.Context, ips []string) ([]*sgwvo.NodeTaskResult, error) {
	return s.fetchPreCheckResult(ctx, ips, consts.ALBNodeOfflinePreCheckKey)
}

func (s *NodeCheckerService) checkDummy(ips []string) []*sgwvo.NodeTaskResult {
	var rets []*sgwvo.NodeTaskResult

	slice.ForEach(ips, func(_ int, ip string) {
		rets = append(rets, &sgwvo.NodeTaskResult{
			IP:     ip,
			Passed: true,
			Tasks: []*sgwvo.TaskResult{
				{
					Success: true,

					UpdatedAt: time.Now().Unix(),
					Task:      "check_dummy",
					Reason:    "no_tasks",
				},
			},
		})
	})

	return rets
}

func (s *NodeCheckerService) checkNLBInstance(cluster *albvo.Cluster, ips []string) ([]*sgwvo.NodeTaskResult, error) {
	taskName := "check_nlb_instance"

	var rets []*sgwvo.NodeTaskResult

	readyNodeIPs := cluster.ReadyNodeIPs()
	if len(readyNodeIPs) == 0 {
		slice.ForEach(ips, func(_ int, ip string) {
			rets = append(rets, &sgwvo.NodeTaskResult{
				IP:     ip,
				Passed: true,
				Tasks: []*sgwvo.TaskResult{
					{
						Success:   true,
						UpdatedAt: time.Now().Unix(),
						Task:      taskName,
						Reason:    "no_ready_nodes",
					},
				},
			})
		})

		return rets, nil
	}

	// if not the last batch ready nodes, then pass
	if !slice.ContainSubSlice(ips, readyNodeIPs) {
		slice.ForEach(ips, func(_ int, ip string) {
			rets = append(rets, &sgwvo.NodeTaskResult{
				IP:     ip,
				Passed: true,
				Tasks: []*sgwvo.TaskResult{
					{
						Success:   true,
						UpdatedAt: time.Now().Unix(),
						Task:      taskName,
						Reason:    "nodes_not_the_last_batch_ready_nodes",
					},
				},
			})
		})

		return rets, nil
	}

	// the last batch ready nodes
	/*
		1. fetch ALB instances then NLB instances
		2. fetch NLB instances to check advertise status
	*/
	instances, err := s.Cluster.GetInstancesByUUID(cluster.UUID)
	if err != nil {
		return nil, errors.Wrap(err, "fetch_instances_failed")
	}

	nlbInstanceIDs := slice.FlatMap(instances, func(_ int, ins *albvo.Instance) []string {
		return slice.Map(ins.NLBParams, func(_ int, nlb *albvo.NLBParam) string {
			return nlb.InstanceID
		})
	})

	nlbInstances, err := s.NLBInstance.ListByID(nlbInstanceIDs)
	if err != nil {
		return nil, errors.Wrap(err, "fetch_nlb_instances_failed")
	}

	// whether all the advertisement status is disabled
	_, ok := slice.FindBy(nlbInstances, func(_ int, nlb *nlbvo.Instance) bool {
		return nlb.CanAdvertiseVIP
	})
	// find at least one advertise status is enabled
	if ok {
		slice.ForEach(ips, func(_ int, ip string) {
			rets = append(rets, &sgwvo.NodeTaskResult{
				IP:     ip,
				Passed: false,
				Tasks: []*sgwvo.TaskResult{
					{
						Success:   false,
						UpdatedAt: time.Now().Unix(),
						Task:      taskName,
						Reason:    "partial_instances_advertised",
					},
				},
			})
		})

		return rets, nil
	}

	slice.ForEach(ips, func(_ int, ip string) {
		rets = append(rets, &sgwvo.NodeTaskResult{
			IP:     ip,
			Passed: true,
			Tasks: []*sgwvo.TaskResult{
				{
					Success:   true,
					UpdatedAt: time.Now().Unix(),
					Task:      taskName,
					Reason:    "all_instances_unadvertised",
				},
			},
		})
	})

	return rets, nil
}
