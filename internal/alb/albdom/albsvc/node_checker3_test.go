package albsvc

import (
	"fmt"
	"net"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

func TestNodeCheckerService_checkEtcdServerList(t *testing.T) {
	t.Parallel()

	fakeit := gofakeit.New(time.Now().Unix())

	currentIP := fakeit.IPv4Address()
	readyNodeIP := fakeit.IPv4Address()

	svcName := fakeit.Word()
	UUID := fakeit.UUID()

	etcdNode1 := net.JoinHostPort(fakeit.IPv4Address(), fmt.Sprint(fakeit.Number(1000, 9999)))
	etcdNode2 := net.JoinHostPort(fakeit.IPv4Address(), fmt.Sprint(fakeit.Number(1000, 9999)))
	etcdNode3 := net.JoinHostPort(fakeit.IPv4Address(), fmt.Sprint(fakeit.Number(1000, 9999)))
	etcdNodes1 := fmt.Sprintf("etcd://%s,%s/%s", etcdNode1, etcdNode2, svcName)
	etcdNodes2 := fmt.Sprintf("etcd://%s,%s/%s", etcdNode1, etcdNode2, svcName)
	etcdNodes3 := fmt.Sprintf("etcd://%s,%s/%s", etcdNode3, etcdNode1, svcName)

	t.Run("etcd_server_list_ok", func(t *testing.T) {
		t.Parallel()

		ctrl := gomock.NewController(t)
		mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
		mockTocex := toc.NewMockTocexAdapter(ctrl)
		mockTocexReady := toc.NewMockTocexAdapter(ctrl)

		node := &NodeCheckerService{
			TraceID: t.Name(),
			Cluster: mockCluster,
		}

		// Mock tocex for current node
		mockTocex.EXPECT().EnsureTOCExClient().Return(nil).Times(1)
		mockTocex.EXPECT().ReadFile(gomock.Eq("/etc/sgw/etcd")).
			Return(etcdNodes1, nil).Times(1)

		// Mock tocex for ready node
		mockTocexReady.EXPECT().EnsureTOCExClient().Return(nil).Times(1)
		mockTocexReady.EXPECT().ReadFile(gomock.Eq("/etc/sgw/etcd")).
			Return(etcdNodes2, nil).Times(1)

		sgwNode1, err := toc.NewSGWNodeAdapter(mockTocex)
		assert.NoError(t, err)
		sgwNode2, err := toc.NewSGWNodeAdapter(mockTocexReady)
		assert.NoError(t, err)

		result := node.checkEtcdServers(sgwNode1, sgwNode2)
		assert.NotEmpty(t, result.Reason)
		assert.True(t, result.Success)
		assert.Equal(t, "check_etcd_server_list", result.Task)
	})

	t.Run("etcd_no_other_server_ok", func(t *testing.T) {
		t.Parallel()

		ctrl := gomock.NewController(t)
		mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
		mockTocex := toc.NewMockTocexAdapter(ctrl)

		node := &NodeCheckerService{
			TraceID: t.Name(),
			Cluster: mockCluster,
		}

		// Mock cluster with two nodes
		mockCluster.EXPECT().GetByUUID(gomock.Eq(UUID)).Return(&albvo.Cluster{
			Nodes: []*sgwvo.Node{
				{
					IP:         currentIP,
					NodeStatus: consts.NodeStatusNotReady,
				},
				{
					IP:         readyNodeIP,
					NodeStatus: consts.NodeStatusNotReady,
				},
			},
		}, nil).Times(1)

		mockTocex.EXPECT().EnsureTOCExClient().Return(nil).Times(1)

		sgwNode1, err := toc.NewSGWNodeAdapter(mockTocex)
		assert.NoError(t, err)
		cluster, err := mockCluster.GetByUUID(UUID)
		assert.NoError(t, err)
		nodeIPs := slice.Difference(cluster.ReadyNodeIPs(), []string{currentIP})

		results, err := node.fetchReadyCheckResult(&readyChecker{
			nodeIPs: nodeIPs,
			sgwNode: sgwNode1,
		})
		assert.NoError(t, err)
		assert.Len(t, results, 0)
	})

	t.Run("etcd_not_match_failed", func(t *testing.T) {
		t.Parallel()

		ctrl := gomock.NewController(t)
		mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
		mockTocex := toc.NewMockTocexAdapter(ctrl)
		mockTocexReady := toc.NewMockTocexAdapter(ctrl)

		node := &NodeCheckerService{
			TraceID: t.Name(),
			Cluster: mockCluster,
		}

		// Mock tocex for current node
		mockTocex.EXPECT().EnsureTOCExClient().Return(nil).Times(1)
		mockTocex.EXPECT().ReadFile(gomock.Eq("/etc/sgw/etcd")).
			Return(etcdNodes1, nil).Times(1)

		// Mock tocex for ready node
		mockTocexReady.EXPECT().EnsureTOCExClient().Return(nil).Times(1)
		mockTocexReady.EXPECT().ReadFile(gomock.Eq("/etc/sgw/etcd")).
			Return(etcdNodes3, nil).Times(1)

		sgwNode1, err := toc.NewSGWNodeAdapter(mockTocex)
		assert.NoError(t, err)
		sgwNode2, err := toc.NewSGWNodeAdapter(mockTocexReady)
		assert.NoError(t, err)

		result := node.checkEtcdServers(sgwNode1, sgwNode2)
		assert.False(t, result.Success)
		assert.Equal(t, "check_etcd_server_list", result.Task)
		assert.Contains(t, result.Reason, "etcd_server_list_not_matched")
	})
}
