package albsvc

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/swp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/swp/swpvo"
)

// TicketService service for ticket
type TicketService struct {
	TraceID    string
	Ticket     swp.TicketAdapter
	Controller albctl.Controller
}

// Add raise a ticket of adding nodes into cluster
func (s *TicketService) Add(ctx context.Context, req *albvo.ClusterNodeTicketRequest) (*swpvo.Ticket, error) {
	resp, err := s.raise(ctx, req, consts.TicketAdd)
	if err != nil {
		return nil, errors.WithMessage(err, "raise_add_nodes_ticket_failed")
	}

	return resp.Ticket, nil
}

// Retire raise a ticket of removing nodes from cluster
func (s *TicketService) Retire(ctx context.Context, req *albvo.ClusterNodeTicketRequest) (*albvo.TicketData, error) {
	resp, err := s.raise(ctx, req, consts.TicketRetire)
	if err != nil {
		return nil, errors.WithMessage(err, "raise_remove_nodes_ticket_failed")
	}

	return resp, nil
}

func (s *TicketService) raise(ctx context.Context, req *albvo.ClusterNodeTicketRequest, kind string) (
	*albvo.TicketData, error,
) {
	ret := albvo.TicketData{}

	var request *swpvo.TicketRequest
	switch kind {
	case consts.TicketAdd:
		request = req.ToSWPTicketAddNodeRequest()
	case consts.TicketRetire:
		request = req.ToSWPTicketRemoveNodeRequest()
	default:
		return nil, errors.Errorf("unsupported_ticket_type_%s", kind)
	}

	s.Ticket.SetToken(req.Token)
	swpTicket, err := s.Ticket.Create(request)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": s.TraceID,
			"type":     kind,
			"ips":      req.IPs,
		}).Error("create_ticket_error")

		return nil, errors.WithMessage(err, "raise_ticket_failed")
	}

	ret.Ticket = swpTicket

	if kind == consts.TicketAdd {
		return &ret, nil
	}

	var items []core.ItemError

	for _, ip := range req.IPs {
		alb, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			items = append(items, core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		alb.Status.State = consts.PreRetiringState.Status
		alb.Status.Reason = consts.PreRetiringState.Reason

		alb.Spec.ALBNode.SWPTicket = swpTicket.ID
		alb.Spec.ALBNode.Options["ticket_id"] = cast.ToString(swpTicket.ID)
		alb.Spec.ALBNode.Options["applicant_user"] = swpTicket.ApplicantUser
		err = s.Controller.UpdateWithContext(ctx, alb)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"trace_id": s.TraceID,
				"ip":       ip,
				"ticket":   swpTicket.ID,
			}).Error("update_alb_cr_for_retirement_failed")

			items = append(items, core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})
		}
	}

	ret.Errors = items

	return &ret, nil
}

func (s *TicketService) Online(ctx context.Context, req *albvo.TicketRequest) (*albvo.TicketData, error) {
	return s.create(ctx, req)
}

func (s *TicketService) Offline(ctx context.Context, req *albvo.TicketRequest) (*albvo.TicketData, error) {
	return s.create(ctx, req)
}

func (s *TicketService) create(ctx context.Context, req *albvo.TicketRequest) (*albvo.TicketData, error) {
	ret := albvo.TicketData{}
	s.Ticket.SetToken(req.Token)
	swpTicket, err := s.Ticket.Create(req.ToSWPTicket())
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id":      s.TraceID,
			"type":          req.Type,
			"ips":           req.IPs,
			"title":         req.Title,
			"template_name": req.TemplateName,
		}).Error("create_ticket_error")

		return nil, errors.WithMessage(err, "create_ticket_failed")
	}

	var items []core.ItemError

	for _, ip := range req.IPs {
		alb, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			items = append(items, core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		switch req.Type {
		case consts.TicketOnline:
			alb.Status.State = consts.PreRunningState.Status
			alb.Status.Reason = strings.Join([]string{req.Reason, consts.PreRunningState.Reason}, "\n")
		case consts.TicketOffline:
			alb.Status.State = consts.PreOfflineState.Status
			alb.Status.Reason = strings.Join([]string{req.Reason, consts.PreOfflineState.Reason}, "\n")
		case consts.TicketMA:
			alb.Status.State = consts.PreMaintenanceState.Status
			alb.Status.Reason = strings.Join([]string{req.Reason, consts.PreMaintenanceState.Reason}, "\n")
			alb.Spec.ALBNode.Options["ma_reason"] = req.Reason
		}

		alb.Spec.ALBNode.SWPTicket = swpTicket.ID
		alb.Spec.ALBNode.Options["ticket_id"] = cast.ToString(swpTicket.ID)
		alb.Spec.ALBNode.Options["applicant_user"] = swpTicket.ApplicantUser
		err = s.Controller.UpdateWithContext(ctx, alb)
		if err != nil {
			items = append(items, core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})
		}
	}

	log.Logger().WithField("trace_id", s.TraceID).WithField("type", req.Type).WithField("title", req.Title).
		WithField("template_name", req.TemplateName).WithField("node_name", req.FormData.Nodes).
		Info("create_ticket_success")

	ret.Ticket = swpTicket
	ret.Errors = items

	return &ret, nil
}

// HotUpdate upgrade/downgrade component version
func (s *TicketService) HotUpdate(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error) {
	s.Ticket.SetToken(req.Token)
	ticket, err := s.Ticket.Create(req)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("req", req).
			Error("create_update_component_ticket_failed")

		return nil, errors.WithMessage(err, "create_update_ticket_failed")
	}

	log.Logger().WithField("trace_id", s.TraceID).Info("create_update_component_ticket_success")

	return ticket, nil
}

// Block alb cluster traffic
func (s *TicketService) Block(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error) {
	s.Ticket.SetToken(req.Token)
	ticket, err := s.Ticket.Create(req)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("req", req).
			Error("create_block_cluster_traffic_ticket_failed")

		return nil, errors.WithMessage(err, "create_block_cluster_traffic_ticket_failed")
	}

	log.Logger().WithField("trace_id", s.TraceID).Info("create_block_cluster_traffic_ticket_success")

	return ticket, nil
}

// BlockByDomains  block alb cluster traffic by domains
func (s *TicketService) BlockByDomains(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error) {
	s.Ticket.SetToken(req.Token)
	ticket, err := s.Ticket.Create(req)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("req", req).
			Error("create_block_traffic_by_domains_ticket_failed")

		return nil, errors.WithMessage(err, "create_block_traffic_by_domains_ticket_failed")
	}

	log.Logger().WithField("trace_id", s.TraceID).Info("create_block_traffic_by_domains_ticket_success")

	return ticket, nil
}

// OpenByDomains  open alb cluster traffic by domains
func (s *TicketService) OpenByDomains(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error) {
	s.Ticket.SetToken(req.Token)
	ticket, err := s.Ticket.Create(req)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("req", req).
			Error("create_open_traffic_by_domains_ticket_failed")

		return nil, errors.WithMessage(err, "create_open_traffic_by_domains_ticket_failed")
	}

	log.Logger().WithField("trace_id", s.TraceID).Info("create_open_traffic_by_domains_ticket_success")

	return ticket, nil
}

// Open alb cluster traffic
func (s *TicketService) Open(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error) {
	s.Ticket.SetToken(req.Token)
	ticket, err := s.Ticket.Create(req)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("req", req).
			Error("create_open_cluster_traffic_ticket_failed")

		return nil, errors.WithMessage(err, "create_open_cluster_traffic_ticket_failed")
	}

	log.Logger().WithField("trace_id", s.TraceID).Info("create_open_cluster_traffic_ticket_success")

	return ticket, nil
}

// FreshClusterConfig fresh cluster config
func (s *TicketService) FreshClusterConfig(_ context.Context, req *swpvo.TicketRequest) (*swpvo.Ticket, error) {
	s.Ticket.SetToken(req.Token)
	ticket, err := s.Ticket.Create(req)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("req", req).
			Error("fresh_cluster_config_ticket_failed")

		return nil, errors.WithMessage(err, "fresh_cluster_config_ticket_failed")
	}

	log.Logger().WithField("trace_id", s.TraceID).Info("fresh_cluster_config_ticket_success")

	return ticket, nil
}
