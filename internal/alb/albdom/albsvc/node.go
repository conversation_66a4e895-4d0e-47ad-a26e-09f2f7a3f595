package albsvc

import (
	"context"
	"fmt"
	"strings"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/byteutils"
	"git.garena.com/shopee/go-shopeelib/json"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsinf/opscah"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

// NodeService node service
type NodeService struct {
	TraceID string

	Meta       toc.MetaAdapter
	Server     toc.ServerAdapter
	ServerV3   toc.ServerV3Adapter
	Controller albctl.Controller
	Cluster    sgw.L7ClusterAdapter
	ALBCluster sgw.ALBClusterAdapter
}

func NewNodeService(traceID string) *NodeService {
	return &NodeService{
		TraceID:    traceID,
		Meta:       toc.NewMetaAdapter(traceID),
		Server:     toc.NewServerAdapter(traceID),
		ServerV3:   toc.NewServerV3Adapter(traceID),
		Controller: albctl.ALBController,
		Cluster:    sgw.NewALBClusterAdapter(traceID),
		ALBCluster: sgw.NewALBCluster(traceID),
	}
}

func (s *NodeService) createALB(ctx context.Context, req *albvo.ClusterNodeTicketRequest, albCR *v1alpha1.ALB) error {
	// If the request has a dedicated state, then modify the alb cr's state
	// Initialised: Means the node already joins the alb cluster,
	// but the state is Offline, NotReady to accept traffic.
	// Running: Means the node already joins the alb cluster, and the state is Online, Ready to accept traffic now.
	state, err := consts.NewState(req.State)
	if err != nil {
		return errors.WithMessage(err, "fetch_state_failed")
	}
	switch state.Code {
	case consts.InitialisedState.Code, consts.RunningState.Code:
		albCR.Status.State = state.Status
		albCR.Status.Reason = state.Reason
	}

	err = s.Controller.CreateWithContext(ctx, albCR)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id":       s.TraceID,
			"name":           albCR.Name,
			"cluster":        req.Cluster,
			"sdu":            req.SDU,
			"ip":             albCR.Spec.LanIP,
			"alb_sd_tag":     albCR.Spec.ALBNode.ALBSdImageTag,
			"alb_agent_tag":  albCR.Spec.ALBNode.ALBAgentImageTag,
			"nginx_lb_tag":   albCR.Spec.ALBNode.NginxLbImageTag,
			"alb_metric_tag": albCR.Spec.ALBNode.MetricsImageTag,
			"nginx_version":  albCR.Spec.ALBNode.NginxVersion,
		}).Error("create_alb_error")

		return errors.WithMessage(err, "create_alb_cr_failed")
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":       s.TraceID,
		"name":           albCR.Name,
		"cluster":        req.Cluster,
		"sdu":            req.SDU,
		"ip":             albCR.Spec.LanIP,
		"alb_sd_tag":     albCR.Spec.ALBNode.ALBSdImageTag,
		"alb_agent_tag":  albCR.Spec.ALBNode.ALBAgentImageTag,
		"nginx_lb_tag":   albCR.Spec.ALBNode.NginxLbImageTag,
		"alb_metric_tag": albCR.Spec.ALBNode.MetricsImageTag,
		"nginx_version":  albCR.Spec.ALBNode.NginxVersion,
	}).Info("create_alb_successfully")

	return nil
}

// Add a node into the alb cluster
func (s *NodeService) Add(ctx context.Context, req *albvo.ClusterNodeTicketRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError
	for _, ip := range req.IPs {
		albCR, err := s.GenALBCR(req, ip)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"trace_id": s.TraceID,
				"ip":       ip,
				"sdu":      req.SDU,
				"cluster":  req.Cluster,
			}).Error("gen_alb_cr_error")

			return nil, errors.WithMessage(err, "gen_alb_cr_failed")
		}

		alb, err := s.Controller.GetWithContext(ctx, albCR.Name)
		if err != nil {
			if !apierrors.IsNotFound(err) {
				log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("name", albCR.Name).
					Error("get_alb_error")

				errs = append(errs, &core.ItemError{
					Message: err.Error(),
					IP:      ip,
				})

				continue
			}

			// Not Found ALBCR then create
			err = s.createALB(ctx, req, albCR)
			if err != nil {
				errs = append(errs, &core.ItemError{
					Message: err.Error(),
					IP:      ip,
				})
			}

			continue
		}

		// the alb cr already exist
		if alb.Status.State != consts.InitialisedState.Status && alb.Status.State != consts.RunningState.Status {
			continue
		}

		if req.State != consts.InitialisedState.Status && req.State != consts.RunningState.Status {
			continue
		}

		if alb.Status.State == req.State && !albvo.IsNodeCRNeedToUpdate(alb, albCR) {
			continue
		}

		alb.Status.State = req.State
		if req.State == consts.InitialisedState.Status {
			alb.Status.Reason = consts.InitialisedState.Reason
		} else {
			alb.Status.Reason = consts.RunningState.Reason
		}

		alb.Spec.ALBNode.ALBName = albCR.Spec.ALBNode.ALBName
		alb.Spec.ALBNode.ClusterName = albCR.Spec.ALBNode.ClusterName
		alb.Spec.ALBNode.ClusterUUID = albCR.Spec.ALBNode.ClusterUUID
		alb.Spec.ALBNode.TOCCluster = albCR.Spec.ALBNode.TOCCluster
		alb.Spec.ALBNode.SDU = albCR.Spec.ALBNode.SDU
		alb.Spec.ALBNode.ALBSdImageTag = albCR.Spec.ALBNode.ALBSdImageTag
		alb.Spec.ALBNode.ALBAgentImageTag = albCR.Spec.ALBNode.ALBAgentImageTag
		alb.Spec.ALBNode.NginxLbImageTag = albCR.Spec.ALBNode.NginxLbImageTag
		alb.Spec.ALBNode.MetricsImageTag = albCR.Spec.ALBNode.MetricsImageTag
		alb.Spec.ALBNode.NginxVersion = albCR.Spec.ALBNode.NginxVersion
		alb.Spec.ALBNode.ScertmsVersion = albCR.Spec.ALBNode.ScertmsVersion
		alb.Spec.ALBNode.Options["applicant_user"] = req.ApplicantUser
		alb.Spec.ALBNode.SWPTicket = req.TicketID

		err = s.Controller.UpdateWithContext(ctx, alb)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"trace_id":       s.TraceID,
				"name":           albCR.Name,
				"cluster":        req.Cluster,
				"sdu":            req.SDU,
				"ip":             albCR.Spec.LanIP,
				"alb_sd_tag":     albCR.Spec.ALBNode.ALBSdImageTag,
				"alb_agent_tag":  albCR.Spec.ALBNode.ALBAgentImageTag,
				"nginx_lb_tag":   albCR.Spec.ALBNode.NginxLbImageTag,
				"alb_metric_tag": albCR.Spec.ALBNode.MetricsImageTag,
				"nginx_version":  albCR.Spec.ALBNode.NginxVersion,
			}).Error("update_alb_cr_error")

			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		log.Logger().WithFields(log.Fields{
			"trace_id":       s.TraceID,
			"name":           albCR.Name,
			"cluster":        req.Cluster,
			"sdu":            req.SDU,
			"ip":             albCR.Spec.LanIP,
			"alb_sd_tag":     albCR.Spec.ALBNode.ALBSdImageTag,
			"alb_agent_tag":  albCR.Spec.ALBNode.ALBAgentImageTag,
			"nginx_lb_tag":   albCR.Spec.ALBNode.NginxLbImageTag,
			"alb_metric_tag": albCR.Spec.ALBNode.MetricsImageTag,
			"nginx_version":  albCR.Spec.ALBNode.NginxVersion,
		}).Info("update_alb_cr_success")
	}

	return errs, nil
}

// GenALBCR generate alb cr
func (s *NodeService) GenALBCR(req *albvo.ClusterNodeTicketRequest, ip string) (*v1alpha1.ALB, error) {
	meta, err := s.ALBCluster.Meta(req.UUID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_alb_meta_failed")
	}

	if req.Version == nil {
		version, err := s.fetchVersionByMeta(meta)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_version_by_vars_failed")
		}
		req.Version = version
	}
	if req.Version == nil {
		return nil, errors.New("component_version_is_nil")
	}

	server, err := s.ServerV3.Get(ip)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_server_failed")
	}

	node := req.GenALBNode(meta)
	node.TOCCluster = server.Cluster

	alb := &v1alpha1.ALB{
		TypeMeta: metav1.TypeMeta{
			Kind:       configs.ALB.Kube.Kind,
			APIVersion: configs.ALB.Kube.APIVersion,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: albvo.CRName(ip),
			Labels: map[string]string{
				"sdu":     node.SDU,
				"cluster": node.ClusterName,
			},
		},
		Spec: v1alpha1.ALBSpec{
			Type:    consts.BizALBType,
			LanIP:   ip,
			ALBNode: *node,
		},
		Status: v1alpha1.ALBStatus{
			State:  consts.SpareState.Status,
			Reason: consts.SpareState.Reason,
		},
	}

	return alb, nil
}

// Retire Remove a node from the alb cluster
func (s *NodeService) Retire(ctx context.Context, req *albvo.ClusterNodeTicketRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError

	for _, ip := range req.IPs {
		alb, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("ip", ip).Error("get_alb_cr_error")
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		state, _ := consts.NewState(alb.Status.State)
		if state.Code > consts.InitialisedState.Code {
			continue
		}

		err = s.Controller.UpdateState(ctx, albvo.CRName(ip), consts.RetiringState)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"trace_id": s.TraceID,
				"ip":       ip,
				"state":    state,
			}).Error("update_alb_cr_retiring_error")

			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		log.Logger().WithFields(log.Fields{
			"trace_id": s.TraceID,
			"ip":       ip,
			"state":    state,
		}).Info("update_alb_cr_retiring_success")
	}

	return errs, nil
}

// State get alb state
func (s *NodeService) State(ctx context.Context, ip string) (*albvo.NodeState, error) {
	alb, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("ip", ip).Error("get_alb_state_error")

		return nil, errors.WithMessage(err, "fetch_alb_cr_failed")
	}

	return albvo.ToNodeState(alb), nil
}

func (s *NodeService) UpdateState(ctx context.Context, ip string, state *consts.State) error {
	err := s.Controller.UpdateState(ctx, albvo.CRName(ip), *state)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("ip", ip).Error("update_alb_cr_state_error")

		return errors.WithMessage(err, "update_alb_state_failed")
	}

	return nil
}

func (s *NodeService) Runtime(ctx context.Context, ip string) (*v1alpha1.ALB, error) {
	alb, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", s.TraceID).WithField("ip", ip).Error("get_alb_cr_error")

		return nil, errors.WithMessage(err, "fetch_alb_cr_failed")
	}

	return alb, nil
}

// HotUpdateOne hot-update one node
func (s *NodeService) HotUpdateOne(ctx context.Context, req *albvo.HotUpdateOneNodeRequest) error {
	ip := req.IP
	albName := albvo.CRName(ip)
	alb, err := s.Controller.GetWithContext(ctx, albName)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": s.TraceID,
			"ip":       ip,
			"alb_name": albName,
		}).Error("fetch_alb_error")

		return errors.WithMessage(err, "fetch_alb_cr_failed")
	}

	state, _ := consts.NewState(alb.Status.State)
	switch req.OperationType {
	case consts.UpdateTocexItem:
		alb.Spec.ALBNode.HotUpdateConfig = *req.ToTocexConfig()
		if !state.Equal(consts.InitialisedState) && !state.Equal(consts.RunningState) {
			return fmt.Errorf("node:%s is_not_in_initialised_or_running_state", ip)
		}
	case consts.RollbackTocexItem:
		alb.Spec.ALBNode.HotUpdateConfig = *req.ToTocexRollbackConfig()
		switch state.Code {
		case consts.InitialisedHotUpdateState.Code:
			alb.Status.State = consts.InitialisedRollbackState.Status
			alb.Status.Reason = consts.InitialisedRollbackState.Reason
		case consts.RunningHotUpdateState.Code:
			alb.Status.State = consts.RunningRollbackState.Status
			alb.Status.Reason = consts.RunningRollbackState.Reason
		default:
			if !state.Equal(consts.InitialisedState) && !state.Equal(consts.RunningState) {
				return fmt.Errorf("node:%s is_not_in_initialised_or_running_state", ip)
			}
		}
	}
	if alb.Spec.ALBNode.Options == nil {
		alb.Spec.ALBNode.Options = make(map[string]string)
	}
	alb.Spec.ALBNode.Options["applicant_user"] = req.ApplicantUser
	// only initialed or running check hot-update type, will change the state

	err = s.Controller.UpdateWithContext(ctx, alb)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": s.TraceID,
			"ip":       ip,
			"alb_name": albName,
		}).Error("update_alb_cr_error")

		return errors.WithMessage(err, "update_alb_cr_failed")
	}

	log.Logger().WithFields(log.Fields{
		"trace_id": s.TraceID,
		"ip":       ip,
		"alb_name": albName,
	}).Info("update_alb_cr_success")

	return nil
}

// HotUpdate hot-update
func (s *NodeService) HotUpdate(ctx context.Context, req *albvo.HotUpdateNodeRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError

	for _, ip := range req.Nodes {
		albName := albvo.CRName(ip)

		alb, err := s.Controller.GetWithContext(ctx, albName)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"trace_id": s.TraceID,
				"ip":       ip,
				"alb_name": albName,
			}).Error("get_alb_error")

			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		switch alb.Status.State {
		case consts.InitialisedHotUpdateState.Status, consts.RunningHotUpdateState.Status:
			errs = append(errs, &core.ItemError{
				Message: fmt.Sprintf("alb_node_current_state_%s_invalid_cannot_hot_update", alb.Status.State),
				IP:      ip,
			})

			continue
		}

		alb.Spec.ALBNode.HotUpdateConfig = *req.ToTocexConfig()
		if alb.Spec.ALBNode.Options == nil {
			alb.Spec.ALBNode.Options = make(map[string]string)
		}
		alb.Spec.ALBNode.Options["applicant_user"] = req.ApplicantUser
		// only initialed or running check hot-update type, will change the state

		err = s.Controller.UpdateWithContext(ctx, alb)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"trace_id": s.TraceID,
				"ip":       ip,
				"alb_name": albName,
			}).Error("update_alb_error")

			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		log.Logger().WithFields(log.Fields{
			"trace_id": s.TraceID,
			"ip":       ip,
			"alb_name": albName,
		}).Info("update_alb_success")
	}

	log.Logger().WithField("trace_id", s.TraceID).WithField("req", req).
		Info("batch_hot_update_service_success")

	return errs, nil
}

// Rollback rollback
func (s *NodeService) Rollback(ctx context.Context, req *albvo.HotUpdateNodeBaseRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError

	for _, ip := range req.Nodes {
		albName := albvo.CRName(ip)

		alb, ierr := s.Controller.GetWithContext(ctx, albName)
		if ierr != nil {
			log.Logger().WithError(ierr).WithField("trace_id", s.TraceID).WithField("ip", ip).
				WithField("alb_name", albName).Error("alb_rollback_error")

			errs = append(errs, &core.ItemError{
				Message: ierr.Error(),
				IP:      ip,
			})

			continue
		}

		if alb.Spec.ALBNode.HotUpdateConfig.Version != req.Version {
			continue
		}

		alb.Spec.ALBNode.Options["applicant_user"] = req.ApplicantUser
		alb.Spec.ALBNode.HotUpdateConfig.Type = consts.RollbackTocexItem
		state, _ := consts.NewState(alb.Status.State)
		switch state.Code {
		case consts.InitialisedHotUpdateState.Code:
			alb.Status.State = consts.InitialisedRollbackState.Status
			alb.Status.Reason = consts.InitialisedRollbackState.Reason
		case consts.RunningHotUpdateState.Code:
			alb.Status.State = consts.RunningRollbackState.Status
			alb.Status.Reason = consts.RunningRollbackState.Reason
		}

		ierr = s.Controller.UpdateWithContext(ctx, alb)
		if ierr != nil {
			log.Logger().WithError(ierr).WithField("trace_id", s.TraceID).WithField("ip", ip).
				WithField("alb_name", albName).Error("update_alb_rollback_error")

			errs = append(errs, &core.ItemError{
				Message: ierr.Error(),
				IP:      ip,
			})
		}
	}

	log.Logger().WithField("trace_id", s.TraceID).
		Info("batch_hot_update_rollback_service_success")

	return errs, nil
}

// Abort only works at HotUpdate and Rollback state
func (s *NodeService) Abort(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError
	slice.ForEach(req.IPs, func(_ int, ip string) {
		alb, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			errs = append(errs, &core.ItemError{
				IP:      ip,
				Message: err.Error(),
			})

			return
		}

		state, _ := consts.NewState(alb.Status.State)

		switch state.Code {
		case consts.RunningRollbackState.Code, consts.RunningHotUpdateState.Code:
			alb.Status.State = consts.RunningState.Status
			alb.Status.Reason = consts.RunningState.Reason
		case consts.InitialisedRollbackState.Code, consts.InitialisedHotUpdateState.Code:
			alb.Status.State = consts.InitialisedState.Status
			alb.Status.Reason = consts.InitialisedState.Reason
		default:
			errs = append(errs, &core.ItemError{
				IP:      ip,
				Message: fmt.Sprintf("node:%s is_not_in_initialised_or_running_state", ip),
			})

			return
		}

		alb.Spec.ALBNode.HotUpdateConfig.Type = ""

		if err := s.Controller.UpdateWithContext(ctx, alb); err != nil {
			errs = append(errs, &core.ItemError{
				IP:      ip,
				Message: err.Error(),
			})
		}
	})

	return errs, nil
}

// Recover is a function in the NodeService struct that handles the recovery of nodes.
//
// It takes a context.Context object and a *albvo.NodeRequest object as parameters.
// It returns a slice of *core.ItemError objects and an error.
//
//nolint:nolintlint,dupl
func (s *NodeService) Recover(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError
	slice.ForEach(req.IPs, func(_ int, ip string) {
		alb, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			errs = append(errs, &core.ItemError{
				IP:      ip,
				Message: err.Error(),
			})

			return
		}

		state, _ := consts.NewState(alb.Status.State)
		if state.Code != consts.MaintenanceState.Code {
			errs = append(errs, &core.ItemError{
				IP:      ip,
				Message: fmt.Sprintf("node:%s is_not_in_maintenance_state", ip),
			})

			return
		}

		alb.Status.State = consts.PostCheckState.Status
		alb.Status.Reason = consts.PostCheckState.Reason
		if err = s.Controller.UpdateWithContext(ctx, alb); err != nil {
			errs = append(errs, &core.ItemError{
				IP:      ip,
				Message: err.Error(),
			})
		}
	})

	return errs, nil
}

// ReInit re-initializes the NodeService.
//
// The ReInit function takes a context.Context object and a *albvo.NodeRequest object as input parameters.
// It returns a slice of *core.ItemError and an error.
//
//nolint:nolintlint,dupl
func (s *NodeService) ReInit(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError
	slice.ForEach(req.IPs, func(_ int, ip string) {
		alb, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			errs = append(errs, &core.ItemError{
				IP:      ip,
				Message: err.Error(),
			})

			return
		}

		state, _ := consts.NewState(alb.Status.State)
		if state.Code >= consts.InitialisedState.Code {
			errs = append(errs, &core.ItemError{
				IP:      ip,
				Message: fmt.Sprintf("node:%s is_not_in_initialised_state", ip),
			})

			return
		}

		alb.Status.State = consts.SpareState.Status
		alb.Status.Reason = consts.SpareState.Reason
		if err = s.Controller.UpdateWithContext(ctx, alb); err != nil {
			errs = append(errs, &core.ItemError{
				IP:      ip,
				Message: err.Error(),
			})
		}
	})

	return errs, nil
}

func (s *NodeService) Reset(ctx context.Context, req *albvo.ResetNodeRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError

	for _, ip := range req.IPs {
		albName := albvo.CRName(ip)

		alb, ierr := s.Controller.GetWithContext(ctx, albName)
		if ierr != nil {
			log.Logger().WithError(ierr).WithField("trace_id", s.TraceID).WithField("ip", ip).
				WithField("alb_name", albName).
				Error("get_alb_clean_error")

			errs = append(errs, &core.ItemError{
				Message: ierr.Error(),
				IP:      ip,
			})

			continue
		}

		if alb.Spec.ALBNode.HotUpdateConfig.Type != consts.UnspecTocexItem &&
			alb.Spec.ALBNode.HotUpdateConfig.Type != consts.CompleteTocexJob {
			log.Logger().WithError(ierr).WithField("trace_id", s.TraceID).WithField("ip", ip).WithField("alb_name", albName).
				WithField("hot_update_type", alb.Spec.ALBNode.HotUpdateConfig.Type).
				Warn("hot_update_clean_error_type_must_be_complete_or_empty")

			continue
		}

		if alb.Spec.ALBNode.HotUpdateConfig.Version != req.Version {
			log.Logger().WithField("trace_id", s.TraceID).WithField("ip", ip).WithField("alb_name", albName).
				WithField("target_version", alb.Spec.ALBNode.HotUpdateConfig.Version).
				WithField("req_version", req.Version).
				Warn("version_not_equal_cannot_execute_hot_update_cleanup")

			continue
		}

		alb.Spec.ALBNode.Options["applicant_user"] = req.ApplicantUser
		alb.Spec.ALBNode.HotUpdateConfig = v1alpha1.TocexHotUpdateConfig{
			Type:               "",
			Templates:          nil,
			Components:         nil,
			RollbackTemplates:  nil,
			RollbackComponents: nil,
			Version:            "",
		}

		ierr = s.Controller.UpdateWithContext(ctx, alb)
		if ierr != nil {
			log.Logger().WithError(ierr).WithField("trace_id", s.TraceID).WithField("ip", ip).WithField("alb_name", albName).
				Error("update_alb_clean_error")

			errs = append(errs, &core.ItemError{
				Message: ierr.Error(),
				IP:      ip,
			})
		}
	}

	log.Logger().WithField("trace_id", s.TraceID).
		Info("batch_hot_update_clean_service_success")

	return errs, nil
}

func (s *NodeService) ListRuntime(ctx context.Context, req *albvo.NodeRequest) ([]*v1alpha1.ALB, error) {
	var albs []*v1alpha1.ALB

	for _, ip := range req.IPs {
		albInfo, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			log.Logger().WithField("ip", ip).WithError(err).Error("fetch node runtime failure")
			if apierrors.IsNotFound(err) {
				continue
			}

			return nil, errors.WithMessage(err, "fetch_alb_cr_failed")
		}

		albs = append(albs, albInfo)
	}

	return albs, nil
}

func (s *NodeService) UpdateSpec(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError

	for _, ip := range req.IPs {
		alb, err := s.Controller.GetWithContext(ctx, albvo.CRName(ip))
		if err != nil {
			if apierrors.IsNotFound(err) {
				continue
			}

			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		cluster, err := s.Cluster.GetByNode(ip)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		az, err := s.Meta.AZ(cluster.AZ)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		server, err := s.ServerV3.Get(ip)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		alb.Spec.ALBNode.Zone = cluster.AZ
		alb.Spec.ALBNode.Segment = cluster.Segment
		alb.Spec.ALBNode.IDC = cluster.RZ
		alb.Spec.ALBNode.ClusterUUID = cluster.UUID
		alb.Spec.ALBNode.ClusterName = cluster.Name
		alb.Spec.ALBNode.TOCCluster = server.Cluster

		// it'll affect tocex token choice
		if az.IsGeneral() {
			switch cluster.RZ {
			case consts.RZBr1:
				alb.Spec.ALBNode.Env = consts.EnvLive
			default:
				alb.Spec.ALBNode.Env = cluster.Env
			}
		} else {
			alb.Spec.ALBNode.Env = consts.EnvLive
		}

		err = s.Controller.UpdateWithContext(ctx, alb)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})
		}
	}

	return errs, nil
}

// Tags get tags from toc
func (s *NodeService) Tags(_ context.Context, req *albvo.NodeRequest) (map[string]tocvo.Tags, error) {
	tags, err := s.ServerV3.ServersTags(req.IPs)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_server_tags_failed")
	}

	return tags, nil
}

func (s *NodeService) TagVariables(_ context.Context, req *albvo.NodeRequest) (map[string]*tocvo.ALBGroupVar, error) {
	vars := make(map[string]*tocvo.ALBGroupVar)

	for _, ip := range req.IPs {
		groupVar, err := s.Server.GetALBGroupVar(ip)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip": ip,
			}).Warn("not_found_server_from_toc")

			continue
		}

		vars[ip] = groupVar
	}

	return vars, nil
}

func (s *NodeService) fetchVersionByMeta(meta *sgwvo.ALBClusterConfigMeta) (*albvo.NodeComponentVersion, error) {
	version := s.Controller.GetComponentVersion(meta.RZ, meta.Env)
	if !version.IsUnknownFound() {
		return version, nil
	}

	version = s.Controller.GetComponentVersionByAZ(meta.AZ)
	if !version.IsUnknownFound() {
		return version, nil
	}

	version = s.Controller.GetComponentVersionByGlobal()
	if !version.IsUnknownFound() {
		return version, nil
	}

	unknownCmp, unknowVersion := version.UnknownComponentVersion()

	return nil, errors.Errorf("unknown component %s version %s", unknownCmp, unknowVersion)
}

func (s *NodeService) FulfillTaskResults(ctx context.Context, node *albvo.NodeInfo) error {
	elasticRedis, err := opscah.GetElasticRedis()
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"alb":   node.ALBName,
			"state": node.State,
		}).Warn("redis_not_initialized_skip_task_results")

		return nil
	}

	var tasks []string
	if vals, err := elasticRedis.HGet(ctx, node.ALBName, node.State).Bytes(); err != nil {
		if errors.Is(err, redis.Nil) {
			return nil
		}

		log.Logger().WithError(err).WithFields(log.Fields{
			"alb":   node.ALBName,
			"state": node.State,
		}).Error("fetch_state_task_failed")

		return errors.WithMessage(err, "fetch_state_task_failed")
	} else if err = json.Unmarshal(vals, &tasks); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"alb_name": node.ALBName,
			"state":    node.State,
		}).Error("unmarshal_state_task_failed")

		return errors.WithMessage(err, "unmarshal_state_task_failed")
	}

	if len(tasks) == 0 {
		return nil
	}

	var results []*sgwvo.TaskResult
	var completed int
	keyState := fmt.Sprintf("%s-%s", node.ALBName, node.State)
	vals := elasticRedis.HMGet(ctx, keyState, tasks...).Val()

	var lastUpdateAt int64
	slice.ForEach(vals, func(i int, v interface{}) {
		val, ok := v.(string)
		if !ok {
			return
		}

		var result *sgwvo.TaskResult
		if err := json.Unmarshal(byteutils.ToBytes(val), &result); err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"alb_name": node.ALBName,
				"state":    node.State,
			}).Error("unmarshal_state_task_failed")

			return
		}

		// if lastUpdateAt greater than updateAt, means the task has been stale, reset it
		if lastUpdateAt > result.UpdatedAt {
			result.Reset()
		}
		lastUpdateAt = result.UpdatedAt

		if result.Success {
			completed++
		}

		results = append(results, result)
	})

	if len(tasks) > 0 {
		node.Details.Progress = completed * 100 / len(tasks)
	}
	node.Details.Tasks = results

	return nil
}

// Infos return alb node info
func (s *NodeService) Infos(ctx context.Context, nodes []*sgwvo.Node) []*albvo.NodeInfo {
	var infos []*albvo.NodeInfo
	slice.ForEach(nodes, func(i int, node *sgwvo.Node) {
		albCRName := node.ALBCRName()
		albCR, err := s.Controller.GetWithContext(ctx, albCRName)
		if err != nil {
			log.Logger().WithError(err).WithField("alb", albCRName).Warn("fetch_node_cr_failed")

			return
		}

		nodeInfo := albvo.ToInfo(node, albCR)
		if err = s.FulfillTaskResults(ctx, nodeInfo); err != nil {
			log.Logger().WithError(err).WithField("alb", albCRName).Warn("fulfill_task_results_failed")
		}

		infos = append(infos, nodeInfo)
	})

	return infos
}

func (s *NodeService) InfosInInitialising(ctx context.Context, ips []string) []*albvo.NodeInfo {
	var infos []*albvo.NodeInfo

	slice.ForEach(ips, func(i int, ip string) {
		albCRName := albvo.CRName(ip)
		albCR, err := s.Controller.GetWithContext(ctx, albCRName)
		if err != nil {
			log.Logger().WithError(err).WithField("alb", albCRName).Warn("fetch_node_cr_failed")

			return
		}

		nodeInfo := albvo.ToNodeInfo(albCR)
		if err = s.FulfillTaskResults(ctx, nodeInfo); err != nil {
			log.Logger().WithError(err).WithField("alb", albCRName).Warn("fulfill_task_results_failed")
		}

		infos = append(infos, nodeInfo)
	})

	return infos
}

func (s *NodeService) GetStateNodes(ctx context.Context, req *sgwvo.StateNodesRequest) []*sgwvo.StateNodeInfo {
	var res []*sgwvo.StateNodeInfo
	albList, err := s.Controller.GetALBList(ctx)
	if err != nil {
		log.Logger().WithError(err).Error("get_alb_list_failed")

		return nil
	}

	for _, alb := range albList.Items {
		if strings.EqualFold(alb.Status.State, req.State) {
			res = append(res, &sgwvo.StateNodeInfo{
				NodeIP:      alb.Spec.LanIP,
				TicketID:    alb.Spec.ALBNode.SWPTicket,
				Application: consts.ALB,
				ClusterUUID: alb.Spec.ALBNode.ClusterUUID,
				Segment:     alb.Spec.ALBNode.Segment,
				AZ:          alb.Spec.ALBNode.Zone,
				RZ:          alb.Spec.ALBNode.IDC,
				Reason:      alb.Spec.ALBNode.Options["ma_reason"],
				State:       req.State,
			})
		}
	}

	// get toc state
	if strings.EqualFold(req.State, consts.InitialisedState.Status) && len(res) != 0 {
		servers, err := s.ServerV3.Servers(slice.Map(res, func(_ int, n *sgwvo.StateNodeInfo) string {
			return n.NodeIP
		}))
		if err != nil {
			log.Logger().WithError(err).Info("get_server_from_toc_failed")

			return nil
		}

		slice.ForEach(res, func(_ int, n *sgwvo.StateNodeInfo) {
			server, ok := servers[n.NodeIP]
			if ok {
				n.TocState = server.State
			} else {
				log.Logger().WithField("ip", n.NodeIP).Error("get_toc_info_failed")
			}
		})
	}

	return res
}

func (s *NodeService) UpdateNodeTicket(ctx context.Context, req *sgwvo.UpdateNodesRequest) ([]*core.ItemError, error) {
	var errs []*core.ItemError

	for _, node := range req.Nodes {
		err := s.Controller.UpdateMATicket(ctx, albvo.CRName(node.NodeIP), node.TicketID)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      node.NodeIP,
			})
		}
	}

	return errs, nil
}

func (s *NodeService) ExpireNodes(ctx context.Context) ([]*core.ItemError, error) {
	var errs []*core.ItemError
	albList, err := s.Controller.GetALBList(ctx)
	if err != nil {
		log.Logger().Error(err.Error())

		return nil, errors.WithMessage(err, "get_alb_list_failed")
	}

	clusters, err := s.Cluster.Dump()
	if err != nil {
		log.Logger().Error(err.Error())

		return nil, errors.WithMessage(err, "get_alb_cluster_failed")
	}

	totalNodes := map[string]struct{}{}

	slice.ForEach(clusters, func(_ int, c *albvo.Cluster) {
		slice.ForEach(c.Nodes, func(_ int, n *sgwvo.Node) {
			totalNodes[n.IP] = struct{}{}
		})
	})

	slice.ForEach(albList.Items, func(_ int, alb v1alpha1.ALB) {
		if strings.EqualFold(alb.Status.State, consts.InitialisedState.Status) ||
			strings.EqualFold(alb.Status.State, consts.RunningState.Status) {
			if _, find := totalNodes[alb.Spec.LanIP]; !find {
				if err := s.Controller.DeleteWithContext(ctx, &alb); err != nil {
					errs = append(errs, &core.ItemError{Message: err.Error(), IP: alb.Spec.LanIP})
				}
			}
		}
	})

	return errs, nil
}
