package albsvc

import (
	"context"
	"strings"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albprov"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type NodeProvisionService struct {
	TraceID    string
	Server     toc.ServerAdapter
	ServerV3   toc.ServerV3Adapter
	Provision  albprov.Provision
	Controller albctl.Controller
	Meta       toc.MetaAdapter
	Cluster    sgw.L7ClusterAdapter
	Cluster2   sgw.ALBClusterAdapter
}

func NewNodeProvisionService(traceID string) *NodeProvisionService {
	return &NodeProvisionService{
		TraceID:    traceID,
		Server:     toc.NewServerAdapter(traceID),
		ServerV3:   toc.NewServerV3Adapter(traceID),
		Controller: albctl.ALBController,
		Meta:       toc.NewMetaAdapter(traceID),
		Cluster:    sgw.NewALBClusterAdapter(traceID),
		Cluster2:   sgw.NewALBCluster(traceID),
	}
}

func (s *NodeProvisionService) Configs(_ context.Context, req *albvo.NodeRequest) ([]*toclib.ProvisionNodeConfig, error) {
	var cfgs []*toclib.ProvisionNodeConfig

	for _, ip := range req.IPs {
		tocex, err := toc.NewTocexAdapterWithIP(ip, s.TraceID)
		if err != nil {
			log.Logger().WithError(err).WithField("ip", ip).Error("toc_new_machine_failed")

			continue
		}

		cfg, err := tocex.GetNodeProvision()
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip": ip,
			}).Warn("get_node_provision_failed")

			continue
		}

		cfgs = append(cfgs, cfg)
	}

	return cfgs, nil
}

func (s *NodeProvisionService) albNodeServer(ip string) (*tocvo.ALBNodeServer, error) {
	cluster, err := s.Cluster.GetNodeByIP(ip)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	meta, err := s.Cluster2.Meta(cluster.ClusterUUID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_meta_failed")
	}

	tags, err := s.ServerV3.Tags(ip)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_server_tags_failed")
	}
	log.Logger().WithFields(log.Fields{
		"ip":   ip,
		"meta": meta.L7ClusterMeta,
	}).Info("fetch_alb_node_server")

	node := toc.NewTocexAdapter(tocvo.ToNodeByL7Meta(ip, &meta.L7ClusterMeta))
	agent, err := node.GetAgent()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_agent_failed")
	}

	segment, err := s.Meta.Segment(agent.AZv2, agent.SegmentName)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":    ip,
			"agent": agent,
		}).Error("fetch_segment_failed")

		return nil, errors.WithMessage(err, "fetch_segment_failed")
	}

	svrTocV3, err := s.ServerV3.Server(ip)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_server_v3_failed")
	}

	svrV3 := tocvo.ServerV3(*svrTocV3)

	svr := &tocvo.ALBNodeServer{
		Meta:     meta,
		Agent:    agent,
		Tags:     tags,
		Segment:  segment,
		ServerV3: &svrV3,
	}

	return svr, nil
}

func (s *NodeProvisionService) GenerateConfig(_ context.Context, req *albvo.NodeProvisionComponentRequest) (
	*tocvo.NodeProvision, error,
) {
	svr, err := s.albNodeServer(req.IP)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_alb_node_server_failed")
	}

	if s.Provision == nil {
		provision, err := albprov.NewNodeProvision(svr, s.TraceID)
		if err != nil {
			return nil, errors.WithMessage(err, "new_node_provision_failed")
		}

		s.Provision = provision
	}

	var provision *tocvo.NodeProvision
	if strings.EqualFold(req.OperationType, consts.DeleteTocexItem) {
		provision, err = s.Provision.DeletedComponent(req.Component, req.Version)
	} else {
		provision, err = s.Provision.UpgradedComponent(req.Component, req.Version)
	}
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_provision_config_failed")
	}

	return provision, nil
}

func (s *NodeProvisionService) DeleteConfigs(_ context.Context, configs []toclib.ProvisionNodeConfig) error {
	for _, config := range configs {
		cfg := config
		tocex, err := toc.NewTocexAdapterWithIP(cfg.HostIP, s.TraceID)
		if err != nil {
			return errors.WithMessage(err, "fetch_tocex_failed")
		}

		err = tocex.DeleteProvision(&cfg)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip": cfg.HostIP,
			}).Error("delete_node_provision_failed")

			return errors.WithMessage(err, "delete_provision_failed")
		}
	}

	return nil
}

func (s *NodeProvisionService) DeleteTemplate(_ context.Context, req *albvo.NodeProvisionTemplateRequest) (
	[]*core.ItemError, error,
) {
	var errs []*core.ItemError

	for _, ip := range req.IPs {
		svr, err := s.albNodeServer(ip)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		prov, err := albprov.NewNodeProvision(svr, s.TraceID)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		cfg, err := prov.NodeConfigWithTemplates(req.Paths)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		node := toc.NewTocexAdapter(svr.Node())
		err = node.DeleteProvision(cfg)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":        ip,
				"templates": len(cfg.Templates),
			}).Error("delete_node_provision_template_failed")

			continue
		}

		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":        ip,
			"templates": len(cfg.Templates),
		}).Info("delete_node_provision_template_success")
	}

	return errs, nil
}

func (s *NodeProvisionService) DeleteComponent(_ context.Context, req *albvo.NodeComponentRequest) (
	[]*core.ItemError, error,
) {
	var errs []*core.ItemError

	for _, ip := range req.IPs {
		svr, err := s.albNodeServer(ip)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_alb_node_server_failed")
		}

		prov, err := albprov.NewNodeProvision(svr, s.TraceID)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		cfg, err := prov.NodeConfigWithComponents(req.Components)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		node := toc.NewTocexAdapter(svr.Node())
		err = node.DeleteProvision(cfg)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":         ip,
				"components": len(cfg.Components),
				"templates":  len(cfg.Templates),
			}).Error("delete_node_provision_component_failed")

			continue
		}

		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":         ip,
			"components": len(cfg.Components),
			"templates":  len(cfg.Templates),
		}).Info("delete_node_provision_component_success")
	}

	return errs, nil
}

func (s *NodeProvisionService) UpdateComponent(_ context.Context, req *albvo.NodeUpdateComponentRequest) (
	[]*core.ItemError, error,
) {
	var errs []*core.ItemError

	components := req.ComponentMap()

	for _, ip := range req.IPs {
		svr, err := s.albNodeServer(ip)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_alb_node_server_failed")
		}

		node := toc.NewTocexAdapter(svr.Node())
		cfg, err := node.GetNodeProvision()
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		var coms []toclib.ProvisionNodeComponent
		slice.ForEach(cfg.Components, func(_ int, component toclib.ProvisionNodeComponent) {
			name := component.Name
			if component.Type == cmp.ComponentTypeDocker {
				name = component.Name
			}

			if _, ok := components[name]; ok {
				component.Disabled = req.Disabled
				coms = append(coms, component)
			}
		})

		if len(coms) == 0 {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip": ip,
			}).Warn("not_found_node_provision_component")

			continue
		}

		err = node.SetNodeProvision(&toclib.ProvisionNodeConfig{
			Components: coms,
			HostIP:     ip,
		})
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":         ip,
				"components": req.Components,
				"disabled":   req.Disabled,
			}).Error("update_node_provision_component_failed")

			continue
		}

		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":         ip,
			"components": req.Components,
			"disabled":   req.Disabled,
		}).Info("update_node_provision_component_success")
	}

	return errs, nil
}

func (s *NodeProvisionService) PurgeContainers(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	return s.purgeConfigs(ctx, req, cmp.ComponentTypeDocker), nil
}

func (s *NodeProvisionService) purgeConfigs(_ context.Context, req *albvo.NodeRequest, typ string) []*core.ItemError {
	var errs []*core.ItemError
	for _, ip := range req.IPs {
		svr, err := s.albNodeServer(ip)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		prov, err := albprov.NewNodeProvision(svr, s.TraceID)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		cfg, err := prov.NodeConfig(typ)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			continue
		}

		node := toc.NewTocexAdapter(svr.Node())
		err = node.DeleteProvision(cfg)
		if err != nil {
			errs = append(errs, &core.ItemError{
				Message: err.Error(),
				IP:      ip,
			})

			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":         ip,
				"components": len(cfg.Components),
				"templates":  len(cfg.Templates),
			}).Error("delete_node_provision_failed")

			continue
		}

		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":         ip,
			"components": len(cfg.Components),
			"templates":  len(cfg.Templates),
		}).Info("delete_node_provision_success")
	}

	return errs
}

// PurgeConfigs purge configs from tocex
func (s *NodeProvisionService) PurgeConfigs(ctx context.Context, req *albvo.NodeRequest) ([]*core.ItemError, error) {
	return s.purgeConfigs(ctx, req, cmp.ComponentTypeUnSpec), nil
}

// Version return alb provision components' version
func (s *NodeProvisionService) Version(ip string) (*albvo.NodeComponentVersion, error) {
	groupVar, err := s.Server.GetALBGroupVar(ip)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_alb_group_var_failed")
	}

	version, err := s.fetchVersionByVars(groupVar)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_version_by_vars_failed")
	}

	return version, nil
}

func (s *NodeProvisionService) fetchVersionByVars(groupVar *tocvo.ALBGroupVar) (*albvo.NodeComponentVersion, error) {
	version := s.Controller.GetComponentVersion(groupVar.IDC, groupVar.Env)
	if !version.IsUnknownFound() {
		return version, nil
	}

	version = s.Controller.GetComponentVersionByAZ(groupVar.AZ)
	if !version.IsUnknownFound() {
		return version, nil
	}

	version = s.Controller.GetComponentVersionByGlobal()
	if !version.IsUnknownFound() {
		return version, nil
	}

	unknownCmp, unknowVersion := version.UnknownComponentVersion()

	return nil, errors.Errorf("unknown component %s version %s", unknownCmp, unknowVersion)
}
