package albsvc

import (
	"context"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

func TestNodeCheckerService_checkRingBuffer(t *testing.T) {
	t.Parallel()

	t.Run("local", func(t *testing.T) {
		t.<PERSON>l()

		if ciEnv {
			return
		}

		node := &NodeCheckerService{
			TraceID: t.Name(),
			Meta:    toc.NewMetaAdapter(t.Name()),
			Server:  toc.NewServerAdapter(t.Name()), ServerV3: toc.NewServerV3Adapter(t.Name()),
		}

		tocex := toc.NewTocexAdapter(&tocvo.Node{
			HostIP:  configs.E2E.ALB.Node2,
			AZ:      "ap-sg-1-general-x",
			Env:     "test",
			Segment: "General",
			Cluster: "shopee",
		})
		result := node.checkNicOptimization(tocex)
		assert.NotEmpty(t, result)
		assert.True(t, result.Success)
	})
}

func TestNodeCheckerService_checkIPTables(t *testing.T) {
	t.Parallel()

	t.Run("local", func(t *testing.T) {
		t.Parallel()

		if ciEnv {
			return
		}

		node := &NodeCheckerService{
			TraceID: t.Name(),
			Meta:    toc.NewMetaAdapter(t.Name()),
			Server:  toc.NewServerAdapter(t.Name()), ServerV3: toc.NewServerV3Adapter(t.Name()),
		}

		tocex := toc.NewTocexAdapter(&tocvo.Node{
			HostIP:  configs.E2E.ALB.Node2,
			AZ:      "ap-sg-1-general-x",
			Env:     "test",
			Segment: "General",
			Cluster: "shopee",
		})
		result := node.checkIPTables(configs.E2E.ALB.Node2, tocex)
		assert.NotEmpty(t, result)
		assert.False(t, result.Success)
	})
}

func TestNodeCheckerService_PreCheck(t *testing.T) {
	t.Parallel()

	t.Run("local", func(t *testing.T) {
		t.Parallel()

		if ciEnv {
			return
		}

		node := &NodeCheckerService{
			TraceID: t.Name(),
			Meta:    toc.NewMetaAdapter(t.Name()),
			Server:  toc.NewServerAdapter(t.Name()), ServerV3: toc.NewServerV3Adapter(t.Name()),
		}

		result, err := node.PreCheck(context.Background(), &albvo.ClusterNodeIPsRequest{
			IPs:  []string{configs.E2E.ALB.Node2},
			UUID: configs.E2E.ALB.UUID2,
		})

		assert.NoError(t, err)
		assert.Equal(t, 1, len(result))
		assert.True(t, result[0].Passed)
	})
}

func TestNodeCheckerService_shouldCheckVRRPIPTables(t *testing.T) {
	t.Parallel()

	azName := "ap-sg-1-private-a"
	azName2 := "ap-sg-1-general-x"
	mockServerV3 := toc.NewMockServerV3Adapter(gomock.NewController(t))

	mockMeta := toc.NewMockMetaAdapter(gomock.NewController(t))
	mockMeta.EXPECT().Segment(gomock.Any(), gomock.Any()).Return(&tocvo.Segment{
		NetworkVersion: "V3.0",
	}, nil).AnyTimes()
	mockMeta.EXPECT().NetworkVersion(gomock.Eq(azName), gomock.Any()).Return(
		consts.NetworkV1, nil).AnyTimes()
	mockMeta.EXPECT().NetworkVersion(gomock.Eq(azName2), gomock.Any()).Return(
		consts.NetworkV1, nil).AnyTimes()

	t.Run("valid", func(t *testing.T) {
		t.Parallel()

		node := &NodeCheckerService{
			TraceID:  t.Name(),
			Meta:     mockMeta,
			Server:   toc.NewServerAdapter(t.Name()),
			ServerV3: mockServerV3,
		}

		mockServerV3.EXPECT().Server(gomock.Eq(configs.E2E.ALB.Node2)).Return(&toclib.ServerV3{
			AZ: azName,
			Tags: []toclib.Tag{
				{TagCategoryKey: consts.HAMTagName, Value: consts.HAMNonStd1.Name},
			},
			IPWan: gofakeit.IPv4Address(),
		}, nil).Times(1)

		ok := node.shouldCheckVRRPIPTables(configs.E2E.ALB.Node2)
		assert.True(t, ok)
	})

	t.Run("invalid", func(t *testing.T) {
		t.Parallel()

		node := &NodeCheckerService{
			TraceID:  t.Name(),
			Meta:     mockMeta,
			Server:   toc.NewServerAdapter(t.Name()),
			ServerV3: mockServerV3,
		}

		t.Run("not_ham_nonstd1", func(t *testing.T) {
			mockServerV3.EXPECT().Server(gomock.Eq(configs.E2E.ALB.Node)).Return(&toclib.ServerV3{
				AZ: azName,
				Tags: []toclib.Tag{
					{TagCategoryKey: consts.HAMTagName, Value: consts.HAMStd1.Name},
				},
			}, nil).Times(1)

			ok := node.shouldCheckVRRPIPTables(configs.E2E.ALB.Node)
			assert.False(t, ok)
		})

		t.Run("not_private_az", func(t *testing.T) {
			mockServerV3.EXPECT().Server(gomock.Eq(configs.E2E.ALB.Node2)).Return(&toclib.ServerV3{
				AZ: azName2,
				Tags: []toclib.Tag{
					{TagCategoryKey: consts.HAMTagName, Value: consts.HAMNonStd1.Name},
				},
			}, nil).Times(1)

			ok := node.shouldCheckVRRPIPTables(configs.E2E.ALB.Node2)
			assert.False(t, ok)
		})
	})
}

func TestNodeCheckerService_nginxTestTimeout(t *testing.T) {
	t.Parallel()

	node := &NodeCheckerService{}
	// NB: ALB_PRECHECK_NGINX_TEST_TIMEOUT is set in init() in base_test.go
	timeout := node.nginxTestTimeout()

	assert.NotEqual(t, configs.ALB.Precheck.NginxTestTimeout, timeout.Seconds())
	assert.Equal(t, albNginxTestTimeout, timeout.Seconds())
}

func TestNodeCheckerService_checkServerTree(t *testing.T) {
	t.Parallel()
	node := NodeCheckerService{
		ServerV3: toc.NewServerV3Adapter(t.Name()),
	}

	t.Run("unit_test_ok", func(t *testing.T) {
		t.Parallel()

		ret := node.checkTreeNode(configs.E2E.ALB.Node2)
		assert.True(t, ret.Success)
	})
}

func TestNodeCheckerService_checkNicDriver(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	setupTest := func(
		drivers map[string]*tocvo.NicDriver,
		kernelVersion string,
	) (*NodeCheckerService, *toc.MockNetAdapter, *toc.MockOSAdapter) {
		mockNet := toc.NewMockNetAdapter(ctrl)
		mockOS := toc.NewMockOSAdapter(ctrl)

		mockNet.EXPECT().
			NicDrivers().
			Return(drivers, nil).
			Times(1)

		if kernelVersion != "" {
			mockOS.EXPECT().
				KernelVersion().
				Return(kernelVersion, nil).
				Times(1)
		}

		service := &NodeCheckerService{
			Net: mockNet,
			OS:  mockOS,
		}

		return service, mockNet, mockOS
	}

	t.Run("success", func(t *testing.T) {
		t.Parallel()

		drivers := map[string]*tocvo.NicDriver{
			"enp26s0f0": {
				Name:       "enp26s0f0",
				DriverName: "mlx5_core",
				Version:    "5.15.0-52-shopee-generic",
			},
		}

		service, _, _ := setupTest(drivers, "")
		result := service.checkNicDriver(nil)

		assert.True(t, result.Success)
		assert.Equal(t, "check_nic_driver", result.Task)
		assert.Equal(t, "nic_driver_is_up_to_date", result.Reason)
		assert.WithinDuration(t, time.Now(), time.Unix(result.UpdatedAt, 0), time.Second)
	})

	t.Run("ice_driver_with_old_version", func(t *testing.T) {
		t.Parallel()

		drivers := map[string]*tocvo.NicDriver{
			"enp26s0f0": {
				Name:       "enp26s0f0",
				DriverName: "ice",
				Version:    "5.15.0-52-shopee-generic",
			},
		}

		service, _, _ := setupTest(drivers, "5.15.0-52-shopee-generic")
		result := service.checkNicDriver(nil)

		assert.False(t, result.Success)
		assert.Equal(t, "check_nic_driver", result.Task)
		assert.Equal(t, "nic_enp26s0f0_ice_driver_is_not_up_to_date", result.Reason)
		assert.WithinDuration(t, time.Now(), time.Unix(result.UpdatedAt, 0), time.Second)
	})

	t.Run("ice_driver_with_new_version", func(t *testing.T) {
		t.Parallel()

		drivers := map[string]*tocvo.NicDriver{
			"enp26s0f0": {
				Name:       "enp26s0f0",
				DriverName: "ice",
				Version:    "5.15.0-53-shopee-generic",
			},
		}

		service, _, _ := setupTest(drivers, "5.15.0-52-shopee-generic")
		result := service.checkNicDriver(nil)

		assert.True(t, result.Success)
		assert.Equal(t, "check_nic_driver", result.Task)
		assert.Equal(t, "nic_driver_is_up_to_date", result.Reason)
		assert.WithinDuration(t, time.Now(), time.Unix(result.UpdatedAt, 0), time.Second)
	})

	t.Run("ensure_net_adapter_failed", func(t *testing.T) {
		t.Parallel()

		service := &NodeCheckerService{
			Net: nil,
		}

		result := service.checkNicDriver(nil)

		assert.False(t, result.Success)
		assert.Equal(t, "check_nic_driver", result.Task)
		assert.Contains(t, result.Reason, "tocexadapter_is_required")
		assert.WithinDuration(t, time.Now(), time.Unix(result.UpdatedAt, 0), time.Second)
	})

	t.Run("ensure_os_adapter_failed", func(t *testing.T) {
		t.Parallel()

		mockNet := toc.NewMockNetAdapter(ctrl)
		service := &NodeCheckerService{
			Net: mockNet,
			OS:  nil,
		}

		result := service.checkNicDriver(nil)

		assert.False(t, result.Success)
		assert.Equal(t, "check_nic_driver", result.Task)
		assert.Contains(t, result.Reason, "tocexadapter_is_required")
		assert.WithinDuration(t, time.Now(), time.Unix(result.UpdatedAt, 0), time.Second)
	})

	t.Run("nic_drivers_failed", func(t *testing.T) {
		t.Parallel()

		mockNet := toc.NewMockNetAdapter(ctrl)
		mockOS := toc.NewMockOSAdapter(ctrl)

		mockNet.EXPECT().
			NicDrivers().
			Return(nil, assert.AnError).
			Times(1)

		service := &NodeCheckerService{
			Net: mockNet,
			OS:  mockOS,
		}

		result := service.checkNicDriver(nil)

		assert.False(t, result.Success)
		assert.Equal(t, "check_nic_driver", result.Task)
		assert.Contains(t, result.Reason, "fetch_driver_failed")
		assert.WithinDuration(t, time.Now(), time.Unix(result.UpdatedAt, 0), time.Second)
	})

	t.Run("kernel_version_failed", func(t *testing.T) {
		t.Parallel()

		drivers := map[string]*tocvo.NicDriver{
			"enp26s0f0": {
				Name:       "enp26s0f0",
				DriverName: "ice",
				Version:    "5.15.0-52-shopee-generic",
			},
		}

		mockNet := toc.NewMockNetAdapter(ctrl)
		mockOS := toc.NewMockOSAdapter(ctrl)

		mockNet.EXPECT().
			NicDrivers().
			Return(drivers, nil).
			Times(1)

		mockOS.EXPECT().
			KernelVersion().
			Return("", assert.AnError).
			Times(1)

		service := &NodeCheckerService{
			Net: mockNet,
			OS:  mockOS,
		}

		result := service.checkNicDriver(nil)

		assert.False(t, result.Success)
		assert.Equal(t, "check_nic_driver", result.Task)
		assert.Contains(t, result.Reason, "fetch_kernel_version_failed")
		assert.WithinDuration(t, time.Now(), time.Unix(result.UpdatedAt, 0), time.Second)
	})
}
