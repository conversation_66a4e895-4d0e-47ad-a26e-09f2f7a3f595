package albsvc

import (
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/etcd/etcdvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/zk/zkvo"
)

// it works on status=Ready Nodes

type readyChecker struct {
	sgwNode toc.SGWNodeAdapter // to online node
	nodeIPs []string           // ready Node IPs
}

func (s *NodeCheckerService) fetchReadyCheckResult(
	checker *readyChecker,
) ([]*sgwvo.TaskResult, error) {
	var rets []*sgwvo.TaskResult

	if len(checker.nodeIPs) == 0 {
		return rets, nil
	}

	nodeIP2, _ := slice.Random(checker.nodeIPs)
	tocex2, err := toc.NewTocexAdapterWithIP(nodeIP2, s.TraceID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_tocex_failed")
	}

	sgwNode2, err := toc.NewSGWNodeAdapter(tocex2)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_sgw_node_failed")
	}

	etcdRet := wrapCheckWithTiming(func() *sgwvo.TaskResult {
		return s.checkEtcdServers(checker.sgwNode, sgwNode2)
	})

	// Track zk check
	zkRet := wrapCheckWithTiming(func() *sgwvo.TaskResult {
		return s.checkZKServers(checker.sgwNode, sgwNode2)
	})

	rets = append(rets, etcdRet, zkRet)

	return rets, nil
}

//nolint:dupl
func (s *NodeCheckerService) checkEtcdServers(node toc.SGWNodeAdapter, node2 toc.SGWNodeAdapter) *sgwvo.TaskResult {
	const taskName = "check_etcd_server_list"
	now := time.Now().Unix()

	targetEtcd, err := node.SGWEtcd()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_sgw_node_failed").Error(),
		}
	}

	targetEtcdMeta := &etcdvo.Meta{ClusterURL: targetEtcd}
	targetEtcdEndpoints, err := targetEtcdMeta.Endpoints()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_sgw_etcd_endpoint_failed").Error(),
		}
	}

	etcd, err := node2.SGWEtcd()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_ready_node_failed").Error(),
		}
	}

	etcdMeta := &etcdvo.Meta{ClusterURL: etcd}
	etcdEndpoints, err := etcdMeta.Endpoints()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_sgw_etcd_endpoint_failed").Error(),
		}
	}

	// Compare the etcd server lists
	if !etcdvo.ETCDEndpoints(targetEtcdEndpoints).Equals(etcdEndpoints) {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    "etcd_server_list_not_matched",
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: now,
		Task:      taskName,
		Reason:    "etcd_server_list_matched",
	}
}

//nolint:dupl
func (s *NodeCheckerService) checkZKServers(node toc.SGWNodeAdapter, node2 toc.SGWNodeAdapter) *sgwvo.TaskResult {
	const taskName = "check_zk_server_list"
	now := time.Now().Unix()

	targetZK, err := node.MesosZK()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_mesos_zk_failed").Error(),
		}
	}

	targetZKMeta := &zkvo.Meta{ClusterURL: targetZK}
	targetZKEndpoints, err := targetZKMeta.Endpoints()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_zk_endpoint_failed").Error(),
		}
	}

	zk, err := node2.MesosZK()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_ready_node_failed").Error(),
		}
	}

	zkMeta := &zkvo.Meta{ClusterURL: zk}
	zkEndpoints, err := zkMeta.Endpoints()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_zk_endpoint_failed").Error(),
		}
	}

	// Compare the etcd server lists
	if !zkvo.ZKEndpoints(targetZKEndpoints).Equals(zkEndpoints) {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: now,
			Task:      taskName,
			Reason:    "mesos_zk_not_matched",
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: now,
		Task:      taskName,
		Reason:    "mesos_zk_matched",
	}
}
