package albsvc

import (
	"time"

	"github.com/pkg/errors"
	"github.com/sourcegraph/conc/iter"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

// TrafficService traffic service
type TrafficService struct {
	TraceID string
}

func (s *TrafficService) Block(req *albvo.ControlTrafficFormRequest) ([]*sgwvo.NodeTaskResult, error) {
	results, err := iter.MapErr(req.IPs, func(ip *string) (*sgwvo.NodeTaskResult, error) {
		node := *ip

		tocex, err := toc.NewTocexAdapterWithIP(node, s.TraceID)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_tocex_failed")
		}

		ret := s.BlockTrafficByIP(tocex)

		var tasks []*sgwvo.TaskResult
		tasks = append(tasks, ret)

		ret = s.checkHAStatus(tocex)
		tasks = append(tasks, ret)

		return &sgwvo.NodeTaskResult{
			IP:     node,
			Passed: ret.Success,
			Tasks:  tasks,
		}, nil
	})
	if err != nil {
		return nil, errors.WithMessage(err, "block_traffic_failed")
	}

	return results, nil
}

func (s *TrafficService) BlockByDomains(req *albvo.ControlTrafficFormRequest) ([]*sgwvo.NodeTaskResult, error) {
	results, err := iter.MapErr(req.IPs, func(ip *string) (*sgwvo.NodeTaskResult, error) {
		node := *ip

		tocex, err := toc.NewTocexAdapterWithIP(node, s.TraceID)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_tocex_failed")
		}

		ret := s.BlockTrafficByDomainsByIP(tocex, req.Domains)

		var tasks []*sgwvo.TaskResult
		tasks = append(tasks, ret)

		return &sgwvo.NodeTaskResult{
			IP:     node,
			Passed: ret.Success,
			Tasks:  tasks,
		}, nil
	})
	if err != nil {
		return nil, errors.WithMessage(err, "block_traffic_by_domains_failed")
	}

	return results, nil
}

func (s *TrafficService) OpenByDomains(req *albvo.ControlTrafficFormRequest) ([]*sgwvo.NodeTaskResult, error) {
	results, err := iter.MapErr(req.IPs, func(ip *string) (*sgwvo.NodeTaskResult, error) {
		node := *ip

		tocex, err := toc.NewTocexAdapterWithIP(node, s.TraceID)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_tocex_failed")
		}

		ret := s.DeleteBlockDomainsByIP(tocex)

		var tasks []*sgwvo.TaskResult
		tasks = append(tasks, ret)

		return &sgwvo.NodeTaskResult{
			IP:     node,
			Passed: ret.Success,
			Tasks:  tasks,
		}, nil
	})
	if err != nil {
		return nil, errors.WithMessage(err, "block_traffic_by_domains_failed")
	}

	return results, nil
}

func (s *TrafficService) BlockTrafficByIP(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	_, err := tocex.RunTask(consts.BlockTrafficScript)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      "stop_keepalived_or_bird",
			Reason:    errors.Wrapf(err, "stop_keepalived_or_bird_failed").Error(),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      "stop_keepalived_or_bird",
		Reason:    "stop_keepalived_or_bird_success",
	}
}

func (s *TrafficService) BlockTrafficByDomainsByIP(tocex toc.TocexAdapter, domains []string) *sgwvo.TaskResult {
	script, err := consts.BlockTrafficByDomainsScript(domains)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      "generated_block_traffic_by_domains_script",
			Reason:    errors.Wrapf(err, "generated_block_traffic_by_domains_script_failed").Error(),
		}
	}
	_, err = tocex.RunTask(script)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      "block_traffic_by_domains",
			Reason:    errors.Wrapf(err, "block_traffic_by_domains_failed").Error(),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      "block_traffic_by_domains",
		Reason:    "block_traffic_by_domains_success",
	}
}

func (s *TrafficService) DeleteBlockDomainsByIP(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	_, err := tocex.RunTask(consts.DeletedBlockedDomainsScript)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      "delete_blocked_domains",
			Reason:    errors.Wrapf(err, "delete_blocked_domains_failed").Error(),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      "delete_blocked_domains",
		Reason:    "delete_blocked_domains_success",
	}
}

func (s *TrafficService) Open(req *albvo.ControlTrafficFormRequest) ([]*sgwvo.NodeTaskResult, error) {
	results, err := iter.MapErr(req.IPs, func(ip *string) (*sgwvo.NodeTaskResult, error) {
		node := *ip

		tocex, err := toc.NewTocexAdapterWithIP(node, s.TraceID)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_tocex_failed")
		}

		ret := s.openByIP(tocex)

		var tasks []*sgwvo.TaskResult
		tasks = append(tasks, ret)

		ret = s.checkHAStatus(tocex)
		tasks = append(tasks, ret)

		return &sgwvo.NodeTaskResult{
			IP:     node,
			Passed: ret.Success,
			Tasks:  tasks,
		}, nil
	})
	if err != nil {
		return nil, errors.WithMessage(err, "open_traffic_failed")
	}

	return results, nil
}

func (s *TrafficService) openByIP(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	_, err := tocex.RunTask(consts.OpenTrafficScript)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      "start_keepalived_or_bird",
			Reason:    errors.Wrapf(err, "start_keepalived_or_bird_failed").Error(),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      "start_keepalived_or_bird",
		Reason:    "start_keepalived_or_bird_success",
	}
}

func (s *TrafficService) checkHAStatus(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	resp, err := tocex.RunTask(consts.CheckKeepalivedOrBirdStatus)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      "check_keepalived_or_bird_status",
			Reason:    errors.Wrapf(err, "start_keepalived_or_bird_failed").Error(),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      "check_keepalived_or_bird_status",
		Reason:    resp,
	}
}
