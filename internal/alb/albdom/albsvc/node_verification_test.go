package albsvc

import (
	"net"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/duke-git/lancet/v2/cryptor"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

const (
	LiveClusterUUID    = "fa4f3ea1-f562-52c8-a01d-2d8b04ab9911"
	NonLiveClusterUUID = "fa4f3ea1-f562-52c8-a01d-2d8b04ab9912"
)

func TestNodeService_VerifyTagAndVariable(t *testing.T) {
	t.Parallel()

	t.Run("server_tags_failed", func(t *testing.T) {
		t.Parallel()

		t.Run("mock", func(t *testing.T) {
			t.Parallel()

			fakeit := gofakeit.New(time.Now().Unix())
			nodeIP := fakeit.IPv4Address()
			node := newTestNodeVerifierService(t)
			server := toc.NewMockServerV3Adapter(gomock.NewController(t))
			server.EXPECT().Servers(gomock.Eq([]string{nodeIP})).
				Return(nil, errors.New("not found")).AnyTimes()
			node.ServerV3 = server

			err := node.VerifyServerMeta(&albvo.ClusterNodeVerifyRequest{IPs: []string{nodeIP}, UUID: fakeit.UUID()})
			require.Error(t, err)
			assert.Contains(t, err.Error(), "fetch_servers_failed")
		})
	})
	t.Run("verify_tags_failed", func(t *testing.T) {
		t.Parallel()

		t.Run("local", func(t *testing.T) {
			t.Parallel()

			fakeit := gofakeit.New(time.Now().Unix())
			node := &NodeVerifierService{
				TraceID:  t.Name(),
				Server:   toc.NewServerAdapter(t.Name()),
				ServerV3: toc.NewServerV3Adapter(t.Name()),
				Cluster:  sgw.NewALBClusterAdapter(t.Name()),

				Ext2Cluster: sgw.NewExt2Cluster(t.Name()),
			}

			err := node.VerifyServerMeta(&albvo.ClusterNodeVerifyRequest{
				IPs: []string{fakeit.IPv4Address()}, UUID: fakeit.UUID(),
			})
			require.Error(t, err)
			assert.Contains(t, err.Error(), "fetch_servers_failed")
		})
	})
}

func fetchFakeALBClusterConfigMeta(t *testing.T) *sgwvo.ALBClusterConfigMeta {
	t.Helper()

	fakeit := gofakeit.New(time.Now().Unix())

	meta := &sgwvo.ALBClusterConfigMeta{}
	assert.NoError(t, fakeit.Struct(meta))
	meta.ECMPBGPVIPs = []string{
		"********",
	}

	wanVIP := generatePublicIPv4()
	meta.ECMPBGPWanVIPs = []string{
		wanVIP,
	}

	meta.BGPASMapping = map[string]string{
		fakeit.IPv4Address(): "40008000",
		fakeit.IPv4Address(): "40008001",
	}

	haInstance := sgwvo.KeepalivedInstance{}
	assert.NoError(t, fakeit.Struct(&haInstance))
	haInstance.VIP = wanVIP
	meta.HAInstances = []*sgwvo.HAInstance{
		{
			Node:      fakeit.IPv4Address(),
			Instances: []*sgwvo.KeepalivedInstance{&haInstance},
		},
	}

	meta.SegmentKey = cryptor.Md5String(meta.Segment)

	return meta
}

func TestNodeService_fakeALBGroupVar(t *testing.T) {
	groupVar := fetchFakeALBClusterConfigMeta(t)
	assert.NotNil(t, groupVar)
}

func generatePublicIPv4() string {
	for {
		ip := gofakeit.IPv4Address()

		if !net.ParseIP(ip).IsPrivate() {
			return ip
		}
	}
}

func TestNodeService_verifyHAVIPs(t *testing.T) {
	node := newTestNodeVerifierService(t)
	meta := fetchFakeALBClusterConfigMeta(t)

	// Add mock handling for L7ClusterAdapter to test NONSTD1 mode
	clusterAdapter := sgw.NewMockL7ClusterAdapter(gomock.NewController(t))
	node.Cluster = clusterAdapter

	t.Run("ok_non_std3", func(t *testing.T) {
		err := node.verifyHAVIPs(meta, consts.HAMNonStd3)
		assert.NoError(t, err)
	})

	t.Run("nil_ecmp_bgp_vips", func(t *testing.T) {
		meta.ECMPBGPVIPs = nil
		err := node.verifyHAVIPs(meta, consts.HAMNonStd3)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not found variable ecmp_bgp_vips")
	})

	t.Run("public_ecmp_bgp_vips", func(t *testing.T) {
		meta.ECMPBGPVIPs = []string{
			generatePublicIPv4(),
		}
		err := node.verifyHAVIPs(meta, consts.HAMNonStd3)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "must be private")
	})

	t.Run("no_ips_in_ecmp_bgp_wan_vips", func(t *testing.T) {
		meta.ECMPBGPWanVIPs = nil
		err := node.verifyHAVIPs(meta, consts.HAMNonStd2)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not found variable ecmp_bgp_wan_vips")
	})

	t.Run("priv_ips_in_ecmp_bgp_wan_vips", func(t *testing.T) {
		meta.ECMPBGPWanVIPs = []string{
			"********",
		}
		err := node.verifyHAVIPs(meta, consts.HAMNonStd2)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "must be public")
	})

	// Mock cluster info for NONSTD1 mode test
	t.Run("nonstd1_mode", func(t *testing.T) {
		// Clear VIPs from keepalived instances
		tempGroupVar := fetchFakeALBClusterConfigMeta(t)
		tempGroupVar.NetworkType = consts.NetworkTypeWan.Name

		for _, instance := range tempGroupVar.HAInstances {
			instance.Instances = nil
		}

		err := node.verifyHAVIPs(tempGroupVar, consts.HAMNonStd1)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "wan_vip_required ha_mode=NonStd1")
	})
}

func verifyNodeHAGroupVarHAMNonStd2BGPASMapping(t *testing.T, tocex toc.TocexAdapter) error {
	t.Helper()

	return verifyNodeHAGroupVarBGPASMapping(t, tocex, consts.HAMNonStd2)
}

func verifyNodeHAGroupVarHAMNonStd3BGPASMapping(t *testing.T, tocex toc.TocexAdapter) error {
	t.Helper()

	return verifyNodeHAGroupVarBGPASMapping(t, tocex, consts.HAMNonStd3)
}

func verifyNodeHAGroupVarBGPASMapping(t *testing.T, tocex toc.TocexAdapter, ham consts.HighAvailableMode) error {
	t.Helper()

	node := newTestNodeVerifierService(t)
	groupVar := fetchFakeALBClusterConfigMeta(t)

	groupVar.BGPASMapping = nil

	return node.verifyPrivateClusterConfig(ham, groupVar, tocex)
}

func TestNodeService_verifyHAGroupVarNoBGPASMapping(t *testing.T) {
	t.Parallel()

	t.Run("fetch_route_failed", func(t *testing.T) {
		t.Parallel()

		fakeit := gofakeit.New(time.Now().Unix())

		tNode := &tocvo.Node{
			HostIP: fakeit.IPv4Address(),
			AZ:     "sg2",
			Env:    "test",
		}

		tocex := toc.NewTocexAdapter(tNode)
		err := verifyNodeHAGroupVarHAMNonStd3BGPASMapping(t, tocex)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "fetch_route_tables_failed")
	})

	t.Run("local", func(t *testing.T) {
		t.Parallel()

		if ciEnv {
			return
		}

		tNode := &tocvo.Node{
			HostIP: configs.E2E.ALB.Node2, AZ: "sg2",
			Env: "test",
		}

		tocex := toc.NewTocexAdapter(tNode)
		err := verifyNodeHAGroupVarHAMNonStd3BGPASMapping(t, tocex)
		assert.NoError(t, err)
	})

	t.Run("mock", func(t *testing.T) {
		t.Parallel()

		tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
		tocex.EXPECT().ReadFile("/proc/net/route").DoAndReturn(func(path string) (string, error) {
			return `Iface	Destination	Gateway 	Flags	RefCnt	Use	Metric	Mask		MTU	Window	IRTT
bond0.1000	00000000	BE6E810A	0003	0	0	0	00000000	0	0	0`, nil
		}).AnyTimes()

		tocex.EXPECT().RunTask(gomock.Any()).Return("", nil).AnyTimes()

		// BGPASMapping should be privateIP:as with 2 peers
		err := verifyNodeHAGroupVarHAMNonStd3BGPASMapping(t, tocex)
		assert.NoError(t, err)
	})
}

func TestNodeService_verifyHAGroupVarNoBGPASMapping_noRouteTables(t *testing.T) {
	t.Parallel()

	tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
	tocex.EXPECT().ReadFile("/proc/net/route").DoAndReturn(func(path string) (string, error) {
		return `Iface	Destination	Gateway 	Flags	RefCnt	Use	Metric	Mask		MTU	Window	IRTT`, nil
	}).AnyTimes()

	err := verifyNodeHAGroupVarHAMNonStd3BGPASMapping(t, tocex)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no default route table")
}

func TestNodeService_verifyHAGroupVarNoBGPASMapping_peerShouldBePrivate(t *testing.T) {
	t.Parallel()

	tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
	tocex.EXPECT().ReadFile("/proc/net/route").DoAndReturn(func(path string) (string, error) {
		return `Iface	Destination	Gateway 	Flags	RefCnt	Use	Metric	Mask		MTU	Window	IRTT
bond0.1000	00000000	3EE67867	0003	0	0	0	00000000	0	0	0
`, nil
	}).AnyTimes()

	tocex.EXPECT().RunTask(gomock.Any()).Return("", nil).AnyTimes()

	err := verifyNodeHAGroupVarHAMNonStd3BGPASMapping(t, tocex)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must be private")
}

func TestNodeService_verifyHAGroupVarNoBGPASMapping_peerShouldBePublic(t *testing.T) {
	t.Parallel()

	tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
	tocex.EXPECT().ReadFile("/proc/net/route").DoAndReturn(func(path string) (string, error) {
		return `Iface	Destination	Gateway 	Flags	RefCnt	Use	Metric	Mask		MTU	Window	IRTT
bond0.1000	00000000	BE6E810A	0003	0	0	0	00000000	0	0	0
`, nil
	}).AnyTimes()

	err := verifyNodeHAGroupVarHAMNonStd2BGPASMapping(t, tocex)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must be public")
}

func TestVerifyHardware(t *testing.T) {
	t.Parallel()

	node := newTestNodeVerifierService(t)

	tocex := toc.NewTocexAdapter(&tocvo.Node{
		HostIP:  configs.E2E.ALB.Node2,
		AZ:      "ap-sg-1-general-x",
		Env:     "test",
		Cluster: consts.Shopee,
	})

	err := node.verifyServerHardware(tocex)
	assert.NoError(t, err)
}

func TestVerifyDataDirMounted(t *testing.T) {
	t.Parallel()

	t.Run("unit_test_ok", func(t *testing.T) {
		t.Parallel()

		mockTocex := toc.NewMockTocexAdapter(gomock.NewController(t))
		mockTocex.EXPECT().ExistFile(gomock.Eq(consts.DataDirMountCheckSkipFile)).
			Return(false, nil).Times(1)
		mockTocex.EXPECT().ReadFile(gomock.Eq("/proc/mounts")).Return(`
/dev/sda5 /data ext4 rw,relatime 0 0
`, nil).AnyTimes()

		node := newTestNodeVerifierService(t)

		err := node.verifyDataDirMounted(mockTocex)
		assert.NoError(t, err)
	})

	t.Run("local", func(t *testing.T) {
		t.Parallel()

		if ciEnv {
			return
		}

		tocex := toc.NewTocexAdapter(&tocvo.Node{
			HostIP:  configs.E2E.ALB.Node2,
			AZ:      "ap-sg-1-general-x",
			Env:     "test",
			Segment: "General",
			Cluster: "shopee",
		})

		node := newTestNodeVerifierService(t)

		err := node.verifyDataDirMounted(tocex)

		assert.NoError(t, err)
	})

	t.Run("skip_file_exists", func(t *testing.T) {
		t.Parallel()

		mockTocex := toc.NewMockTocexAdapter(gomock.NewController(t))

		// mock tocex.ExistFile
		mockTocex.EXPECT().ExistFile(consts.DataDirMountCheckSkipFile).Return(true, nil).Times(1)
		// mock tocex.MountPoints
		mockTocex.EXPECT().ReadFile(gomock.Eq("/proc/mounts")).Return(``, nil).Times(0)

		node := newTestNodeVerifierService(t)

		err := node.verifyDataDirMounted(mockTocex)
		assert.NoError(t, err)
	})
}

func TestNodeServiceVerifyClusterRackFailed(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	node := newTestNodeVerifierService(t)

	// Mock ServerV3.Server calls (rack1, with error)
	mockServerV3, ok := node.ServerV3.(*toc.MockServerV3Adapter)
	assert.True(t, ok)
	assert.NotNil(t, mockServerV3)
	if ok && mockServerV3 != nil {
		mockServerV3.EXPECT().Server(gomock.Any()).AnyTimes().Return(&toclib.ServerV3{
			Rack: "rack1",
			AZ:   "ap-sg-1-general-x",
		}, nil)
	}

	mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
	assert.NotNil(t, mockCluster)

	mockCluster.EXPECT().GetByUUID(gomock.Eq(LiveClusterUUID)).Return(&albvo.Cluster{
		Env: gofakeit.RandomString([]string{consts.EnvLive, consts.EnvLiveish}),
		Nodes: []*sgwvo.Node{
			{IP: gofakeit.IPv4Address()},
			{IP: gofakeit.IPv4Address()},
			{IP: gofakeit.IPv4Address()},
			{IP: gofakeit.IPv4Address()},
		},
	}, nil).AnyTimes()

	node.Cluster = mockCluster

	err := node.verifyClusterRack(LiveClusterUUID, []string{configs.E2E.ALB.Node2})
	assert.Error(t, err, "rack_rack1_in_cluster_greater_than_50_percent_for_10.129.110.145")
}

func TestNodeServiceVerifyClusterRackOk(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	node := newTestNodeVerifierService(t)

	mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
	assert.NotNil(t, mockCluster)

	mockCluster.EXPECT().GetByUUID(gomock.Eq(LiveClusterUUID)).Return(&albvo.Cluster{
		Env: gofakeit.RandomString([]string{consts.EnvLive, consts.EnvLiveish}),
		Nodes: []*sgwvo.Node{
			{IP: gofakeit.IPv4Address()},
			{IP: gofakeit.IPv4Address()},
			{IP: gofakeit.IPv4Address()},
			{IP: gofakeit.IPv4Address()},
		},
	}, nil).AnyTimes()
	mockCluster.EXPECT().GetByUUID(gomock.Eq(NonLiveClusterUUID)).Return(&albvo.Cluster{
		Env: gofakeit.RandomString([]string{
			consts.EnvTest,
			consts.EnvDev,
			consts.EnvStaging,
			consts.EnvUat,
			consts.EnvStable,
		}),
	}, nil).AnyTimes()

	node.Cluster = mockCluster

	// Mock ServerV3.Server calls (rack1, no error)
	t.Run("general_az", func(t *testing.T) {
		mockServerV3, ok := node.ServerV3.(*toc.MockServerV3Adapter)
		assert.True(t, ok)
		assert.NotNil(t, mockServerV3)
		if ok && mockServerV3 != nil {
			mockServerV3.EXPECT().Server(gomock.Any()).Times(3).Return(&toclib.ServerV3{
				Rack: "rack1",
				AZ:   "ap-sg-1-general-x",
			}, nil)
			mockServerV3.EXPECT().Server(gomock.Any()).Times(2).Return(&toclib.ServerV3{
				Rack: "rack2",
				AZ:   "ap-sg-1-general-x",
			}, nil)
		}

		err := node.verifyClusterRack(LiveClusterUUID, []string{configs.E2E.ALB.Node2})
		assert.NoError(t, err)
	})

	t.Run("internal_az", func(t *testing.T) {
		// Mock ServerV3.Server calls (rack1, but internal az)
		mockServerV3, ok := node.ServerV3.(*toc.MockServerV3Adapter)
		assert.True(t, ok)
		assert.NotNil(t, mockServerV3)
		if ok && mockServerV3 != nil {
			mockServerV3.EXPECT().Server(gomock.Any()).Times(5).Return(&toclib.ServerV3{
				Rack: "rack1",
				AZ:   "ap-th-1-private-a",
			}, nil)
		}

		err := node.verifyClusterRack(LiveClusterUUID, []string{configs.E2E.ALB.Node2})
		assert.NoError(t, err)
	})

	t.Run("nonlive_cluster", func(t *testing.T) {
		err := node.verifyClusterRack(NonLiveClusterUUID, []string{configs.E2E.ALB.Node2})
		assert.NoError(t, err)
	})
}

func TestNodeServiceVerifyTags(t *testing.T) {
	t.Parallel()

	node := newTestNodeVerifierService(t)

	t.Run("unit_test_ok", func(t *testing.T) {
		t.Parallel()

		tags := tocvo.Tags{
			{TagCategoryKey: "environment", Value: "test"},
			{TagCategoryKey: "zone", Value: "zone_test"},
			{TagCategoryKey: "ham", Value: "ham_test"},
			{TagCategoryKey: "idc", Value: "sg"},
			{TagCategoryKey: "function", Value: "nginx"},
			{TagCategoryKey: "application", Value: "alb"},
			{TagCategoryKey: consts.InfraPlatformL1TagName, Value: "AZ"},
			{TagCategoryKey: consts.InfraPlatformL2TagName, Value: "traffic_scheduling"},
		}

		err := node.verifyTags(&tocvo.ServerV3{
			IPLan:   configs.E2E.ALB.Node2,
			Cluster: consts.Shopee,
			AZ:      "ap-sg-1-private-a",
			Tags:    tags,
		})
		assert.NoError(t, err)
	})

	t.Run("unit_test_case_insensitive", func(t *testing.T) {
		t.Parallel()

		tags := tocvo.Tags{
			{TagCategoryKey: "environment", Value: "test"},
			{TagCategoryKey: "zone", Value: "zone_test"},
			{TagCategoryKey: "ham", Value: "ham_test"},
			{TagCategoryKey: "idc", Value: "sg"},
			{TagCategoryKey: "function", Value: "nginx"},
			{TagCategoryKey: "application", Value: "alb"},
			{TagCategoryKey: consts.InfraPlatformL1TagName, Value: "Az"},
			{TagCategoryKey: consts.InfraPlatformL2TagName, Value: "tRaFfIc_SchedUlinG"},
		}

		err := node.verifyTags(&tocvo.ServerV3{
			IPLan:   configs.E2E.ALB.Node2,
			Cluster: consts.Shopee,
			AZ:      "ap-sg-1-private-a",
			Tags:    tags,
		})
		assert.NoError(t, err)
	})

	t.Run("unit_test_bad", func(t *testing.T) {
		t.Parallel()

		tagsBad := tocvo.Tags{
			{TagCategoryKey: "environment", Value: "test"},
			{TagCategoryKey: "zone", Value: "zone_test"},
			{TagCategoryKey: "ham", Value: "ham_test"},
			{TagCategoryKey: "idc", Value: "sg"},
			{TagCategoryKey: "function", Value: "nginx"},
			{TagCategoryKey: "application", Value: "alb"},
			{TagCategoryKey: consts.InfraPlatformL1TagName, Value: "BZ"},
			{TagCategoryKey: consts.InfraPlatformL2TagName, Value: "traffic_scheduling"},
		}
		err := node.verifyTags(&tocvo.ServerV3{
			IPLan:   configs.E2E.ALB.Node2,
			Cluster: consts.Shopee,
			AZ:      "ap-sg-1-private-a",
			Tags:    tagsBad,
		})
		assert.Error(t, err, "invalid_infra_platform_BZ_traffic_scheduling")
	})
}

func newTestNodeVerifierService(t *testing.T) *NodeVerifierService {
	t.Helper()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	node := NodeVerifierService{
		TraceID:  t.Name(),
		Server:   toc.NewServerAdapter(t.Name()),
		ServerV3: toc.NewMockServerV3Adapter(ctrl),
		Meta:     toc.NewMetaAdapter(t.Name()),
		Cluster:  sgw.NewMockL7ClusterAdapter(ctrl),

		Ext2Cluster: sgw.NewExt2Cluster(t.Name()),
	}

	return &node
}
