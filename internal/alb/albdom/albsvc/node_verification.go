package albsvc

import (
	"fmt"
	"math"
	"net"
	"strings"

	"github.com/c-robinson/iplib"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/meta"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/ops"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
)

type NodeVerifierService struct {
	TraceID  string
	Meta     toc.MetaAdapter
	Server   toc.ServerAdapter
	ServerV3 toc.ServerV3Adapter
	Cluster  sgw.L7ClusterAdapter
	NodeMeta meta.NodeAdapter

	ALBCluster  sgw.ALBClusterAdapter
	Ext2Cluster sgw.Ext2ClusterAdapter
}

func NewNodeVerifierService(traceID string) *NodeVerifierService {
	return &NodeVerifierService{
		TraceID:     traceID,
		Meta:        toc.NewMetaAdapter(traceID),
		Server:      toc.NewServerAdapter(traceID),
		ServerV3:    toc.NewServerV3Adapter(traceID),
		Cluster:     sgw.NewALBClusterAdapter(traceID),
		NodeMeta:    meta.NewNodeAdapter(traceID),
		ALBCluster:  sgw.NewALBCluster(traceID),
		Ext2Cluster: sgw.NewExt2Cluster(traceID),
	}
}

func (s *NodeVerifierService) verifyTags(server *tocvo.ServerV3) error {
	svrTag := tocvo.Tags(server.Tags)
	tags := svrTag.Map()

	var tag string // invalid tag name
	if env := tags[consts.EnvironmentTagName]; env == "" {
		tag = consts.EnvironmentTagName
	}

	if app := tags[consts.ApplicationTagName]; app == "" || !strings.EqualFold(app, configs.ALB.Application) {
		tag = consts.ApplicationTagName
	}

	if fun := tags[consts.FunctionTagName]; fun == "" || !strings.EqualFold(fun, configs.ALB.Function) {
		tag = consts.FunctionTagName
	}

	idc, az := tags[consts.IDCTagName], tags[consts.AZTagName]
	if idc == "" && az == "" {
		tag = "IDC or AZ"
	}

	azInfo, _ := s.Meta.AZ(server.AZ)
	if azInfo.IsPrivate() || !strings.EqualFold(server.Segment, consts.SegmentGeneral) {
		/* NB: ham can be ignored in new cluster
		if ham := tags[consts.HAMTagName]; ham == "" {
			tag = consts.HAMTagName
		}
		*/
		if zone := tags[consts.ZoneTagName]; zone == "" {
			tag = consts.ZoneTagName
		}
	}

	if tag != "" {
		return fmt.Errorf("invalid_tag_%s", tag)
	}

	if err := svrTag.IsValidInfraPlatform(); err != nil {
		return errors.WithMessage(err, "invalid_infra_platform")
	}

	return nil
}

func (s *NodeVerifierService) verifyHAVIPs(
	cfg *sgwvo.ALBClusterConfigMeta,
	ham consts.HighAvailableMode,
) error {
	hamCode := ham.Code

	// Check for LAN VIPs for NONSTD3 and above
	if hamCode >= consts.HAMNonStd3.Code {
		if len(cfg.ECMPBGPVIPs) == 0 {
			return fmt.Errorf("not found variable ecmp_bgp_vips under %s", ham.Name)
		}

		// Verify that all IPs in ECMPBGPVIPs are private (LAN) IPs.
		// Filter finds any non-private (WAN) IPs.
		if err := ensureNoIPMatches(cfg.ECMPBGPVIPs, isWAN); err != nil {
			return errors.WithMessage(err, "ecmp_bgp_vips must be private under "+ham.Name)
		}

		if hamCode == consts.HAMNonStd4.Code {
			return nil
		}
	}

	// Check for WAN VIPs for NONSTD1
	if hamCode == consts.HAMNonStd1.Code {
		if cfg.IsWAN() {
			if !cfg.IsKeepalivedHasWANVIP() {
				return fmt.Errorf("wan_vip_required ha_mode=%s", ham.Name)
			}

			return nil
		}

		// LAN cluster case
		if !cfg.IsKeepalivedHasLANVIP() {
			return fmt.Errorf("lan_vip_required ha_mode=%s", ham.Name)
		}

		return nil
	}

	// Check for WAN VIPs for NONSTD2 and NONSTD3
	if len(cfg.ECMPBGPWanVIPs) == 0 {
		return fmt.Errorf("not found variable ecmp_bgp_wan_vips under %s", ham.Name)
	}

	if err := ensureNoIPMatches(cfg.ECMPBGPWanVIPs, isLAN); err != nil {
		return errors.WithMessage(err, "ecmp_bgp_wan_vips must be public under "+ham.Name)
	}

	return nil
}

func (s *NodeVerifierService) verifyECMPBGPMeta(
	ham consts.HighAvailableMode, cfg *sgwvo.ALBClusterConfigMeta, node toc.TocexAdapter,
) error {
	if len(cfg.BGPASMapping) == 0 {
		inet := toc.NewNetAdapter(node, s.TraceID)
		tables, err := inet.RouteTables()
		if err != nil {
			return errors.WithMessage(err, "fetch_route_tables_failed")
		}

		tab, ok := tables.Default()
		if !ok {
			return errors.New("variable ecmp_bgp_peer_as_mapping not found also no default route table")
		}

		peerIP1 := iplib.PreviousIP(tab.Gateway)
		peerIP2 := iplib.PreviousIP(peerIP1)
		cfg.BGPASMapping = map[string]string{
			peerIP1.String(): cast.ToString(configs.Mgmt.BGP.RemoteAS),
			peerIP2.String(): cast.ToString(configs.Mgmt.BGP.RemoteAS),
		}
	}

	for peer := range cfg.BGPASMapping {
		if ip := net.ParseIP(peer); ip != nil {
			if ham.Code >= consts.HAMNonStd3.Code && !ip.IsPrivate() {
				return fmt.Errorf("variable ecmp_bgp_peer_as_mapping peer %s must be private", peer)
			}
			if ham.Code == consts.HAMNonStd2.Code && ip.IsPrivate() {
				return fmt.Errorf("variable ecmp_bgp_peer_as_mapping peer %s must be public", peer)
			}

			_, err := node.RunTask(fmt.Sprintf(`nc -zv -w 1 %s 179`, ip))
			if err != nil {
				return errors.WithMessage(err, "check_bgp_peer_failed")
			}
		}
	}

	return nil
}

func (s *NodeVerifierService) newTocexAdapter(server *tocvo.ServerV3) (toc.TocexAdapter, error) {
	if server.IsShopee() {
		tocex, err := toc.NewTocexAdapterWithIP(server.IPLan, s.TraceID)

		return tocex, errors.WithMessage(err, fmt.Sprintf("fetch_tocex_failed_for_%s", server.IPLan))
	}

	return toc.NewTocexAdapter(server.ToNode()), nil
}

func (s *NodeVerifierService) verifyServerHardware(tocex toc.TocexAdapter) error {
	ip := tocex.HostIP()
	hw, err := s.Server.HardwareV2(ip)
	if err != nil {
		if strings.Contains(err.Error(), "record not found") {
			log.Logger().WithFields(log.Fields{
				"trace_id": s.TraceID,
				"server":   ip,
			}).Warn("not_found_hardware")

			return nil
		}

		return errors.WithMessage(err, fmt.Sprintf("fetch_hw_failed_for_%s", ip))
	}

	if len(hw.Nic) == 0 {
		log.Logger().WithFields(log.Fields{
			"trace_id": s.TraceID,
			"server":   ip,
		}).Warn("not_found_interfaces")

		return nil
	}

	interfaces, err := tocex.RunTask(tpl.ListInterfaceScript)
	if err != nil {
		return errors.WithMessage(err, "fetch_interfaces_failed")
	}

	links := strings.Split(strings.TrimSpace(interfaces), "\n")
	if ok := slice.ContainSubSlice(links, hw.Interfaces()); !ok {
		diffs := slice.Difference(hw.Interfaces(), links)

		return errors.Errorf("not_found_interface_%s", strings.Join(diffs, ","))
	}

	return nil
}

func (s *NodeVerifierService) verifyTocexAgent(tocex toc.TocexAdapter) error {
	if _, err := tocex.GetAgent(); err != nil {
		return errors.WithMessage(err, "fetch_tocex_agent_failed")
	}

	return nil
}

func (s *NodeVerifierService) verifyDataDirMounted(tocex toc.TocexAdapter) error {
	file := toc.NewFileAdapter(tocex)
	if ok, err := file.Exist(consts.DataDirMountCheckSkipFile); ok {
		return nil
	} else if err != nil {
		return errors.WithMessage(err, "fetch_data_dir_mount_check_skip_file_failed")
	}

	fs := toc.NewFileSystemAdapter(tocex)
	mountPoints, err := fs.MountPoints()
	if err != nil {
		return errors.WithMessage(err, "fetch_mount_points_failed")
	}

	if _, ok := mountPoints.FindMountPoint(consts.DataDir); !ok {
		return errors.New("data_dir_not_mounted")
	}

	return nil
}

func (s *NodeVerifierService) verifyServerV3(server *tocvo.ServerV3) error {
	ip := server.IPLan

	if err := s.verifyTags(server); err != nil {
		return errors.WithMessage(err, "verify_tags_failed")
	}

	tocex, err := s.newTocexAdapter(server)
	if err != nil {
		return errors.WithMessage(err, fmt.Sprintf("fetch_tocex_failed_for_%s", server.IPLan))
	}

	if err := s.verifyTocexAgent(tocex); err != nil {
		return errors.WithMessage(err, fmt.Sprintf("verify_tocex_agent_failed_for_%s", ip))
	}

	if err := s.verifyDataDirMounted(tocex); err != nil {
		return errors.WithMessage(err, fmt.Sprintf("verify_data_dir_mounted_failed_for_%s", ip))
	}

	if err := s.verifyServerHardware(tocex); err != nil {
		return errors.WithMessage(err, fmt.Sprintf("verify_server_hardware_failed_for_%s", ip))
	}

	return nil
}

// VerifyServerMeta verify server meta
func (s *NodeVerifierService) VerifyServerMeta(req *albvo.ClusterNodeVerifyRequest) error {
	ips := req.IPs

	servers, err := s.ServerV3.Servers(ips)
	if err != nil {
		return errors.WithMessage(err, "fetch_servers_failed")
	}

	cluster, err := s.Cluster.GetByUUID(req.UUID)
	if err != nil {
		return errors.WithMessage(err, "fetch_cluster_failed")
	}

	bizSGW := configs.ALB.BizSGW()

	for _, ip := range ips {
		server, ok := servers[ip]
		if !ok {
			return fmt.Errorf("not_found_server_for_%s", ip)
		}

		az, err := s.Meta.AZ(cluster.AZ)
		if err != nil {
			return errors.WithMessage(err, "fetch_az_failed")
		}

		if az.IsGeneral() {
			matched := false
			for _, biz := range server.Businesses {
				if strings.EqualFold(biz.Business, bizSGW.Name) {
					matched = true
				}
			}
			if !matched {
				return fmt.Errorf("node_%s_unbind_biz %s", ip, bizSGW.Name)
			}
		}

		serverV3 := tocvo.ServerV3(*server)

		if err = s.verifyServerV3(&serverV3); err != nil {
			return errors.WithMessage(err, fmt.Sprintf("verify_server_failed_for_%s", ip))
		}

		if err = s.verifyClusterMeta(req.UUID, &serverV3); err != nil {
			return errors.WithMessage(err, fmt.Sprintf("verify_tag_vars_failed_for_%s", ip))
		}

		if err = s.verifyKV(&serverV3, cluster.RZ, cluster.Env); err != nil {
			return errors.WithMessage(err, fmt.Sprintf("verify_kv_failed_for_%s", ip))
		}
	}

	if err := s.verifyClusterRack(req.UUID, req.IPs); err != nil {
		return errors.WithMessage(err, "verify_cluster_rack_failed")
	}

	return nil
}

// verifyPrivateClusterConfig verify private variable
// Modify verifyPrivateClusterConfig signature to accept server object
func (s *NodeVerifierService) verifyPrivateClusterConfig(
	ham consts.HighAvailableMode,
	cfg *sgwvo.ALBClusterConfigMeta,
	node toc.TocexAdapter,
) error {
	// NonStd1 work as primary-secondary mode
	// NonStd2+, need domain proxy for etcd; it requires ecmp-vips and segment_code
	if err := s.verifyHAVIPs(cfg, ham); err != nil {
		return errors.WithMessage(err, "verify_ha_vip_failed")
	}

	if ham.Code == consts.HAMNonStd1.Code {
		// should omit nil check; len() for map[string]*... is defined as zero (gosimple)
		if len(cfg.HAInstances) == 0 || len(cfg.HAInstance(node.HostIP())) == 0 {
			return errors.New("not found variable ha_instances")
		}

		return nil
	}

	if err := s.verifyECMPBGPMeta(ham, cfg, node); err != nil {
		return errors.WithMessage(err, "verify_ecmp_bgp_group_variables_failed")
	}

	// NonStd2 must find WAN IP on Node
	if ham.Code == consts.HAMNonStd2Code {
		inet := toc.NewNetAdapter(node, s.TraceID)
		network, err := inet.Network()
		if err != nil {
			return errors.WithMessage(err, "fetch_node_wan_network_failed")
		}

		if !network.IsExistedWAN() {
			return errors.New("not_found_WANIP_address_under_HAM-NonStd2")
		}
	}

	return nil
}

func (s *NodeVerifierService) verifyClusterMeta(uuid string, server *tocvo.ServerV3) error {
	config, err := s.ALBCluster.Meta(uuid)
	if err != nil {
		return errors.WithMessage(err, "fetch_cluster_meta_failed")
	}

	azInfo, _ := s.Meta.AZ(config.AZ)
	if azInfo.IsGeneral() && strings.EqualFold(server.Segment, consts.SegmentGeneral) {
		return nil
	}

	if _, err = config.SegmentCode(); err != nil {
		return errors.WithMessage(err, "fetch_segment_code_failed")
	}

	segmentMeta := azInfo.Segment(config.Segment)
	netVer := consts.ParseNetworkVersion(segmentMeta.NetworkVersion)
	ham, err := server.HAMode(netVer)
	if err != nil {
		return errors.WithMessage(err, "fetch_ham_failed")
	}

	node, err := s.newTocexAdapter(server)
	if err != nil {
		return errors.WithMessage(err, fmt.Sprintf("fetch_tocex_failed_for_%s", server.IPLan))
	}

	if err = s.verifyPrivateClusterConfig(ham, config, node); err != nil {
		return errors.WithMessage(err, "verify_private_cluster_config_failed")
	}

	if err := s.verifyHTTPProxy(config.RZ, node); err != nil {
		return errors.WithMessage(err, "verify_http_proxy_failed")
	}

	return nil
}

func (s *NodeVerifierService) verifyHTTPProxy(rz string, node toc.TocexAdapter) error {
	ext2Clusters, err := s.Ext2Cluster.DumpByRZ(rz)
	if err != nil {
		return errors.WithMessage(err, "fetch_ext2_clusters_failed")
	}

	if ext2Clusters.IsHTTPTunnelSupported() {
		script := ext2Clusters.HTTPTunnelCheckScript()
		if _, err := node.RunTask(script); err != nil {
			log.Logger().WithError(err).WithField("script", script).Error("check_http_tunnel_failed")

			return errors.WithMessage(err, "check_http_tunnel_failed")
		}
	}

	return nil
}

func (s *NodeVerifierService) verifyKV(server *tocvo.ServerV3, rz, env string) error {
	node, err := s.newTocexAdapter(server)
	if err != nil {
		return errors.WithMessage(err, fmt.Sprintf("fetch_tocex_failed_for_%s", server.IPLan))
	}

	if err := s.verifyZK(node, rz, env); err != nil {
		return errors.WithMessage(err, "verify_zk_failed")
	}
	if err := s.verifyEtcd(node, rz, env); err != nil {
		return errors.WithMessage(err, "verify_etcd_failed")
	}

	return nil
}

func (s *NodeVerifierService) verifyZK(node toc.TocexAdapter, rz, env string) error {
	zk := ops.NewZKAdapter(s.TraceID)
	clusterMeta, err := zk.ClusterMeta(rz, env)
	if err != nil {
		return errors.WithMessage(err, "fetch_zk_meta_failed")
	}

	script, err := clusterMeta.CheckScript()
	if err != nil {
		return errors.WithMessage(err, "fetch_zk_check_script_failed")
	}

	if _, err = node.RunTask(script); err != nil {
		return errors.WithMessage(err, "check_zk_failed")
	}

	return nil
}

func (s *NodeVerifierService) verifyEtcd(node toc.TocexAdapter, rz, env string) error {
	etcd := ops.NewEtcdAdapter(s.TraceID)
	clusterMeta, err := etcd.ClusterMeta(rz, env)
	if err != nil {
		return errors.WithMessage(err, "fetch_etcd_meta_failed")
	}

	script, err := clusterMeta.CheckScript()
	if err != nil {
		return errors.WithMessage(err, "fetch_etcd_check_script_failed")
	}

	if _, err = node.RunTask(script); err != nil {
		return errors.WithMessage(err, "check_etcd_failed")
	}

	return nil
}

// serverIsVM check if the server is VM, or bear metal / container
func (s *NodeVerifierService) serverIsVM(server *toclib.ServerV3) bool {
	// TODO: need a field to indicate the server is VM or not
	return server.Rack == ""
}

// verifyClusterRack verify the node's rack in the cluster
func (s *NodeVerifierService) verifyClusterRack(clusterUUID string, ips []string) error {
	cluster, err := s.Cluster.GetByUUID(clusterUUID)
	if err != nil {
		return errors.WithMessage(err, "fetch_cluster_failed")
	}

	if cluster == nil {
		return errors.New("not_found_cluster")
	}

	// NB: skip the rack check for non-live env
	if consts.IsNonLiveEnv(cluster.Env) {
		log.Logger().WithFields(log.Fields{
			"trace_id": s.TraceID,
			"env":      cluster.Env,
			"uuid":     clusterUUID,
		}).Debug("skip_rack_check_for_non_live_env")

		return nil
	}

	const halfDivisor = 2
	totalNodes := len(cluster.Nodes) + len(ips)
	halfTotalNodes := int(math.Ceil(float64(totalNodes) / halfDivisor))

	rackCounts := make(map[string]int)
	for _, node := range cluster.Nodes {
		server, err := s.ServerV3.Server(node.IP)
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("fetch_server_failed_for_%s", node.IP))
		}
		if s.serverIsVM(server) {
			continue
		}
		rackCounts[server.Rack]++
	}

	for _, ip := range ips {
		server, err := s.ServerV3.Server(ip)
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("fetch_server_failed_for_%s", ip))
		}

		if s.serverIsVM(server) {
			continue
		}
		rackCounts[server.Rack]++

		az, err := s.Meta.AZ(server.AZ)
		if err != nil {
			return errors.WithMessage(err, "fetch_az_failed")
		}

		// NB: check if the cluster is general, if so,
		// the rack count should not exceed 50% of the total nodes
		if az.IsGeneral() {
			if rackCounts[server.Rack] > halfTotalNodes {
				return fmt.Errorf("rack_%s_in_cluster_greater_than_50_percent_for_%s", server.Rack, ip)
			}
		}
	}

	return nil
}

func isLAN(_ int, ip string) bool {
	ipNet := net.ParseIP(ip)
	if ipNet == nil {
		return false
	}

	return ipNet.IsPrivate()
}

func isWAN(_ int, ip string) bool {
	ipNet := net.ParseIP(ip)
	if ipNet == nil {
		return false
	}

	return !ipNet.IsPrivate()
}

func ensureNoIPMatches(ips []string, matchFn func(_ int, ip string) bool) error {
	if matcheIPs := slice.Filter(ips, matchFn); len(matcheIPs) > 0 {
		return fmt.Errorf("found_invalid_ips_%s", strings.Join(matcheIPs, "_"))
	}

	return nil
}
