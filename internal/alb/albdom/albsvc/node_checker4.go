package albsvc

import (
	"time"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

// it works on Private AZ

func (s *NodeCheckerService) checkHAService(
	node string,
	tocex toc.TocexAdapter,
	netVer consts.NetworkVersion,
) *sgwvo.TaskResult {
	taskName := "check_ha_service"

	server, err := s.ServerV3.Server(node)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "fetch_server_v3_failed").Error(),
		}
	}
	serverV3 := tocvo.ServerV3(*server)

	ham, err := serverV3.HAMode(netVer)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "fetch_ham_failed").Error(),
		}
	}

	var ret *sgwvo.TaskResult
	// ALB High Availability Mode https://confluence.shopee.io/x/sxf3Vg
	switch {
	case ham.IsHAMWithVRRP():
		ret = s.checkServiceIsDisabled(tocex, cmp.KeepalivedComponent.Service)
	case ham.IsHAMWithECMP():
		ret = s.checkServiceIsDisabled(tocex, cmp.BirdComponent.Service)
	default:
		log.Logger().WithField("node", node).Warn("no_ha_component_for_hamode")
		ret = &sgwvo.TaskResult{
			Success:   true,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "no_ha_component_needed",
		}
	}

	return ret
}

func (s *NodeCheckerService) fetchNodeCheckByTag(
	node string,
	tocex toc.TocexAdapter,
	netVer consts.NetworkVersion,
) []*sgwvo.TaskResult {
	var rets []*sgwvo.TaskResult

	ret := wrapCheckWithTiming(func() *sgwvo.TaskResult {
		return s.checkHAService(node, tocex, netVer)
	})

	if ret != nil {
		rets = append(rets, ret)
	}

	return rets
}
