package albsvc

import (
	"fmt"

	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/arti"
)

type ComponentService struct {
	TraceID string

	deb    arti.DebianAdapter
	pip    arti.PIPAdapter
	harbor arti.HarborAdapter
}

func NewComponentService(traceID string) *ComponentService {
	return &ComponentService{
		TraceID: traceID,

		deb:    arti.NewDebianAdapter(traceID),
		pip:    arti.NewPIPAdapter(traceID),
		harbor: arti.NewHarborAdapter(traceID),
	}
}

// Versions retrieve the component versions based on the given request.
//
// It takes a ComponentRequest parameter and returns a slice of
// ComponentVersion pointers and an error.
func (s *ComponentService) Versions(req *albvo.ComponentRequest) ([]*albvo.ComponentVersion, error) {
	var versions []*albvo.ComponentVersion
	var err error

	com := cmp.ALBComponent(req.Name)
	if com == nil {
		return nil, errors.Errorf("component_%s_unsupported", req.Name)
	}

	switch com.Type {
	case cmp.ComponentTypeDocker:
		versions, err = s.GetDockerTags(req)
	case cmp.ComponentTypeAPT:
		versions, err = s.GetAPTVersions(req)
	case cmp.ComponentTypePIP:
		versions, err = s.GetPIPVersions(req)
	}

	return versions, errors.WithMessage(err, "fetch_component_version_failed")
}

func (s *ComponentService) GetAPTVersions(req *albvo.ComponentRequest) ([]*albvo.ComponentVersion, error) {
	records, err := s.deb.ListRecords(req.Name)
	if err != nil {
		return nil, errors.WithMessage(err, "list_debian_package_versions_failed")
	}

	var versions []*albvo.ComponentVersion
	for _, record := range records {
		versions = append(versions, &albvo.ComponentVersion{
			Version: record.Version,
			Created: record.CreatedAt,
		})
	}

	return versions, nil
}

func (s *ComponentService) GetPIPVersions(req *albvo.ComponentRequest) ([]*albvo.ComponentVersion, error) {
	vers, err := s.pip.ListVersions(req.Name)
	if err != nil {
		return nil, errors.WithMessage(err, "list_pip_package_version_failed")
	}

	var versions []*albvo.ComponentVersion
	for _, v := range vers {
		versions = append(versions, &albvo.ComponentVersion{
			Version: v,
			Created: 0,
		})
	}

	return versions, nil
}

func (s *ComponentService) GetDockerTags(req *albvo.ComponentRequest) ([]*albvo.ComponentVersion, error) {
	com := cmp.ALBComponent(req.Name)
	images, err := s.harbor.ListTags(&arti.HarborRequest{
		Name:     com.ImageName(),
		Env:      req.Env,
		Query:    "tags=*", // filter for alb
		PageSize: consts.DefaultTopN,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "list_docker_image_tags_failed")
	}

	var versions []*albvo.ComponentVersion
	for _, item := range images {
		versions = append(versions, &albvo.ComponentVersion{
			Version: item.Name,
			Created: int(item.PushTime.Unix()),
		})
	}

	return versions, nil
}

func (s *ComponentService) IsVersionExist(req *albvo.ComponentVersionRequest) (bool, error) {
	com := cmp.ALBComponent(req.Name)
	if com == nil {
		return false, errors.Errorf("component_%s_unsupported", req.Name)
	}

	switch com.Type {
	case cmp.ComponentTypeDocker:
		images, err := s.harbor.ListTags(&arti.HarborRequest{
			Name:     com.ImageName(),
			Env:      req.Env,
			Query:    fmt.Sprintf("tags=%s", req.Version),
			PageSize: consts.DefaultTopN,
		})
		if err != nil {
			return false, errors.WithMessage(err, "list_docker_image_tags_failed")
		}

		return len(images) > 0, nil

	case cmp.ComponentTypePIP:
		ok, err := s.pip.IsVersionExist(req.Name, req.Version)
		if err != nil {
			return false, errors.WithMessage(err, "check_pip_package_version_failed")
		}

		return ok, nil

	case cmp.ComponentTypeAPT:
		ok, err := s.deb.IsVersionExist(req.Name, req.Version)
		if err != nil {
			return false, errors.WithMessage(err, "check_apt_package_version_failed")
		}

		return ok, nil
	}

	return false, nil
}
