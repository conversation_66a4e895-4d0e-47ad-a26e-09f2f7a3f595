package albsvc

import (
	"context"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albprov"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

func fetchFakeNodeComponentVersion() *albvo.NodeComponentVersion {
	return &albvo.NodeComponentVersion{
		ALBMetrics: gofakeit.AppVersion(),
		ALBSD:      gofakeit.AppVersion(),
		SGWAgent:   gofakeit.AppVersion(),
		NginxLB:    gofakeit.AppVersion(),
		Nginx:      gofakeit.AppVersion(),
	}
}

func TestNodeProvisionService_ProvisionVersion_failed(t *testing.T) {
	node := NodeProvisionService{TraceID: t.Name()}
	fakeNodeIP0 := gofakeit.IPv4Address()
	fakeNodeIP1 := gofakeit.IPv4Address()
	fakeNodeIP2 := gofakeit.IPv4Address()

	ctl := albctl.NewMockController(gomock.NewController(t))
	ctl.EXPECT().GetComponentVersion(gomock.Any(), gomock.Any()).Return(&albvo.NodeComponentVersion{
		ALBMetrics: consts.UnknownVersion,
	}).AnyTimes()
	ctl.EXPECT().GetComponentVersionByAZ(gomock.Any()).Return(fetchFakeNodeComponentVersion()).AnyTimes()

	server := toc.NewMockServerAdapter(gomock.NewController(t))

	server.EXPECT().GetALBGroupVar(gomock.Eq(fakeNodeIP0)).
		Return(nil, errors.New("not found")).AnyTimes()
	server.EXPECT().GetALBGroupVar(gomock.Eq(fakeNodeIP1)).DoAndReturn(func(ip string) (*tocvo.ALBGroupVar, error) {
		albGroupVar := tocvo.ALBGroupVar{}
		assert.NoError(t, gofakeit.Struct(&albGroupVar))

		return &albGroupVar, nil
	}).AnyTimes()
	server.EXPECT().GetALBGroupVar(gomock.Eq(fakeNodeIP2)).DoAndReturn(func(ip string) (*tocvo.ALBGroupVar, error) {
		albGroupVar := tocvo.ALBGroupVar{}
		assert.NoError(t, gofakeit.Struct(&albGroupVar))

		return &albGroupVar, nil
	}).AnyTimes()

	node.Controller = ctl
	node.Server = server

	_, err := node.Version(fakeNodeIP0)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "fetch_alb_group_var_failed")

	_, err = node.Version(fakeNodeIP1)
	assert.NoError(t, err)

	_, err = node.Version(fakeNodeIP2)
	assert.NoError(t, err)
}

func TestNodeProvisionService_ProvisionVersion_via_ctl(t *testing.T) {
	node := NodeProvisionService{TraceID: t.Name()}
	fakeNodeIP0 := gofakeit.IPv4Address()

	ctl := albctl.NewMockController(gomock.NewController(t))
	ctl.EXPECT().GetComponentVersion(gomock.Any(), gomock.Any()).Return(fetchFakeNodeComponentVersion()).AnyTimes()
	ctl.EXPECT().GetComponentVersionByAZ(gomock.Any()).Return(fetchFakeNodeComponentVersion()).AnyTimes()

	server := toc.NewMockServerAdapter(gomock.NewController(t))

	server.EXPECT().GetALBGroupVar(gomock.Eq(fakeNodeIP0)).DoAndReturn(func(ip string) (*tocvo.ALBGroupVar, error) {
		albGroupVar := tocvo.ALBGroupVar{}
		assert.NoError(t, gofakeit.Struct(&albGroupVar))

		return &albGroupVar, nil
	}).AnyTimes()

	node.Controller = ctl
	node.Server = server

	ver, err := node.Version(fakeNodeIP0)
	assert.NoError(t, err)
	assert.NotNil(t, ver)
	assert.NotEqual(t, consts.UnknownVersion, ver.ALBMetrics)
}

func TestNodeProvisionService_ProvisionVersion(t *testing.T) {
	node := NodeProvisionService{TraceID: t.Name()}
	fakeNodeIP0 := gofakeit.IPv4Address()

	ctl := albctl.NewMockController(gomock.NewController(t))
	ctl.EXPECT().GetComponentVersion(gomock.Any(), gomock.Any()).Return(&albvo.NodeComponentVersion{
		ALBMetrics: consts.UnknownVersion,
	}).AnyTimes()
	ctl.EXPECT().GetComponentVersionByAZ(gomock.Any()).Return(fetchFakeNodeComponentVersion()).AnyTimes()

	server := toc.NewMockServerAdapter(gomock.NewController(t))

	server.EXPECT().GetALBGroupVar(gomock.Eq(fakeNodeIP0)).DoAndReturn(func(ip string) (*tocvo.ALBGroupVar, error) {
		albGroupVar := tocvo.ALBGroupVar{}
		assert.NoError(t, gofakeit.Struct(&albGroupVar))

		return &albGroupVar, nil
	}).AnyTimes()

	node.Controller = ctl
	node.Server = server

	ver, err := node.Version(fakeNodeIP0)
	assert.NoError(t, err)
	assert.NotNil(t, ver)
	assert.NotEqual(t, consts.UnknownVersion, ver.ALBMetrics)
}

func TestNodeProvisionService_GenUpgradedProvisionConfig_failed(t *testing.T) {
	fakeit := gofakeit.New(time.Now().Unix())
	fakeNodeIP := fakeit.IPv4Address()
	fakeUUID := fakeit.UUID()

	cluster := configs.E2E.ALB.Shopee
	nodeIP := cluster.Node
	clusterUUID := cluster.UUID

	mockCluster := sgw.NewMockL7ClusterAdapter(gomock.NewController(t))
	mockCluster.EXPECT().GetNodeByIP(gomock.Any()).DoAndReturn(func(ip string) (*sgwvo.Node, error) {
		switch ip {
		case fakeNodeIP:
			return &sgwvo.Node{
				ClusterUUID: fakeUUID,
			}, nil
		case nodeIP:
			return &sgwvo.Node{
				ClusterUUID: clusterUUID,
			}, nil
		default:
			return nil, errors.New("not found")
		}
	}).AnyTimes()
	mockCluster2 := sgw.NewMockALBClusterAdapter(gomock.NewController(t))
	mockCluster2.EXPECT().Meta(gomock.Any()).DoAndReturn(func(uuid string) (*sgwvo.ALBClusterConfigMeta, error) {
		switch uuid {
		case fakeUUID:
			return nil, errors.New("fetch_cluster_meta_failed")
		case clusterUUID:
			return &sgwvo.ALBClusterConfigMeta{
				L7ClusterMeta: sgwvo.L7ClusterMeta{
					RZ:      cluster.RZ,
					Env:     cluster.Env,
					AZ:      cluster.AZ,
					Segment: cluster.Segment,
				},
			}, nil
		default:
			return &sgwvo.ALBClusterConfigMeta{}, nil
		}
	}).AnyTimes()

	mockMeta := toc.NewMockMetaAdapter(gomock.NewController(t))
	mockMeta.EXPECT().Segment(gomock.Any(), gomock.Any()).Return(&tocvo.Segment{
		NetworkVersion: "V3.0",
	}, nil).AnyTimes()

	node := NodeProvisionService{
		TraceID:  t.Name(),
		Server:   toc.NewServerAdapter(t.Name()),
		ServerV3: toc.NewServerV3Adapter(t.Name()),
		Cluster:  mockCluster,
		Cluster2: mockCluster2,
		Meta:     mockMeta,
	}

	_, err := node.GenerateConfig(context.Background(), &albvo.NodeProvisionComponentRequest{
		IP:        fakeNodeIP,
		Component: cmp.ScertmsComponent.Name,
		Version:   "1.0.0",
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "fetch_cluster_meta_failed")

	_, err = node.GenerateConfig(context.Background(), &albvo.NodeProvisionComponentRequest{
		IP:        nodeIP,
		Component: fakeit.Name(),
		Version:   "1.0.0",
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported_component_")
}

func TestNodeProvisionService_GenUpgradedProvisionConfig(t *testing.T) {
	mockMeta := toc.NewMockMetaAdapter(gomock.NewController(t))
	mockMeta.EXPECT().Segment(gomock.Any(), gomock.Any()).Return(&tocvo.Segment{
		NetworkVersion: "V3.0",
	}, nil).AnyTimes()

	node := NodeProvisionService{
		TraceID:  t.Name(),
		Server:   toc.NewServerAdapter(t.Name()),
		ServerV3: toc.NewServerV3Adapter(t.Name()),
		Cluster:  sgw.NewALBClusterAdapter(t.Name()),
		Cluster2: sgw.NewALBCluster(t.Name()),
		Meta:     mockMeta,
	}

	if ciEnv {
		mockProv := albprov.NewMockProvision(gomock.NewController(t))
		mockProv.EXPECT().UpgradedComponent(gomock.Any(), gomock.Any()).
			DoAndReturn(func(name, version string) (*tocvo.NodeProvision, error) {
				return &tocvo.NodeProvision{}, nil
			}).AnyTimes()

		node.Provision = mockProv
	}

	_, err := node.GenerateConfig(context.Background(), &albvo.NodeProvisionComponentRequest{
		IP:        configs.E2E.ALB.Shopee.Node,
		Component: cmp.ScertmsComponent.Name,
		Version:   "1.0.0",
	})
	assert.NoError(t, err)
}

func TestNodeProvisionService_albNodeServer(t *testing.T) {
	traceID := t.Name()

	svc := &NodeProvisionService{
		TraceID:    traceID,
		Server:     toc.NewServerAdapter(traceID),
		ServerV3:   toc.NewServerV3Adapter(traceID),
		Controller: albctl.ALBController,
		Cluster:    sgw.NewALBClusterAdapter(traceID),
		Cluster2:   sgw.NewALBCluster(traceID),
		Meta:       toc.NewMetaAdapter(traceID),
	}
	svr, err := svc.albNodeServer(configs.E2E.ALB.Shopee.Node)
	assert.NoError(t, err)
	assert.NotNil(t, svr)
}
