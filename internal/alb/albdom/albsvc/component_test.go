package albsvc

import (
	"testing"

	"github.com/golang/mock/gomock"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/arti/artivo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/arti"
)

func TestComponentService_IsVersionExist(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name   string
		req    *albvo.ComponentVersionRequest
		newSvc func(*gomock.Controller) *ComponentService
	}{
		{
			name: "Nginx-1.0.0",
			req: &albvo.ComponentVersionRequest{
				Name:    "nginx-shopee",
				Env:     "test",
				Version: "1.0.0",
			},
			newSvc: func(c *gomock.Controller) *ComponentService {
				mock := arti.NewMockDebianAdapter(c)
				mock.EXPECT().IsVersionExist("nginx-shopee", "1.0.0").Return(true, nil)

				return &ComponentService{deb: mock}
			},
		},
		{
			name: "Sgw-Agent-1.0.1",
			req: &albvo.ComponentVersionRequest{
				Name:    "sgw-agent",
				Env:     "live",
				Version: "1.0.1",
			},
			newSvc: func(c *gomock.Controller) *ComponentService {
				mock := arti.NewMockHarborAdapter(c)
				mock.EXPECT().ListTags(gomock.Any()).Return([]*artivo.Tag{{ID: 1}}, nil)

				return &ComponentService{harbor: mock}
			},
		},
		{
			name: "scertms",
			req: &albvo.ComponentVersionRequest{
				Name:    "scertms",
				Env:     "test",
				Version: "1.0.2",
			},
			newSvc: func(c *gomock.Controller) *ComponentService {
				mock := arti.NewMockPIPAdapter(c)
				mock.EXPECT().IsVersionExist("scertms", "1.0.2").Return(true, nil)

				return &ComponentService{pip: mock}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc := tt.newSvc(ctrl)
			got, err := svc.IsVersionExist(tt.req)
			if err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			if !got {
				t.Error("Expected version to exist")
			}
		})
	}
}
