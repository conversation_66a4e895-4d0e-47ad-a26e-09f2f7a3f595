package albsvc

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

type NodeComponentService struct {
	TraceID string
	Node    toc.TocexAdapter
}

func (s *NodeComponentService) aptComponents() (map[string]string, error) {
	//nolint:nolintlint,lll
	baseComponentCmd := `dpkg-query --list |grep -E 'docker-shopee|docker-shopee-dev|lxcfs-shopee|shopee-driver-manager' | awk '{print $2,$3}'`
	ret, err := s.Node.RunTask(baseComponentCmd)
	if err != nil {
		return nil, errors.WithMessage(err, "dpkg-query_failed")
	}

	versions := make(map[string]string)
	if ret != "" {
		for _, line := range strings.Split(ret, "\n") {
			items := strings.Split(line, " ")
			if len(items) > 1 {
				versions[items[0]] = items[1]
			}
		}
	}

	return versions, nil
}

func (s *NodeComponentService) Dump(_ context.Context) (*albvo.NodeComponentVersion, error) {
	docker := toc.NewDockerAdapter(s.Node)
	versions, err := docker.ImageVersion(true)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_docker_component_failed")
	}

	aptVersions, err := s.aptComponents()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_apt_component_failed")
	}

	for pkg, version := range aptVersions {
		versions[pkg] = version
	}

	ret, err := s.Node.RunTask(consts.NginxVersionScript)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_nginx_version_failed")
	}
	versions[cmp.NginxComponent.Name] = strings.TrimSuffix(ret, "\n")

	if len(versions) > 0 {
		nv := albvo.NodeComponentVersion{}

		nv.Nginx = versions[cmp.NginxComponent.Name]
		nv.SGWAgent = versions[cmp.SGWAgentComponent.Name]
		nv.ALBSD = versions[cmp.ALBSDComponent.Name]
		nv.ALBMetrics = versions[cmp.ALBMetricsComponent.Name]
		nv.ALBWAF = versions[cmp.ALBWAFComponent.Name]
		nv.NginxLB = versions[cmp.MesosNginxLBComponent.ImageName()]

		return &nv, nil
	}

	return nil, fmt.Errorf("not found any components")
}
