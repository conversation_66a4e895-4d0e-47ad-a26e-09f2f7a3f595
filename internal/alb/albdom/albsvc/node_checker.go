package albsvc

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/cryptor"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/sourcegraph/conc/iter"

	"git.garena.com/shopee/go-shopeelib/byteutils"
	"git.garena.com/shopee/go-shopeelib/json"
	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/etcd/etcdvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/worker"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsinf/opscah"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/zk/zkvo"
)

var (
	// albNodeLimiter is a global singleton for limiting concurrent ALB node processing
	albNodeLimiter *worker.ConcurrencyLimiter
	limiterOnce    sync.Once
)

// getALBNodeLimiter returns the singleton ALB node limiter
func getALBNodeLimiter() *worker.ConcurrencyLimiter {
	limiterOnce.Do(func() {
		albNodeLimiter = worker.NewConcurrencyLimiter(
			"alb_node_checker",
			configs.ALB.Precheck.MaxNodeWorkers,
		)
	})

	return albNodeLimiter
}

type NodeCheckerService struct {
	TraceID string

	Server   toc.ServerAdapter
	ServerV3 toc.ServerV3Adapter
	Meta     toc.MetaAdapter
	Net      toc.NetAdapter
	OS       toc.OSAdapter

	Cluster     sgw.L7ClusterAdapter
	NLBInstance sgw.NLBInstanceAdapter
}

// wrapCheckWithTiming wraps a check function with timing measurement
func wrapCheckWithTiming(checkFunc func() *sgwvo.TaskResult) *sgwvo.TaskResult {
	startTime := time.Now()
	result := checkFunc()

	// Defensive check: if checkFunc returns nil, return a default error result
	if result == nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      "unknown_check",
			Reason:    "check_function_returned_nil",
			StartedAt: startTime.Unix(),
			Duration:  time.Since(startTime).Seconds(),
		}
	}

	result.StartedAt = startTime.Unix()
	result.Duration = time.Since(startTime).Seconds()

	return result
}

func (s *NodeCheckerService) ensureNetAdapter(tocex toc.TocexAdapter) error {
	if s.Net != nil {
		return nil
	}

	if tocex == nil {
		return errors.New("tocexadapter_is_required")
	}

	netAdapter := toc.NewNetAdapter(tocex, s.TraceID)
	if netAdapter == nil {
		return errors.New("failed_to_create_netadapter")
	}

	s.Net = netAdapter

	return nil
}

func (s *NodeCheckerService) ensureOSAdapter(tocex toc.TocexAdapter) error {
	if s.OS != nil {
		return nil
	}

	if tocex == nil {
		return errors.New("tocexadapter_is_required")
	}

	osAdapter := toc.NewOSAdapter(tocex)
	if osAdapter == nil {
		return errors.New("failed_to_create_osadapter")
	}

	s.OS = osAdapter

	return nil
}

func (s *NodeCheckerService) checkNFConnTrackMax(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	taskName := "check_nf_conntrack_max"
	startTime := time.Now()

	net := toc.NewNetAdapter(tocex, s.TraceID)
	conntrackMax, err := net.ConnTrackMax()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "read_nf_conntrack_max_failed").Error(),
			StartedAt: startTime.Unix(),
			Duration:  time.Since(startTime).Seconds(),
		}
	}

	if conntrackMax < consts.NFConnTrackMax {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    fmt.Sprintf("nf_conntrack_max_%d_less_than_%d", conntrackMax, consts.NFConnTrackMax),
			StartedAt: startTime.Unix(),
			Duration:  time.Since(startTime).Seconds(),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "nf_conntrack_max_is_ok",
		StartedAt: startTime.Unix(),
		Duration:  time.Since(startTime).Seconds(),
	}
}

func (s *NodeCheckerService) checkUOASRCVersion(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	taskName := "check_uoa_srcversion"

	srcversion, err := tocex.ReadFile("/sys/module/uoa/srcversion")
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "read_uoa_srcversion_failed, try to `modprobe uoa`").Error(),
		}
	}

	modversion, err := tocex.RunTask("modinfo uoa | grep srcversion | awk '{print $2}'")
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "read_uoa_modversion_failed").Error(),
		}
	}

	if !strings.EqualFold(strings.TrimSpace(srcversion), strings.TrimSpace(modversion)) {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "uoa_srcversion_not_the_same_as_modversion",
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "uoa_srcversion_is_ok",
	}
}

func (s *NodeCheckerService) checkNicOptimization(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	taskName := "check_nic_optimization"

	bond0, err := tocex.RunTask(consts.ListBond0InterfacesScript)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "read_bond0_failed").Error(),
		}
	}

	slaves := strings.Split(bond0, "\n")
	slaves = slice.Filter(slaves, func(_ int, slave string) bool {
		return slave != ""
	})
	if len(slaves) == 0 {
		return &sgwvo.TaskResult{
			Success:   true,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "not_found_bond_slaves",
		}
	}

	slaves = slice.Map(slaves, func(_ int, slave string) string {
		return "-e " + slave
	})

	script := "shopee_linux_ethernet_optimization -n " + strings.Join(slaves, " ")

	stdout, err := tocex.RunTask(script)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "run_script_failed").Error(),
		}
	}

	lines := slice.Filter(strings.Split(stdout, "\n"), func(_ int, line string) bool {
		return strings.HasPrefix(line, "Ignoreing")
	})
	if len(lines) != 0 {
		lines = slice.Map(lines, func(_ int, line string) string {
			return strings.ReplaceAll(line, "Ignoreing", "unoptimized")
		})

		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "ringbuffer_isn't_optimized\n\n" + strings.Join(lines, "\n"),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "ringbuffer_is_optimized",
	}
}

func (s *NodeCheckerService) checkNPMIPTables(ip string, rules []string) *sgwvo.TaskResult {
	taskName := "check_iptables"

	server, err := s.ServerV3.Server(ip)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_server_failed").Error(),
		}
	}

	svr3 := *server
	serverV3 := tocvo.ServerV3(svr3)
	if !serverV3.IsShopee() && !serverV3.IsGeneral() {
		return &sgwvo.TaskResult{
			Success:   true,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "server_is_not_shopee_general",
		}
	}

	// limit Shopee General AZ
	sdnRules := slice.Filter(rules, func(_ int, line string) bool {
		return strings.HasPrefix(strings.TrimSpace(line), "-A INPUT-SDNChain")
	})

	var notFoundRule []string
	maputil.ForEach(consts.ALBIngressRuleRegexes, func(name string, regex *regexp.Regexp) {
		_, ok := slice.FindBy(sdnRules, func(_ int, rule string) bool {
			return regex.MatchString(rule)
		})
		if !ok {
			notFoundRule = append(notFoundRule, name)
		}
	})

	if len(notFoundRule) != 0 {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "not_found_iptables_rules\n\n" + strings.Join(notFoundRule, "\n"),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "npm_iptables_rules_exist",
	}
}

func (s *NodeCheckerService) checkIPTables(ip string, tocex toc.TocexAdapter) *sgwvo.TaskResult {
	taskName := "check_iptables"
	vars, err := s.Server.GetALBGroupVar(ip)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_tag_vars_failed").Error(),
		}
	}

	if s.shouldCheckVRRPIPTables(ip) {
		if err := vars.CheckVRRPIPTables(); err != nil {
			return &sgwvo.TaskResult{
				Success:   false,
				UpdatedAt: time.Now().Unix(),
				Task:      taskName,
				Reason:    errors.Wrap(err, "check_vrrp_iptables_failed").Error(),
			}
		}
	}

	rules, err := tocex.RunTask("iptables-save")
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_iptables_failed").Error(),
		}
	}

	iptableRules := strings.Split(rules, "\n")
	if len(vars.IPTableRules) == 0 {
		return s.checkNPMIPTables(ip, iptableRules)
	}

	rulem := convertor.ToMap(iptableRules, func(line string) (string, struct{}) {
		return cryptor.Md5Byte(byteutils.ToBytes(strings.TrimSpace(line))), struct{}{}
	})

	nonExist := slice.Filter(vars.IPTableRules, func(_ int, rule string) bool {
		_, ok := rulem[cryptor.Md5Byte(byteutils.ToBytes(strings.TrimSpace(rule)))]

		return !ok && !strings.HasPrefix(strings.TrimSpace(rule), "# ")
	})

	if len(nonExist) != 0 {
		err = errors.Errorf("non_exist_iptables_rules\n\n%s", strings.Join(nonExist, "\n"))

		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "check_iptables_failed").Error(),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "custom_iptables_rules_exist",
	}
}

// checkPortParallel performs parallel port checks for TCP 80, TCP 443, and UDP 443
func (s *NodeCheckerService) checkPort(ip string, tocex toc.TocexAdapter) *sgwvo.TaskResult {
	taskName := "check_tcp_udp_port"

	type portCheck struct {
		port     string
		protocol string
		command  string
		result   error
	}

	// Generate port check commands using template
	tcp80Cmd, _ := tpl.PortCheck(ip, "80", "tcp")
	tcp443Cmd, _ := tpl.PortCheck(ip, "443", "tcp")
	udp443Cmd, _ := tpl.PortCheck(ip, "443", "udp")

	checks := []portCheck{
		{"80", "tcp", tcp80Cmd, nil},
		{"443", "tcp", tcp443Cmd, nil},
		{"443", "udp", udp443Cmd, nil},
	}

	// Run all port checks in parallel
	var wg sync.WaitGroup
	for i := range checks {
		wg.Add(1)
		go func(check *portCheck) {
			defer wg.Done()
			_, check.result = tocex.RunTask(check.command)
		}(&checks[i])
	}

	// Wait for all checks to complete
	wg.Wait()

	// Check results and return first failure, or success if all pass
	for _, check := range checks {
		if check.result != nil {
			return &sgwvo.TaskResult{
				Success:   false,
				UpdatedAt: time.Now().Unix(),
				Task:      taskName,
				Reason:    check.protocol + "_" + check.port + "_port_is_not_accepted",
			}
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "all_ports_accessible",
	}
}

func (s *NodeCheckerService) checkServiceIsActive(tocex toc.TocexAdapter, service string) *sgwvo.TaskResult {
	taskName := fmt.Sprintf("check_%s_is_active", service)

	systemctl := toc.NewSystemctlAdapter(tocex)
	ok, err := systemctl.IsActive(service)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "check_service_%s_is_active_failed", service).Error(),
		}
	}

	if !ok {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    service + "_is_inactive",
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    service + "_is_active",
	}
}

func (s *NodeCheckerService) checkServiceIsDisabled(tocex toc.TocexAdapter, service string) *sgwvo.TaskResult {
	taskName := fmt.Sprintf("check_%s_is_disabled", service)

	systemctl := toc.NewSystemctlAdapter(tocex)
	ok, err := systemctl.IsEnabled(service)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "check_service_%s_is_disabled_failed", service).Error(),
		}
	}

	if ok {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    service + "_is_enabled",
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    service + "_is_disabled",
	}
}

// nginxTestTimeout returns the timeout for nginx test task
func (s *NodeCheckerService) nginxTestTimeout() time.Duration {
	timeout := configs.ALB.NginxTestTimeout()
	const multiplier = 2

	if timeout == 0 {
		timeout = configs.Mgmt.Timeout() * multiplier // 2 times of default timeout
	}

	return timeout
}

func (s *NodeCheckerService) checkNginxTest(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	taskName := "check_nginx_test"

	timeout := s.nginxTestTimeout()
	ret, err := tocex.WithTimeout(timeout).
		RunTask(`nginx -t 2>&1 | grep "/etc/nginx/nginx.conf test"`)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "ngin_test_failed").Error(),
		}
	}

	if !strings.Contains(strings.TrimSpace(ret), "test is successful") {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "nginx_test_is_failed",
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "nginx_test_is_successful",
	}
}

func (s *NodeCheckerService) checkDockerContainers(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	taskName := "check_docker_containers"

	docker := toc.NewDockerAdapter(tocex)
	containers, err := docker.Containers()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_containers_failed").Error(),
		}
	}

	containers2 := convertor.ToMap(containers, func(container *toc.Container) (string, *toc.Container) {
		return container.Name, container
	})

	for _, comp := range slice.Filter(cmp.ALBCoreComponents, func(_ int, item cmp.Component) bool {
		return item.Type == cmp.ComponentTypeDocker
	}) {
		container, ok := containers2[comp.Service]
		if !ok {
			return &sgwvo.TaskResult{
				Success:   false,
				UpdatedAt: time.Now().Unix(),
				Task:      taskName,
				Reason:    comp.Service + "_is_not_found",
			}
		}

		if !container.IsRunning() {
			return &sgwvo.TaskResult{
				Success:   false,
				UpdatedAt: time.Now().Unix(),
				Task:      taskName,
				Reason:    comp.Service + "_is_not_running",
			}
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "all_docker_containers_are_running",
	}
}

func (s *NodeCheckerService) checkServicesFile(tocex toc.TocexAdapter, clusterUUID string) *sgwvo.TaskResult {
	taskName := "check_services_file"

	cluster := sgw.NewALBClusterAdapter(s.TraceID)
	cls, err := cluster.GetByUUID(clusterUUID)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_cluster_failed").Error(),
		}
	}

	if cls.ListenerCount == 0 {
		return &sgwvo.TaskResult{
			Success:   true,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "no_listeners_in_cluster",
		}
	}

	node, err := sgw.NewALBNodeAdapter(s.TraceID, tocex)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "create_node_failed").Error(),
		}
	}

	file, err := node.NginxServiceFile()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_services_file_failed").Error(),
		}
	}

	if file.Size <= consts.ALBNginxServicesMinSize {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "services_file_is_empty",
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "services_file_is_not_empty",
	}
}

//nolint:dupl
func (s *NodeCheckerService) checkSGWEtcd(tocex toc.TocexAdapter, clusterUUID string) *sgwvo.TaskResult {
	taskName := "check_sgw_etcd"

	cluster, err := s.Cluster.GetByUUID(clusterUUID)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_cluster_failed").Error(),
		}
	}

	clusterEtcdMeta := &etcdvo.Meta{ClusterURL: cluster.NodeEtcd}
	clusterEtcdEndpoints, err := clusterEtcdMeta.Endpoints()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_cluster_etcd_endpoint_failed").Error(),
		}
	}

	node, err := toc.NewSGWNodeAdapter(tocex)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_node_failed").Error(),
		}
	}

	etcd, err := node.SGWEtcd()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_etcd_failed").Error(),
		}
	}

	etcdMeta := &etcdvo.Meta{ClusterURL: etcd}
	etcdEndpoints, err := etcdMeta.Endpoints()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_etcd_endpoint_failed").Error(),
		}
	}

	if !etcdvo.ETCDEndpoints(clusterEtcdEndpoints).Equals(etcdEndpoints) {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason: fmt.Sprintf("sgw_etcd_not_matched-(%s)%s-expected:%s",
				tocex.HostIP(),
				etcdvo.ETCDEndpoints(etcdEndpoints),
				etcdvo.ETCDEndpoints(clusterEtcdEndpoints),
			),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "sgw_etcd_matched",
	}
}

//nolint:dupl
func (s *NodeCheckerService) checkMesosZK(tocex toc.TocexAdapter, clusterUUID string) *sgwvo.TaskResult {
	taskName := "check_mesos_zk"

	cluster, err := s.Cluster.GetByUUID(clusterUUID)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_cluster_failed").Error(),
		}
	}

	clusterZKMeta := &zkvo.Meta{ClusterURL: cluster.NodeZK}
	clusterZKEndpoints, err := clusterZKMeta.Endpoints()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_cluster_zk_endpoint_failed").Error(),
		}
	}

	node, err := toc.NewSGWNodeAdapter(tocex)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_node_failed").Error(),
		}
	}

	zk, err := node.MesosZK()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_zk_failed").Error(),
		}
	}

	zkMeta := &zkvo.Meta{ClusterURL: zk}
	zkEndpoints, err := zkMeta.Endpoints()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_zk_endpoint_failed").Error(),
		}
	}

	if !zkvo.ZKEndpoints(clusterZKEndpoints).Equals(zkEndpoints) {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,

			Reason: fmt.Sprintf("mesos_zk_not_matched-(%s)%s-expected:%s",
				tocex.HostIP(),
				zkvo.ZKEndpoints(zkEndpoints),
				zkvo.ZKEndpoints(clusterZKEndpoints),
			),
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "mesos_zk_matched",
	}
}

func (s *NodeCheckerService) checkListeners(tocex toc.TocexAdapter, clusterUUID string) *sgwvo.TaskResult {
	taskName := "check_listeners"

	cluster := sgw.NewALBClusterAdapter(s.TraceID)
	cls, err := cluster.GetByUUID(clusterUUID)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_cluster_failed").Error(),
		}
	}

	if cls.ListenerCount == 0 && cls.ListenerCountFromInstances() == 0 {
		return &sgwvo.TaskResult{
			Success:   true,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "no_listeners_binding",
		}
	}

	instance := sgw.NewALBInstanceAdapter(s.TraceID)
	listeners := slice.Map(cls.Instances, func(_ int, ins *albvo.Instance) []string {
		req := &sgw.L7ListenerBoundRequest{
			InstanceID: ins.InstanceID,
		}
		req.Pager = consts.NewDefaultPager()
		bindings, err := instance.ListenerBindings(req)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"instance_id":  ins.InstanceID,
				"cluster_id":   cls.UUID,
				"cluster_name": cls.Name,
			}).Error("fetch_instance_listeners_binding_failed")

			return nil
		}

		return slice.Map(bindings, func(_ int, binding *albvo.InstanceListenerBinding) string {
			if binding == nil {
				return ""
			}

			if binding.Listener == nil || binding.Listener.DomainName == "" {
				return binding.DomainName
			}

			return binding.Listener.DomainName
		})
	})

	listeners2 := slice.FlatMap(listeners, func(_ int, listener []string) []string {
		return listener
	})
	listeners2 = slice.Unique(listeners2)

	files, err := tocex.ListFiles("/etc/nginx/http-enabled/")
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrap(err, "fetch_listeners_failed").Error(),
		}
	}

	// filter hidden files
	filesAtNode := slice.Filter(files, func(_ int, f *toclib.FileInfo) bool {
		return !strings.HasPrefix(f.Name, ".")
	})
	filesAtNode2 := slice.Map(filesAtNode, func(_ int, f *toclib.FileInfo) string {
		return f.Name
	})

	listener3 := slice.Difference(listeners2, filesAtNode2)
	if len(listener3) > 0 {
		// filter those only under `enabled`
		listener := sgw.NewALBListenerAdapter(s.TraceID)
		listeners4, err := listener.ListByCluster(clusterUUID)
		if err != nil {
			return &sgwvo.TaskResult{
				Success:   false,
				UpdatedAt: time.Now().Unix(),
				Task:      taskName,
				Reason:    errors.Wrap(err, "fetch_listeners_failed").Error(),
			}
		}

		notEnabledListeners := convertor.ToMap(listener3, func(item string) (string, struct{}) {
			return item, struct{}{}
		})

		listeners5 := slice.Filter(listeners4, func(_ int, lis *albvo.Listener) bool {
			if _, ok := notEnabledListeners[lis.DomainName]; ok {
				return lis.IsEnabled()
			}

			return false
		})

		domains := slice.Map(listeners5, func(_ int, lis *albvo.Listener) string {
			return lis.DomainName
		})
		if len(domains) == 0 {
			return &sgwvo.TaskResult{
				Success:   true,
				UpdatedAt: time.Now().Unix(),
				Task:      taskName,
				Reason:    fmt.Sprintf("%d_listeners_are_enabled", len(listeners2)),
			}
		}

		reason := fmt.Sprintf("%d_listeners_are_not_enabled\n\n%s", len(domains), strings.Join(domains, "\n"))

		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    reason,
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    fmt.Sprintf("%d_listeners_are_enabled", len(listeners2)),
	}
}

// restartAllContainersWithHealthCheck restarts containers with health check polling instead of fixed sleep
func (s *NodeCheckerService) restartAllContainers(tocex toc.TocexAdapter, maxWaitTime time.Duration) *sgwvo.TaskResult {
	taskName := "restart_all_containers"

	_, err := tocex.RunTask(`docker restart mesos-nginx-lb alb-sd`)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "restart_container_failed").Error(),
		}
	}

	// Use health check polling with exponential backoff instead of fixed sleep
	return s.waitForContainersReady(tocex, maxWaitTime)
}

// waitForContainersReady polls container health with exponential backoff
func (s *NodeCheckerService) waitForContainersReady(tocex toc.TocexAdapter, maxWaitTime time.Duration) *sgwvo.TaskResult {
	const (
		initialInterval = 1 * time.Second
		maxInterval     = 5 * time.Second
		backoffFactor   = 1.5
	)

	startTime := time.Now()
	interval := initialInterval

	for time.Since(startTime) < maxWaitTime {
		// Check if containers are running and healthy
		checkCmd := tpl.ContainerHealthCheck()
		result, err := tocex.RunTask(checkCmd)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"task":    "check_containers_running",
				"command": checkCmd,
				"result":  result,
				"elapsed": time.Since(startTime).String(),
			}).Warn("failed_to_check_container_health")

			// Wait before next check with exponential backoff
			time.Sleep(interval)
			interval = min(time.Duration(float64(interval)*backoffFactor), maxInterval)

			continue
		}

		if len(result) > 0 && strings.HasPrefix(result, "2") {
			elapsed := time.Since(startTime)

			return &sgwvo.TaskResult{
				Success:   true,
				UpdatedAt: time.Now().Unix(),
				Task:      "restart_all_containers",
				Reason:    "containers_ready_in_" + elapsed.String(),
			}
		}

		// Wait before next check with exponential backoff
		time.Sleep(interval)
		interval = min(time.Duration(float64(interval)*backoffFactor), maxInterval)
	}

	// Timeout reached, but containers might still be starting
	elapsed := time.Since(startTime)

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      "restart_all_containers",
		Reason:    "containers_restarted_max_wait_" + elapsed.String(),
	}
}

func (s *NodeCheckerService) checkALBReadiness(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	taskName := "check_alb_readiness"

	node, err := sgw.NewALBNodeAdapter(s.TraceID, tocex)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "new_alb_node_adapter_failed").Error(),
		}
	}

	ready, err := node.IsALBSDReady()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "check_alb_sd_readiness_failed").Error(),
		}
	}

	if !ready {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "alb_sd_is_not_ready",
		}
	}

	ready, err = node.IsSGWAgentReady()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "check_sgw_agent_readiness_failed").Error(),
		}
	}

	if !ready {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "sgw_agent_is_not_ready",
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "alb_is_ready",
	}
}

func (s *NodeCheckerService) checkTreeNode(node string) *sgwvo.TaskResult {
	taskName := "check_tree_node"
	svrV3, err := s.ServerV3.Server(node)
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "fetch_server_failed",
		}
	}

	serverV3vo := tocvo.ServerV3(*svrV3)
	if !serverV3vo.IsClusterSupported() {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "bind_server_to_tree_non_supported_cluster",
		}
	}

	if err = serverV3vo.VerifyResourceNodes(consts.ALB); err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    "server_tree_incorrect",
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "tree_node_correct",
	}
}

func (s *NodeCheckerService) fetchNodeCheckResult(
	ctx context.Context,
	uuid, node string,
	tocex toc.TocexAdapter,
	tracker *albvo.PreCheckTracker,
) []*sgwvo.TaskResult {
	// Create checker pool with max 5 concurrent checkers per node
	pool := worker.NewCheckerPool(configs.ALB.Precheck.MaxNodeWorkers)
	defer pool.Release()

	// Helper to create checker tasks with tracking
	createCheckerTask := func(checkName string, priority int, checkFunc func() *sgwvo.TaskResult) *worker.CheckerTask {
		return &worker.CheckerTask{
			Name:     checkName,
			Priority: priority,
			Func: func() *sgwvo.TaskResult {
				// Check if context is cancelled
				if ctxErr := ctx.Err(); ctxErr != nil {
					result := &sgwvo.TaskResult{
						Success:   false,
						UpdatedAt: time.Now().Unix(),
						Task:      checkName,
						Reason:    fmt.Sprintf("cancelled_due_to_timeout_%v", ctxErr),
					}
					if tracker != nil {
						tracker.CompleteSpecificCheck(node, checkName, false, "cancelled_due_to_timeout")
					}

					return result
				}

				if tracker != nil {
					tracker.UpdateNodeCheck(node, checkName)
				}

				result := wrapCheckWithTiming(checkFunc)
				if tracker != nil {
					tracker.CompleteNodeCheck(node)
					tracker.CompleteSpecificCheck(node, checkName, result.Success, result.Reason)
				}

				return result
			},
		}
	}

	// Define checker tasks with priorities based on dependencies
	// Priority 1: Must run first (container restart)
	priority := 1
	pool.AddTask(createCheckerTask("restart_all_containers", priority, func() *sgwvo.TaskResult {
		return s.restartAllContainers(tocex, configs.ALB.RestartSleepTime())
	}))

	// Priority 2: System checks (can run in parallel)
	priority++
	pool.AddTask(createCheckerTask("check_nf_conntrack_max", priority, func() *sgwvo.TaskResult {
		return s.checkNFConnTrackMax(tocex)
	}))
	pool.AddTask(createCheckerTask("check_uoa_srcversion", priority, func() *sgwvo.TaskResult {
		return s.checkUOASRCVersion(tocex)
	}))
	pool.AddTask(createCheckerTask("check_nic_optimization", priority, func() *sgwvo.TaskResult {
		return s.checkNicOptimization(tocex)
	}))
	pool.AddTask(createCheckerTask("check_nic_driver", priority, func() *sgwvo.TaskResult {
		return s.checkNicDriver(tocex)
	}))

	// Priority 3: Service checks (can run in parallel)
	priority++
	pool.AddTask(createCheckerTask("check_iptables", priority, func() *sgwvo.TaskResult {
		return s.checkIPTables(node, tocex)
	}))
	pool.AddTask(createCheckerTask("check_tcp_udp_port", priority, func() *sgwvo.TaskResult {
		return s.checkPort(node, tocex)
	}))
	pool.AddTask(createCheckerTask("check_nginx_is_active", priority, func() *sgwvo.TaskResult {
		return s.checkServiceIsActive(tocex, cmp.NginxComponent.Service)
	}))
	pool.AddTask(createCheckerTask("check_docker_containers", priority, func() *sgwvo.TaskResult {
		return s.checkDockerContainers(tocex)
	}))

	// Priority 4: Configuration checks (can run in parallel)
	priority++
	pool.AddTask(createCheckerTask("check_nginx_test", priority, func() *sgwvo.TaskResult {
		return s.checkNginxTest(tocex)
	}))
	pool.AddTask(createCheckerTask("check_sgw_etcd", priority, func() *sgwvo.TaskResult {
		return s.checkSGWEtcd(tocex, uuid)
	}))
	pool.AddTask(createCheckerTask("check_mesos_zk", priority, func() *sgwvo.TaskResult {
		return s.checkMesosZK(tocex, uuid)
	}))
	pool.AddTask(createCheckerTask("check_tree_node", priority, func() *sgwvo.TaskResult {
		return s.checkTreeNode(node)
	}))

	// Priority 5: Final checks (can run in parallel)
	priority++
	pool.AddTask(createCheckerTask("check_listeners", priority, func() *sgwvo.TaskResult {
		return s.checkListeners(tocex, uuid)
	}))
	pool.AddTask(createCheckerTask("check_services_file", priority, func() *sgwvo.TaskResult {
		return s.checkServicesFile(tocex, uuid)
	}))
	pool.AddTask(createCheckerTask("check_alb_readiness", priority, func() *sgwvo.TaskResult {
		return s.checkALBReadiness(tocex)
	}))

	// Execute all tasks through the pool
	pool.Execute(ctx)

	return pool.GetResults()
}

// PreCheck check conditions for online nodes
/*
check nf_conntrack_max
check uoa srcversion
check ringbuffer
check iptables, according to tag vars, tcp:80/443, udp:443
check nginx is-active
check docker containers under running
check listeners with binding
*/
func (s *NodeCheckerService) PreCheck(ctx context.Context, req *albvo.ClusterNodeIPsRequest) (
	[]*sgwvo.NodeTaskResult, error,
) {
	// Create tracker for timing and progress monitoring
	tracker := albvo.NewPreCheckTracker(len(req.IPs))

	cluster, err := s.Cluster.GetByUUID(req.UUID)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_cluster_failed")
	}

	azInfo, err := s.Meta.AZ(cluster.AZ)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_az_failed")
	}

	segmentMeta := azInfo.Segment(cluster.Segment)
	netVer := consts.ParseNetworkVersion(segmentMeta.NetworkVersion)
	nodeIPs := slice.Difference(cluster.ReadyNodeIPs(), req.IPs)

	// Process nodes with global concurrency control
	results, err := iter.MapErr(req.IPs, func(ip *string) (*sgwvo.NodeTaskResult, error) {
		node := *ip

		// Acquire a slot from the global limiter
		limiter := getALBNodeLimiter()
		if err := limiter.Acquire(ctx); err != nil {
			return nil, errors.WithMessage(err, "failed_to_acquire_node_slot")
		}
		defer limiter.Release()

		tocex, err := toc.NewTocexAdapterWithIP(node, s.TraceID)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_tocex_failed")
		}

		sgwNode, err := toc.NewSGWNodeAdapter(tocex)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_sgw_node_failed")
		}

		rets := s.fetchNodeCheckResult(ctx, req.UUID, node, tocex, tracker)

		if azInfo.IsPrivate() {
			rets = append(rets, s.fetchNodeCheckByTag(node, tocex, netVer)...)
		}

		checkRets, err := s.fetchReadyCheckResult(&readyChecker{
			sgwNode: sgwNode,
			nodeIPs: nodeIPs,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_ready_check_result_failed")
		}

		rets = append(rets, checkRets...)

		_, ok := slice.FindBy(rets, func(_ int, r *sgwvo.TaskResult) bool {
			return !r.Success // false means failed
		})

		return &sgwvo.NodeTaskResult{
			IP:     node,
			Passed: !ok,
			Tasks:  rets,
		}, nil
	})
	if err != nil {
		return nil, errors.WithMessage(err, "precheck_node_failed")
	}

	s.storePreCheckResult(ctx, results)

	return results, nil
}

func (s *NodeCheckerService) storePreCheckResult(
	ctx context.Context,
	results []*sgwvo.NodeTaskResult,
) {
	results2 := convertor.ToMap(results, func(result *sgwvo.NodeTaskResult) (string, interface{}) {
		ret, err := json.Marshal(result)
		if err != nil {
			log.Logger().WithField("ip", result.IP).WithError(err).Error("precheck_node_marshal_failed")

			return result.IP, nil
		}

		return result.IP, ret
	})

	cmd := opscah.ElasticRedis.HSet(ctx, consts.ALBNodePreCheckKey, results2)
	if cmd.Err() != nil {
		log.Logger().WithField("key", consts.ALBNodePreCheckKey).WithError(cmd.Err()).Error("set_precheck_node_failed")
	}
}

func (s *NodeCheckerService) FetchPreCheckResult(ctx context.Context, ips []string) ([]*sgwvo.NodeTaskResult, error) {
	return s.fetchPreCheckResult(ctx, ips, consts.ALBNodePreCheckKey)
}

func (s *NodeCheckerService) fetchPreCheckResult(ctx context.Context, ips []string, key string) (
	[]*sgwvo.NodeTaskResult, error,
) {
	results, err := opscah.ElasticRedis.HMGet(ctx, key, ips...).Result()
	if err != nil {
		log.Logger().WithField("key", key).WithError(err).Error("fetch_precheck_node_failed")

		return nil, errors.WithMessage(err, "fetch_precheck_node_failed")
	}

	var rets []*sgwvo.NodeTaskResult
	slice.ForEach(results, func(i int, result interface{}) {
		val, ok := result.(string)
		if !ok {
			return
		}

		var ret sgwvo.NodeTaskResult
		if err := json.Unmarshal(byteutils.ToBytes(val), &ret); err != nil {
			log.Logger().WithError(err).WithField("ret", result).Error("unmarshal_precheck_node_failed")

			return
		}

		rets = append(rets, &ret)
	})

	return rets, nil
}

func (s *NodeCheckerService) shouldCheckVRRPIPTables(ip string) bool {
	server, err := s.ServerV3.Server(ip)
	if err != nil {
		log.Logger().WithField("ip", ip).WithError(err).
			Warn("fetch_server_failed")

		return false
	}
	serverV3 := tocvo.ServerV3(*server)

	if serverV3.IsGeneral() {
		/*
			log.Logger().WithField("ip", ip).
				Warn("server_is_general")
		*/

		return false
	}

	netVer, err := s.Meta.NetworkVersion(server.AZ, server.Segment)
	if err != nil {
		log.Logger().WithFields(log.Fields{
			"az":      server.AZ,
			"segment": server.Segment,
			"ip":      ip,
		}).WithError(err).Warn("fetch_network_version_failed")

		return false
	}

	ham, err := serverV3.HAMode(netVer)
	if err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":  ip,
			"net": netVer,
		}).WithError(err).Warn("determine_ham_failed")

		return false
	}

	if !ham.IsHAMWithVRRP() {
		/*
			log.Logger().WithFields(log.Fields{
				"ip":      ip,
				"ham":     ham,
				"hamCode": hamCode,
			}).Warn("not_ham_non_std1")
		*/

		return false
	}

	return true
}

func (s *NodeCheckerService) checkNicDriver(tocex toc.TocexAdapter) *sgwvo.TaskResult {
	taskName := "check_nic_driver"

	if err := s.ensureNetAdapter(tocex); err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "check_nic_driver_failed").Error(),
		}
	}

	if err := s.ensureOSAdapter(tocex); err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "check_nic_driver_failed").Error(),
		}
	}

	drivers, err := s.Net.NicDrivers()
	if err != nil {
		return &sgwvo.TaskResult{
			Success:   false,
			UpdatedAt: time.Now().Unix(),
			Task:      taskName,
			Reason:    errors.Wrapf(err, "fetch_driver_failed").Error(),
		}
	}

	var kernelVersion string

	for _, driver := range drivers {
		if driver.IsICE() {
			if kernelVersion == "" {
				kernelVersion, err = s.OS.KernelVersion()
				if err != nil {
					return &sgwvo.TaskResult{
						Success:   false,
						UpdatedAt: time.Now().Unix(),
						Task:      taskName,
						Reason:    errors.Wrapf(err, "fetch_kernel_version_failed").Error(),
					}
				}
			}

			if driver.VersionWithoutPatch(kernelVersion) {
				return &sgwvo.TaskResult{
					Success:   false,
					UpdatedAt: time.Now().Unix(),
					Task:      taskName,
					Reason:    fmt.Sprintf("nic_%s_ice_driver_is_not_up_to_date", driver.Name),
				}
			}
		}
	}

	return &sgwvo.TaskResult{
		Success:   true,
		UpdatedAt: time.Now().Unix(),
		Task:      taskName,
		Reason:    "nic_driver_is_up_to_date",
	}
}
