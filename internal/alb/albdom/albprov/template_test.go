package albprov

import (
	"fmt"
	"net"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
)

func fetchNodeProvision(t *testing.T, svr *tocvo.ALBNodeServer) *NodeProvision {
	t.Helper()

	server := toc.NewServerAdapter(t.Name())

	azInfo, err := toc.NewMetaAdapter(t.Name()).AZ(svr.Meta.AZ)
	require.NoError(t, err)

	prov := &NodeProvision{
		TraceID:    t.Name(),
		NodeServer: svr,

		Agent: svr.Agent,
		Meta:  svr.Meta,
		Tags:  svr.Tags,

		tocex: toc.NewTocexAdapter(svr.Node()),

		server:      server,
		AZInfo:      azInfo,
		ext2cluster: sgw.NewExt2Cluster(t.Name()),
	}

	return prov
}

func fetchNodeHATemplates(t *testing.T, svr *tocvo.ALBNodeServer, inet toc.NetAdapter) (
	[]toclib.ProvisionNodeTemplate, error,
) {
	t.Helper()

	prov := fetchNodeProvision(t, svr)
	if inet != nil {
		prov.inet = inet
	}

	return prov.HATemplates()
}

func TestNodeProvision_HATemplates_failed(t *testing.T) {
	svr := fetchALBServerByIP(t, configs.E2E.ALB.Node2)
	prov := fetchNodeProvision(t, svr)

	// mock server, return nonstd-3
	mockSvrV3 := tocvo.NewMockIServerV3(gomock.NewController(t))
	mockSvrV3.EXPECT().HAMode(gomock.Any()).Return(consts.HAMNonStd3, nil).AnyTimes()
	svr.ServerV3 = mockSvrV3

	svr.Meta.ECMPBGPVIPs = nil
	svr.Meta.ECMPBGPWanVIPs = nil
	_, err := prov.HATemplates()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not_found_any_VIPs")
	svr.Meta.ECMPBGPWanVIPs = []string{gofakeit.IPv4Address()}

	svr.Agent.IP = ""
	_, err = prov.HATemplates()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "fetch_bird_exporter_service_failed")
	svr.Agent.IP = configs.E2E.ALB.Node2

	svr.Tags = tocvo.Tags{
		{
			TagCategoryKey: consts.HAMTagName,
			Value:          consts.HAMNonStd1.Name,
		},
	}
	svr.Meta.BGPASMapping = nil

	inet := toc.NewMockNetAdapter(gomock.NewController(t))
	inet.EXPECT().RouteTables().Return(tocvo.RoutingTables{
		{
			Destination: net.ParseIP("0.0.0.0"),
			Gateway:     net.ParseIP(gofakeit.IPv4Address()),
		},
	}, nil).AnyTimes()
	_, err = fetchNodeHATemplates(t, svr, inet)
	assert.NoError(t, err)
}

func TestNodeProvision_HATemplates_NonStd2_WAN(t *testing.T) {
	t.Parallel()

	svr := fetchALBServerByIP(t, configs.E2E.ALB.Node2)
	svr.Tags = tocvo.Tags{
		{
			TagCategoryKey: consts.HAMTagName,
			Value:          consts.HAMNonStd2.Name,
		},
	}

	t.Run("local", func(t *testing.T) {
		t.Parallel()

		if ciEnv {
			return
		}

		_, err := fetchNodeHATemplates(t, svr, nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not_found_wan_ip")
	})

	t.Run("ci", func(t *testing.T) {
		t.Parallel()

		inet := toc.NewMockNetAdapter(gomock.NewController(t))
		inet.EXPECT().Network().Return(&tocvo.NodeNetwork{
			Network: tocvo.Network{
				Vlans: map[string]tocvo.VLan{
					"wan": {
						Addresses: []string{"************/24"},
					},
				},
			},
		}, nil).AnyTimes()

		_, err := fetchNodeHATemplates(t, svr, inet)
		assert.NoError(t, err)
	})
}

func TestNodeProvision_HATemplates_NonStd2_BGPASMapping_route_failed(t *testing.T) {
	svr := fetchALBServerByIP(t, configs.E2E.ALB.Node2)
	svr.Tags = tocvo.Tags{
		{
			TagCategoryKey: consts.HAMTagName,
			Value:          consts.HAMNonStd2.Name,
		},
	}
	svr.Meta.BGPASMapping = nil

	// mock server, return nonstd-3
	mockSvrV3 := tocvo.NewMockIServerV3(gomock.NewController(t))
	mockSvrV3.EXPECT().HAMode(gomock.Any()).Return(consts.HAMNonStd3, nil).AnyTimes()
	svr.ServerV3 = mockSvrV3

	inet := toc.NewMockNetAdapter(gomock.NewController(t))
	inet.EXPECT().RouteTables().Return(nil, fmt.Errorf("read_procfs_failed")).AnyTimes()
	inet.EXPECT().Network().DoAndReturn(func() (*tocvo.NodeNetwork, error) {
		return &tocvo.NodeNetwork{
			Network: tocvo.Network{
				Vlans: map[string]tocvo.VLan{
					"wan": {
						Addresses: []string{"************/24"},
					},
				},
			},
		}, nil
	}).AnyTimes()

	_, err := fetchNodeHATemplates(t, svr, inet)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "fetch_route_tables_failed")
}

func TestNodeProvision_HATemplates_NonStd2_BGPASMapping_route_no_default(t *testing.T) {
	svr := fetchALBServerByIP(t, configs.E2E.ALB.Node2)
	svr.Tags = tocvo.Tags{
		{
			TagCategoryKey: consts.HAMTagName,
			Value:          consts.HAMNonStd2.Name,
		},
	}
	svr.Meta.BGPASMapping = nil

	// mock server, return nonstd-3
	mockSvrV3 := tocvo.NewMockIServerV3(gomock.NewController(t))
	mockSvrV3.EXPECT().HAMode(gomock.Any()).Return(consts.HAMNonStd3, nil).AnyTimes()
	svr.ServerV3 = mockSvrV3

	inet := toc.NewMockNetAdapter(gomock.NewController(t))
	inet.EXPECT().RouteTables().Return(tocvo.RoutingTables{
		{
			Destination: net.ParseIP("10.0.0.0"),
			Gateway:     net.ParseIP(gofakeit.IPv4Address()),
		},
	}, nil).AnyTimes()
	inet.EXPECT().Network().DoAndReturn(func() (*tocvo.NodeNetwork, error) {
		return &tocvo.NodeNetwork{
			Network: tocvo.Network{
				Vlans: map[string]tocvo.VLan{
					"wan": {
						Addresses: []string{"************/24"},
					},
				},
			},
		}, nil
	}).AnyTimes()

	_, err := fetchNodeHATemplates(t, svr, inet)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "fetch_default_route_tables_failed")
}

func TestNodeProvision_HATemplates_NonStd2_BGPASMapping(t *testing.T) {
	svr := fetchALBServerByIP(t, configs.E2E.ALB.Node2)
	svr.Tags = tocvo.Tags{
		{
			TagCategoryKey: consts.HAMTagName,
			Value:          consts.HAMNonStd2.Name,
		},
	}
	svr.Meta.BGPASMapping = nil

	inet := toc.NewMockNetAdapter(gomock.NewController(t))
	inet.EXPECT().RouteTables().Return(tocvo.RoutingTables{
		{
			Destination: net.ParseIP("0.0.0.0"),
			Gateway:     net.ParseIP(gofakeit.IPv4Address()),
		},
	}, nil).AnyTimes()
	inet.EXPECT().Network().DoAndReturn(func() (*tocvo.NodeNetwork, error) {
		return &tocvo.NodeNetwork{
			Network: tocvo.Network{
				Vlans: map[string]tocvo.VLan{
					"wan": {
						Addresses: []string{"************/24"},
					},
				},
			},
		}, nil
	}).AnyTimes()

	_, err := fetchNodeHATemplates(t, svr, inet)
	assert.NoError(t, err)
}

func TestNodeProvision_Templates_hardware_interface_failed(t *testing.T) {
	prov := fetchNodeProvision(t, fetchALBServerByIP(t, configs.E2E.ALB.Node2))

	server := toc.NewMockServerAdapter(gomock.NewController(t))
	server.EXPECT().HardwareV2(gomock.Eq(configs.E2E.ALB.Node2)).Return(nil, fmt.Errorf("not found")).AnyTimes()

	prov.server = server

	_, err := prov.SystemTemplates()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestNodeProvision_Templates_hardware_interface(t *testing.T) {
	prov := fetchNodeProvision(t, fetchALBServerByIP(t, configs.E2E.ALB.Node2))

	server := toc.NewMockServerAdapter(gomock.NewController(t))
	server.EXPECT().HardwareV2(gomock.Eq(configs.E2E.ALB.Node2)).DoAndReturn(func(ip string) (*tocvo.HardwareV2, error) {
		hw := tocvo.HardwareV2{}
		assert.NoError(t, gofakeit.Struct(&hw))

		return &hw, nil
	}).AnyTimes()

	prov.server = server

	templates, err := prov.SystemTemplates()
	assert.NoError(t, err)
	assert.Greater(t, len(templates), 0)
}

func TestNodeProvision_Templates(t *testing.T) {
	prov := fetchNodeProvision(t, fetchALBServerByIP(t, configs.E2E.ALB.Node2))

	server := toc.NewMockServerAdapter(gomock.NewController(t))
	server.EXPECT().HardwareV2(gomock.Eq(configs.E2E.ALB.Node2)).Return(nil, fmt.Errorf("record not found")).AnyTimes()

	prov.server = server

	tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
	tocex.EXPECT().RunTask(gomock.Eq(tpl.ListInterfaceScript)).Return(`lo`, nil).AnyTimes()
	prov.tocex = tocex

	templates, err := prov.Templates()
	assert.NoError(t, err)
	assert.Greater(t, len(templates), 0)
}

func TestNodeProvision_HATemplates(t *testing.T) {
	t.Parallel()

	svr := fetchALBServerByIP(t, configs.E2E.ALB.Node2)

	// mock server, return nonstd-3
	mockSvrV3 := tocvo.NewMockIServerV3(gomock.NewController(t))
	mockSvrV3.EXPECT().HAMode(gomock.Any()).Return(consts.HAMNonStd3, nil).AnyTimes()
	svr.ServerV3 = mockSvrV3

	prov := fetchNodeProvision(t, svr)
	prov.AZInfo = &tocvo.AZ{AZType: consts.AZTypePrivate}

	t.Run("basic", func(t *testing.T) {
		t.Parallel()

		templates, err := prov.HATemplates()
		assert.NoError(t, err)

		// Find Bird Service Unit template
		var birdTemplate *toclib.ProvisionNodeTemplate
		var matchingTemplate toclib.ProvisionNodeTemplate
		found := false

		for _, tp := range templates {
			if tp.Path == tpl.BirdServiceUnitTemplate.Path {
				matchingTemplate = tp
				found = true

				break
			}
		}

		if found {
			birdTemplate = &matchingTemplate
		}

		assert.NotNil(t, birdTemplate, "Bird Service Unit template should exist")
		if birdTemplate != nil {
			assert.True(t, birdTemplate.AutoCreate)
			assert.Equal(t, tpl.BirdServiceUnitContent, birdTemplate.Content)
			assert.Equal(t, uint16(consts.FilePermissionRWRR), birdTemplate.Permission)
			assert.Equal(t, tpl.BirdServiceUnitPostCommand, birdTemplate.PostCommand)
		}
	})

	t.Run("all_templates", func(t *testing.T) {
		t.Parallel()

		templates, err := prov.HATemplates()
		assert.NoError(t, err)

		// Verify all expected templates are present
		expectedPaths := []string{
			tpl.BirdServiceUnitTemplate.Path,
			tpl.BirdConfTemplate.Path,
		}

		actualPaths := make([]string, len(templates))
		for i, template := range templates {
			actualPaths[i] = template.Path
		}

		for _, expectedPath := range expectedPaths {
			assert.Contains(t, actualPaths, expectedPath,
				"Template with path %s should exist", expectedPath)
		}
	})
}

func TestNodeProvision_haPrimaryTemplates(t *testing.T) {
	fakeit := gofakeit.New(time.Now().Unix())
	fakeIP := fakeit.IPv4Address()

	tests := []struct {
		name               string
		nodeProvision      *NodeProvision
		contentContains    []string // Strings that should be in the content
		contentNotContains []string // Strings that should NOT be in the content
		wantErr            bool
		wantErrMsg         string
	}{
		{
			name: "success case",
			nodeProvision: &NodeProvision{
				NodeServer: &tocvo.ALBNodeServer{
					Tags: tocvo.Tags{
						{
							TagCategoryKey: "function",
							Value:          "test-function",
						},
					},
					Agent: &toclib.AgentInfo{
						IP: fakeIP,
					},
				},
				Agent: &toclib.AgentInfo{
					IP: fakeIP,
				},
				Meta: &sgwvo.ALBClusterConfigMeta{
					L7ClusterConfig: sgwvo.L7ClusterConfig{
						HAInstances: []*sgwvo.HAInstance{
							{
								Node: fakeIP,
								Instances: []*sgwvo.KeepalivedInstance{
									{
										Name:            "instance1",
										Interface:       "eth0",
										State:           "MASTER",
										VirtualRouterID: 101,
										Priority:        150,
										VIP:             "*************",
										WithoutAuth:     false,
									},
								},
							},
						},
					},
				},
			},
			contentContains: []string{
				"instance1",
				"MASTER",        // State should be in the content
				"*************", // VIP should be in the content
			},
			contentNotContains: []string{
				"BACKUP", // Should not contain BACKUP state
			},
			wantErr: false,
		},
		{
			name: "without auth",
			nodeProvision: &NodeProvision{
				NodeServer: &tocvo.ALBNodeServer{
					Tags: tocvo.Tags{
						{
							TagCategoryKey: "function",
							Value:          "test-function",
						},
					},
				},
				Agent: &toclib.AgentInfo{
					IP: fakeIP,
				},
				Meta: &sgwvo.ALBClusterConfigMeta{
					L7ClusterConfig: sgwvo.L7ClusterConfig{
						HAInstances: []*sgwvo.HAInstance{
							{
								Node: fakeIP,
								Instances: []*sgwvo.KeepalivedInstance{
									{
										Name:            "instance1",
										Interface:       "eth0",
										State:           "MASTER",
										VirtualRouterID: 101,
										Priority:        150,
										VIP:             "*************",
										WithoutAuth:     true,
									},
								},
							},
						},
					},
				},
			},
			contentContains: []string{
				"instance1",
				"MASTER",        // State should be in the content
				"*************", // VIP should be in the content
			},
			contentNotContains: []string{
				"BACKUP",         // Should not contain BACKUP state
				"authentication", // Should not contain authentication block
			},
			wantErr: false,
		},
		{
			name: "no HA instances",
			nodeProvision: &NodeProvision{
				NodeServer: &tocvo.ALBNodeServer{
					Tags: tocvo.Tags{
						{
							TagCategoryKey: "function",
							Value:          "test-function-no-instance",
						},
					},
				},
				Meta: &sgwvo.ALBClusterConfigMeta{
					L7ClusterConfig: sgwvo.L7ClusterConfig{
						HAInstances: []*sgwvo.HAInstance{
							{
								Node:      fakeIP,
								Instances: []*sgwvo.KeepalivedInstance{},
							},
						},
					},
				},
				Agent: &toclib.AgentInfo{
					IP: fakeit.IPv4Address(),
				},
			},
			contentContains:    []string{},
			contentNotContains: nil,
			wantErr:            false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			templates, err := tt.nodeProvision.haPrimaryTemplates()

			// check if content is nil
			if tt.contentContains == nil {
				if len(templates) > 0 {
					assert.Empty(t, templates[0].Content, "Template content should be empty")
				}
			}

			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.wantErrMsg)
			} else {
				require.NoError(t, err)
				require.Len(t, templates, 2, "Expected exactly two templates")

				// Check template properties
				assert.True(t, templates[0].AutoCreate)
				assert.Equal(t, tpl.KeepalivedConfTemplate.Path, templates[0].Path)

				// Check content contains expected strings
				for _, expectedContent := range tt.contentContains {
					assert.Contains(t, templates[0].Content, expectedContent,
						"Template content should contain '%s'", expectedContent)
				}

				// Check content does NOT contain specified strings
				for _, unexpectedContent := range tt.contentNotContains {
					assert.NotContains(t, templates[0].Content, unexpectedContent,
						"Template content should NOT contain '%s'", unexpectedContent)
				}
			}
		})
	}
}
