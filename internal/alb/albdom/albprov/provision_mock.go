// Code generated by MockGen. DO NOT EDIT.
// Source: provision.go

// Package albprov is a generated GoMock package.
package albprov

import (
	reflect "reflect"

	albvo "git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	consts "git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	tocvo "git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	toclib "git.garena.com/shopee/go-shopeelib/toclib"
	gomock "github.com/golang/mock/gomock"
)

// MockProvision is a mock of Provision interface.
type MockProvision struct {
	ctrl     *gomock.Controller
	recorder *MockProvisionMockRecorder
}

// MockProvisionMockRecorder is the mock recorder for MockProvision.
type MockProvisionMockRecorder struct {
	mock *MockProvision
}

// NewMockProvision creates a new mock instance.
func NewMockProvision(ctrl *gomock.Controller) *MockProvision {
	mock := &MockProvision{ctrl: ctrl}
	mock.recorder = &MockProvisionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProvision) EXPECT() *MockProvisionMockRecorder {
	return m.recorder
}

// Component mocks base method.
func (m *MockProvision) Component(name, version string) (*toclib.ProvisionNodeComponent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Component", name, version)
	ret0, _ := ret[0].(*toclib.ProvisionNodeComponent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Component indicates an expected call of Component.
func (mr *MockProvisionMockRecorder) Component(name, version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Component", reflect.TypeOf((*MockProvision)(nil).Component), name, version)
}

// Components mocks base method.
func (m *MockProvision) Components(version *albvo.NodeComponentVersion) ([]toclib.ProvisionNodeComponent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Components", version)
	ret0, _ := ret[0].([]toclib.ProvisionNodeComponent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Components indicates an expected call of Components.
func (mr *MockProvisionMockRecorder) Components(version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Components", reflect.TypeOf((*MockProvision)(nil).Components), version)
}

// DeletedComponent mocks base method.
func (m *MockProvision) DeletedComponent(name, version string) (*tocvo.NodeProvision, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletedComponent", name, version)
	ret0, _ := ret[0].(*tocvo.NodeProvision)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletedComponent indicates an expected call of DeletedComponent.
func (mr *MockProvisionMockRecorder) DeletedComponent(name, version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletedComponent", reflect.TypeOf((*MockProvision)(nil).DeletedComponent), name, version)
}

// HAMode mocks base method.
func (m *MockProvision) HAMode() consts.HighAvailableMode {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HAMode")
	ret0, _ := ret[0].(consts.HighAvailableMode)
	return ret0
}

// HAMode indicates an expected call of HAMode.
func (mr *MockProvisionMockRecorder) HAMode() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HAMode", reflect.TypeOf((*MockProvision)(nil).HAMode))
}

// NodeConfig mocks base method.
func (m *MockProvision) NodeConfig(typ string) (*toclib.ProvisionNodeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeConfig", typ)
	ret0, _ := ret[0].(*toclib.ProvisionNodeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NodeConfig indicates an expected call of NodeConfig.
func (mr *MockProvisionMockRecorder) NodeConfig(typ interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeConfig", reflect.TypeOf((*MockProvision)(nil).NodeConfig), typ)
}

// NodeConfigWithComponents mocks base method.
func (m *MockProvision) NodeConfigWithComponents(components []string) (*toclib.ProvisionNodeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeConfigWithComponents", components)
	ret0, _ := ret[0].(*toclib.ProvisionNodeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NodeConfigWithComponents indicates an expected call of NodeConfigWithComponents.
func (mr *MockProvisionMockRecorder) NodeConfigWithComponents(components interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeConfigWithComponents", reflect.TypeOf((*MockProvision)(nil).NodeConfigWithComponents), components)
}

// NodeConfigWithTemplates mocks base method.
func (m *MockProvision) NodeConfigWithTemplates(templates []string) (*toclib.ProvisionNodeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeConfigWithTemplates", templates)
	ret0, _ := ret[0].(*toclib.ProvisionNodeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NodeConfigWithTemplates indicates an expected call of NodeConfigWithTemplates.
func (mr *MockProvisionMockRecorder) NodeConfigWithTemplates(templates interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeConfigWithTemplates", reflect.TypeOf((*MockProvision)(nil).NodeConfigWithTemplates), templates)
}

// NodeReversedConfig mocks base method.
func (m *MockProvision) NodeReversedConfig(typ string) (*toclib.ProvisionNodeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeReversedConfig", typ)
	ret0, _ := ret[0].(*toclib.ProvisionNodeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NodeReversedConfig indicates an expected call of NodeReversedConfig.
func (mr *MockProvisionMockRecorder) NodeReversedConfig(typ interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeReversedConfig", reflect.TypeOf((*MockProvision)(nil).NodeReversedConfig), typ)
}

// Templates mocks base method.
func (m *MockProvision) Templates() ([]toclib.ProvisionNodeTemplate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Templates")
	ret0, _ := ret[0].([]toclib.ProvisionNodeTemplate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Templates indicates an expected call of Templates.
func (mr *MockProvisionMockRecorder) Templates() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Templates", reflect.TypeOf((*MockProvision)(nil).Templates))
}

// UpgradedComponent mocks base method.
func (m *MockProvision) UpgradedComponent(name, version string) (*tocvo.NodeProvision, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpgradedComponent", name, version)
	ret0, _ := ret[0].(*tocvo.NodeProvision)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpgradedComponent indicates an expected call of UpgradedComponent.
func (mr *MockProvisionMockRecorder) UpgradedComponent(name, version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpgradedComponent", reflect.TypeOf((*MockProvision)(nil).UpgradedComponent), name, version)
}
