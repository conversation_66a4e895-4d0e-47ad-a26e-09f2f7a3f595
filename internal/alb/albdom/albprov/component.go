package albprov

import (
	"fmt"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
)

func (n *NodeProvision) dependentComponents(comp cmp.Component) []string {
	return append(comp.Depends(), comp.DependImages(configs.Mgmt.HarborDomain(n.Meta.Env), n.Meta.Env)...)
}

func (n *NodeProvision) dependentTemplatePaths(comp cmp.Component) []string {
	paths := tpl.TemplatePaths(comp.DependentTemplates)
	if n.AZInfo.IsPrivate() {
		paths = append(paths, tpl.TemplatePaths(comp.DependentPrivateTemplates)...)
	}

	if !strings.EqualFold(n.Agent.Env, consts.EnvLive) {
		paths = append(paths, tpl.TemplatePaths(comp.DependentNonLiveTemplates)...)
	}

	return paths
}

// NginxComponent for Nginx
func (n *NodeProvision) NginxComponent(version string) *toclib.ProvisionNodeComponent {
	return &toclib.ProvisionNodeComponent{
		IsService:           true,
		StopServiceOnDelete: true,
		Name:                cmp.NginxComponent.Name,
		ServiceName:         cmp.NginxComponent.Service,
		Type:                cmp.NginxComponent.Type,
		Version:             version,
		DependentTemplates:  n.dependentTemplatePaths(cmp.NginxComponent),
		DependentComponents: n.dependentComponents(cmp.NginxComponent),
		PreCommand:          tpl.NginxPreInstallScript,
		PostCommand:         "systemctl daemon-reload &&systemctl enable nginx && systemctl restart nginx",
		HealthycheckCommand: "nginx -t 2>&1 && kill -s 0 $(cat /var/run/nginx.pid)",
	}
}

// NginxUpgradedComponent for Nginx upgraded
func (n *NodeProvision) NginxUpgradedComponent(version string) (*toclib.ProvisionNodeComponent, error) {
	nginxUpgradePreScript, err := tpl.NginxUpgradePreScript(version)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_nginx_upgrade_pre_script_failed")
	}

	nginxUpgradeScript := tpl.NginxUpgrade()

	return &toclib.ProvisionNodeComponent{
		Name:                cmp.UpgradeNginxComponent.Name,
		Type:                cmp.UpgradeNginxComponent.Type,
		Version:             cast.ToString(time.Now().Unix()),
		PreCommand:          nginxUpgradePreScript,
		Script:              nginxUpgradeScript,
		HealthycheckCommand: tpl.PostCheckNginxVersionScript,
	}, nil
}

// ScertmsComponent for SCERTMS
func (n *NodeProvision) ScertmsComponent(version string) *toclib.ProvisionNodeComponent {
	return &toclib.ProvisionNodeComponent{
		IsService:               true,
		StopServiceOnDelete:     true,
		RemovePackageOnDisabled: true,
		Type:                    cmp.ScertmsComponent.Type,
		Name:                    cmp.ScertmsComponent.Name,
		ServiceName:             cmp.ScertmsComponent.Service,
		Version:                 version,
		DependentTemplates:      n.dependentTemplatePaths(cmp.ScertmsComponent),
		DependentComponents:     n.dependentComponents(cmp.ScertmsComponent),
		RemovePathsOnDelete:     append(n.dependentTemplatePaths(cmp.ScertmsComponent), tpl.S3ConfigsTemplate2.Path),
		HealthycheckCommand:     tpl.ScertmsHealthyScript,
		PostCommand:             "install_scertmsd",
	}
}

const (
	cpusetStartIndex = 8
	cpusetEndIndex   = 3
)

func (n *NodeProvision) cpus() string {
	var cpus string
	switch n.Agent.ResourceType {
	case consts.VMServer:
		cpus = fmt.Sprintf("%d-%d", 0, n.Agent.CPUCount-1)
	default:
		cpus = fmt.Sprintf("%d-%d", n.Agent.CPUCount-cpusetEndIndex-cpusetStartIndex, n.Agent.CPUCount-cpusetEndIndex)
	}

	return cpus
}

// ALBWAFComponent for ALB WAF
func (n *NodeProvision) ALBWAFComponent(version string) (*toclib.ProvisionNodeComponent, error) {
	env := n.Meta.Env
	serviceArgs := []string{
		"run",
		fmt.Sprintf("--env=%s", env),
		"--cid=sg",
		fmt.Sprintf("--idc=%s", n.Meta.RZ),
	}

	image := cmp.ALBWAFComponent.Artifact(configs.Mgmt.HarborDomain(env), env)
	preCommand, err := tpl.ALBWAFPreCommand(&tpl.DockerImage{
		Image:   image,
		Tag:     version,
		Service: cmp.ALBWAFComponent.Service,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "pre_command_failed")
	}

	com := &toclib.ProvisionNodeComponent{
		IsService:           true,
		StopServiceOnDelete: true,
		Name:                image,
		Type:                cmp.ALBWAFComponent.Type,
		Version:             version,
		ServiceName:         cmp.ALBWAFComponent.Service,
		DependentTemplates:  n.dependentTemplatePaths(cmp.ALBWAFComponent),
		DependentComponents: n.dependentComponents(cmp.ALBWAFComponent),
		PreCommand:          preCommand,
		HealthycheckCommand: tpl.ALBWAFHealthyScript,
		ServiceOptions: []string{
			"--restart=always",
			"--name=alb-waf",
			"--net=host",
			fmt.Sprintf("--cpuset-cpus=%s", n.cpus()),
			"--cap-add=SYS_PTRACE",
			"--entrypoint=smb",
			"--security-opt=seccomp:unconfined",
			"--env=PORT=8081",
			fmt.Sprintf("--env=SERVICE_NAME=alb-waf-%s-sg", env),
			fmt.Sprintf("--env=MESOS_TASK_ID=alb-waf-%s-sg.1", env),
			"--env=MESOS_CONTAINER_NAME=alb-waf",
			"--env-file=/etc/sgw/env",
			"--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
			"--volume=/etc/hosts:/etc/hosts:ro",
			"--volume=/etc/mesos:/etc/mesos:ro",
			"--volume=/etc/nginx/service_deps:/etc/nginx/service_deps",
			"--volume=/etc/mesos-lb:/etc/mesos-lb",
			fmt.Sprintf("--volume=/data/log/alb-waf-%s-sg:/data/log/alb-waf-%s-sg", env, env),
			fmt.Sprintf("--volume=/data/tmp/alb-waf-%s-sg:/data/tmp", env),
		},
		ServiceArgs: serviceArgs,
	}

	return com, nil
}

// NginxLBComponent for alb nginx lb
func (n *NodeProvision) NginxLBComponent(version string) (*toclib.ProvisionNodeComponent, error) {
	env := n.Meta.Env
	serviceArgs := []string{
		"run",
		fmt.Sprintf("--env=%s", env),
		"--cid=sg",
		fmt.Sprintf("--idc=%s", n.Meta.RZ),
	}

	image := cmp.MesosNginxLBComponent.Artifact(configs.Mgmt.HarborDomain(env), env)
	preCommand, err := tpl.NginxLBPreCommand(&tpl.DockerImage{
		Image:   image,
		Tag:     version,
		Service: cmp.MesosNginxLBComponent.Service,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "pre_command_failed")
	}

	com := &toclib.ProvisionNodeComponent{
		IsService:           true,
		StopServiceOnDelete: true,
		Name:                image,
		Type:                cmp.MesosNginxLBComponent.Type,
		Version:             version,
		ServiceName:         cmp.MesosNginxLBComponent.Service,
		DependentTemplates:  n.dependentTemplatePaths(cmp.MesosNginxLBComponent),
		DependentComponents: n.dependentComponents(cmp.MesosNginxLBComponent),
		PreCommand:          preCommand,
		PostCommand:         "",
		// healthy check command error: Error Opening file /etc/nginx/geoip/GeoIPISP.dat
		// nc: connect to localhost port 80 (tcp) failed: Cannot assign requested address
		HealthycheckCommand: tpl.NginxLBHealthyScript,
		ServiceOptions: []string{
			"--restart=always",
			"--privileged",
			"--name=mesos-nginx-lb",
			"--entrypoint=smb",
			"--pid=host",
			"--net=host",
			"--volume=/usr/sbin/nginx:/usr/sbin/nginx",
			"--volume=/var/run:/var/run",
			"--volume=/usr/share/openresty:/usr/share/openresty",
			"--volume=/etc/mesos-lb:/etc/mesos-lb",
			"--volume=/etc/mesos:/etc/mesos",
			"--volume=/etc/nginx:/etc/nginx",
			"--volume=/etc/lualib:/etc/lualib",
			"--volume=/etc/sgw:/etc/sgw",
			"--volume=/etc/shopee-lb:/etc/shopee-lb",
			"--volume=/etc/shopee-alb:/etc/shopee-alb",
			"--volume=/var/log/nginx:/var/log/nginx",
			"--volume=/data/nginx:/data/nginx",
			"--volume=/data/shopee:/data/shopee",
			"--volume=/var/lib:/var/lib",
			"--volume=/usr/local/ssl/lib:/usr/local/ssl/lib",
			"--volume=/usr/local/lib:/usr/local/lib",
			"--volume=/usr/local/lib64:/usr/local/lib64",
			"--volume=/var/www:/var/www",
			fmt.Sprintf("--env=SERVICE_NAME=nginx-config-%s-sg", env),
			fmt.Sprintf("--env=MESOS_TASK_ID=nginx-config-%s-sg.1", env),
			"--env=MESOS_CONTAINER_NAME=nginx-config",
			"--env=DEPLOYMENT=new",
			"--env-file=/etc/sgw/env",
			fmt.Sprintf("--volume=/data/log/nginx-config-%s-sg:/data/log/nginx-config-%s-sg", env, env),
			fmt.Sprintf("--volume=/data/tmp/nginx-config-%s-sg:/data/tmp", env),
		},
		ServiceArgs: serviceArgs,
	}

	return com, nil
}

// ALBSDComponent for ALB SD
func (n *NodeProvision) ALBSDComponent(version string) (*toclib.ProvisionNodeComponent, error) {
	env := n.Meta.Env
	serviceArgs := []string{
		"run",
		fmt.Sprintf("--env=%s", env),
		"--cid=sg",
		fmt.Sprintf("--idc=%s", n.Meta.RZ),
	}

	image := cmp.ALBSDComponent.Artifact(configs.Mgmt.HarborDomain(env), env)
	preCommand, err := tpl.ALBSDPreCommand(&tpl.DockerImage{
		Image:   image,
		Tag:     version,
		Service: cmp.ALBSDComponent.Service,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "pre_command_failed")
	}

	com := &toclib.ProvisionNodeComponent{
		IsService:           true,
		StopServiceOnDelete: true,
		Name:                image,
		Type:                cmp.ALBSDComponent.Type,
		Version:             version,
		ServiceName:         cmp.ALBSDComponent.Service,
		DependentTemplates:  n.dependentTemplatePaths(cmp.ALBSDComponent),
		DependentComponents: n.dependentComponents(cmp.ALBSDComponent),
		PreCommand:          preCommand,
		HealthycheckCommand: tpl.ALBSDHealthyScript,
		ServiceOptions: []string{
			"--restart=always",
			"--name=alb-sd",
			"--net=host",
			fmt.Sprintf("--cpuset-cpus=%s", n.cpus()),
			"--memory=6000M",
			"--cap-add=SYS_PTRACE",
			"--entrypoint=smb",
			"--security-opt=seccomp:unconfined",
			"--env=PORT=9118",
			fmt.Sprintf("--env=SERVICE_NAME=alb-sd-%s-sg", env),
			fmt.Sprintf("--env=MESOS_TASK_ID=alb-sd-%s-sg.1", env),
			"--env=MESOS_CONTAINER_NAME=alb-sd",
			"--env=NAMING_APPID=namingbva9xt4xw8",
			"--env-file=/etc/sgw/env",
			"--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
			"--volume=/etc/hosts:/etc/hosts:ro",
			"--volume=/etc/mesos:/etc/mesos:ro",
			"--volume=/etc/sgw:/etc/sgw",
			"--volume=/etc/nginx/service_deps:/etc/nginx/service_deps",
			"--volume=/etc/mesos-lb:/etc/mesos-lb",
			fmt.Sprintf("--volume=/data/log/alb-sd-%s-sg:/data/log/alb-sd-%s-sg", env, env),
			fmt.Sprintf("--volume=/data/tmp/alb-sd-%s-sg:/data/tmp", env),
			"--volume=/etc/shopee-alb/dynamic_configs/configcenter/alb-sd:/etc/shopee-alb/dynamic_configs/configcenter/alb-sd",
		},
		ServiceArgs: serviceArgs,
	}

	return com, nil
}

// SGWAgentComponent for sgw agent
func (n *NodeProvision) SGWAgentComponent(version string) (*toclib.ProvisionNodeComponent, error) {
	env := n.Meta.Env

	var memory string
	if consts.IsNonLiveEnv(env) {
		memory = "12G"
	} else {
		memory = "6G"
	}

	serviceArgs := []string{
		"run",
		fmt.Sprintf("--env=%s", env),
		"--cid=sg",
		fmt.Sprintf("--idc=%s", n.Meta.RZ),
	}

	image := cmp.SGWAgentComponent.Artifact(configs.Mgmt.HarborDomain(env), env)
	preCommand, err := tpl.SGWAgentPreCommand(&tpl.DockerImage{
		Image:   image,
		Tag:     version,
		Service: cmp.SGWAgentComponent.Service,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "pre_command_failed")
	}

	com := &toclib.ProvisionNodeComponent{
		IsService:           true,
		StopServiceOnDelete: true,
		Name:                image,
		Type:                cmp.SGWAgentComponent.Type,
		Version:             version,
		ServiceName:         cmp.SGWAgentComponent.Service,
		DependentTemplates:  n.dependentTemplatePaths(cmp.SGWAgentComponent),
		DependentComponents: n.dependentComponents(cmp.SGWAgentComponent),
		PreCommand:          preCommand,
		HealthycheckCommand: tpl.SGWAgentHealthyScript,
		ServiceOptions: []string{
			"--restart=always",
			"--name=sgw-agent",
			"--privileged",
			"--pid=host",
			"--net=host",
			fmt.Sprintf("--cpuset-cpus=%s", n.cpus()),
			fmt.Sprintf("--memory=%s", memory),
			"--entrypoint=smb",
			"--env=PORT=9119",
			fmt.Sprintf("--env=SERVICE_NAME=sgw-agent-%s-sg", env),
			fmt.Sprintf("--env=MESOS_TASK_ID=sgw-agent-%s-sg.1", env),
			"--env-file=/etc/sgw/env",
			"--env=MESOS_CONTAINER_NAME=sgw-agent",
			"--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
			"--volume=/etc/mesos-lb:/etc/mesos-lb:ro",
			"--volume=/etc/hosts:/etc/hosts:ro",
			"--volume=/etc/mesos:/etc/mesos:ro",
			"--volume=/usr/share/openresty:/usr/share/openresty:ro",
			"--volume=/usr/sbin/nginx:/usr/sbin/nginx",
			"--volume=/var/run:/var/run",
			"--volume=/etc/nginx:/etc/nginx",
			"--volume=/etc/lualib:/etc/lualib",
			"--volume=/etc/sgw:/etc/sgw",
			"--volume=/etc/shopee-lb:/etc/shopee-lb",
			"--volume=/etc/shopee-alb:/etc/shopee-alb",
			"--volume=/etc/sgw-agent:/etc/sgw-agent",
			"--volume=/var/log/nginx:/var/log/nginx",
			"--volume=/data/nginx:/data/nginx",
			"--volume=/data/shopee:/data/shopee",
			"--volume=/var/lib:/var/lib",
			"--volume=/usr/local/ssl/lib:/usr/local/ssl/lib",
			"--volume=/usr/local/lib:/usr/local/lib",
			"--volume=/usr/local/lib64:/usr/local/lib64",
			fmt.Sprintf("--volume=/data/log/sgw-agent-%s-sg:/data/log/sgw-agent-%s-sg", env, env),
			fmt.Sprintf("--volume=/data/tmp/sgw-agent-%s-sg:/data/tmp", env),
		},
		ServiceArgs: serviceArgs,
	}

	return com, nil
}

// ALBMetricsComponent for ALB metrics
func (n *NodeProvision) ALBMetricsComponent(version string) (*toclib.ProvisionNodeComponent, error) {
	env := n.Meta.Env
	serviceArgs := []string{
		"run",
		fmt.Sprintf("--env=%s", env),
		"--cid=sg",
		fmt.Sprintf("--idc=%s", n.Meta.RZ),
	}

	image := cmp.ALBMetricsComponent.Artifact(configs.Mgmt.HarborDomain(env), env)
	preCommand, err := tpl.ALBMetricsPreCommand(&tpl.DockerImage{
		Image:   image,
		Tag:     version,
		Service: cmp.ALBMetricsComponent.Service,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "pre_command_failed")
	}

	com := &toclib.ProvisionNodeComponent{
		IsService:           true,
		StopServiceOnDelete: true,
		Name:                image,
		Type:                cmp.ALBMetricsComponent.Type,
		Version:             version,
		ServiceName:         cmp.ALBMetricsComponent.Service,
		DependentTemplates:  n.dependentTemplatePaths(cmp.ALBMetricsComponent),
		DependentComponents: n.dependentComponents(cmp.ALBMetricsComponent),
		PreCommand:          preCommand,
		HealthycheckCommand: tpl.ALBMetricsHealthyScript,
		ServiceOptions: []string{
			"--restart=always",
			"--name=alb-metrics",
			"--pid=host",
			"--net=host",
			"--cpus=2",
			"--memory=6000M",
			"--cap-add=SYS_PTRACE",
			"--entrypoint=smb",
			"--security-opt=seccomp:unconfined",
			"--env=PORT=9116",
			fmt.Sprintf("--env=SERVICE_NAME=alb-metrics-%s-sg", env),
			fmt.Sprintf("--env=MESOS_TASK_ID=alb-metrics-%s-sg.1", env),
			"--env=MESOS_CONTAINER_NAME=alb-metrics",
			"--env-file=/etc/sgw/env",
			"--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
			"--volume=/etc/hosts:/etc/hosts:ro",
			"--volume=/etc/mesos:/etc/mesos:ro",
			"--volume=/etc/nginx/service_deps:/etc/nginx/service_deps",
			"--volume=/etc/mesos-lb:/etc/mesos-lb",
			"--volume=/etc/sgw:/etc/sgw",
			"--volume=/etc/nginx:/etc/nginx",
			fmt.Sprintf("--volume=/data/log/alb-metrics-%s-sg:/data/log/alb-metrics-%s-sg", env, env),
			fmt.Sprintf("--volume=/data/tmp/alb-metrics-%s-sg:/data/tmp", env),
		},
		ServiceArgs: serviceArgs,
	}

	return com, nil
}

// UpgradedComponent returns the upgraded component
func (n *NodeProvision) UpgradedComponent(name, version string) (*tocvo.NodeProvision, error) {
	var component *toclib.ProvisionNodeComponent
	var err error

	switch name {
	case cmp.ALBMetricsComponent.Name:
		component, err = n.ALBMetricsComponent(version)
	case cmp.ALBWAFComponent.Name:
		component, err = n.ALBWAFComponent(version)
	case cmp.ALBSDComponent.Name:
		component, err = n.ALBSDComponent(version)
	case cmp.MesosNginxLBComponent.Name:
		component, err = n.NginxLBComponent(version)
	case cmp.NginxComponent.Name:
		component, err = n.NginxUpgradedComponent(version)
	case cmp.SGWAgentComponent.Name:
		component, err = n.SGWAgentComponent(version)
	case cmp.ScertmsComponent.Name:
		component = n.ScertmsComponent(version)
	}

	if err != nil {
		return nil, errors.WithMessage(err, "fetch_component_failed")
	}

	if component == nil {
		return nil, fmt.Errorf("unsupported_component_%s", name)
	}

	// remove dependent_components bcz no related provision config on legacy nodes
	component.DependentComponents = nil

	templates, err := n.Templates()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_templates_failed")
	}

	tempMap := convertor.ToMap(templates,
		func(temp toclib.ProvisionNodeTemplate) (string, toclib.ProvisionNodeTemplate) {
			return temp.Path, temp
		})

	var temps []toclib.ProvisionNodeTemplate
	for _, t := range component.DependentTemplates {
		if temp, ok := tempMap[t]; ok {
			temps = append(temps, temp)
		}
	}

	com := &tocvo.NodeProvision{
		Component: component,
		Templates: temps,
	}

	return com, nil
}

// DeletedComponent returns the deleted component
func (n *NodeProvision) DeletedComponent(name, version string) (*tocvo.NodeProvision, error) {
	var component *toclib.ProvisionNodeComponent
	var err error

	switch name {
	case cmp.ScertmsComponent.Name:
		component = n.ScertmsComponent(version)

	case cmp.ALBWAFComponent.Name:
		component, err = n.ALBWAFComponent(version)
	}

	if err != nil {
		return nil, errors.WithMessage(err, "fetch_component_failed")
	}

	if component == nil {
		return nil, fmt.Errorf("unsupported_component_%s", name)
	}

	// remove dependent_components bcz no related provision config on legacy nodes
	component.DependentComponents = nil
	component.Disabled = true
	component.EnsureServiceInactive = true
	component.DisableRestartService = true

	templates, err := n.Templates()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_templates_failed")
	}
	templates = append(templates, n.legacyTemplates()...)

	tempMap := convertor.ToMap(templates,
		func(temp toclib.ProvisionNodeTemplate) (string, toclib.ProvisionNodeTemplate) {
			return temp.Path, temp
		})

	var temps []toclib.ProvisionNodeTemplate
	for _, t := range component.DependentTemplates {
		if temp, ok := tempMap[t]; ok {
			temps = append(temps, temp)
		}
	}

	com := &tocvo.NodeProvision{
		Component: component,
		Templates: temps,
	}

	return com, nil
}

func (n *NodeProvision) dockerComponent() toclib.ProvisionNodeComponent {
	return toclib.ProvisionNodeComponent{
		IsService:           true,
		StopServiceOnDelete: false, // cannot stop, otherwise containers cannot stop and delete by tocex
		Name:                cmp.DockerComponent.Name,
		Type:                cmp.DockerComponent.Type,
		ServiceName:         cmp.DockerComponent.Service,
		DependentComponents: n.dependentComponents(cmp.DockerComponent),
		DependentTemplates:  n.dependentTemplatePaths(cmp.DockerComponent),
		PostCommand:         fmt.Sprintf("systemctl daemon-reload && systemctl restart docker && nc -zvw 1 %s 9323", n.Agent.IP),
		HealthycheckCommand: fmt.Sprintf("docker login https://%s", configs.Mgmt.HarborDomain(n.Meta.Env)),
	}
}

// CmdsExporterComponent returns cmds exporter component
func (n *NodeProvision) CmdsExporterComponent() toclib.ProvisionNodeComponent {
	return toclib.ProvisionNodeComponent{
		IsService:           true,
		StopServiceOnDelete: true,
		Name:                cmp.CmdsExporterComponent.Name,
		Type:                cmp.CmdsExporterComponent.Type,
		ServiceName:         cmp.CmdsExporterComponent.Service,
		Version:             cmp.CmdsExporterComponent.Version,
		DependentTemplates:  n.dependentTemplatePaths(cmp.CmdsExporterComponent),
		DependentComponents: n.dependentComponents(cmp.CmdsExporterComponent),
		HealthycheckCommand: tpl.CmdsExporterHealthCheckScript,
		PostCommand:         tpl.CmdsExporterPostScript,
	}
}

// BaseComponents returns base components
func (n *NodeProvision) BaseComponents() ([]toclib.ProvisionNodeComponent, error) {
	var gitConfigScript string
	var err error

	ext2clusters, err := n.ext2cluster.DumpByRZ(n.Meta.RZ)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_git_config_proxy_failed")
	}

	if ext2clusters.IsHTTPTunnelSupported() {
		gitConfigScript, err = tpl.GitConfigProxy(&tpl.HTTPTunnel{
			HTTPProxy:  ext2clusters.HTTPProxy(),
			HTTPSProxy: ext2clusters.HTTPSProxy(),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_git_config_proxy_failed")
		}
	}

	components := []toclib.ProvisionNodeComponent{
		{
			IsService:           true,
			StopServiceOnDelete: false,
			Name:                cmp.LxcfsComponent.Name,
			ServiceName:         cmp.LxcfsComponent.Service,
			Type:                cmp.LxcfsComponent.Type,
			PostCommand:         "systemctl daemon-reload",
			HealthycheckCommand: "lxcfs -v",
		},
		{
			Name:                cmp.ZooKeeperCli.Name,
			Type:                cmp.ZooKeeperCli.Type,
			HealthycheckCommand: "command -v zookeepercli",
		},
		{
			Name:    cmp.ETCDClient.Name,
			Type:    cmp.ETCDClient.Type,
			Version: cast.ToString(time.Now().Unix()),
			Script: tpl.EtcdctlInstallScript(&tpl.EtcdServiceData{
				Version:    configs.ALB.Version.Etcdctl,
				HTTPProxy:  ext2clusters.HTTPProxy(),
				HTTPSProxy: ext2clusters.HTTPSProxy(),
			}),
			PostCommand:         tpl.EtcdctlPostCheckScript,
			HealthycheckCommand: "command -v etcdctl",
		},
		{
			Name:                cmp.GeoIPBinComponent.Name,
			Type:                cmp.GeoIPBinComponent.Type,
			HealthycheckCommand: "geoiplookup -v 127.0.0.1",
		},
		{
			Name:                cmp.InstallUOAComponent.Name,
			Type:                cmp.InstallUOAComponent.Type,
			Version:             cast.ToString(time.Now().Unix()),
			PreCommand:          tpl.UOAPreInstallScript,
			Script:              tpl.UOAInstallScript,
			DependentComponents: n.dependentComponents(cmp.InstallUOAComponent),
			DependentTemplates:  n.dependentTemplatePaths(cmp.InstallUOAComponent),
			PostCommand:         tpl.UOAPostInstallScript,
		},
		{
			Name:                cmp.ModprobeBRFilterComponent.Name,
			Type:                cmp.ModprobeBRFilterComponent.Type,
			Version:             cast.ToString(time.Now().Unix()),
			Script:              tpl.BRFilterScript,
			HealthycheckCommand: tpl.BRFilterCheckScript,
		},
		{
			Name:                cmp.FreshCertComponent.Name,
			Type:                cmp.FreshCertComponent.Type,
			Version:             cast.ToString(time.Now().Unix()),
			Script:              tpl.FreshCertScript,
			DependentComponents: n.dependentComponents(cmp.FreshCertComponent),
			DependentTemplates:  n.dependentTemplatePaths(cmp.FreshCertComponent),
		},
		{
			Name:                cmp.NginxCompatibilityComponent.Name,
			Type:                cmp.NginxCompatibilityComponent.Type,
			Version:             cast.ToString(time.Now().Unix()),
			Script:              tpl.NginxCompatibilityScript,
			HealthycheckCommand: tpl.NginxCompatibilityHealthyScript,
			DependentComponents: n.dependentComponents(cmp.NginxCompatibilityComponent),
		},
		{
			Name:                cmp.LinuxEthernetComponent.Name,
			Type:                cmp.LinuxEthernetComponent.Type,
			PostCommand:         tpl.OptimizeInterfaceScript,
			HealthycheckCommand: "shopee_linux_ethernet_optimization -h > /dev/null",
		},
		{
			Name:        cmp.ApportComponent.Name,
			Type:        cmp.ApportComponent.Type,
			PostCommand: tpl.ApportEnableScript,
		},
		{
			Name:                cmp.JQComponent.Name,
			Type:                cmp.JQComponent.Type,
			HealthycheckCommand: `jq --version  && echo "{}" | jq`,
		},
		{
			Name:                cmp.NginxLuaEbpfToolkitComponent.Name,
			Type:                cmp.NginxLuaEbpfToolkitComponent.Type,
			HealthycheckCommand: `/usr/local/nginx-lua-ebpf-toolkit/bin/ngx_lj_on_cpu_profile -h > /dev/null`,
		},
		{
			Name:                cmp.PyYAMLComponent.Name,
			Type:                cmp.PyYAMLComponent.Type,
			HealthycheckCommand: `python -c "import yaml"`,
		},
		{
			Name:                cmp.FlameGraphComponent.Name,
			Type:                cmp.FlameGraphComponent.Type,
			PreCommand:          gitConfigScript,
			Script:              tpl.FlameGraphInstallScript,
			HealthycheckCommand: "cd /opt/FlameGraph; ./test.sh",
		},
		{
			Name:                cmp.FreshLibsComponent.Name,
			Type:                cmp.FreshLibsComponent.Type,
			Version:             cast.ToString(time.Now().Unix()),
			Script:              tpl.FreshLibsInstallScript,
			HealthycheckCommand: tpl.FreshLibsHealthCheckScript,
		},
		n.dockerComponent(),
		n.CmdsExporterComponent(),
	}

	return components, nil
}

// CoreComponents return all core components
func (n *NodeProvision) CoreComponents(version *albvo.NodeComponentVersion) ([]toclib.ProvisionNodeComponent, error) {
	nginxLB, err := n.NginxLBComponent(version.NginxLB)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_nginx_lb_failed")
	}
	if !n.IsSupportEtcdProxy() {
		nginxLB.DependentTemplates = slice.Filter(nginxLB.DependentTemplates, func(i int, template string) bool {
			return !strings.EqualFold(template, tpl.SGWEtcdProxyTemplate.Path)
		})
	}

	albSD, err := n.ALBSDComponent(version.ALBSD)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_alb_sd_failed")
	}

	sgwAgent, err := n.SGWAgentComponent(version.SGWAgent)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_sgw_agent_failed")
	}

	albMetrics, err := n.ALBMetricsComponent(version.ALBMetrics)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_alb_metrics_failed")
	}

	coreComponents := []toclib.ProvisionNodeComponent{
		*n.NginxComponent(version.Nginx),
		*nginxLB,
		*albSD,
		*sgwAgent,
		*albMetrics,
	}

	return coreComponents, nil
}

// HAComponents return all HA components
func (n *NodeProvision) HAComponents() ([]toclib.ProvisionNodeComponent, error) {
	// keepalived
	if n.NodeServer.IsHAMWithVRRP() {
		return n.haPrimaryComponents(), nil
	}

	// bird
	haComponents, err := n.haECMPComponents()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_ha_ecmp_components_failed")
	}

	return haComponents, nil
}

func (n *NodeProvision) haECMPComponents() ([]toclib.ProvisionNodeComponent, error) {
	// only fetch one type VIP LAN or WAN, LAN first
	vips := n.Meta.ECMPBGPVIPs
	if len(vips) == 0 {
		vips = n.Meta.ECMPBGPWanVIPs
	}
	if len(vips) == 0 {
		return nil, fmt.Errorf("not found any VIPs")
	}

	configVIPsScript, err := tpl.ConfigVIPsOnLo(vips)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_config_vip_script_failed")
	}
	checkVIPsScript, err := tpl.CheckVIPsOnLo(vips)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_check_vip_script_failed")
	}
	healthyScript, err := tpl.BirdHealthyScript(vips)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_bird_healthy_script_failed")
	}

	ext2clusters, err := n.ext2cluster.DumpByRZ(n.Meta.RZ)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_bird_config_proxy_failed")
	}

	birdExporterScript, err := tpl.BirdExporterScript(&tpl.HTTPTunnel{
		HTTPProxy:  ext2clusters.HTTPProxy(),
		HTTPSProxy: ext2clusters.HTTPSProxy(),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_bird_exporter_script_failed")
	}

	haComponents := []toclib.ProvisionNodeComponent{
		{
			StopServiceOnDelete: true,
			Name:                cmp.ConfigVIPsComponent.Name,
			Type:                cmp.ConfigVIPsComponent.Type,
			Version:             cast.ToString(time.Now().Unix()),
			Script:              configVIPsScript,
			HealthycheckCommand: checkVIPsScript,
		},
		{
			StopServiceOnDelete: true,
			Name:                cmp.BirdComponent.Name,
			Type:                cmp.BirdComponent.Type,
			DependentComponents: n.dependentComponents(cmp.BirdComponent),
			DependentTemplates:  n.dependentTemplatePaths(cmp.BirdComponent),
			HealthycheckCommand: healthyScript,
			PreCommand:          tpl.BirdPreCommand,
			PostCommand:         tpl.BirdPostCommand,
		},
		{
			StopServiceOnDelete: true,
			Name:                cmp.BirdExporterComponent.Name,
			Type:                cmp.BirdExporterComponent.Type,
			Script:              birdExporterScript,
			DependentComponents: n.dependentComponents(cmp.BirdExporterComponent),
			DependentTemplates:  n.dependentTemplatePaths(cmp.BirdExporterComponent),
			PostCommand:         tpl.BirdExporterPostCommand,
			HealthycheckCommand: fmt.Sprintf("curl http://%s:9324/metrics -I", n.Agent.IP),
		},
		{
			Name: cmp.Pip3Component.Name,
			Type: cmp.Pip3Component.Type,
			// python3-pip maybe not include /usr/bin/pip3, so we need to check both
			HealthycheckCommand: "unset -f command_not_found_handle; (pip3 --version || python3 -m pip show pip)",
		},
		{
			Name:                cmp.NodeExporterTextfileComponent.Name,
			Type:                cmp.NodeExporterTextfileComponent.Type,
			Script:              tpl.TextfileInstallScript(tpl.TextfileInstaller{Component: "bird"}),
			DependentComponents: n.dependentComponents(cmp.NodeExporterTextfileComponent),
			PreCommand:          tpl.TextfileManagerInstallScript,
			PostCommand:         tpl.TextfileInstallPostScript,
			Version:             cast.ToString(time.Now().Unix()),
		},
	}

	return haComponents, nil
}

func (n *NodeProvision) haPrimaryComponents() []toclib.ProvisionNodeComponent {
	haComponents := []toclib.ProvisionNodeComponent{
		{
			StopServiceOnDelete: true,
			Name:                cmp.KeepalivedComponent.Name,
			Type:                cmp.KeepalivedComponent.Type,
			DependentComponents: n.dependentComponents(cmp.KeepalivedComponent),
			DependentTemplates:  n.dependentTemplatePaths(cmp.KeepalivedComponent),
			PreCommand:          tpl.KeepalivedPreCommand,
			PostCommand:         tpl.KeepalivedPostCommand,
		},
	}

	return haComponents
}

// PrivateComponents fetch private components
func (n *NodeProvision) PrivateComponents() ([]toclib.ProvisionNodeComponent, error) {
	if n.HAM.Code > consts.HAMNonStd3.Code {
		return nil, nil
	}

	conf, err := n.SGWEtcdProxyConf()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_sgw_etcd_proxy_conf_failed")
	}
	healthyScript, err := tpl.SGWEtcdProxyHealthyScript(conf.ServerName)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_sgw_etcd_proxy_healthy_script_failed")
	}

	// tips: if PreCommand or PostCommand or HealthyCommand content changed,
	// need to set a new version to trigger script execution
	privateComponents := []toclib.ProvisionNodeComponent{
		{
			StopServiceOnDelete: true,
			Name:                cmp.ConfigSGWEtcdProxy.Name,
			Type:                cmp.ConfigSGWEtcdProxy.Type,
			Version:             cast.ToString(time.Now().Unix()), // ensure execute every time
			Script:              "nginx -t 2>&1 && systemctl reload nginx",
			DependentComponents: n.dependentComponents(cmp.ConfigSGWEtcdProxy),
			DependentTemplates:  n.dependentTemplatePaths(cmp.ConfigSGWEtcdProxy),
			PreCommand:          tpl.SGWEtcdProxyLocalConfPostScript,
			PostCommand:         "sleep 2 # wait nginx reload ok",
			HealthycheckCommand: healthyScript,
		},
	}

	return privateComponents, nil
}

func (n *NodeProvision) Component(name, version string) (*toclib.ProvisionNodeComponent, error) {
	components, err := n.Components(albvo.NewNodeComponentVersion(name, version))
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_component_failed")
	}

	for _, component := range components {
		if component.Type != cmp.ComponentTypeDocker && component.Name == name {
			if version != "" {
				component.Version = version
			}

			return &component, nil
		}

		if component.Type == cmp.ComponentTypeDocker && component.ServiceName == name {
			return &component, nil
		}
	}

	return nil, fmt.Errorf("component %s not found", name)
}

// Components bcz these components only work on installation, so it'll not take effect on hot-update
func (n *NodeProvision) Components(version *albvo.NodeComponentVersion) ([]toclib.ProvisionNodeComponent, error) {
	baseComponents, err := n.BaseComponents()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_base_component_failed")
	}

	var haComponents []toclib.ProvisionNodeComponent
	if n.HAMode().IsNonStdHAM() {
		haComponents, err = n.HAComponents()
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_ha_component_failed")
		}
	}

	var privateComponents []toclib.ProvisionNodeComponent
	if n.IsPrivateAZSeg() {
		privateComponents, err = n.PrivateComponents()
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_private_az_component_failed")
		}
	}

	var coreComponents []toclib.ProvisionNodeComponent
	if version != nil {
		coreComponents, err = n.CoreComponents(version)
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_core_component_failed")
		}
	}

	log.Logger().WithFields(log.Fields{
		"base":    len(baseComponents),
		"core":    len(coreComponents),
		"ha":      len(haComponents),
		"private": len(privateComponents),
	}).Info("fetch_provision_component")

	var components []toclib.ProvisionNodeComponent
	components = append(components, baseComponents...)
	components = append(components, coreComponents...)
	components = append(components, haComponents...)
	components = append(components, privateComponents...)

	// verify component, avoid empty name or type
	for _, com := range components {
		if com.Name == "" || com.Type == "" {
			return nil, fmt.Errorf("invalid component found")
		}
	}

	return components, nil
}

// BriefComponents return component list from consts.ALBComponents with consts.HAComponents optionally
func (n *NodeProvision) BriefComponents() ([]cmp.Component, error) {
	if !n.HAMode().IsNonStdHAM() {
		return cmp.ALBComponents(), nil
	}

	components := cmp.ALBComponents()
	components = append(components, cmp.HAComponents...)
	if n.AZInfo.IsPrivate() {
		components = append(components, cmp.ConfigSGWEtcdProxy)
	}

	return components, nil
}

// ExhaustiveComponents return component list from consts.ALBComponents with consts.HAComponents,
// and sgw etcd proxy components optionally
func (n *NodeProvision) ExhaustiveComponents() ([]cmp.Component, error) {
	components := cmp.ALBComponents()
	components = append(components, cmp.HAComponents...)

	if n.AZInfo.IsPrivate() {
		components = append(components, cmp.ConfigSGWEtcdProxy)
	}

	return components, nil
}

func (n *NodeProvision) componentMap(typ string, cfgs *toclib.ProvisionNodeConfig) map[string]toclib.ProvisionNodeComponent {
	compsMap := make(map[string]toclib.ProvisionNodeComponent)
	for _, comp := range cfgs.Components {
		if typ == cmp.ComponentTypeUnSpec {
			if comp.Type == cmp.ComponentTypeDocker {
				compsMap[comp.ServiceName] = comp
			} else {
				compsMap[comp.Name] = comp
			}

			continue
		}

		if comp.Type == typ {
			if comp.Type == cmp.ComponentTypeDocker {
				compsMap[comp.ServiceName] = comp
			} else {
				compsMap[comp.Name] = comp
			}
		}
	}

	return compsMap
}

// NodeConfig fetch specified type component's config
// 1. fetch provision configs
// 2. foreach every component, filter those type docker
// 3. foreach component's dependent template, got them
func (n *NodeProvision) NodeConfig(typ string) (*toclib.ProvisionNodeConfig, error) {
	components, err := n.BriefComponents()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_component_list_failed")
	}

	provision, err := n.nodeConfig(typ, components)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_node_config_failed")
	}

	return provision, nil
}

// NodeReversedConfig fetch specified type component's config
// return HA components as possible
func (n *NodeProvision) NodeReversedConfig(typ string) (*toclib.ProvisionNodeConfig, error) {
	components, err := n.ExhaustiveComponents()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_component_list_failed")
	}

	provision, err := n.nodeConfig(typ, components)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_node_config_failed")
	}

	if err := n.validateHAModeComponents(provision); err != nil {
		return nil, errors.WithMessage(err, "validate_ha_mode_components_failed")
	}

	return provision, nil
}

func (n *NodeProvision) nodeConfig(typ string, components []cmp.Component) (*toclib.ProvisionNodeConfig, error) {
	cfgs, err := n.tocex.GetNodeProvision()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_provision_failed")
	}

	compsMap := n.componentMap(typ, cfgs)

	tempMap := make(map[string]toclib.ProvisionNodeTemplate)
	for _, temp := range cfgs.Templates {
		tempMap[temp.Path] = temp
	}

	var comps []toclib.ProvisionNodeComponent
	var temps []toclib.ProvisionNodeTemplate
	tempMark := make(map[string]struct{})
	for _, com := range components {
		var matchedComp *toclib.ProvisionNodeComponent
		if com.Type == cmp.ComponentTypeDocker {
			if comp, ok := compsMap[com.Service]; ok {
				matchedComp = &comp
				comps = append(comps, comp)
			}
		} else {
			if comp, ok := compsMap[com.Name]; ok {
				matchedComp = &comp
				comps = append(comps, comp)
			}
		}

		if matchedComp != nil {
			for _, temp := range matchedComp.DependentTemplates {
				if template, ok := tempMap[temp]; ok {
					if _, ok = tempMark[temp]; !ok {
						temps = append(temps, template)
					}
				}
			}
		}
	}

	// remove stale templates
	slice.ForEach(tpl.LegacyTemplates, func(_ int, temp tpl.Template) {
		if template, ok := tempMap[temp.Path]; ok {
			if _, ok = tempMark[temp.Path]; !ok {
				temps = append(temps, template)
			}
		}
	})

	return &toclib.ProvisionNodeConfig{
		HostIP:     n.Agent.IP,
		Components: comps,
		Templates:  temps,
	}, nil
}

// NodeConfigWithComponents fetch specified components' config
// 1. fetch provNode components then foreach them
// 2. map of components slice
// 3. foreach components, check it with Name or ServiceName
func (n *NodeProvision) NodeConfigWithComponents(components []string) (*toclib.ProvisionNodeConfig, error) {
	cfgs, err := n.tocex.GetNodeProvision()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_provision_failed")
	}

	compsMap := make(map[string]struct{})
	for _, comp := range components {
		compsMap[comp] = struct{}{}
	}

	tempMap := make(map[string]toclib.ProvisionNodeTemplate)
	for _, temp := range cfgs.Templates {
		tempMap[temp.Path] = temp
	}

	var comps []toclib.ProvisionNodeComponent
	var temps []toclib.ProvisionNodeTemplate
	tempMark := make(map[string]struct{})
	for _, com := range cfgs.Components {
		var matched bool
		if _, ok := compsMap[com.Name]; ok {
			matched = true
		} else if _, ok = compsMap[com.ServiceName]; ok {
			matched = true
		}

		if matched {
			comps = append(comps, com)

			for _, temp := range com.DependentTemplates {
				if template, ok := tempMap[temp]; ok {
					if _, ok := tempMark[temp]; !ok {
						temps = append(temps, template)
					}
				}
			}
		}
	}

	return &toclib.ProvisionNodeConfig{
		HostIP:     n.Agent.IP,
		Components: comps,
		Templates:  temps,
	}, nil
}

// NodeConfigWithTemplates fetch specified templates' config
func (n *NodeProvision) NodeConfigWithTemplates(templates []string) (*toclib.ProvisionNodeConfig, error) {
	cfgs, err := n.tocex.GetNodeProvision()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_node_provision_failed")
	}

	tempMap := make(map[string]struct{})
	for _, temp := range templates {
		tempMap[temp] = struct{}{}
	}

	var temps []toclib.ProvisionNodeTemplate
	for _, temp := range cfgs.Templates {
		if _, ok := tempMap[temp.Path]; ok {
			temps = append(temps, temp)
		}
	}

	return &toclib.ProvisionNodeConfig{
		HostIP:    n.Agent.IP,
		Templates: temps,
	}, nil
}

func (n *NodeProvision) validateHAModeComponents(cfgs *toclib.ProvisionNodeConfig) error {
	if len(cfgs.Components) == 0 {
		return nil
	}

	// check if the node is not in non-standard HA mode
	if !n.HAMode().IsNonStdHAM() {
		haCompsMap := make(map[string]struct{})
		for _, comp := range cmp.HAComponents {
			haCompsMap[comp.Name] = struct{}{}
		}

		badComps := []string{}
		for _, comp := range cfgs.Components {
			name := comp.Name
			if comp.Type == cmp.ComponentTypeDocker {
				name = comp.ServiceName
			}

			if _, ok := haCompsMap[name]; ok {
				badComps = append(badComps, name)
			}
		}

		// The node is actually in standard HA mode, but there are components configured in non-standard HA mode
		if len(badComps) > 0 {
			return fmt.Errorf("nonstd_ha_components_%s_in_standard_ha_mode", strings.Join(badComps, ","))
		}
	}

	return nil
}
