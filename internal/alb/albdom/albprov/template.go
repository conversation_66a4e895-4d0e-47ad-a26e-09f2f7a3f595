package albprov

import (
	"fmt"
	"strings"

	"github.com/c-robinson/iplib"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/ops"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
)

func (n *NodeProvision) SGWTemplates() ([]toclib.ProvisionNodeTemplate, error) {
	zk := ops.NewZKAdapter(n.TraceID)
	zkMeta, err := zk.ClusterMeta(n.Meta.RZ, n.Meta.Env)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_zk_cluster_meta_failed")
	}

	zkCheckScript, err := zkMeta.CheckScript()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_zk_check_script_failed")
	}

	etcd := ops.NewEtcdAdapter(n.TraceID)
	etcdMeta, err := etcd.ClusterMeta(n.Meta.RZ, n.Meta.Env)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_etcd_cluster_meta_failed")
	}

	sgwEtcdCheckScript, err := etcdMeta.CheckScript()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_sgw_etcd_check_script_failed")
	}

	ext2Clusters, err := n.ext2cluster.DumpByRZ(n.Meta.RZ)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_ext2cluster_failed")
	}

	sgwEnv := n.Meta.SGWEnv()
	sgwEnv.NodeIP = n.Agent.IP
	sgwEnv.HTTPProxy = ext2Clusters.HTTPProxy()
	sgwEnv.HTTPSProxy = ext2Clusters.HTTPSProxy()
	env, err := tpl.SGWEnvMeta(&sgwEnv)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_sgw_env_failed")
	}

	var templates []toclib.ProvisionNodeTemplate
	templates = append(templates, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     zkMeta.ClusterURL,
		Permission:  consts.FilePermissionRWRR,
		Path:        tpl.MesosZKTemplate.Path,
		PreCommand:  zkCheckScript,
		PostCommand: tpl.MesosZKPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     etcdMeta.ClusterURL,
		Path:        tpl.SGWEtcdTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PreCommand:  sgwEtcdCheckScript,
		PostCommand: tpl.SGWEtcdPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     env,
		Path:        tpl.SGWEnvTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.SGWEnvPostScript,
	})

	return templates, nil
}

func (n *NodeProvision) PrivateTemplates() ([]toclib.ProvisionNodeTemplate, error) {
	conf, err := n.SGWEtcdProxyConf()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_etcd_proxy_conf_failed")
	}

	etcdProxyConf, err := tpl.SGWEtcdProxyLocalConf(conf)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_etcd_proxy_local_failed")
	}

	return []toclib.ProvisionNodeTemplate{
		{
			AutoCreate:  true,
			AutoDelete:  true,
			Path:        tpl.SGWEtcdProxyConfTemplate.Path,
			Content:     etcdProxyConf,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.SGWEtcdProxyLocalConfPostScript,
		},
		{
			AutoCreate:  true,
			AutoDelete:  true,
			Path:        tpl.SSLSecureParamsTemplate.Path,
			Content:     tpl.SSLSecureParams,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.SSLSecureParamsPostScript,
		},
	}, nil
}

func (n *NodeProvision) ALBTemplates() ([]toclib.ProvisionNodeTemplate, error) {
	perfDumperScript := tpl.PerfDumper()

	dataPlaneBackupScript := tpl.DataPlaneBackup()

	dataPlaneRestoreScript := tpl.DataPlaneRestore()

	logRotateCronScript, err := tpl.LogRotateCronScript()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_logrotate_cron_failed")
	}

	ext2Clusters, err := n.ext2cluster.DumpByRZ(n.Meta.RZ)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_ext2cluster_failed")
	}

	host, port, err := ext2Clusters.ProxyHostPort()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_proxy_host_port_failed")
	}

	s3cfg, err := tpl.S3Cfg(&tpl.S3Conf{
		Host:      configs.ALB.Cert.Scertmsd.Host,
		AccessKey: configs.ALB.Cert.Scertmsd.AccessKey,
		SecretKey: configs.ALB.Cert.Scertmsd.SecretKey,
		ProxyHost: host,
		ProxyPort: port,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_s3_cfg_failed")
	}

	var templates []toclib.ProvisionNodeTemplate

	templates = append(templates, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     tpl.NginxLogRotateScript,
		Path:        tpl.LogRotateNgxTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.NginxLogrotatePostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     tpl.LogRotateScript,
		Permission:  consts.FilePermissionRWXRXRX,
		Path:        tpl.LogRotateTemplate.Path,
		PostCommand: tpl.LogrotatePostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     logRotateCronScript,
		Path:        tpl.LogRotateCronTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PreCommand:  "rm -fv /etc/cron.daily/logrotate",
		PostCommand: tpl.LogrotateCronPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     perfDumperScript,
		Path:        tpl.NgxPerfDumpTemplate.Path,
		Permission:  consts.FilePermissionRWXRXRX,
		PostCommand: tpl.NgxPerfDumperPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     tpl.PerfDumperCronScript,
		Path:        tpl.NgxPerfDumpCronTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.NgxPerfDumperCronPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     dataPlaneBackupScript,
		Path:        tpl.ALBBackupTemplate.Path,
		Permission:  consts.FilePermissionRWXRXRX,
		PostCommand: tpl.ALBBackupPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     dataPlaneRestoreScript,
		Path:        tpl.ALBRestoreTemplate.Path,
		Permission:  consts.FilePermissionRWXRXRX,
		PostCommand: tpl.ALBRestorePostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     tpl.DataPlaneBackupCronScript,
		Path:        tpl.ALBBackupCronTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.ALBBackupCronPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     "{}",
		Path:        tpl.NgxServicesJSONTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.NginxServiceScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     n.Meta.Env,
		Path:        tpl.MesosLBEnvTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.MesosLBEnvPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     n.Meta.RZ,
		Path:        tpl.MesosLBIDCTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.MesosLBIDCPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     s3cfg,
		Path:        tpl.S3CfgTemplate.Path,
		Permission:  consts.FilePermissionRW,
		PostCommand: tpl.S3CfgPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     tpl.UOAConf,
		Path:        tpl.ModuleUOATemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.ModuleUOAPostScript,
	})

	return templates, nil
}

func (n *NodeProvision) DockerTemplates() ([]toclib.ProvisionNodeTemplate, error) {
	dockerConfig, err := tpl.DockerConfigs(tpl.DockerConfig{
		Auth:     configs.Mgmt.Harbor[consts.EnvLive].Auth,
		AuthTest: configs.Mgmt.Harbor[consts.EnvTest].Auth,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_docker_config_failed")
	}

	dockerDaemonJSON, err := n.Meta.DockerDaemon(n.Agent.IP, n.NodeServer.DockerRoot())
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_docker_daemon_config_failed")
	}

	var templates []toclib.ProvisionNodeTemplate
	templates = append(templates, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		Content:     dockerConfig,
		Permission:  consts.FileRW,
		Path:        tpl.DockerRootConfigTemplate.Path,
		PostCommand: tpl.DockerRootConfigPostScript,
	}, toclib.ProvisionNodeTemplate{
		AutoCreate:  true,
		Content:     dockerDaemonJSON,
		Permission:  consts.FilePermissionRWRR,
		Path:        tpl.DockerDaemonJSONTemplate.Path,
		PostCommand: tpl.DockerDaemonPostScript,
	})

	ext2clusters, err := n.ext2cluster.DumpByRZ(n.Meta.RZ)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_http_proxy_failed")
	}

	if ext2clusters.IsHTTPTunnelSupported() {
		dockerServiceProxy, err := ext2clusters.SystemdProxy()
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_docker_service_proxy_failed")
		}

		templates = append(templates, toclib.ProvisionNodeTemplate{
			AutoCreate:  true,
			Content:     dockerServiceProxy,
			Permission:  consts.FilePermissionRWRR,
			Path:        tpl.DockerServiceHTTPProxyTemplate.Path,
			PostCommand: tpl.DockerProxyPostScript,
		})
	}

	// it's non-live env
	if !strings.EqualFold(n.Agent.Env, consts.EnvLive) {
		templates = append(templates, toclib.ProvisionNodeTemplate{
			AutoCreate:  true,
			Content:     tpl.ClearDockerCronScript,
			Path:        tpl.DockerClearCronTemplate.Path,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.DockerClearCronPostScript,
		})
	}

	return templates, nil
}

func (n *NodeProvision) interfaces() ([]string, error) {
	hardware, err := n.server.HardwareV2(n.Agent.IP)
	if err != nil {
		if !strings.Contains(err.Error(), "record not found") {
			return nil, errors.WithMessage(err, "fetch_hardware_failed")
		}
	}
	if hardware != nil && len(hardware.Interfaces()) != 0 {
		links := append(hardware.Interfaces(), []string{"lo", "all", "default"}...)

		return links, nil
	}

	interfaces, err := n.tocex.RunTask(tpl.ListInterfaceScript)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_node_interfaces_failed")
	}

	links := strings.Split(strings.TrimSpace(interfaces), "\n")

	return links, nil
}

func (n *NodeProvision) SystemTemplates() ([]toclib.ProvisionNodeTemplate, error) {
	interfaces, err := n.interfaces()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_interfaces_failed")
	}

	nic := tpl.NetInterface{Links: interfaces}
	disableRPFilter, err := tpl.DisableRPFilter(&nic)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_disable_rp_filter_failed")
	}

	templates := []toclib.ProvisionNodeTemplate{{
		AutoCreate:  true,
		AutoDelete:  true,
		Content:     "to reset sysctl config items",
		Path:        tpl.SysctlTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.SysctlScript,
	}, {
		AutoCreate:  true,
		AutoDelete:  true,
		Path:        tpl.DisableCoreDumpTemplate.Path,
		Content:     "to disable core-dump",
		Permission:  consts.FilePermissionRWRR,
		PostCommand: tpl.DisableCoreDumpScript,
	}, {
		AutoCreate:  true,
		Path:        tpl.SysctlDisableRPFilterTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		Content:     disableRPFilter,
		PostCommand: tpl.DisableRPFilterPostScript,
	}, {
		AutoCreate:  true,
		Path:        tpl.SysctlEnlargeConntrackTemplate.Path,
		Permission:  consts.FilePermissionRWRR,
		Content:     tpl.SysctlEnlargeConntrackConf,
		PostCommand: tpl.SysctlEnlargeConntrackScript,
	}}

	return templates, nil
}

func (n *NodeProvision) SMAPTemplates() ([]toclib.ProvisionNodeTemplate, error) {
	templates := []toclib.ProvisionNodeTemplate{
		{
			AutoCreate: true,
			Content:    "ensure dir /data/log/smap-cmdsexporter-log",
			Path:       tpl.CmdsExporterLogTemplate.Path,
			Permission: consts.FilePermissionRWRR,
		},
	}

	ext2clusters, err := n.ext2cluster.DumpByRZ(n.Meta.RZ)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_git_config_proxy_failed")
	}

	if ext2clusters.IsHTTPTunnelSupported() {
		httpProxyEnv, err := ext2clusters.HTTPProxyEnv()
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_http_proxy_env_failed")
		}

		templates = append(templates, toclib.ProvisionNodeTemplate{
			AutoCreate:  true,
			Content:     httpProxyEnv,
			Path:        tpl.CmdsExporterEnvTemplate.Path,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.CmdsExporterEnvScript,
		})
	}

	return templates, nil
}

// bgpGroups returns bgp groups
func (n *NodeProvision) bgpGroups() ([]tpl.BGPGroup, error) {
	var groups []tpl.BGPGroup

	bgp := configs.Mgmt.BGP
	localAS := bgp.LocalAS
	if n.Meta.ECMPBGPLocalAS != 0 {
		localAS = n.Meta.ECMPBGPLocalAS
	}

	nextHop := n.Agent.IP // HAM-NonStd3\4
	namePrefix := "lan"
	// next-hop must be wan
	if n.NodeServer.IsHAMNonStd2() {
		network, err := n.inet.Network()
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_wan_ip_failed")
		}

		if !network.IsExistedWAN() {
			return nil, errors.New("not_found_wan_ip")
		}

		nextHop = network.FirstWANIPAddr()
		namePrefix = "wan"
	}

	if len(n.Meta.BGPASMapping) == 0 {
		routingTables, err := n.inet.RouteTables()
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_route_tables_failed")
		}

		tab, ok := routingTables.Default()
		if !ok {
			return nil, errors.New("fetch_default_route_tables_failed")
		}

		peerIP1 := iplib.PreviousIP(tab.Gateway)
		peerIP2 := iplib.PreviousIP(peerIP1)
		n.Meta.BGPASMapping = map[string]string{
			peerIP1.String(): cast.ToString(bgp.RemoteAS),
			peerIP2.String(): cast.ToString(bgp.RemoteAS),
		}
	}

	i := 0
	for neighbor, remoteAS := range n.Meta.BGPASMapping {
		i++
		groups = append(groups, tpl.BGPGroup{
			LocalAS:  cast.ToString(localAS),
			RemoteAS: remoteAS,
			NextHop:  nextHop,
			Neighbor: neighbor,
			Name:     namePrefix + cast.ToString(i),

			KeepaliveTime: bgp.KeepaliveTime,
			HoldTime:      bgp.HoldTime,
		})
	}

	return groups, nil
}

func (n *NodeProvision) HATemplates() ([]toclib.ProvisionNodeTemplate, error) {
	haTemplates := make([]toclib.ProvisionNodeTemplate, 0)

	switch {
	case n.NodeServer.IsHAMWithVRRP():
		templates, err := n.haPrimaryTemplates()
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_ha_primary_failed")
		}
		haTemplates = append(haTemplates, templates...)
	case n.NodeServer.IsHAMWithECMP():
		templates, err := n.haECMPTemplates()
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_ha_ecmp_failed")
		}
		haTemplates = append(haTemplates, templates...)
	default:
		log.Logger().WithField("node", n.Agent.IP).Warn("no_templates_for_ha")
	}

	return haTemplates, nil
}

func (n *NodeProvision) haECMPTemplates() ([]toclib.ProvisionNodeTemplate, error) {
	// only fetch one type VIP LAN or WAN, LAN first
	if len(n.Meta.ECMPBGPVIPs) == 0 && len(n.Meta.ECMPBGPWanVIPs) == 0 {
		return nil, fmt.Errorf("not_found_any_VIPs")
	}

	vips := n.Meta.ECMPBGPVIPs
	if len(vips) == 0 {
		vips = n.Meta.ECMPBGPWanVIPs
	}

	persistVIPs, err := tpl.PersistentVIPsForLo(vips)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_persistent_vip_failed")
	}

	birdExporterService, err := tpl.BirdExporterService(n.Agent.IP)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_bird_exporter_service_failed")
	}

	groups, err := n.bgpGroups()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_bgp_groups_failed")
	}

	birdConf, err := tpl.BirdConf(&tpl.ConfigBird{
		VIPs:      vips,
		RouterID:  n.Agent.IP,
		BGPGroups: groups,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_bird_conf_failed")
	}

	templates := []toclib.ProvisionNodeTemplate{
		{
			AutoCreate:  true,
			Path:        tpl.SysctlDisableLoARPTemplate.Path,
			Content:     tpl.DisableLoARPScript,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.DisableLoARPPostScript,
		},
		{
			AutoCreate:  true,
			Path:        tpl.PersistentVIPsForLoTemplate.Path,
			Content:     persistVIPs,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.PersistentVIPsLoPostScript,
		},
		{
			AutoCreate: true,
			Path:       tpl.BirdConfTemplate.Path,
			Content:    birdConf,
			Permission: consts.FilePermissionRWRR,
		},
		{
			AutoCreate:  true,
			Path:        tpl.BirdExporterServiceTemplate.Path,
			Content:     birdExporterService,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.BirdExporterServicePostScript,
		},
		{
			AutoCreate:  true,
			Path:        tpl.BirdServiceUnitTemplate.Path,
			Content:     tpl.BirdServiceUnitContent,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.BirdServiceUnitPostCommand,
		},
		{
			AutoCreate:  true,
			Path:        tpl.BirdServiceSGWFlagTemplate.Path,
			Content:     tpl.BirdServiceSGWFlagContent,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.BirdServiceSGWFlagPostScript,
		},
	}

	return templates, nil
}

func (n *NodeProvision) haPrimaryTemplates() ([]toclib.ProvisionNodeTemplate, error) {
	function := n.NodeServer.Tags.Function()
	instances := make(map[string]tpl.Instance)
	nodeInstances := n.Meta.HAInstance(n.Agent.IP)
	if len(nodeInstances) == 0 {
		// NB: we donot raise fatal error here
		log.Logger().WithField("node", n.Agent.IP).Warn("no_ha_instances")
	}

	slice.ForEach(nodeInstances, func(_ int, value *sgwvo.KeepalivedInstance) {
		instances[value.Name] = tpl.Instance{
			Interface:   value.Interface,
			State:       value.State,
			RouterID:    cast.ToString(value.VirtualRouterID),
			Priority:    value.Priority,
			VIPs:        []string{value.VIP},
			Function:    function,
			WithoutAuth: value.WithoutAuth,
		}
	})

	conf := &tpl.ConfigKeepalived{
		Function:  function,
		Instances: instances,
	}
	keepalivedConf, err := tpl.KeepalivedConf(conf)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_keepalived_conf_failed")
	}

	templates := []toclib.ProvisionNodeTemplate{
		{
			AutoCreate: true,
			Path:       tpl.KeepalivedConfTemplate.Path,
			Content:    keepalivedConf,
			Permission: consts.FilePermissionRWRR,
		},
		{
			AutoCreate:  true,
			Path:        tpl.KeepalivedServiceSGWFlagTemplate.Path,
			Content:     tpl.KeepalivedServiceSGWFlagContent,
			Permission:  consts.FilePermissionRWRR,
			PostCommand: tpl.KeepalivedServiceSGWFlagPostScript,
		},
	}

	return templates, nil
}

// Templates skip those in https://git.garena.com/shopee/devops/shopee-ops/-/tree/master/ansible/roles/az/common/tasks
func (n *NodeProvision) Templates() ([]toclib.ProvisionNodeTemplate, error) {
	var err error

	templates, err := n.SGWTemplates()
	if err != nil {
		return nil, err
	}

	ngTemps, err := n.ALBTemplates()
	if err != nil {
		return nil, err
	}

	dockTemps, err := n.DockerTemplates()
	if err != nil {
		return nil, err
	}

	sysTemps, err := n.SystemTemplates()
	if err != nil {
		return nil, err
	}

	smapTemps, err := n.SMAPTemplates()
	if err != nil {
		return nil, err
	}

	var haTemps []toclib.ProvisionNodeTemplate
	if n.NodeServer.IsNonStdHAM() {
		haTemps, err = n.HATemplates()
		if err != nil {
			return nil, err
		}
	}

	if n.IsPrivateAZSeg() {
		privateTemps, err := n.PrivateTemplates()
		if err != nil {
			return nil, err
		}
		templates = append(templates, privateTemps...)
	}

	templates = append(templates, ngTemps...)
	templates = append(templates, dockTemps...)
	templates = append(templates, sysTemps...)
	templates = append(templates, smapTemps...)
	templates = append(templates, haTemps...)

	return templates, nil
}

func (n *NodeProvision) legacyTemplates() []toclib.ProvisionNodeTemplate {
	return slice.Map(tpl.LegacyTemplates, func(_ int, temp tpl.Template) toclib.ProvisionNodeTemplate {
		return toclib.ProvisionNodeTemplate{
			Path: temp.Path,
		}
	})
}

// IsSupportEtcdProxy check if node supports etcd proxy
// @return true if node supports etcd proxy
// in Private AZ support etcd proxy:
// 1. the segment with 1 Tire FW and Single Zone
// 2. the segment with 2 Tire FW and Multiple Zone with dmz-inbound
func (n *NodeProvision) IsSupportEtcdProxy() bool {
	if !n.AZInfo.IsPrivate() {
		return false
	}

	if n.NodeServer.IsDMZInBound() || n.NodeServer.IsDMZPartner() {
		return true
	}

	segment := n.AZInfo.Segment(n.NodeServer.Agent.SegmentName)
	if segment == nil {
		return false
	}

	if consts.FWTier1FW.Equal(segment.FirewallTier) && consts.SvcZoneSingleZone.Equal(segment.ServiceZone) {
		return true
	}

	if consts.FWTier2FW.Equal(segment.FirewallTier) && consts.SvcZoneMultipleZone.Equal(segment.ServiceZone) {
		return true
	}

	if segment.IsFirewallNonExist() {
		if consts.NetworkV1.Equal(segment.NetworkVersion) || consts.NetworkV2.Equal(segment.NetworkVersion) {
			return true
		}
	}

	return false
}
