package albprov

import (
	"fmt"

	"github.com/duke-git/lancet/v2/cryptor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/etcd/etcdvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/ops"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
)

//go:generate mockgen -destination provision_mock.go -package albprov -source provision.go

type Provision interface {
	Templates() ([]toclib.ProvisionNodeTemplate, error)
	Components(version *albvo.NodeComponentVersion) ([]toclib.ProvisionNodeComponent, error)
	Component(name, version string) (*toclib.ProvisionNodeComponent, error)
	UpgradedComponent(name, version string) (*tocvo.NodeProvision, error)
	DeletedComponent(name, version string) (*tocvo.NodeProvision, error)
	NodeConfig(typ string) (*toclib.ProvisionNodeConfig, error)
	NodeReversedConfig(typ string) (*toclib.ProvisionNodeConfig, error)
	NodeConfigWithComponents(components []string) (*toclib.ProvisionNodeConfig, error)
	NodeConfigWithTemplates(templates []string) (*toclib.ProvisionNodeConfig, error)
	HAMode() consts.HighAvailableMode
}

type NodeProvision struct {
	TraceID string

	NodeServer *tocvo.ALBNodeServer
	Agent      *toclib.AgentInfo
	Meta       *sgwvo.ALBClusterConfigMeta
	Tags       []toclib.Tag
	AZInfo     *tocvo.AZ
	HAM        consts.HighAvailableMode

	tocex       toc.TocexAdapter
	inet        toc.NetAdapter
	server      toc.ServerAdapter
	ext2cluster sgw.Ext2ClusterAdapter
}

func NewNodeProvision(svr *tocvo.ALBNodeServer, traceID string) (Provision, error) {
	metaToc := toc.NewMetaAdapter(traceID)
	az, err := metaToc.AZ(svr.Meta.AZ)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_az_failed")
	}

	tocex := toc.NewTocexAdapter(svr.Node())

	return &NodeProvision{
		TraceID:    traceID,
		NodeServer: svr,

		Agent:  svr.Agent,
		Meta:   svr.Meta,
		Tags:   svr.Tags,
		AZInfo: az,
		HAM:    svr.HAM(),

		tocex: tocex,
		inet:  toc.NewNetAdapter(tocex, traceID),

		server:      toc.NewServerAdapter(traceID),
		ext2cluster: sgw.NewExt2Cluster(traceID),
	}, nil
}

func (n *NodeProvision) IsPrivateAZSeg() bool {
	return n.AZInfo.IsPrivateAZSeg(n.Meta.Segment)
}

// SGWEtcdProxyConf return sgw-etcd-proxy config
/*
1. fetch segment, found then return md5sum
2. not found then from group_var
*/
func (n *NodeProvision) SGWEtcdProxyConf() (*tpl.EtcdProxyConf, error) {
	var code string
	var err error

	if n.Agent.SegmentName != "" {
		code = cryptor.Md5String(n.Agent.SegmentName)
	} else {
		code, err = n.Meta.SegmentCode()
		if err != nil {
			return nil, errors.WithMessage(err, "fetch_segment_code_failed")
		}
	}

	/*
		points, err := n.GroupVar.SGWEtcdEndpoints() // deprecated
	*/
	etcd := ops.NewEtcdAdapter(n.TraceID)
	etcdMeta, err := etcd.ClusterMeta(n.Meta.RZ, n.Meta.Env)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_etcd_cluster_meta_failed")
	}

	endpoints, err := etcdMeta.Endpoints()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_etcd_endpoints_failed")
	}

	points := slice.Map(endpoints, func(_ int, endpoint etcdvo.Endpoint) string {
		return fmt.Sprintf("%s:%d", endpoint.Host, endpoint.Port)
	})

	cert := configs.ALB.EtcdCert()
	certDir := cert.Live
	if n.Meta.Env != consts.EnvLive {
		certDir = cert.NonLive
	}

	var upstream string
	if n.Meta.IDCCode != "" {
		upstream = fmt.Sprintf("%s_%s_%s_etcd", n.Meta.AZ, code, n.Meta.IDCCode)
	} else {
		upstream = fmt.Sprintf("%s_%s_etcd", n.Meta.AZ, code)
	}

	conf := tpl.EtcdProxyConf{
		Env:              n.Meta.Env,
		Upstream:         upstream,
		ServerName:       n.Meta.SGWEtcdProxyDomainName(code),
		CertDir:          certDir,
		SGWEtcdEndpoints: points,
	}

	return &conf, nil
}

func (n *NodeProvision) HAMode() consts.HighAvailableMode {
	return n.HAM
}
