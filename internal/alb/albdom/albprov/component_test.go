package albprov

import (
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/golang/mock/gomock"
	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ext2/ext2vo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
)

const (
	generalAZSG2ReleaseTestNodeIP = "**************"
	checkNodeProvisionInterval    = 5
	maxCheckTimes                 = 10
)

func mockTocex(t *testing.T, ip string) toc.TocexAdapter {
	t.Helper()

	var tocex toc.TocexAdapter
	fakeit := gofakeit.New(time.Now().Unix())

	if ciEnv {
		tocexMocker := toc.NewMockTocexAdapter(gomock.NewController(t))

		agentInfo := toclib.AgentInfo{}
		err := fakeit.Struct(&agentInfo)
		assert.NoError(t, err)

		agentInfo.Env = "test"
		agentInfo.IP = ip
		agentInfo.AZv2 = "ap-sg-1-general-x"
		agentInfo.SegmentName = "General"
		agentInfo.AZ = "sg2"
		tocexMocker.EXPECT().GetAgent().Return(&agentInfo, nil).AnyTimes()
		tocexMocker.EXPECT().RunTask(gomock.Eq(tpl.ListInterfaceScript)).Return(`lo`, nil).AnyTimes()

		tocex = tocexMocker
	} else {
		tocex2, err := toc.NewTocexAdapterWithIP(ip, t.Name())
		assert.NoError(t, err)

		tocex = tocex2
	}

	return tocex
}

func mockClusterConfigMeta(t *testing.T) *sgwvo.ALBClusterConfigMeta {
	t.Helper()

	meta := &sgwvo.ALBClusterConfigMeta{}

	if ciEnv {
		meta.ECMPBGPWanVIPs = []string{"************"}
		meta.BGPASMapping = map[string]string{
			"************": "************",
			"************": "************",
		}
	}
	if meta.ECMPBGPWanVIPs == nil {
		meta.ECMPBGPWanVIPs = []string{"************"}
	}

	return meta
}

func fetchALBServerByIP(t *testing.T, ip string) *tocvo.ALBNodeServer {
	t.Helper()

	svrV3 := toc.NewServerV3Adapter(t.Name())
	tags, err := svrV3.Tags(ip)
	assert.NoError(t, err)

	toclibSvrV3, err := svrV3.Server(ip)
	assert.NoError(t, err)
	tocvoSvrV3 := tocvo.ServerV3(*toclibSvrV3)

	tocex := mockTocex(t, ip)

	var agent *toclib.AgentInfo
	agent, err = tocex.GetAgent()
	assert.NoError(t, err)

	meta := mockClusterConfigMeta(t)
	meta.AZ = agent.AZv2
	meta.Segment = agent.SegmentName
	meta.RZ = agent.AZ
	meta.Env = agent.Env

	server := &tocvo.ALBNodeServer{
		Meta:  meta,
		Agent: agent,
		Tags:  tags,
		Segment: &tocvo.Segment{
			NetworkVersion: "v3.0",
		},
		ServerV3: &tocvoSvrV3,
	}

	return server
}

func checkNodeProvisionComponents(t *testing.T, node toc.TocexAdapter, names []string) {
	t.Helper()

	var round int
	components := convertor.ToMap(names, func(name string) (string, struct{}) {
		return name, struct{}{}
	})

	for {
		round++
		time.Sleep(checkNodeProvisionInterval * time.Second)

		provision, err := node.GetNodeProvision()
		assert.NoError(t, err)

		ret := make(map[string]struct{}, len(names))

		for _, component := range provision.Components {
			if component.Type == cmp.ComponentTypeDocker {
				if _, ok := components[component.ServiceName]; ok {
					ret[component.ServiceName] = struct{}{}
					assert.Empty(t, component.ErrMsg)
				}
			} else if _, ok := components[component.Name]; ok {
				ret[component.Name] = struct{}{}
				assert.Empty(t, component.ErrMsg)
			}
		}

		finish := false
		slice.ForEach(names, func(_ int, name string) {
			if _, ok := ret[name]; !ok {
				finish = false
				t.Logf("%s not found", name)
			} else {
				finish = true
			}
		})

		t.Logf("%d/%d components found", len(ret), len(names))

		if finish || round >= maxCheckTimes {
			if !finish {
				t.Errorf("checkNodeProvisionComponents: %d/%d components found", len(ret), len(names))
			}

			break
		}
	}
}

func checkNodeProvisionComponentsNonExist(t *testing.T, node toc.TocexAdapter, names []string) {
	t.Helper()

	var round int
	components := convertor.ToMap(names, func(name string) (string, struct{}) {
		return name, struct{}{}
	})

	for {
		round++
		time.Sleep(checkNodeProvisionInterval * time.Second)

		provision, err := node.GetNodeProvision()
		assert.NoError(t, err)

		ret := make(map[string]struct{})

		for _, component := range provision.Components {
			if component.Type == cmp.ComponentTypeDocker {
				if _, ok := components[component.ServiceName]; ok {
					ret[component.Name] = struct{}{}
				}
			} else if _, ok := components[component.Name]; ok {
				ret[component.Name] = struct{}{}
			}
		}

		t.Logf("%d/%d components found", len(ret), len(names))

		if len(ret) == 0 || round >= maxCheckTimes {
			if len(ret) > 0 {
				t.Errorf("checkNodeProvisionComponentsNonExist: %d/%d components found", len(ret), len(names))
			}

			break
		}
	}
}

func checkNodeUpgradeComponent(t *testing.T, node toc.TocexAdapter, setup *componentSetup) {
	t.Helper()

	var round int

	for {
		round++
		time.Sleep(checkNodeProvisionInterval * time.Second)

		provision, err := node.GetNodeProvision()
		assert.NoError(t, err)

		finish := false
		for _, component := range provision.Components {
			if component.Type == cmp.ComponentTypeDocker {
				if component.ServiceName == setup.Component.Service {
					assert.Empty(t, component.ErrMsg)
					if assert.Equal(t, component.Version, setup.Component.Version) {
						finish = true
					}
				}
			} else if component.Name == setup.Component.Name {
				assert.Empty(t, component.ErrMsg)
				if assert.Equal(t, component.Version, setup.Component.Version) {
					finish = true
				}
			}
		}

		if finish || round >= maxCheckTimes {
			if !finish {
				t.Errorf("checkNodeUpgradeComponent: component %s version %s upgrade failed",
					setup.Component.Name, setup.Component.Version)
			}

			break
		}
	}
}

func checkNodeServiceActive(t *testing.T, node toc.TocexAdapter, name string) {
	t.Helper()

	if name == "" {
		return
	}

	ret, err := node.RunTask(fmt.Sprintf("systemctl is-active %s", name))
	assert.NoError(t, err)
	assert.Equal(t, "active", strings.TrimSpace(ret))
}

type provNode struct {
	NodeIP string
}

func (n *provNode) fetchALBServer(t *testing.T) *tocvo.ALBNodeServer {
	t.Helper()

	return fetchALBServerByIP(t, n.NodeIP)
}

func (n *provNode) fetchComponentVersion(t *testing.T) *albvo.NodeComponentVersion {
	t.Helper()

	version := albvo.NodeComponentVersion{}

	err := gofakeit.Struct(&version)
	assert.NoError(t, err)

	return &version
}

func (n *provNode) fetchDockerComponents(t *testing.T) []toclib.ProvisionNodeComponent {
	t.Helper()

	version := n.fetchComponentVersion(t)

	return []toclib.ProvisionNodeComponent{
		{
			Type:        cmp.ComponentTypeDocker,
			ServiceName: cmp.ALBMetricsComponent.Service,
			Version:     version.ALBMetrics,
		},
		{
			Type:        cmp.ComponentTypeDocker,
			ServiceName: cmp.ALBSDComponent.Service,
			Version:     version.ALBSD,
		},
		{
			Type:        cmp.ComponentTypeDocker,
			ServiceName: cmp.SGWAgentComponent.Service,
			Version:     version.SGWAgent,
		},
		{
			Type:        cmp.ComponentTypeDocker,
			ServiceName: cmp.MesosNginxLBComponent.Service,
			Version:     version.NginxLB,
		},
	}
}

type provisionSetup struct {
	Config *toclib.ProvisionNodeConfig
	NodeIP string
}

func setupProvision(t *testing.T, node toc.TocexAdapter, setup *provisionSetup) {
	t.Helper()

	assert.NoError(t, node.SetNodeProvision(setup.Config))
}

type componentSetup struct {
	provNode

	Component                  *cmp.Component
	DependentComponentsVersion map[string]string
}

type componentsSetup struct {
	Components []*cmp.Component
	NodeIP     string
}

type componentsType struct {
	provNode

	Type string
}

func setupComponent(t *testing.T, node toc.TocexAdapter, setup *componentSetup) {
	t.Helper()

	cfgs, err := node.GetNodeProvision()
	assert.NoError(t, err)

	name := setup.Component.Name

	found := false
	for _, cfg := range cfgs.Components {
		if cfg.Type == cmp.ComponentTypeDocker && cfg.ServiceName == name {
			found = true

			break
		} else if cfg.Name == name {
			found = true

			break
		}
	}

	if found {
		checkNodeServiceActive(t, node, setup.Component.Service)

		return
	}

	server := fetchALBServerByIP(t, setup.NodeIP)
	prov, err := NewNodeProvision(server, t.Name())
	assert.NoError(t, err)

	comp, err := prov.Component(name, setup.Component.Version)
	assert.NoError(t, err)

	var components []toclib.ProvisionNodeComponent
	for _, c := range comp.DependentComponents {
		comp2, err := prov.Component(c, setup.DependentComponentsVersion[c])
		assert.NoError(t, err)

		components = append(components, *comp2)
	}
	components = append(components, *comp)

	assert.GreaterOrEqual(t, len(components), 1+len(comp.DependentComponents))

	templates, err := prov.Templates()
	assert.NoError(t, err)

	tpls := slice.Filter(templates, func(_ int, tpl toclib.ProvisionNodeTemplate) bool {
		for _, temp := range comp.DependentTemplates {
			if tpl.Path == temp {
				return true
			}
		}

		return false
	})

	err = node.SetNodeProvision(&toclib.ProvisionNodeConfig{
		HostIP:     setup.NodeIP,
		Components: components,
		Templates:  tpls,
	})
	assert.NoError(t, err)

	checkNodeProvisionComponents(t, node, []string{name})

	checkNodeServiceActive(t, node, setup.Component.Service)
}

func setupComponentsByType(t *testing.T, node toc.TocexAdapter, setup *componentsType) {
	t.Helper()

	server := setup.fetchALBServer(t)
	prov, err := NewNodeProvision(server, t.Name())
	assert.NoError(t, err)

	nodeComponents, err := prov.Components(setup.fetchComponentVersion(t))
	assert.NoError(t, err)

	var components []toclib.ProvisionNodeComponent
	var names []string
	for _, cfg := range nodeComponents {
		if cfg.Type == setup.Type {
			components = append(components, cfg)

			if cfg.Type == cmp.ComponentTypeDocker {
				names = append(names, cfg.ServiceName)
			} else {
				names = append(names, cfg.Name)
			}
		}
	}

	assert.Greater(t, len(components), 0)

	nodeTemplates, err := prov.Templates()
	assert.NoError(t, err)

	templates := slice.Filter(nodeTemplates, func(_ int, tpl toclib.ProvisionNodeTemplate) bool {
		for _, comp := range components {
			for _, temp := range comp.DependentTemplates {
				if tpl.Path == temp {
					return true
				}
			}
		}

		return false
	})

	err = node.SetNodeProvision(&toclib.ProvisionNodeConfig{
		HostIP:     setup.NodeIP,
		Components: components,
		Templates:  templates,
	})
	assert.NoError(t, err)

	checkNodeProvisionComponents(t, node, names)
}

func nodeSetupComponent(t *testing.T, setup *componentSetup) {
	t.Helper()

	var checkDpkgScript string
	if setup.Component.Type == cmp.ComponentTypeAPT {
		checkDpkgScript = fmt.Sprintf(`dpkg-query --list | grep %s | awk '{print $2}'`, setup.Component.Name)
	}

	t.Run("local", func(t *testing.T) {
		if ciEnv {
			return
		}

		tocex, err := toc.NewTocexAdapterWithIP(setup.NodeIP, t.Name())
		assert.NoError(t, err)

		setupComponent(t, tocex, setup)

		if checkDpkgScript != "" {
			ret, err := tocex.RunTask(checkDpkgScript)
			assert.NoError(t, err)
			assert.Contains(t, ret, setup.Component.Name)
		}
	})

	t.Run("mock", func(t *testing.T) {
		tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
		tocex.EXPECT().GetNodeProvision().Return(&toclib.ProvisionNodeConfig{
			HostIP: setup.NodeIP,
			Components: []toclib.ProvisionNodeComponent{
				{
					Type:        setup.Component.Type,
					Name:        setup.Component.Name,
					ServiceName: setup.Component.Service,
					Version:     setup.Component.Version,
				},
			},
		}, nil).AnyTimes()
		tocex.EXPECT().SetNodeProvision(gomock.Any()).Return(nil).AnyTimes()
		if setup.Component.Service != "" {
			checkServiceActiveScript := fmt.Sprintf(`systemctl is-active %s`, setup.Component.Service)
			tocex.EXPECT().RunTask(gomock.Eq(checkServiceActiveScript)).Return("active", nil).AnyTimes()
		}
		if checkDpkgScript != "" {
			tocex.EXPECT().RunTask(gomock.Eq(checkDpkgScript)).Return(setup.Component.Name, nil).AnyTimes()
		}

		setupComponent(t, tocex, setup)

		if checkDpkgScript != "" {
			ret, err := tocex.RunTask(checkDpkgScript)
			assert.NoError(t, err)
			assert.Contains(t, ret, setup.Component.Name)
		}
	})
}

func nodeSetupDockerComponents(t *testing.T, setup *componentsType) {
	t.Helper()

	t.Run("local", func(t *testing.T) {
		if ciEnv {
			return
		}

		tocex, err := toc.NewTocexAdapterWithIP(setup.NodeIP, t.Name())
		assert.NoError(t, err)

		setupComponentsByType(t, tocex, setup)
	})

	t.Run("mock", func(t *testing.T) {
		tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
		tocex.EXPECT().GetNodeProvision().Return(&toclib.ProvisionNodeConfig{
			HostIP:     setup.NodeIP,
			Components: setup.fetchDockerComponents(t),
		}, nil).AnyTimes()
		tocex.EXPECT().SetNodeProvision(gomock.Any()).Return(nil).AnyTimes()

		setupComponentsByType(t, tocex, setup)
	})
}

func removeComponent(t *testing.T, node toc.TocexAdapter, setup *componentSetup) {
	t.Helper()

	cfgs, err := node.GetNodeProvision()
	assert.NoError(t, err)

	name := setup.Component.Name

	found := false
	for _, cfg := range cfgs.Components {
		if cfg.Type == cmp.ComponentTypeDocker && cfg.ServiceName == name {
			found = true

			break
		} else if cfg.Name == name {
			found = true

			break
		}
	}

	if !found {
		return
	}

	server := fetchALBServerByIP(t, setup.NodeIP)
	prov, err := NewNodeProvision(server, t.Name())
	assert.NoError(t, err)

	comp, err := prov.Component(name, setup.Component.Version)
	assert.NoError(t, err)

	err = node.DeleteProvision(&toclib.ProvisionNodeConfig{
		HostIP: setup.NodeIP,
		Components: []toclib.ProvisionNodeComponent{
			*comp,
		},
	})
	assert.NoError(t, err)

	checkNodeProvisionComponentsNonExist(t, node, []string{name})
}

func removeComponents(t *testing.T, node toc.TocexAdapter, setup *componentsSetup) {
	t.Helper()

	cfgs, err := node.GetNodeProvision()
	assert.NoError(t, err)

	names := make(map[string]struct{})
	slice.ForEach(setup.Components, func(i int, component *cmp.Component) {
		if component.Type == cmp.ComponentTypeDocker {
			names[component.Service] = struct{}{}
		} else {
			names[component.Name] = struct{}{}
		}
	})

	found := false
	var components []toclib.ProvisionNodeComponent
	for _, component := range cfgs.Components {
		var name string
		if component.Type == cmp.ComponentTypeDocker {
			name = component.ServiceName
		} else {
			name = component.Name
		}

		if _, ok := names[name]; ok {
			found = true
			components = append(components, component)

			break
		}
	}

	if !found {
		return
	}

	err = node.DeleteProvision(&toclib.ProvisionNodeConfig{
		HostIP:     setup.NodeIP,
		Components: components,
	})
	assert.NoError(t, err)

	checkNodeProvisionComponentsNonExist(t, node, convertor.MapToSlice(names, func(name string, _ struct{}) string {
		return name
	}))
}

func removeComponentsByType(t *testing.T, node toc.TocexAdapter, remove *componentsType) {
	t.Helper()

	cfgs, err := node.GetNodeProvision()
	assert.NoError(t, err)

	var components []toclib.ProvisionNodeComponent
	for _, component := range cfgs.Components {
		if component.Type == remove.Type {
			components = append(components, component)
		}
	}

	if len(components) == 0 {
		return
	}

	err = node.DeleteProvision(&toclib.ProvisionNodeConfig{
		HostIP:     remove.NodeIP,
		Components: components,
	})
	assert.NoError(t, err)

	var names []string
	slice.ForEach(components, func(i int, component toclib.ProvisionNodeComponent) {
		if component.Type == cmp.ComponentTypeDocker {
			names = append(names, component.ServiceName)
		} else {
			names = append(names, component.Name)
		}
	})
	checkNodeProvisionComponentsNonExist(t, node, names)
}

func nodeRemoveComponent(t *testing.T, setup *componentSetup) {
	t.Helper()

	if !ciEnv {
		tocex, err := toc.NewTocexAdapterWithIP(setup.NodeIP, t.Name())
		assert.NoError(t, err)

		removeComponent(t, tocex, setup)
	} else {
		tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
		tocex.EXPECT().GetNodeProvision().Return(&toclib.ProvisionNodeConfig{
			HostIP:     setup.NodeIP,
			Components: []toclib.ProvisionNodeComponent{},
		}, nil).AnyTimes()
		tocex.EXPECT().SetNodeProvision(gomock.Any()).Return(nil).AnyTimes()

		removeComponent(t, tocex, setup)
	}
}

func nodeRemoveComponents(t *testing.T, remove *componentsSetup) {
	t.Helper()

	if !ciEnv {
		node, err := toc.NewTocexAdapterWithIP(remove.NodeIP, "")
		assert.NoError(t, err)

		removeComponents(t, node, remove)
	} else {
		node := toc.NewMockTocexAdapter(gomock.NewController(t))
		node.EXPECT().GetNodeProvision().Return(&toclib.ProvisionNodeConfig{
			HostIP:     remove.NodeIP,
			Components: []toclib.ProvisionNodeComponent{},
		}, nil).AnyTimes()
		node.EXPECT().DeleteProvision(gomock.Any()).Return(nil).AnyTimes()

		removeComponents(t, node, remove)
	}
}

func nodeRemoveComponentByType(t *testing.T, remove *componentsType) {
	t.Helper()

	t.Run("local", func(t *testing.T) {
		if ciEnv {
			return
		}

		tocex, err := toc.NewTocexAdapterWithIP(remove.NodeIP, t.Name())
		assert.NoError(t, err)

		removeComponentsByType(t, tocex, remove)
	})
	t.Run("mock", func(t *testing.T) {
		tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
		tocex.EXPECT().GetNodeProvision().Return(&toclib.ProvisionNodeConfig{
			HostIP:     remove.NodeIP,
			Components: []toclib.ProvisionNodeComponent{},
		}, nil).AnyTimes()
		tocex.EXPECT().DeleteProvision(gomock.Any()).Return(nil).AnyTimes()

		removeComponentsByType(t, tocex, remove)
	})
}

func upgradeComponent(t *testing.T, node toc.TocexAdapter, setup *componentSetup) {
	t.Helper()

	var name string
	if setup.Component.Type == cmp.ComponentTypeDocker {
		name = setup.Component.Service
	} else {
		name = setup.Component.Name
	}
	prov, err := NewNodeProvision(setup.fetchALBServer(t), t.Name())
	assert.NoError(t, err)

	comp, err := prov.UpgradedComponent(name, setup.Component.Version)
	assert.NoError(t, err)

	err = node.SetNodeProvision(&toclib.ProvisionNodeConfig{
		HostIP: setup.NodeIP,
		Components: []toclib.ProvisionNodeComponent{
			*comp.Component,
		},
		Templates: comp.Templates,
	})
	assert.NoError(t, err)

	checkNodeUpgradeComponent(t, node, setup)
}

func nodeUpgradeComponent(t *testing.T, setup *componentSetup) {
	t.Helper()

	t.Run("local", func(t *testing.T) {
		if ciEnv {
			return
		}

		tocex, err := toc.NewTocexAdapterWithIP(setup.NodeIP, "")
		assert.NoError(t, err)

		upgradeComponent(t, tocex, setup)
	})

	t.Run("mock", func(t *testing.T) {
		tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
		tocex.EXPECT().GetNodeProvision().Return(&toclib.ProvisionNodeConfig{
			HostIP: setup.NodeIP,
			Components: []toclib.ProvisionNodeComponent{
				{
					Type:        setup.Component.Type,
					Name:        setup.Component.Name,
					Version:     setup.Component.Version,
					ServiceName: setup.Component.Service,
				},
			},
		}, nil).AnyTimes()
		tocex.EXPECT().SetNodeProvision(gomock.Any()).Return(nil).AnyTimes()

		upgradeComponent(t, tocex, setup)
	})
}

func TestNodeProvision_Components(t *testing.T) {
	node := &provNode{NodeIP: configs.E2E.ALB.Shopee.Node}

	server := node.fetchALBServer(t)
	prov := fetchNodeProvision(t, server)

	components, err := prov.Components(node.fetchComponentVersion(t))
	assert.NoError(t, err)
	assert.Greater(t, len(components), 0)

	tocex := toc.NewMockTocexAdapter(gomock.NewController(t))
	tocex.EXPECT().SetNodeProvision(gomock.Any()).Return(nil).AnyTimes()
	tocex.EXPECT().RunTask(gomock.Eq(tpl.ListInterfaceScript)).Return(`lo`, nil).AnyTimes()
	prov.tocex = tocex

	templates, err := prov.Templates()
	assert.NoError(t, err)
	assert.Greater(t, len(templates), 0)

	setup := &provisionSetup{
		Config: &toclib.ProvisionNodeConfig{
			HostIP:     node.NodeIP,
			Components: components,
			Templates:  templates,
		},
		NodeIP: node.NodeIP,
	}

	setupProvision(t, tocex, setup)
}

func TestSetupNginx(t *testing.T) {
	setup := &componentSetup{
		Component:                  &cmp.NginxComponent,
		DependentComponentsVersion: map[string]string{},
		provNode:                   provNode{NodeIP: generalAZSG2ReleaseTestNodeIP},
	}
	nodeSetupComponent(t, setup)
}

func TestSetupNginxLuaEbpfToolkit(t *testing.T) {
	setup := &componentSetup{
		Component:                  &cmp.NginxLuaEbpfToolkitComponent,
		DependentComponentsVersion: map[string]string{},
		provNode:                   provNode{NodeIP: generalAZSG2ReleaseTestNodeIP},
	}
	nodeSetupComponent(t, setup)
}

func TestNodeProvision_Component_Docker(t *testing.T) {
	setup := &componentSetup{
		Component:                  &cmp.DockerComponent,
		DependentComponentsVersion: map[string]string{},
		provNode:                   provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	nodeRemoveComponent(t, setup)
	nodeSetupComponent(t, setup)
}

func TestDeleteDockerContainers(t *testing.T) {
	remove := &componentsType{
		Type:     cmp.ComponentTypeDocker,
		provNode: provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	nodeRemoveComponentByType(t, remove)
}

func TestSetupDockerContainers(t *testing.T) {
	setup := &componentsType{
		Type:     cmp.ComponentTypeDocker,
		provNode: provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	nodeSetupDockerComponents(t, setup)
}

// Only works at private az, but still haven't dedicated provNode to test
func TestDeleteBird(t *testing.T) {
	setup := &componentsSetup{
		Components: []*cmp.Component{
			&cmp.BirdComponent,
			&cmp.BirdExporterComponent,
		},
		NodeIP: configs.E2E.ALB.Node2,
	}
	nodeRemoveComponents(t, setup)
}

func TestNodeProvision_Component_ShopeeDriverManager(t *testing.T) {
	setup := &componentSetup{
		Component:                  &cmp.DriverManagerComponent,
		DependentComponentsVersion: map[string]string{},
		provNode:                   provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	setup.Component.Version = "1.0.2"
	nodeSetupComponent(t, setup)
}

func TestNodeProvision_Component_InstallUOA(t *testing.T) {
	setup := &componentSetup{
		Component:                  &cmp.InstallUOAComponent,
		DependentComponentsVersion: map[string]string{},
		provNode:                   provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	setup.Component.Version = cast.ToString(time.Now().Unix())
	nodeSetupComponent(t, setup)
}

func TestNodeProvision_Component_Apport(t *testing.T) {
	setup := &componentSetup{
		Component: &cmp.ApportComponent,
		provNode:  provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	nodeSetupComponent(t, setup)
}

func TestNodeProvision_Component_FreshCert(t *testing.T) {
	setup := &componentSetup{
		Component:                  &cmp.FreshCertComponent,
		DependentComponentsVersion: map[string]string{},
		provNode:                   provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	setup.Component.Version = cast.ToString(time.Now().Unix())
	nodeSetupComponent(t, setup)
}

func TestNodeProvision_Component_ZookeeperCli(t *testing.T) {
	setup := &componentSetup{
		Component:                  &cmp.ZooKeeperCli,
		DependentComponentsVersion: map[string]string{},
		provNode:                   provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	nodeRemoveComponent(t, setup)
	nodeSetupComponent(t, setup)
}

func TestNodeProvision_ALBMetricsComponent(t *testing.T) {
	setup := &componentSetup{
		Component:                  &cmp.ALBMetricsComponent,
		DependentComponentsVersion: map[string]string{},
		provNode:                   provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	setup.Component.Version = "v1.1.3_720"
	nodeUpgradeComponent(t, setup)
}

func TestNodeProvision_Component_FreshLibs(t *testing.T) {
	setup := &componentSetup{
		Component: &cmp.FreshLibsComponent,
		provNode:  provNode{NodeIP: configs.E2E.ALB.Node2},
	}
	setup.Component.Version = cast.ToString(time.Now().Unix())
	nodeSetupComponent(t, setup)
}

func TestNodeProvision_NodeReversedConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	mockTocex := toc.NewMockTocexAdapter(ctrl)

	haComs := cmp.HAComponents
	randHACom := haComs[gofakeit.Number(0, len(haComs)-1)]

	albComs := cmp.ALBComponents()
	randALBCom := albComs[gofakeit.Number(0, len(albComs)-1)]

	agentInfo := &toclib.AgentInfo{IP: gofakeit.IPv4Address()}
	azInfo := &tocvo.AZ{AZType: "general"}

	t.Run("ok", func(t *testing.T) {
		mockTocex.EXPECT().GetNodeProvision().Return(&toclib.ProvisionNodeConfig{
			Components: []toclib.ProvisionNodeComponent{
				{Type: cmp.ComponentTypeScript, Name: randALBCom.Name},
			},
		}, nil)

		prov := &NodeProvision{
			Agent:  agentInfo,
			HAM:    consts.HAMUnSpec,
			tocex:  mockTocex,
			AZInfo: azInfo,
		}
		provision, err := prov.NodeReversedConfig(cmp.ComponentTypeUnSpec)
		assert.NoError(t, err)
		assert.NotNil(t, provision)
	})

	t.Run("validate_ha_mode_components_failed", func(t *testing.T) {
		mockTocex.EXPECT().GetNodeProvision().Return(&toclib.ProvisionNodeConfig{
			Components: []toclib.ProvisionNodeComponent{
				{Type: cmp.ComponentTypeScript, Name: randHACom.Name},
			},
		}, nil)

		prov := &NodeProvision{
			Agent:  agentInfo,
			HAM:    consts.HAMUnSpec,
			tocex:  mockTocex,
			AZInfo: azInfo,
		}

		provision, err := prov.NodeReversedConfig(cmp.ComponentTypeUnSpec)
		assert.Contains(t, err.Error(), "validate_ha_mode_components_failed")
		assert.Nil(t, provision)
	})
}

func TestNodeProvision_BaseComponents_httpProxy(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	domain := gofakeit.DomainName()
	httpProxy := "http://" + domain + ":10701"

	mockExt2Cluster := sgw.NewMockExt2ClusterAdapter(ctrl)
	mockExt2Cluster.EXPECT().DumpByRZ("sg2").
		Return(ext2vo.ClusterInfos{
			{Domain: domain},
		}, nil).AnyTimes()

	nodeProv := &NodeProvision{
		ext2cluster: mockExt2Cluster,
		AZInfo: &tocvo.AZ{
			AZType: "general",
		},
		Agent: &toclib.AgentInfo{
			Env: "test",
		},
		Meta: &sgwvo.ALBClusterConfigMeta{
			L7ClusterMeta: sgwvo.L7ClusterMeta{
				RZ: "sg2",
			},
		},
	}

	cmps, err := nodeProv.BaseComponents()
	assert.NoError(t, err)

	var flamegraphcmp toclib.ProvisionNodeComponent
	for _, c := range cmps {
		if c.Name == cmp.FlameGraphComponent.Name {
			flamegraphcmp = c

			break
		}
	}

	assert.NotNil(t, flamegraphcmp)
	preScripts := flamegraphcmp.PreCommand
	assert.Contains(t, preScripts, fmt.Sprintf("http.proxy %s", httpProxy))
	assert.Contains(t, preScripts, fmt.Sprintf("https.proxy %s", httpProxy))
}

func TestNodeProvision_HAComponents_with_NodeExporterTextfile(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	// Mock dependencies
	mockExt2cluster := sgw.NewMockExt2ClusterAdapter(ctrl)

	agentInfo := &toclib.AgentInfo{
		IP:  "**************",
		Env: "test",
		AZ:  "sg2",
	}

	// Setup ext2cluster mock
	ext2clusters := ext2vo.ClusterInfos{
		{
			Domain: "proxy.example.com",
		},
	}

	mockExt2cluster.EXPECT().DumpByRZ(gomock.Any()).Return(ext2clusters, nil).AnyTimes()

	t.Run("no_vips_should_return_error", func(t *testing.T) {
		t.Parallel()

		prov := &NodeProvision{
			Agent: agentInfo,
			AZInfo: &tocvo.AZ{
				AZType: consts.AZTypePrivate,
			},
			Meta:        &sgwvo.ALBClusterConfigMeta{},
			ext2cluster: mockExt2cluster,
		}

		components, err := prov.haECMPComponents()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not found any VIPs")
		assert.Empty(t, components)
	})

	t.Run("ext2cluster_error_should_propagate", func(t *testing.T) {
		t.Parallel()

		mockExt2clusterError := sgw.NewMockExt2ClusterAdapter(ctrl)
		mockExt2clusterError.EXPECT().DumpByRZ(gomock.Any()).Return(nil, fmt.Errorf("ext2cluster error"))

		prov := &NodeProvision{
			Agent: agentInfo,
			AZInfo: &tocvo.AZ{
				AZType: consts.AZTypePrivate,
			},
			Meta: &sgwvo.ALBClusterConfigMeta{
				L7ClusterConfig: sgwvo.L7ClusterConfig{
					ECMPBGPVIPs: []string{"*************"},
				},
			},
			ext2cluster: mockExt2clusterError,
		}

		components, err := prov.haECMPComponents()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "fetch_bird_config_proxy_failed")
		assert.Empty(t, components)
	})

	t.Run("private_az_with_ecmp_should_include_node_exporter_textfile", func(t *testing.T) {
		t.Parallel()

		prov := &NodeProvision{
			Agent: agentInfo,
			AZInfo: &tocvo.AZ{
				AZType: consts.AZTypePrivate,
			},
			Meta: &sgwvo.ALBClusterConfigMeta{
				L7ClusterConfig: sgwvo.L7ClusterConfig{
					ECMPBGPVIPs: []string{"*************", "*************"},
				},
			},
			ext2cluster: mockExt2cluster,
		}

		components, err := prov.haECMPComponents()
		assert.NoError(t, err)
		assert.NotEmpty(t, components)

		// Verify that NodeExporterTextfileComponent is included
		var nodeExporterTextfileComponent *toclib.ProvisionNodeComponent
		for i := range components {
			if components[i].Name == cmp.NodeExporterTextfileComponent.Name {
				nodeExporterTextfileComponent = &components[i]

				break
			}
		}

		assert.NotNil(t, nodeExporterTextfileComponent, "NodeExporterTextfileComponent should be included in Private AZ ECMP mode")

		if nodeExporterTextfileComponent != nil {
			assert.Equal(t, cmp.NodeExporterTextfileComponent.Type, nodeExporterTextfileComponent.Type)
			assert.Contains(t, nodeExporterTextfileComponent.Script, "textfile-install bird")
			assert.NotEmpty(t, nodeExporterTextfileComponent.Version)
			assert.Equal(t, tpl.TextfileManagerInstallScript, nodeExporterTextfileComponent.PreCommand)
			assert.Equal(t, tpl.TextfileInstallPostScript, nodeExporterTextfileComponent.PostCommand)
		}

		// Verify other expected components are also included
		expectedComponents := []string{
			cmp.ConfigVIPsComponent.Name,
			cmp.BirdComponent.Name,
			cmp.BirdExporterComponent.Name,
			cmp.NodeExporterTextfileComponent.Name,
		}

		actualComponents := make([]string, len(components))
		for i, comp := range components {
			actualComponents[i] = comp.Name
		}

		for _, expected := range expectedComponents {
			assert.Contains(t, actualComponents, expected, "Component %s should be included", expected)
		}
	})
}
