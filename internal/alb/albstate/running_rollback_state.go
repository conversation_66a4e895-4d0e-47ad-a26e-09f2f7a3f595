package albstate

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type RunningRollbackStateRunner struct {
	*StateBaseRunner
	*HotUpdateRollbackStateRunner
}

func NewRunningRollbackStateRunner() StateRunner {
	return &RunningRollbackStateRunner{
		StateBaseRunner:              &StateBaseRunner{State: consts.RunningRollbackState},
		HotUpdateRollbackStateRunner: &HotUpdateRollbackStateRunner{},
	}
}

func (r *RunningRollbackStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *RunningRollbackStateRunner) Next(alb *v1alpha1.ALB, c *Controller) error {
	return c.CompareAndUpdateALBStatus(alb, consts.RunningState)
}

func (r *RunningRollbackStateRunner) Run(alb *v1alpha1.ALB, c *Controller) error {
	return r.hotUpdateRollback(alb, c)
}
