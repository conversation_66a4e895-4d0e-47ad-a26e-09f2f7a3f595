package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type ProvisionStateRunner struct {
	StateBaseRunner
}

func (r *ProvisionStateRunner) Next(alb *v1alpha1.ALB, c *Controller) error {
	return c.CompareAndUpdateALBStatus(alb, consts.InitialisingState)
}

func NewProvisionStateRunner() StateRunner {
	return &ProvisionStateRunner{
		StateBaseRunner{State: consts.ProvisionState},
	}
}

func (r *ProvisionStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *ProvisionStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	nodeServer, err := r.fetchALBNodeServer(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_alb_node_server_failed")
	}

	var tasks []core.Task
	tasks = append(
		tasks,
		job.NewNodeProvisionTask(alb, nodeServer),
		job.NewUpdateNodeIPTablesTask(alb, nodeServer, ctl.leapClient),
	)

	return r.doRun(alb, ctl.recorder, tasks)
}
