//nolint:nolintlint,dupl
package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type PreOfflineStateRunner struct {
	StateBaseRunner
}

func NewPreOfflineStateRunner() StateRunner {
	return &PreOfflineStateRunner{StateBaseRunner{State: consts.PreOfflineState}}
}

func (r *PreOfflineStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *PreOfflineStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	return ctl.CompareAndUpdateALBStatus(alb, consts.InitialisedState)
}

func (r *PreOfflineStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	nodeServer, err := r.fetchALBNodeServer(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_node_server_failed")
	}

	isPrivateAZ, err := r.isPrivate(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_is_private_failed")
	}

	var tasks []core.Task

	tasks = append(tasks,
		// check ticket offline status
		job.NewCheckTicketStatusTask(alb, ctl.leapClient, consts.RunningState.Status),
		// check alb node status, make sure the alb node status is NOTREADY
		job.NewCheckNodeMgmtStatusTask(alb, consts.NodeStatusNotReady),
		job.NewMarkSGWStateTask(alb, consts.SGWServiceUnitNotReady),
	)

	if isPrivateAZ {
		ham := nodeServer.HAM()
		if ham.IsHAMWithECMP() {
			tasks = append(tasks, job.NewStopBirdTask(alb, ham.Name))
		} else {
			if !ham.IsHAMWithVRRP() {
				log.Logger().WithFields(log.Fields{
					"ip":  alb.Spec.LanIP,
					"ham": ham.Name,
				}).Error("invalid_ha_mode")
			}

			tasks = append(tasks, job.NewStopKeepalivedTask(alb, ham.Name))
		}

		tasks = append(tasks, job.NewMarkSGWStateTask(alb, consts.SGWServiceUnitNotReady))
	}

	tasks = append(tasks,
		job.NewSendSeeEventTask(alb, ctl.leapClient),
	)

	return r.doRun(alb, ctl.recorder, tasks)
}
