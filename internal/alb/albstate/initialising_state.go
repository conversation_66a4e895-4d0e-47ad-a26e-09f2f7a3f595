package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type InitialisingStateRunner struct {
	StateBaseRunner
}

func (r *InitialisingStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	return ctl.CompareAndUpdateALBStatus(alb, consts.PostCheckState)
}

func NewInitialisingStateRunner() StateRunner {
	return &InitialisingStateRunner{StateBaseRunner{State: consts.InitialisingState}}
}

func (r *InitialisingStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *InitialisingStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	nodeServer, err := r.fetchALBNodeServer(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_alb_node_server_failed")
	}

	ok, err := r.isGeneral(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_is_general_failed")
	}

	var tasks []core.Task
	tasks = append(
		tasks,
		job.NewCheckNodeProvisionStatusTask(alb),
		job.NewCheckNodeIPTablesTask(alb, nodeServer),
		job.NewSyncNodeWAFTask(alb),
		job.NewPersistStaticCacheTask(alb),
	)

	if ok {
		// general AZ
		tasks = append(
			tasks,
			job.NewAddNodeIntoClusterTask(alb),
			job.NewUpsertEtcdEndpointTask(alb, nodeServer),
		)
	} else if nodeServer.IsNonStdHAM() {
		tasks = append(
			tasks,
			job.NewRegisterClusterDomainTask(alb, nodeServer),
			job.NewRegisterEtcdProxyDomainTask(alb, nodeServer),
			job.NewAddNodeIntoClusterTask(alb),
			job.NewUpsertClusterDomainTask(alb, nodeServer),
			job.NewUpsertClusterIPsTask(alb, nodeServer),
			job.NewUpsertEtcdEndpointTask(alb, nodeServer),
		)
	}

	tasks = append(
		tasks,
		job.NewRegisterNodeKVTask(alb),
	)

	return r.doRun(alb, ctl.recorder, tasks)
}
