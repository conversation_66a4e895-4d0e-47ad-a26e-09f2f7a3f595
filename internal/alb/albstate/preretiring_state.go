package albstate

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type PreRetiringStateRunner struct {
	StateBaseRunner
}

func NewPreRetiringStateRunner() StateRunner {
	return &PreRetiringStateRunner{
		StateBaseRunner{State: consts.PreRetiringState},
	}
}

func (r *PreRetiringStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *PreRetiringStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	return ctl.CompareAndUpdateALBStatus(alb, consts.RetiringState)
}

func (r *PreRetiringStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	var tasks []core.Task

	tasks = append(tasks,
		job.NewCheckTicketStatusTask(alb, ctl.leapClient, consts.InitialisedState.Status),
	)

	return r.doRun(alb, ctl.recorder, tasks)
}
