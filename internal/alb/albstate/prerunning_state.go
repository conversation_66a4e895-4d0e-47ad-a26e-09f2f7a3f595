//nolint:nolintlint,dupl
package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type PreRunningStateRunner struct {
	StateBaseRunner
}

func NewPreRunningStateRunner() StateRunner {
	return &PreRunningStateRunner{StateBaseRunner{
		State: consts.PreRunningState,
	}}
}

func (r *PreRunningStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *PreRunningStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	return ctl.CompareAndUpdateALBStatus(alb, consts.RunningState)
}

func (r *PreRunningStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	nodeServer, err := r.fetchALBNodeServer(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_node_server_failed")
	}

	isGeneralAZ, err := r.isGeneral(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_is_general_failed")
	}

	var tasks []core.Task

	tasks = append(tasks,
		job.NewCheckTicketStatusTask(alb, ctl.leapClient, consts.InitialisedState.Status),
		job.NewCheckNodeMgmtStatusTask(alb, consts.NodeStatusReady),
	)

	if isGeneralAZ {
		tasks = append(tasks,
			job.NewUpdateServerStateTask(alb, nodeServer, consts.StateOperating),
		)
	} else {
		tasks = append(tasks,
			// set /etc/sgw/ready for bird and keepalived
			job.NewMarkSGWStateTask(alb, consts.SGWServiceUnitReady),
		)

		ham := nodeServer.HAM()
		if ham.IsHAMWithECMP() {
			tasks = append(tasks,
				job.NewStartBirdTask(alb, ham.Name),
				job.NewCheckNodePeerTask(alb, nodeServer),
			)
		} else {
			if !ham.IsHAMWithVRRP() {
				// log error and fallback to start keepalived
				log.Logger().WithFields(log.Fields{
					"ip":  alb.Spec.LanIP,
					"ham": ham.Name,
				}).Error("invalid_ha_mode")
			}

			tasks = append(tasks, job.NewStartKeepalivedTask(alb, ham.Name))
		}
	}

	tasks = append(tasks,
		job.NewSendSeeEventTask(alb, ctl.leapClient),
	)

	return r.doRun(alb, ctl.recorder, tasks)
}
