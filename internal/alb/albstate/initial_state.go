package albstate

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type InitialStateRunner struct {
	StateBaseRunner
}

func NewInitialStateRunner() StateRunner {
	return &InitialStateRunner{StateBaseRunner{State: consts.InitialisedState}}
}

func (r *InitialStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *InitialStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	switch alb.Spec.ALBNode.HotUpdateConfig.Type {
	case "", consts.CompleteTocexJob:
		return nil
	case consts.RollbackTocexItem:
		return ctl.CompareAndUpdateALBStatus(alb, consts.InitialisedRollbackState)
	case consts.UpdateTocexItem, consts.DeleteTocexItem, consts.AddTocexItem:
		return ctl.CompareAndUpdateALBStatus(alb, consts.InitialisedHotUpdateState)
	default:
		return nil
	}
}

func (r *InitialStateRunner) Run(_ *v1alpha1.ALB, _ *Controller) error {
	return nil
}
