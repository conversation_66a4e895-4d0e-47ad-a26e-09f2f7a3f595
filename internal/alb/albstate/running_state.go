package albstate

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type RunningStateRunner struct {
	StateBaseRunner
}

func NewRunningStateRunner() StateRunner {
	return &RunningStateRunner{StateBaseRunner{State: consts.RunningState}}
}

func (r *RunningStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *RunningStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	if alb.Spec.ALBNode.HotUpdateConfig.Type == "" ||
		alb.Spec.ALBNode.HotUpdateConfig.Type == consts.CompleteTocexJob {
		return nil
	}

	switch alb.Spec.ALBNode.HotUpdateConfig.Type {
	case "", consts.CompleteTocexJob:
		return nil
	case consts.RollbackTocexItem:
		return ctl.CompareAndUpdateALBStatus(alb, consts.RunningRollbackState)
	case consts.DeleteTocexItem, consts.UpdateTocexItem, consts.AddTocexItem:
		return ctl.CompareAndUpdateALBStatus(alb, consts.RunningHotUpdateState)
	default:
		return nil
	}
}

func (r *RunningStateRunner) Run(_ *v1alpha1.ALB, _ *Controller) error {
	return nil
}
