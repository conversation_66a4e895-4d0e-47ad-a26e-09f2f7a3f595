package albstate

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/client-go/tools/record"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"git.garena.com/shopee/go-shopeelib/json"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsinf/opscah"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

var (
	// store those finished task
	stateRunnerResult sync.Map
	stateRunner       sync.Map
)

type StateRunner interface {
	Register()
	Current() consts.State
	Run(alb *v1alpha1.ALB, c *Controller) error
	Disable() bool
	Next(alb *v1alpha1.ALB, c *Controller) error
}

type StateBaseRunner struct {
	State consts.State
}

func (r *StateBaseRunner) Disable() bool {
	return configs.ALB.IsStateDisable(r.State.Status)
}

func (r *StateBaseRunner) Current() consts.State {
	return r.State
}

func (r *StateBaseRunner) doRun(alb *v1alpha1.ALB, recorder record.EventRecorder, tasks []core.Task) error {
	ctx := context.Background()
	state := r.Current().Status
	keyState := fmt.Sprintf("%s-%s", alb.Name, state)
	if _, loaded := stateRunner.LoadOrStore(keyState, struct{}{}); !loaded {
		// hset albname state tasks
		names := slice.Map(tasks, func(_ int, task core.Task) string {
			return task.Name()
		})

		val, _ := json.Marshal(names)
		cmd := opscah.ElasticRedis.HSet(ctx, alb.Name, state, val)
		if err := cmd.Err(); err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"alb":   alb.Name,
				"state": r.Current().Status,
				"tasks": names,
			}).Error("store_tasks_failed")
		}
	}

	for i := 0; i < len(tasks); i++ {
		task := tasks[i]
		keyTask := fmt.Sprintf("%s-%s-%s", alb.Name, state, task.Name())
		if _, ok := stateRunnerResult.Load(keyTask); !ok {
			// hset albname-state task result
			if err := task.Run(); err != nil {
				recorder.Event(alb, corev1.EventTypeWarning, consts.ResultFailed, err.Error())

				val, _ := json.Marshal(&sgwvo.TaskResult{
					Task:      task.Name(),
					Success:   false,
					Reason:    err.Error(),
					UpdatedAt: time.Now().Unix(),
				})

				cmd := opscah.ElasticRedis.HSet(ctx, keyState, task.Name(), val)
				if err := cmd.Err(); err != nil {
					log.Logger().WithError(err).WithFields(log.Fields{
						"alb":   alb.Name,
						"state": r.Current().Status,
						"task":  task.Name(),
					}).Error("store_task_result_failed")
				}

				return errors.WithMessage(err, fmt.Sprintf("run_task_%s_failed", task.Name()))
			}

			stateRunnerResult.Store(keyTask, struct{}{})
			message := fmt.Sprintf("%s:%s", state, task.Name())
			recorder.Event(alb, corev1.EventTypeNormal, consts.ResultSuccess, message)
			val, _ := json.Marshal(&sgwvo.TaskResult{
				Task:      task.Name(),
				Success:   true,
				UpdatedAt: time.Now().Unix(),
			})
			opscah.ElasticRedis.HSet(ctx, keyState, task.Name(), val)
		}
	}

	return nil
}

func (r *StateBaseRunner) fetchALBNodeServer(alb *v1alpha1.ALB) (*tocvo.ALBNodeServer, error) {
	server := toc.NewServerAdapter(string(alb.UID))
	serverVariables, err := server.GetALBServerVariables(alb.Spec.LanIP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"az":      alb.Spec.ALBNode.Zone,
			"uid":     alb.UID,
			"cluster": alb.Spec.ALBNode.ALBName,
			"state":   r.Current().Status,
		}).Error("fetch_server_group_var_failed")

		return nil, errors.WithMessage(err, "fetch_server_group_var_failed")
	}

	cluster := sgw.NewALBCluster(string(alb.UID))
	cfgMeta, err := cluster.Meta(alb.Spec.ALBNode.ClusterUUID)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"az":      alb.Spec.ALBNode.Zone,
			"uid":     alb.UID,
			"cluster": alb.Spec.ALBNode.ALBName,
			"state":   r.Current().Status,
		}).Error("fetch_cluster_meta_failed")

		return nil, errors.WithMessage(err, "fetch_cluster_meta_failed")
	}

	serverV3 := toc.NewServerV3Adapter(string(alb.UID))
	tags, err := serverV3.Tags(alb.Spec.LanIP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"az":      alb.Spec.ALBNode.Zone,
			"uid":     alb.UID,
			"cluster": alb.Spec.ALBNode.ALBName,
			"state":   r.Current().Status,
		}).Error("fetch_server_tags_failed")

		return nil, errors.WithMessage(err, "fetch_server_tags_failed")
	}
	svrV3, err := serverV3.Server(alb.Spec.LanIP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"az":      alb.Spec.ALBNode.Zone,
			"uid":     alb.UID,
			"cluster": alb.Spec.ALBNode.ALBName,
			"state":   r.Current().Status,
		}).Error("fetch_server_failed")

		return nil, errors.WithMessage(err, "fetch_server_failed")
	}

	node := toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec))
	agent, err := node.GetAgent()
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"az":      alb.Spec.ALBNode.Zone,
			"uid":     alb.UID,
			"cluster": alb.Spec.ALBNode.ALBName,
			"state":   r.Current().Status,
		}).Error("fetch_agent_error")

		return nil, errors.WithMessage(err, "fetch_agent_error")
	}

	meta := toc.NewMetaAdapter(string(alb.UID))
	segment, err := meta.Segment(agent.AZv2, agent.SegmentName)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"az":      alb.Spec.ALBNode.Zone,
			"uid":     alb.UID,
			"cluster": alb.Spec.ALBNode.ALBName,
			"state":   r.Current().Status,
		}).Error("fetch_segment_error")

		return nil, errors.WithMessage(err, "fetch_segment_error")
	}

	serverV3vo := tocvo.ServerV3(*svrV3)
	nodeServer := &tocvo.ALBNodeServer{
		Cluster:        svrV3.Cluster,
		ServerGroupVar: serverVariables, // todo: remove by sgwvo.ALBClusterConfigMeta
		Meta:           cfgMeta,
		Agent:          agent,
		Tags:           tags,
		ServerV3:       &serverV3vo,
		Segment:        segment,
	}

	return nodeServer, nil
}

func (r *StateBaseRunner) isGeneral(alb *v1alpha1.ALB) (bool, error) {
	meta := toc.NewMetaAdapter(string(alb.UID))
	az, err := meta.AZ(alb.Spec.ALBNode.Zone)
	if err != nil {
		return false, errors.WithMessage(err, "fetch_az_failed")
	}

	if az.IsPrivate() {
		return false, nil
	}

	segment := alb.Spec.ALBNode.Segment
	if segment == "" {
		err := errors.New("alb_node_segment_is_empty")
		log.Logger().WithFields(log.Fields{
			"alb_name": alb.Name,
			"node_ip":  alb.Spec.LanIP,
			"az":       alb.Spec.ALBNode.Zone,
		}).WithError(err).Error("segment_is_empty_when_checking_is_general")

		return false, err
	}

	if strings.EqualFold(segment, consts.SegmentGeneral) {
		return true, nil
	}

	return false, nil
}

func (r *StateBaseRunner) isPrivate(alb *v1alpha1.ALB) (bool, error) {
	meta := toc.NewMetaAdapter(string(alb.UID))
	az, err := meta.AZ(alb.Spec.ALBNode.Zone)
	if err != nil {
		return false, errors.WithMessage(err, "fetch_az_failed")
	}

	if az.IsPrivate() {
		return true, nil
	}

	segment := alb.Spec.ALBNode.Segment
	if segment == "" {
		err := errors.New("alb_node_segment_is_empty")
		log.Logger().WithFields(log.Fields{
			"alb_name": alb.Name,
			"node_ip":  alb.Spec.LanIP,
			"az":       alb.Spec.ALBNode.Zone,
		}).WithError(err).Error("segment_is_empty_when_checking_is_private")

		return false, err
	}

	if !strings.EqualFold(segment, consts.SegmentGeneral) {
		return true, nil
	}

	return false, nil
}

func FetchStateRunnerResult() []string {
	var keys []string
	stateRunnerResult.Range(func(key, value any) bool {
		keys = append(keys, cast.ToString(key))

		return true
	})

	return keys
}

func FreshStateRunnerResult(alb string, state string) {
	prefix := fmt.Sprintf("%s-%s", alb, state)
	var keys []string
	stateRunnerResult.Range(func(key, value any) bool {
		if strings.HasPrefix(cast.ToString(key), prefix) {
			keys = append(keys, cast.ToString(key))
		}

		return true
	})

	for _, key := range keys {
		stateRunnerResult.Delete(key)
	}
}

func PurgeStateRunnerResult(alb *v1alpha1.ALB) {
	var keys []string
	stateRunnerResult.Range(func(key, value any) bool {
		if strings.HasPrefix(cast.ToString(key), alb.Name) {
			keys = append(keys, cast.ToString(key))
		}

		return true // must return true then continue
	})

	for _, key := range keys {
		stateRunnerResult.Delete(key)
	}

	log.Logger().WithFields(log.Fields{
		"ip":      alb.Spec.LanIP,
		"idc":     alb.Spec.ALBNode.IDC,
		"env":     alb.Spec.ALBNode.Env,
		"cluster": alb.Spec.ALBNode.ALBName,
		"uid":     alb.UID,
		"task":    len(keys),
	}).Info("purge_state_runner_result")
}
