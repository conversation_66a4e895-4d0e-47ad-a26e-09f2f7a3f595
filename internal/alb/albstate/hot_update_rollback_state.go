package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type HotUpdateRollbackStateRunner struct {
	StateBaseRunner
}

func (r *HotUpdateRollbackStateRunner) hotUpdateRollback(alb *v1alpha1.ALB, ctl *Controller) error {
	var tasks []core.Task
	tasks = append(tasks,
		job.NewHotUpdateRollbackTocexTask(alb),
		job.NewCheckHotUpdateRollbackStatusTask(alb),
		job.NewHotUpdateCleanTask(alb))

	err := r.doRun(alb, ctl.recorder, tasks)
	if err != nil {
		return errors.WithMessage(err, "hot_update_rollback_run_failed")
	}

	// unlikely reach here
	if alb.Spec.ALBNode.HotUpdateConfig.Type != consts.CompleteTocexJob {
		alb.Spec.ALBNode.HotUpdateConfig.Type = consts.CompleteTocexJob
	}

	return nil
}
