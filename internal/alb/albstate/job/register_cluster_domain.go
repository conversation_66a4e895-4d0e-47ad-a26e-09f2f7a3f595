package job

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/dns/dnsvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type RegisterClusterDomainTask struct {
	*ClusterDomainTask

	Agent   *TaskAgent
	DNSAPI  sgw.DNSAPIAdapter
	HAM     consts.HighAvailableMode
	Cluster sgw.L7ClusterAdapter
}

func NewRegisterClusterDomainTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *RegisterClusterDomainTask {
	if svr == nil {
		log.Logger().WithFields(log.Fields{
			"uid": string(alb.UID),
		}).Error("alb_node_server_is_nil")

		return nil
	}

	return &RegisterClusterDomainTask{
		Agent:             NewTaskAgent(alb),
		ClusterDomainTask: NewClusterDomainTask(alb, svr),
		DNSAPI:            sgw.NewDNSAPIAdapter(string(alb.UID)),
		HAM:               svr.HAM(),
		Cluster:           sgw.NewALBClusterAdapter(string(alb.UID)),
	}
}

func (t *RegisterClusterDomainTask) Description() string {
	return ""
}

func (t *RegisterClusterDomainTask) Name() string {
	return AddClusterDomainTaskName
}

func (t *RegisterClusterDomainTask) wanVIPs() []string {
	var ips []string

	if t.HAM.Code == consts.HAMNonStd1.Code {
		ips = t.Meta.KeepalivedWANVIPs()
	} else {
		ips = t.Meta.ECMPBGPWanVIPs
	}

	/*
		log.Logger().WithFields(log.Fields{
			"ips": ips,
		}).Info("wan_vips")
	*/

	return ips
}

func (t *RegisterClusterDomainTask) upsertPublicCentral() error {
	domain, err := t.clusterDomain()
	if err != nil {
		return errors.WithMessage(err, "get_cluster_domain_failed")
	}

	wanVIPs := t.wanVIPs()
	if len(wanVIPs) == 0 {
		log.Logger().WithFields(log.Fields{
			"ip":     t.Agent.IP,
			"domain": domain,
		}).Warn("no_wan_vips_for_domain")

		// NB: don't return error here, just skip
		return nil
	}

	value := strings.Join(wanVIPs, "\n")

	req := &sgw.DNSChangeRequest{
		Action:      "UPSERT",
		Domain:      domain,
		DNSType:     "public",
		EntryType:   "a",
		Value:       value,
		ServiceName: configs.ALB.Nginx().Name,
		Comment:     fmt.Sprintf("alb cluster %s add by %s", t.Agent.ALBName, configs.Mgmt.Ops.Bot.User),
		SingleResp:  false,
	}

	_, err = t.DNSAPI.ChangeRecord(req)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"record":  wanVIPs,
			"domain":  domain,
			"value":   value,
		}).Error("upsert_public_central_dns_failed")

		return errors.WithMessage(err, "upsert_public_central_dns_failed")
	}

	return nil
}

func (t *RegisterClusterDomainTask) upsertPrivateCentral() error {
	domain, err := t.clusterDomain()
	if err != nil {
		return errors.WithMessage(err, "get_cluster_domain_failed")
	}

	wanVIPs := t.wanVIPs()
	if len(wanVIPs) == 0 {
		log.Logger().WithFields(log.Fields{
			"ip":     t.Agent.IP,
			"domain": domain,
		}).Warn("no_wan_vips_for_domain")

		// NB: don't return error here, just skip
		return nil
	}

	value := strings.Join(wanVIPs, "\n")

	req := &sgw.DNSChangeRequest{
		Action:      "UPSERT",
		Domain:      domain,
		DNSType:     "private",
		EntryType:   "a",
		GeoLocation: "central",
		Value:       value,
		ServiceName: configs.ALB.Nginx().Name,
		Comment:     fmt.Sprintf("alb cluster %s add by %s", t.Agent.ALBName, configs.Mgmt.Ops.Bot.User),
		SingleResp:  false,
	}

	_, err = t.DNSAPI.ChangeRecord(req)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"record":  t.Meta.ECMPBGPWanVIPs,
			"domain":  domain,
		}).Error("upsert_private_central_dns_failed")

		return errors.WithMessage(err, "upsert_private_central_dns_failed")
	}

	return nil
}

func (t *RegisterClusterDomainTask) upsertPrivateLocal() error {
	domain, err := t.clusterDomain()
	if err != nil {
		return errors.WithMessage(err, "get_cluster_domain_failed")
	}

	vips := t.Meta.ECMPBGPVIPs
	if len(vips) == 0 {
		log.Logger().WithFields(log.Fields{
			"ip":     t.Agent.IP,
			"domain": domain,
		}).Warn("no_private_vips_for_domain")

		// NB: don't return error here, just skip
		return nil
	}

	req := &sgw.DNSChangeRequest{
		Action:      "UPSERT",
		Domain:      domain,
		DNSType:     "private",
		EntryType:   "a",
		GeoLocation: t.Meta.RZ,
		Value:       strings.Join(vips, "\n"),
		ServiceName: configs.ALB.Nginx().Name,
		Comment:     fmt.Sprintf("alb cluster %s add by %s", t.Agent.ALBName, configs.Mgmt.Ops.Bot.User),
		SingleResp:  false,
	}

	_, err = t.DNSAPI.ChangeRecord(req)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"record":  t.Meta.ECMPBGPVIPs,
			"domain":  domain,
		}).Error("upsert_private_geo_dns_failed")

		return errors.WithMessage(err, "upsert_private_geo_dns_failed")
	}

	return nil
}

func (t *RegisterClusterDomainTask) upsertPrivateDomain(dom *dnsvo.Domain) error {
	var privateCentralFound bool
	var privateLocalFound bool

	if dom.PrivateRecordCount() > 0 {
		for _, r := range dom.Records {
			if r.IsPrivate() {
				// TODO check RecordType and values?
				if r.IsCentral() {
					privateCentralFound = true
				} else if strings.EqualFold(r.GeoLocation, t.Meta.RZ) {
					privateLocalFound = true
				}
			}
		}
	}

	if !privateCentralFound && t.NodeServer.IsHAMWithWAN() {
		if err := t.upsertPrivateCentral(); err != nil {
			return errors.WithMessage(err, "upsert_private_central_record_failed")
		}
	}

	if !privateLocalFound && t.NodeServer.IsHAMWithLAN() {
		if err := t.upsertPrivateLocal(); err != nil {
			return errors.WithMessage(err, "upsert_private_local_record_failed")
		}
	}

	return nil
}

func (t *RegisterClusterDomainTask) addDomain() error {
	if t.NodeServer.IsHAMWithWAN() {
		if err := t.upsertPublicCentral(); err != nil {
			return errors.WithMessage(err, "upsert_public_central_record_failed")
		}

		if err := t.upsertPrivateCentral(); err != nil {
			return errors.WithMessage(err, "upsert_private_central_record_failed")
		}
	}

	if t.NodeServer.IsHAMWithLAN() {
		if err := t.upsertPrivateLocal(); err != nil {
			return errors.WithMessage(err, "upsert_private_local_record_failed")
		}
	}

	return nil
}

func (t *RegisterClusterDomainTask) upsertDomain() error {
	domain, err := t.clusterDomain()
	if err != nil {
		return errors.WithMessage(err, "get_cluster_domain_failed")
	}
	dom, err := t.DNSAPI.Query(domain)
	if err != nil {
		if strings.Contains(err.Error(), sgw.ErrDomainNotFound.Error()) {
			if err := t.addDomain(); err != nil {
				return errors.WithMessage(err, "add_domain_failed")
			}

			return nil
		}

		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"domain":  domain,
		}).Error("fetch_domain_record_from_dns_api_failed")

		return errors.WithMessage(err, "fetch_domain_record_from_dns_api_failed")
	}

	if t.NodeServer.IsHAMWithWAN() && !t.skipLiveEnvDNSUpdate(dom) {
		if err = t.upsertPublicCentral(); err != nil {
			return errors.WithMessage(err, "upsert_public_central_record_failed")
		}
	}

	if t.NodeServer.IsHAMWithLAN() {
		return t.upsertPrivateDomain(dom)
	}

	return nil
}

// Run
/*
for DMZ-inbound HAM NonStd3(WAN<-->LAN VIP)
	1. fetch the domain and WAN VIP from group_var
	2. fetch domain records from DNS-API
	3. if found both Private and Public records, then return
	4. only add not found dns_type
	5. private to LAN VIPs with geo local idc
	6. public to WAN VIPs with central
	7. private to WAN VIPs with central

for DMZ-inbound HAM NonStd2(WAN VIP)
    1. only register public+private WAN with central

for int HAM NonStd4(LAN VIP)
    1. only register private LAN with geo
*/
func (t *RegisterClusterDomainTask) Run() error {
	if !t.NodeServer.IsNetworkZoneSupported() {
		return fmt.Errorf("unsupported network zone %s", t.NodeServer.NetworkZone())
	}

	domain, err := t.clusterDomain()
	if err != nil {
		return errors.WithMessage(err, "get_cluster_domain_failed")
	}

	if t.NodeServer.IsHAMWithWAN() {
		if t.HAM.Code > consts.HAMNonStd1.Code && len(t.Meta.ECMPBGPWanVIPs) == 0 {
			return fmt.Errorf("not_found_any_WAN_VIPs_for %s", domain)
		}

		if t.HAM.Code == consts.HAMNonStd1.Code {
			if err := t.checkKeepalivedVIPs(); err != nil {
				return errors.WithMessage(err, "check_keepalived_vips_failed")
			}
		}
	}

	if t.NodeServer.IsHAMWithLAN() && len(t.Meta.ECMPBGPVIPs) == 0 {
		return fmt.Errorf("not_found_any_LAN_VIPs_for %s", domain)
	}

	return t.upsertDomain()
}

func (t *RegisterClusterDomainTask) skipLiveEnvDNSUpdate(dom *dnsvo.Domain) bool {
	if configs.Mgmt.IsNonLiveEnv() {
		return false
	}

	if dom.PublicRecordCount() > 0 {
		// TODO: remove this condition after new records available checker is ready
		return true
	}

	// NB: don't update live env DNS if the new records are a subset of the existing records
	return !dom.IsPublicRecordSubsetOf(t.wanVIPs())
}

func (t *RegisterClusterDomainTask) checkKeepalivedVIPs() error {
	clusterUUID := t.Agent.ClusterUUID
	if clusterUUID == "" {
		return fmt.Errorf("cluster uuid is empty")
	}

	clusterInfo, err := t.Cluster.GetByUUID(clusterUUID)
	if err != nil {
		return errors.WithMessage(err, "get_az_info_failed cluster uuid="+clusterUUID)
	}

	domain, err := t.clusterDomain()
	if err != nil {
		return errors.WithMessage(err, "get_cluster_domain_failed")
	}

	if clusterInfo.IsWAN() {
		if !t.Meta.IsKeepalivedHasWANVIP() {
			return fmt.Errorf("not_found_any_WAN_VIPs_for %s", domain)
		}

		return nil
	}

	// is lan
	if !t.Meta.IsKeepalivedHasLANVIP() {
		return fmt.Errorf("not_found_any_LAN_VIPs_for %s", domain)
	}

	return nil
}
