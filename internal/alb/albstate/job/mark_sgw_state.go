package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type MarkSGWStateTask struct {
	sgwState string

	ServiceState sgw.ServiceStateAdapter
	Agent        *TaskAgent
}

func NewMarkSGWStateTask(alb *v1alpha1.ALB, sgwState string) *MarkSGWStateTask {
	return &MarkSGWStateTask{
		sgwState: sgwState,

		Agent:        NewTaskAgent(alb),
		ServiceState: sgw.NewServiceState(tocvo.NewTocNode(&alb.Spec)),
	}
}

func (t *MarkSGWStateTask) Description() string {
	return ""
}

func (t *MarkSGWStateTask) Name() string {
	return MarkSGWStateTaskName
}

func (t *MarkSGWStateTask) Run() error {
	var err error
	switch t.sgwState {
	case consts.SGWServiceUnitReady:
		err = t.ServiceState.MarkReady()
	case consts.SGWServiceUnitNotReady:
		err = t.ServiceState.MarkNotReady()
	default:
		return errors.New("invalid sgw state")
	}

	if err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).WithError(err).Error("mark_sgw_ready_failed")

		return errors.WithMessage(err, "mark_sgw_ready_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":  t.Agent.IP,
		"env": t.Agent.Env,
		"uid": t.Agent.UID,
		"az":  t.Agent.AZ,
	}).Info("mark_sgw_state_success")

	return nil
}
