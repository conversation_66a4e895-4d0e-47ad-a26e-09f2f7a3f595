package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
)

type AddNodeIntoClusterTask struct {
	Agent *TaskAgent
}

func NewAddNodeIntoClusterTask(alb *v1alpha1.ALB) *AddNodeIntoClusterTask {
	return &AddNodeIntoClusterTask{
		Agent: NewTaskAgent(alb),
	}
}

func (t *AddNodeIntoClusterTask) Description() string {
	return ""
}

func (t *AddNodeIntoClusterTask) Name() string {
	return AddALBIntoClusterTaskName
}

func (t *AddNodeIntoClusterTask) Run() error {
	cluster := sgw.NewALBClusterAdapter(t.Agent.UID)
	err := cluster.AddNode(t.Agent.IP, t.Agent.ALBName)
	if err != nil {
		log.Logger().WithError(err).WithField("ip", t.Agent.IP).WithField("cluster", t.Agent.ALBName).
			WithField("idc", t.Agent.IDC).WithField("env", t.Agent.Env).
			Error("add_alb_node_into_cluster_error")

		return errors.WithMessage(err, "add_alb_node_into_cluster_error")
	}

	log.Logger().WithField("ip", t.Agent.IP).WithField("idc", t.Agent.IDC).WithField("env", t.Agent.Env).
		WithField("cluster", t.Agent.ALBName).
		Info("add_alb_node_into_cluster_success")

	return nil
}
