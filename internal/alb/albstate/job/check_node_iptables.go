package job

import (
	"github.com/spf13/cast"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type CheckNodeIPTablesTask struct {
	Agent *TaskAgent
	Job   *TocJobTask
	JobID int
}

func NewCheckNodeIPTablesTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *CheckNodeIPTablesTask {
	var jobID int
	if alb.Spec.ALBNode.TocAttributes != nil {
		jobID = cast.ToInt(alb.Spec.ALBNode.TocAttributes["update_iptables_job_id"])
	}

	return &CheckNodeIPTablesTask{
		Agent: NewTaskAgent(alb),
		Job:   NewTocJobTask(svr.Cluster, alb),
		JobID: jobID,
	}
}

func (t *CheckNodeIPTablesTask) Description() string {
	return ""
}

func (t *CheckNodeIPTablesTask) Name() string {
	return CheckNodeIPTablesTaskName
}

func (t *CheckNodeIPTablesTask) Run() error {
	if err := t.Job.CheckResult(t.JobID); err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).WithError(err).Error("update_iptables_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":  t.Agent.IP,
		"env": t.Agent.Env,
		"uid": t.Agent.UID,
		"az":  t.Agent.AZ,
	}).Info("update_iptables_success")

	return nil
}
