package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
)

type CheckNodeStatusTask struct {
	Agent   *TaskAgent
	Sniffer Sniffer
}

func NewCheckNodeStatusTask(alb *v1alpha1.ALB) *CheckNodeStatusTask {
	return &CheckNodeStatusTask{
		Agent:   NewTaskAgent(alb),
		Sniffer: NewALBSniffer(),
	}
}

func (t *CheckNodeStatusTask) Description() string {
	return ""
}

func (t *CheckNodeStatusTask) Name() string {
	return CheckNodeStatusTaskName
}

func (t *CheckNodeStatusTask) Run() error {
	if configs.ALB.IsLiveEnv() && t.Agent.IsNonLiveEnv() {
		// skip nonlive because cannot access from ops to nonlive
		return nil
	}

	_, err := t.Sniffer.Probe(t.Agent.IP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("check_alb_status_error")

		return errors.WithMessage(err, "probe_node_status_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"az":      t.Agent.AZ,
		"env":     t.Agent.Env,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
	}).Info("check_alb_status_success")

	return nil
}
