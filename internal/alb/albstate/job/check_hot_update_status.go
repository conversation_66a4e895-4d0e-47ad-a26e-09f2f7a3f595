package job

import (
	"crypto/md5"
	"fmt"
	"strings"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type CheckHotUpdateStatusTask struct {
	CheckHotUpdateTask
}

func NewCheckHotUpdateStatusTask(alb *v1alpha1.ALB) *CheckHotUpdateStatusTask {
	task := &CheckHotUpdateStatusTask{}
	task.components = make(map[string]*toclib.ProvisionNodeComponent)
	task.templates = make(map[string]*toclib.ProvisionNodeTemplate)
	task.ALB = alb
	task.Node = toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec))

	return task
}

func (t *CheckHotUpdateStatusTask) Description() string {
	return ""
}

func (t *CheckHotUpdateStatusTask) Name() string {
	return CheckHotUpdateStatusTaskName
}

func (t *CheckHotUpdateStatusTask) deleted() error {
	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.Components {
		if _, ok := t.components[item.Name]; ok {
			err := fmt.Errorf("component_name_%s_must_delete_wait_please", item.Name)
			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).WithField("idc", t.ALB.Spec.ALBNode.IDC).
				WithField("env", t.ALB.Spec.ALBNode.Env).
				WithField("type", consts.DeleteTocexItem).Error("wait_component_to_be_deleted")

			return errors.WithMessage(err, "component_found")
		}
	}

	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.Templates {
		if _, ok := t.templates[item.Path]; ok {
			err := fmt.Errorf("template_path_%s_must_delete_wait_please", item.Path)
			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).WithField("idc", t.ALB.Spec.ALBNode.IDC).
				WithField("env", t.ALB.Spec.ALBNode.Env).
				WithField("type", consts.DeleteTocexItem).Error("wait_template_to_be_deleted")

			return errors.WithMessage(err, "template_found")
		}
	}

	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.Templates {
		if t.ALB.Spec.ALBNode.TemplateMap == nil {
			t.ALB.Spec.ALBNode.TemplateMap = make(map[string]string)
		}

		delete(t.ALB.Spec.ALBNode.TemplateMap, item.Path)
	}

	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.Components {
		if t.ALB.Spec.ALBNode.ComponentMap == nil {
			t.ALB.Spec.ALBNode.ComponentMap = make(map[string]string)
		}

		delete(t.ALB.Spec.ALBNode.ComponentMap, item.Name)

		name := item.Name
		if strings.EqualFold(item.Type, cmp.ComponentTypeDocker) {
			name = item.ServiceName
		}
		albvo.UpdateALBComponentVersion(&t.ALB.Spec.ALBNode, name, "")
	}

	t.ALB.Spec.ALBNode.HotUpdateConfig.Components = nil
	t.ALB.Spec.ALBNode.HotUpdateConfig.Templates = nil
	t.ALB.Spec.ALBNode.HotUpdateConfig.Type = consts.CompleteTocexJob

	log.Logger().WithField("ip", t.ALB.Spec.LanIP).WithField("type", consts.DeleteTocexItem).
		WithField("idc", t.ALB.Spec.ALBNode.IDC).WithField("env", t.ALB.Spec.ALBNode.Env).
		Info("check_hot_update_status_success")

	return nil
}

func (t *CheckHotUpdateStatusTask) upsert() error {
	if err := t.checkProvision(); err != nil {
		return errors.WithMessage(err, "provision_failed")
	}

	t.reserveComponentVersion()

	t.ALB.Spec.ALBNode.HotUpdateConfig.Components = nil
	t.ALB.Spec.ALBNode.HotUpdateConfig.Templates = nil
	t.ALB.Spec.ALBNode.HotUpdateConfig.Type = consts.CompleteTocexJob

	log.Logger().WithField("ip", t.ALB.Spec.LanIP).WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
		Info("check_hot_update_status_success")

	return nil
}

func (t *CheckHotUpdateStatusTask) checkProvision() error {
	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.Components {
		name := item.Name
		if item.Type == cmp.ComponentTypeDocker {
			name = item.ServiceName
		}

		if component, ok := t.components[name]; !ok {
			err := fmt.Errorf("component_name_%s_must_exist_wait_please",
				name)

			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				WithField("idc", t.ALB.Spec.ALBNode.IDC).WithField("env", t.ALB.Spec.ALBNode.Env).
				Error("wait_component_provision")

			return errors.WithMessage(err, "component_not_found")
		} else if component.ErrMsg != "" {
			err := fmt.Errorf(component.ErrMsg)

			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).WithField("idc", t.ALB.Spec.ALBNode.IDC).
				WithField("env", t.ALB.Spec.ALBNode.Env).
				Error("wait_component_provision")

			return errors.WithMessage(err, "component_failed")
		} else if item.Version != component.Version {
			// nginx-shopee it must be inconsistent, cause upgrade-nginx component
			if strings.EqualFold(item.Name, cmp.NginxComponent.Name) {
				continue
			}

			err := fmt.Errorf("component_name_%s_version_%s_must_equal_to_version_%s_please_wait",
				name, component.Version, item.Version)
			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).WithField("component_name", item.Name).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				WithField("current_version", component.Version).WithField("target_version", item.Version).
				Error("wait_component_to_be_updated_or_added")

			return errors.WithMessage(err, "component_version_inconsistent")
		}

		if strings.EqualFold(item.Name, cmp.UpgradeNginxComponent.Name) {
			albvo.UpdateALBComponentVersion(&t.ALB.Spec.ALBNode, cmp.NginxComponent.Name, t.nginxVersion())
		} else {
			albvo.UpdateALBComponentVersion(&t.ALB.Spec.ALBNode, name, item.Version)
		}
	}

	for _, template := range t.ALB.Spec.ALBNode.HotUpdateConfig.Templates {
		if nodeTemplate, ok := t.templates[template.Path]; !ok {
			err := fmt.Errorf("template_path_%s_must_exist_wait_please", template.Path)

			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				Error("wait_template_to_be_added")

			return errors.WithMessage(err, "template_not_found")
		} else if nodeTemplate.ErrMsg != "" {
			err := fmt.Errorf(nodeTemplate.ErrMsg)
			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("path", template.Path).WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				Error("wait_template_to_be_added")

			return errors.WithMessage(err, "template_failed")
		} else {
			targetMd5 := fmt.Sprintf("%x", md5.Sum([]byte(template.Content)))
			nodeMd5 := fmt.Sprintf("%x", md5.Sum([]byte(nodeTemplate.Content)))
			if targetMd5 == nodeMd5 {
				return nil
			}

			err := fmt.Errorf("template_path_%s_content_md5_%s_must_equal_to_%s_please_wait",
				template.Path, targetMd5, nodeMd5)

			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).WithField("path", template.Path).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				WithField("current_md5", nodeMd5).WithField("target_md5", targetMd5).
				Error("wait_template_to_be_added_or_updated")

			return errors.WithMessage(err, "template_md5_inconsistent")
		}
	}

	return nil
}

func (t *CheckHotUpdateStatusTask) reserveComponentVersion() {
	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.Components {
		if t.ALB.Spec.ALBNode.ComponentMap == nil {
			t.ALB.Spec.ALBNode.ComponentMap = make(map[string]string)
		}

		t.ALB.Spec.ALBNode.ComponentMap[item.Name] = item.Version
	}

	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.Templates {
		if t.ALB.Spec.ALBNode.TemplateMap == nil {
			t.ALB.Spec.ALBNode.TemplateMap = make(map[string]string)
		}

		t.ALB.Spec.ALBNode.TemplateMap[item.Path] = fmt.Sprintf("%x", md5.Sum([]byte(item.Content)))
	}
}

func (t *CheckHotUpdateStatusTask) Run() error {
	provision, err := t.Node.GetNodeProvision()
	if err != nil {
		log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).WithField("env", t.ALB.Spec.ALBNode.Env).
			WithField("idc", t.ALB.Spec.ALBNode.IDC).
			Error("check_hot_update_status_error")

		return errors.WithMessage(err, "fetch_provision_failed")
	}

	for _, item := range provision.Templates {
		tem := item
		t.templates[item.Path] = &tem
	}

	for _, item := range provision.Components {
		name := item.Name
		if item.Type == cmp.ComponentTypeDocker {
			name = item.ServiceName
		}

		com := item
		t.components[name] = &com
	}

	switch strings.ToLower(t.ALB.Spec.ALBNode.HotUpdateConfig.Type) {
	case consts.UpdateTocexItem, consts.AddTocexItem:
		err = t.upsert()
	case consts.DeleteTocexItem:
		err = t.deleted()
	default:
		t.ALB.Spec.ALBNode.HotUpdateConfig.Type = consts.CompleteTocexJob

		log.Logger().WithField("ip", t.ALB.Spec.LanIP).
			WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
			Warn("check_hot_update_invalid_type")

		return nil
	}

	if err != nil {
		return errors.WithMessage(err, "hotupdate_failed")
	}

	return nil
}
