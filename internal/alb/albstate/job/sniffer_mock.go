// Code generated by MockGen. DO NOT EDIT.
// Source: sniffer.go

// Package job is a generated GoMock package.
package job

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockSniffer is a mock of Sniffer interface.
type MockSniffer struct {
	ctrl     *gomock.Controller
	recorder *MockSnifferMockRecorder
}

// MockSnifferMockRecorder is the mock recorder for MockSniffer.
type MockSnifferMockRecorder struct {
	mock *MockSniffer
}

// NewMockSniffer creates a new mock instance.
func NewMockSniffer(ctrl *gomock.Controller) *MockSniffer {
	mock := &MockSniffer{ctrl: ctrl}
	mock.recorder = &MockSnifferMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSniffer) EXPECT() *MockSnifferMockRecorder {
	return m.recorder
}

// Probe mocks base method.
func (m *MockSniffer) Probe(target string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Probe", target)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Probe indicates an expected call of Probe.
func (mr *MockSnifferMockRecorder) Probe(target interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Probe", reflect.TypeOf((*MockSniffer)(nil).Probe), target)
}
