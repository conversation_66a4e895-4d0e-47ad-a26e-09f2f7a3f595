package job

import (
	"testing"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albprov"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
)

func TestCheckDeleteProvisionTask_Run(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	t.Run("should handle NodeReversedConfig error", func(t *testing.T) {
		task := newTestCheckDeleteProvisionTask(t)
		if prov, ok := task.prov.(*albprov.MockProvision); ok {
			prov.EXPECT().NodeReversedConfig(cmp.ComponentTypeUnSpec).
				Return(nil, errors.New("config error"))
		}

		err := task.Run()
		assert.ErrorContains(t, err, "check_delete_provision_failed")
	})

	t.Run("should fail when reserved components exist", func(t *testing.T) {
		task := newTestCheckDeleteProvisionTask(t)
		if prov, ok := task.prov.(*albprov.MockProvision); ok {
			coms := make([]toclib.ProvisionNodeComponent, 2)
			if err := gofakeit.Struct(&coms); err != nil {
				t.Fatal(err)
			}

			prov.EXPECT().NodeReversedConfig(cmp.ComponentTypeUnSpec).
				Return(&toclib.ProvisionNodeConfig{Components: coms}, nil)
		}

		err := task.Run()
		assert.ErrorContains(t, err, "delete_provision_failed_reserved")
	})

	t.Run("should succeed with no reserved components", func(t *testing.T) {
		task := newTestCheckDeleteProvisionTask(t)
		if prov, ok := task.prov.(*albprov.MockProvision); ok {
			prov.EXPECT().NodeReversedConfig(cmp.ComponentTypeUnSpec).
				Return(&toclib.ProvisionNodeConfig{}, nil)
		}

		err := task.Run()
		assert.NoError(t, err)
	})
}

func newTestCheckDeleteProvisionTask(t *testing.T) *CheckDeleteProvisionTask {
	t.Helper()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	mockProv := albprov.NewMockProvision(ctrl)

	return &CheckDeleteProvisionTask{
		prov: mockProv,
		Agent: &TaskAgent{
			IP:      "*******",
			AZ:      "test-az",
			ALBName: "test-alb",
		},
	}
}
