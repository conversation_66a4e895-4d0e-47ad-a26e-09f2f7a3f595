package job

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type RegisterEtcdProxyDomainTask struct {
	Agent      *TaskAgent
	NodeServer *tocvo.ALBNodeServer
	Meta       *sgwvo.ALBClusterConfigMeta
	DNSAPI     sgw.DNSAPIAdapter

	cachedClusterDomain string
	cluster             sgw.L7ClusterAdapter
}

func NewRegisterEtcdProxyDomainTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *RegisterEtcdProxyDomainTask {
	return &RegisterEtcdProxyDomainTask{
		Agent:      NewTaskAgent(alb),
		NodeServer: svr,
		Meta:       svr.Meta,
		DNSAPI:     sgw.NewDNSAPIAdapter(string(alb.UID)),

		cluster: sgw.NewALBClusterAdapter(string(alb.UID)),
	}
}

func (t *RegisterEtcdProxyDomainTask) Description() string {
	return ""
}

func (t *RegisterEtcdProxyDomainTask) Name() string {
	return AddEtcdProxyDomainTask
}

func (t *RegisterEtcdProxyDomainTask) upsertEtcdProxyDomain(domain string) error {
	// register private central cname
	req := &sgw.DNSChangeRequest{
		Action:      "UPSERT",
		Domain:      domain,
		DNSType:     "private",
		EntryType:   "cname",
		Value:       t.clusterDomain(),
		ServiceName: configs.ALB.Nginx().Name,
		Comment:     fmt.Sprintf("alb cluster %s add by %s", t.Agent.ALBName, configs.Mgmt.Ops.Bot.User),
		SingleResp:  false,
	}

	_, err := t.DNSAPI.ChangeRecord(req)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"record":  t.Meta.ClusterDomain(),
			"domain":  domain,
		}).Error("upsert_sgw_etcd_proxy_dns_failed")

		return errors.WithMessage(err, "upsert_sgw_etcd_proxy_dns_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"idc":     t.Agent.IDC,
		"az":      t.Agent.AZ,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
		"record":  t.Meta.ClusterDomain(),
		"domain":  domain,
	}).Info("upsert_sgw_etcd_proxy_dns_success")

	return nil
}

// only CName record
func (t *RegisterEtcdProxyDomainTask) upsertPrivateCentral() error {
	domain, err := t.Meta.SGWEtcdProxyDomain()
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("fetch_sgw_etcd_proxy_domain_failed")

		return errors.WithMessage(err, "fetch_sgw_etcd_proxy_domain_failed")
	}

	dom, err := t.DNSAPI.Query(domain)
	if err != nil {
		if strings.Contains(err.Error(), sgw.ErrDomainNotFound.Error()) {
			return t.upsertEtcdProxyDomain(domain)
		}

		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"domain":  domain,
		}).Error("fetch_domain_record_from_dns_api_failed")

		return errors.WithMessage(err, "fetch_domain_record_from_dns_api_failed")
	}

	// check if domain's private central CName record existed
	records := make(map[string]struct{})
	for _, r := range dom.Records {
		if r.IsPrivate() && r.IsCentral() && r.IsCNameType() {
			records[r.Domain] = struct{}{}
		}
	}

	// existed
	if _, ok := records[domain]; ok {
		return nil
	}

	return t.upsertEtcdProxyDomain(domain)
}

// dmz-inbound int both through dmz-inbound alb-cluster proxy
func (t *RegisterEtcdProxyDomainTask) clusterDomain() string {
	clusterDomain, err := t.agentWanClusterDomain()
	if err != nil {
		log.Logger().WithError(err).Error("get_cluster_domain_failed")
	}

	if clusterDomain != "" {
		return clusterDomain
	}

	log.Logger().WithFields(log.Fields{
		"ip":   t.Agent.IP,
		"uuid": t.Agent.ClusterUUID,
	}).Warn("get_cluster_domain_from_mgmt_failed")

	return t.Meta.ClusterDomain()
}

func (t *RegisterEtcdProxyDomainTask) agentWanClusterDomain() (string, error) {
	if t.cachedClusterDomain != "" {
		/*
			log.Logger().WithFields(log.Fields{
				"ip":   t.Agent.IP,
				"uuid": t.Agent.ClusterUUID,
			}).Info("get_cached_cluster_domain_success")
		*/

		return t.cachedClusterDomain, nil
	}

	cluster, err := t.cluster.GetByUUID(t.Agent.ClusterUUID)
	if err != nil {
		return "", errors.WithMessage(err, "get_cluster_by_uuid_failed") // More specific error message
	}

	if cluster == nil {
		return "", errors.New("cluster_is_nil")
	}

	var clusterDomains *albvo.ClusterDomains

	if cluster.IsWAN() {
		clusterDomains = cluster.ALBClusterDomains
	} else {
		wanClusters, err := t.cluster.GetByRZ(cluster.RZ)
		if err != nil {
			return "", errors.WithMessage(err, "get_wan_cluster_by_rz_failed")
		}

		for _, c := range wanClusters {
			if c.IsWAN() && c.IsSameRZ(cluster) {
				clusterDomains = c.ALBClusterDomains

				break
			}
		}
	}

	if clusterDomains == nil {
		return "", nil
	}

	if clusterDomains.ExistsDomain() {
		t.cachedClusterDomain = clusterDomains.GetDomain()

		return t.cachedClusterDomain, nil
	}

	return "", nil
}

func (t *RegisterEtcdProxyDomainTask) checkClusterDomain() error {
	domain := t.clusterDomain()
	dom, err := t.DNSAPI.Query(domain)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"domain":  domain,
		}).Error("fetch_domain_record_from_dns_api_failed")

		return errors.WithMessage(err, "fetch_domain_record_from_dns_api_failed")
	}

	var privateCentralFound bool

	if dom.PrivateRecordCount() > 0 && len(dom.Records) != 0 {
		for _, r := range dom.Records {
			if r.IsPrivate() {
				// TODO check RecordType and values?
				if r.IsCentral() {
					privateCentralFound = true
				}
			}
		}
	}

	if !privateCentralFound {
		return fmt.Errorf("not found private central records for %s", domain)
	}

	return nil
}

// Run
/*
	1. fetch the cluster's domain from group_var
	2. fetch domain records from DNS-API
	3. if not found private central records, then return
	4. only add private central CName record for etcd-proxy
*/
func (t *RegisterEtcdProxyDomainTask) Run() error {
	if err := t.checkClusterDomain(); err != nil {
		return err
	}

	return t.upsertPrivateCentral()
}
