//nolint:dupl
package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
)

type StopBirdTask struct {
	BirdTask
	haMode string
}

func NewStopBirdTask(alb *v1alpha1.ALB, haMode string) *StopBirdTask {
	task := &StopBirdTask{
		haMode: haMode,
	}
	task.ALB = alb

	return task
}

func (t *StopBirdTask) Description() string {
	return ""
}

func (t *StopBirdTask) Name() string {
	return StopBirdTaskName
}

func (t *StopBirdTask) Run() error {
	if err := t.Stop(); err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"az":      t.ALB.Spec.ALBNode.Zone,
			"ha_mode": t.haMode,
		}).WithError(err).Error("stop_bird_failed")

		return errors.WithMessage(err, "stop_bird_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.ALB.Spec.LanIP,
		"env":     t.ALB.Spec.ALBNode.Env,
		"idc":     t.ALB.Spec.ALBNode.IDC,
		"az":      t.ALB.Spec.ALBNode.Zone,
		"uid":     t.ALB.UID,
		"cluster": t.ALB.Spec.ALBNode.ALBName,
	}).Info("stop_bird_success")

	return nil
}
