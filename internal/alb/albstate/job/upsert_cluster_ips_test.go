package job

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

func TestUpsertClusterIPsTask_Run(t *testing.T) {
	tests := []struct {
		name          string
		setupMocks    func(*gomock.Controller) (*v1alpha1.ALB, *tocvo.ALBNodeServer, sgw.L7ClusterAdapter)
		expectedError bool
		errorContains string
	}{
		{
			name: "GetByNode returns error",
			setupMocks: func(ctrl *gomock.Controller) (*v1alpha1.ALB, *tocvo.ALBNodeServer, sgw.L7ClusterAdapter) {
				mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
				mockCluster.EXPECT().
					GetByNode(gomock.Any()).
					Return(nil, errors.New("get_by_node_error"))

				mockNodeServer := newTestNodeServer(t)

				return &v1alpha1.ALB{}, mockNodeServer, mockCluster
			},
			expectedError: true,
			errorContains: "fetch_sgw_l7_cluster_by_node_failed",
		},
		{
			name: "HAM with VRRP, skip update because IPs contained",
			setupMocks: func(ctrl *gomock.Controller) (*v1alpha1.ALB, *tocvo.ALBNodeServer, sgw.L7ClusterAdapter) {
				return setupCommonMocks(t, ctrl, consts.HAMNonStd1)
			},
			expectedError: false,
		},
		{
			name: "Non-HAM mode, skip update because IPs match",
			setupMocks: func(ctrl *gomock.Controller) (*v1alpha1.ALB, *tocvo.ALBNodeServer, sgw.L7ClusterAdapter) {
				return setupCommonMocks(t, ctrl, consts.HAMNonStd3)
			},
			expectedError: false,
		},
		{
			name: "UpdateClusterIPs returns error",
			setupMocks: func(ctrl *gomock.Controller) (*v1alpha1.ALB, *tocvo.ALBNodeServer, sgw.L7ClusterAdapter) {
				mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
				mockCluster.EXPECT().
					GetByNode(gomock.Any()).
					Return(&albvo.Cluster{
						UUID:                 "test-uuid",
						ClusterWANIPs:        []string{"*******"},
						ClusterLANIPs:        []string{"********"},
						ClusterPrivateWANIPs: []string{"**********"},
					}, nil)

				// The request will have different IPs, so it won't skip the update
				mockCluster.EXPECT().
					UpdateClusterIPs(gomock.Any()).
					Return(false, errors.New("update_error"))

				mockNodeServer := newTestNodeServer(t)

				if s, ok := mockNodeServer.ServerV3.(*tocvo.MockIServerV3); ok {
					s.EXPECT().HAMode(gomock.Any()).Return(consts.HAMNonStd1, nil).AnyTimes()
				}

				// Make sure req and cls have different IPs to trigger update
				mockNodeServer.ServerGroupVar.GroupVar.KeepalivedWANVIPS = []string{"*******"}

				return &v1alpha1.ALB{}, mockNodeServer, mockCluster
			},
			expectedError: true,
			errorContains: "update_cluster_ips_failed",
		},
		{
			name: "UpdateClusterIPs returns not ok",
			setupMocks: func(ctrl *gomock.Controller) (*v1alpha1.ALB, *tocvo.ALBNodeServer, sgw.L7ClusterAdapter) {
				mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
				mockCluster.EXPECT().
					GetByNode(gomock.Any()).
					Return(&albvo.Cluster{
						UUID:                 "test-uuid",
						Name:                 "test-cluster",
						ClusterWANIPs:        []string{"*******"},
						ClusterLANIPs:        []string{"********"},
						ClusterPrivateWANIPs: []string{"**********"},
					}, nil)

				// The request will have different IPs, so it won't skip the update
				mockCluster.EXPECT().
					UpdateClusterIPs(gomock.Any()).
					Return(false, nil)

				mockNodeServer := newTestNodeServer(t)

				if s, ok := mockNodeServer.ServerV3.(*tocvo.MockIServerV3); ok {
					s.EXPECT().HAMode(gomock.Any()).Return(consts.HAMNonStd1, nil).AnyTimes()
				}

				// Make sure req and cls have different IPs to trigger update
				mockNodeServer.ServerGroupVar.GroupVar.KeepalivedWANVIPS = []string{"*******"}

				return &v1alpha1.ALB{}, mockNodeServer, mockCluster
			},
			expectedError: true,
			errorContains: "update_test-cluster_cluster_ips_failed",
		},
		{
			name: "Successful update",
			setupMocks: func(ctrl *gomock.Controller) (*v1alpha1.ALB, *tocvo.ALBNodeServer, sgw.L7ClusterAdapter) {
				mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
				mockCluster.EXPECT().
					GetByNode(gomock.Any()).
					Return(&albvo.Cluster{
						UUID:                 "test-uuid",
						Name:                 "test-cluster",
						ClusterWANIPs:        []string{"*******"},
						ClusterLANIPs:        []string{"********"},
						ClusterPrivateWANIPs: []string{"**********"},
					}, nil)

				// The request will have different IPs, so it won't skip the update
				mockCluster.EXPECT().
					UpdateClusterIPs(gomock.Any()).
					Return(true, nil)

				mockNodeServer := newTestNodeServer(t)

				// Make sure req and cls have different IPs to trigger update
				mockNodeServer.ServerGroupVar.GroupVar.KeepalivedWANVIPS = []string{"*******"}

				if s, ok := mockNodeServer.ServerV3.(*tocvo.MockIServerV3); ok {
					s.EXPECT().HAMode(gomock.Any()).Return(consts.HAMNonStd1, nil).AnyTimes()
				}

				return &v1alpha1.ALB{}, mockNodeServer, mockCluster
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			alb, nodeServer, mockCluster := tt.setupMocks(ctrl)

			task := &UpsertClusterIPsTask{
				Agent:      NewTaskAgent(alb),
				NodeServer: nodeServer,
				Meta:       nodeServer.Meta,
				Cluster:    mockCluster,
			}

			err := task.Run()

			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test the helper functions
func TestMergeAndSortIPs(t *testing.T) {
	tests := []struct {
		name        string
		existingIPs []string
		newIPs      []string
		expected    []string
	}{
		{
			name:        "Merge with no duplicates",
			existingIPs: []string{"*******", "*******"},
			newIPs:      []string{"*******", "*******"},
			expected:    []string{"*******", "*******", "*******", "*******"},
		},
		{
			name:        "Merge with duplicates",
			existingIPs: []string{"*******", "*******"},
			newIPs:      []string{"*******", "*******"},
			expected:    []string{"*******", "*******", "*******"},
		},
		{
			name:        "Empty existing IPs",
			existingIPs: []string{},
			newIPs:      []string{"*******", "*******"},
			expected:    []string{"*******", "*******"},
		},
		{
			name:        "Empty new IPs",
			existingIPs: []string{"*******", "*******"},
			newIPs:      []string{},
			expected:    []string{"*******", "*******"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := mergeAndSortIPs(tt.existingIPs, tt.newIPs)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAreAnyIPsContained(t *testing.T) {
	tests := []struct {
		name        string
		ipsToCheck  []string
		existingIPs []string
		expected    bool
	}{
		{
			name:        "IPs are contained",
			ipsToCheck:  []string{"*******", "*******"},
			existingIPs: []string{"*******", "*******", "*******"},
			expected:    true,
		},
		{
			name:        "Some IPs are contained",
			ipsToCheck:  []string{"*******", "*******"},
			existingIPs: []string{"*******", "*******", "*******"},
			expected:    true,
		},
		{
			name:        "No IPs are contained",
			ipsToCheck:  []string{"*******", "*******"},
			existingIPs: []string{"*******", "*******", "*******"},
			expected:    false,
		},
		{
			name:        "Empty ipsToCheck",
			ipsToCheck:  []string{},
			existingIPs: []string{"*******", "*******"},
			expected:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := areAnyIPsContained(tt.ipsToCheck, tt.existingIPs)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSortAndCompareIPs(t *testing.T) {
	tests := []struct {
		name        string
		existingIPs []string
		newIPs      []string
		expected    bool
	}{
		{
			name:        "Equal ordered IPs",
			existingIPs: []string{"*******", "*******", "*******"},
			newIPs:      []string{"*******", "*******", "*******"},
			expected:    true,
		},
		{
			name:        "Equal unordered IPs",
			existingIPs: []string{"*******", "*******", "*******"},
			newIPs:      []string{"*******", "*******", "*******"},
			expected:    true,
		},
		{
			name:        "Different IPs",
			existingIPs: []string{"*******", "*******"},
			newIPs:      []string{"*******", "*******"},
			expected:    false,
		},
		{
			name:        "Different lengths",
			existingIPs: []string{"*******", "*******"},
			newIPs:      []string{"*******", "*******", "*******"},
			expected:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := sortAndCompareIPs(tt.existingIPs, tt.newIPs)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func newTestNodeServer(t *testing.T) *tocvo.ALBNodeServer {
	t.Helper()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	mockServerV3 := tocvo.NewMockIServerV3(ctrl)

	node := &tocvo.ALBNodeServer{
		ServerGroupVar: &tocvo.ALBServerAndVariables{
			GroupVar: &tocvo.ALBGroupVar{},
		},
		Segment: &tocvo.Segment{
			NetworkVersion: "v3.0",
		},
		ServerV3: mockServerV3,
		Meta:     &sgwvo.ALBClusterConfigMeta{},
	}

	return node
}

func setupCommonMocks(
	t *testing.T,
	ctrl *gomock.Controller,
	haMode consts.HighAvailableMode,
) (*v1alpha1.ALB, *tocvo.ALBNodeServer, sgw.L7ClusterAdapter) {
	t.Helper()

	mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
	mockCluster.EXPECT().
		GetByNode(gomock.Any()).
		Return(&albvo.Cluster{
			UUID:                 "test-uuid",
			ClusterWANIPs:        []string{"*******", "*******"},
			ClusterLANIPs:        []string{"********", "********"},
			ClusterPrivateWANIPs: []string{"**********", "**********"},
		}, nil)

	mockCluster.EXPECT().UpdateClusterIPs(gomock.Any()).Return(true, nil)

	mockNodeServer := newTestNodeServer(t)
	if s, ok := mockNodeServer.ServerV3.(*tocvo.MockIServerV3); ok {
		s.EXPECT().HAMode(gomock.Any()).Return(haMode, nil).AnyTimes()
	}

	return &v1alpha1.ALB{}, mockNodeServer, mockCluster
}
