package job

import (
	"fmt"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type UpsertClusterIPsTask struct {
	Agent      *TaskAgent
	NodeServer *tocvo.ALBNodeServer
	Meta       *sgwvo.ALBClusterConfigMeta
	Cluster    sgw.L7ClusterAdapter
}

func NewUpsertClusterIPsTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *UpsertClusterIPsTask {
	agent := NewTaskAgent(alb)

	return &UpsertClusterIPsTask{
		Agent:      agent,
		NodeServer: svr,
		Meta:       svr.Meta,
		Cluster:    sgw.NewALBClusterAdapter(agent.UID),
	}
}

func (t *UpsertClusterIPsTask) Description() string {
	return ""
}

func (t *UpsertClusterIPsTask) Name() string {
	return UpsertClusterIPsTaskName
}

func (t *UpsertClusterIPsTask) Run() error {
	cls, err := t.Cluster.GetByNode(t.Agent.IP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("fetch_sgw_l7_cluster_by_node_failed")

		return errors.WithMessage(err, "fetch_sgw_l7_cluster_by_node_failed")
	}

	req := &sgw.L7ClusterIPsUpdateRequest{
		UUID: cls.UUID,
	}

	var skipUpdateClusterIPs bool

	if t.NodeServer.IsHAMWithVRRP() {
		req.ClusterWANIPs = t.Meta.KeepalivedWANVIPs()
		req.ClusterLANIPs = t.Meta.KeepalivedLANVIPs()
		req.ClusterPrivateWANIPs = t.Meta.KeepalivedPrivateWANVIPs()

		// Check if all request IPs are already present in cluster configuration
		skipUpdateClusterIPs = areAnyIPsContained(req.ClusterWANIPs, cls.ClusterWANIPs) &&
			areAnyIPsContained(req.ClusterLANIPs, cls.ClusterLANIPs) &&
			areAnyIPsContained(req.ClusterPrivateWANIPs, cls.ClusterPrivateWANIPs)

		if !skipUpdateClusterIPs {
			// If we need to update, merge existing IPs with new IPs
			req.ClusterWANIPs = mergeAndSortIPs(cls.ClusterWANIPs, req.ClusterWANIPs)
			req.ClusterLANIPs = mergeAndSortIPs(cls.ClusterLANIPs, req.ClusterLANIPs)
			req.ClusterPrivateWANIPs = mergeAndSortIPs(cls.ClusterPrivateWANIPs, req.ClusterPrivateWANIPs)
		}
	} else {
		req.ClusterWANIPs = t.Meta.WANVIPs()
		req.ClusterLANIPs = t.Meta.LANVIPs()
		req.ClusterPrivateWANIPs = t.Meta.PrivateWANVIPs()

		skipUpdateClusterIPs = sortAndCompareIPs(req.ClusterWANIPs, cls.ClusterWANIPs) &&
			sortAndCompareIPs(req.ClusterLANIPs, cls.ClusterLANIPs) &&
			sortAndCompareIPs(req.ClusterPrivateWANIPs, cls.ClusterPrivateWANIPs)
	}

	if skipUpdateClusterIPs {
		log.Logger().WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"req":     req,
		}).Info("update_cluster_ips_no_change")

		return nil
	}

	ok, err := t.Cluster.UpdateClusterIPs(req)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"req":     req,
		}).Error("update_cluster_ips_failed")

		return errors.WithMessage(err, "update_cluster_ips_failed")
	}

	if !ok {
		err = fmt.Errorf("update_%s_cluster_ips_failed", cls.Name)
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"req":     req,
		}).Error("update_cluster_ips_failed")

		return errors.WithMessage(err, "update_cluster_ips_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"idc":     t.Agent.IDC,
		"az":      t.Agent.AZ,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
		"req":     req,
	}).Info("update_cluster_ips_successfully")

	return nil
}

// mergeAndSortIPs merges source IPs into target IPs and sorts the result
func mergeAndSortIPs(existingIPs, newIPs []string) []string {
	result := make([]string, 0, len(existingIPs)+len(newIPs))

	// Add all existing IPs
	result = append(result, existingIPs...)

	// Add new IPs that don't already exist
	for _, ip := range newIPs {
		if !slice.Contain(result, ip) {
			result = append(result, ip)
		}
	}

	// Sort for consistent ordering
	slice.Sort(result)

	return result
}

// areAnyIPsContained checks if all IPs in ipsToCheck are contained in existingIPs
func areAnyIPsContained(ipsToCheck, existingIPs []string) bool {
	for _, ip := range ipsToCheck {
		if slice.Contain(existingIPs, ip) {
			return true
		}
	}

	return false
}

func sortAndCompareIPs(existingIPs, newIPs []string) bool {
	// Sort both slices
	slice.Sort(existingIPs)
	slice.Sort(newIPs)

	// Compare the sorted slices
	return slice.Equal(existingIPs, newIPs)
}
