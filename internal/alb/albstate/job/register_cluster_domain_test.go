package job

import (
	"strings"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/dns/dnsvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
)

func TestRegisterClusterDomainTask_Run(t *testing.T) {
	node := my0StagingNode0()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	task := NewRegisterClusterDomainTask(node.ALB(t), svr)
	if svr.IsNetworkZoneSupported() {
		assert.NoError(t, task.Run())

		return
	}

	assert.Error(t, task.Run())
}

func withMockCluster(t *testing.T, task *RegisterClusterDomainTask) {
	t.Helper()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	fakeit := gofakeit.New(time.Now().Unix())

	mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
	mockCluster.EXPECT().GetByUUID(gomock.Any()).Return(&albvo.Cluster{
		ALBClusterDomains: &albvo.ClusterDomains{
			WanDomain: fakeit.DomainName(),
		},
	}, nil).AnyTimes()

	task.cluster = mockCluster
}

func withMockDefaultCluster(t *testing.T, task *RegisterClusterDomainTask) {
	t.Helper()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)
	mockCluster.EXPECT().GetByUUID(gomock.Any()).Return(&albvo.Cluster{}, nil).AnyTimes()

	task.cluster = mockCluster
}

func TestRegisterClusterDomainTask_clusterDomain(t *testing.T) {
	node := my0StagingNode0()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	task := NewRegisterClusterDomainTask(node.ALB(t), svr)
	withMockCluster(t, task)

	domain, err := task.clusterDomain()
	assert.NoError(t, err)
	assert.NotEmpty(t, domain)

	_, err = task.NodeServer.Meta.SGWEtcdProxyDomain()
	assert.NoError(t, err)
}

func TestNewRegisterClusterDomainTask_ClusterBaseDomain(t *testing.T) {
	node := my0StagingNode0()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	baseDomain := svr.Meta.ClusterDomainSuffix()

	task := NewRegisterClusterDomainTask(node.ALB(t), svr)
	assert.NotNil(t, task)

	withMockDefaultCluster(t, task)

	domain, err := task.clusterDomain()
	assert.NoError(t, err)
	assert.True(t, strings.HasSuffix(domain, baseDomain),
		"clusterDomain should end with %s", baseDomain)
}

func TestRegisterClusterDomainTask_upsertDomain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	node := my0StagingNode0()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	fakeit := gofakeit.New(time.Now().Unix())

	task := NewRegisterClusterDomainTask(node.ALB(t), svr)
	assert.NotNil(t, task)

	withMockCluster(t, task)

	domain, err := task.clusterDomain()
	assert.NoError(t, err)
	ip1 := fakeit.IPv4Address()
	ip2 := fakeit.IPv4Address()
	ip3 := fakeit.IPv4Address()

	mockDNSAPI := sgw.NewMockDNSAPIAdapter(ctrl)

	t.Run("upsertDomain_ok", func(t *testing.T) {
		mockDNSAPI.EXPECT().Query(domain).Return(&dnsvo.Domain{
			Name: domain,
			Records: []*dnsvo.Record{
				{
					Domain:    fakeit.DomainName(),
					DNSType:   "public",
					EntryType: "a",
					Value:     ip1,
				},
			},
		}, nil).Times(2)
		mockDNSAPI.EXPECT().ChangeRecord(gomock.Any()).Return(nil, nil).
			Times(1)

		task.DNSAPI = mockDNSAPI
		task.Meta.ECMPBGPWanVIPs = []string{ip1, ip2}

		// old ips: ip1
		// new ips: ip1, ip2
		err = task.upsertDomain()
		assert.NoError(t, err)

		dom, err := task.DNSAPI.Query(domain)
		assert.NoError(t, err)
		assert.NotNil(t, dom)

		ips := task.wanVIPs()
		assert.True(t, dom.IsPublicRecordSubsetOf(ips))
	})

	t.Run("upsertDomain_failed", func(t *testing.T) {
		mockDNSAPI.EXPECT().Query(domain).Return(&dnsvo.Domain{
			Name: domain,
			Records: []*dnsvo.Record{
				{
					Domain:    fakeit.DomainName(),
					DNSType:   "public",
					EntryType: "a",
					Value:     ip1,
				},
				{
					Domain:    fakeit.DomainName(),
					DNSType:   "public",
					EntryType: "a",
					Value:     ip2,
				},
				{
					Domain:    fakeit.DomainName(),
					DNSType:   "public",
					EntryType: "a",
					Value:     ip3,
				},
			},
		}, nil).Times(2)
		mockDNSAPI.EXPECT().ChangeRecord(gomock.Any()).Return(nil, nil).
			Times(1)

		ips := []string{ip1}
		task.DNSAPI = mockDNSAPI
		task.Meta.ECMPBGPWanVIPs = ips

		// NB: no error here, but the upsertDomain should be skipped
		err = task.upsertDomain()
		assert.NoError(t, err)

		// old ips: ip1, ip2, ip3
		// new ips: ip1
		dom, err := task.DNSAPI.Query(domain)
		assert.NoError(t, err)
		assert.NotNil(t, dom)

		ok := dom.IsPublicRecordSubsetOf(ips)
		assert.False(t, ok)

		ok = dom.IsPublicRecordSubsetOf([]string{})
		assert.False(t, ok)

		ok = dom.IsPublicRecordSubsetOf([]string{ip1, ip2})
		assert.False(t, ok)

		ok = dom.IsPublicRecordSubsetOf([]string{ip1, ip2, ip3})
		assert.False(t, ok)

		ok = dom.IsPublicRecordSubsetOf([]string{
			ip1, ip2,
			fakeit.IPv4Address(),
			fakeit.IPv4Address(),
		})
		assert.False(t, ok)
	})
}

func TestRegisterClusterDomainTask_upsertPublicCentral(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	node := my0StagingNode0()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	mockDNSAPI := sgw.NewMockDNSAPIAdapter(ctrl)
	mockClusterAdapter := sgw.NewMockL7ClusterAdapter(ctrl)

	task := &RegisterClusterDomainTask{
		Agent: NewTaskAgent(node.ALB(t)),
		ClusterDomainTask: &ClusterDomainTask{
			NodeServer: svr,
			Meta:       svr.Meta,
			cluster:    mockClusterAdapter,
			Agent: &TaskAgent{
				ClusterUUID: gofakeit.UUID(),
			},
		},
		DNSAPI:  mockDNSAPI,
		Cluster: sgw.NewALBClusterAdapter(t.Name()),
	}

	t.Run("empty_wan_vips", func(t *testing.T) {
		// Set empty WAN VIPs
		task.Meta.ECMPBGPWanVIPs = []string{}

		// old domain already exists, should not create a new one
		mockClusterAdapter.EXPECT().GetByUUID(gomock.Any()).
			Return(&albvo.Cluster{}, nil).Times(1)

		// Should return nil without making any DNS API calls
		err := task.upsertPublicCentral()
		assert.NoError(t, err)
	})

	t.Run("non_empty_wan_vips", func(t *testing.T) {
		ip1 := gofakeit.IPv4Address()
		ip2 := gofakeit.IPv4Address()
		task.Meta.ECMPBGPWanVIPs = []string{ip1, ip2}

		// old domain already exists, should not create a new one
		mockClusterAdapter.EXPECT().GetByUUID(gomock.Any()).
			Return(&albvo.Cluster{}, nil).Times(1)

		mockDNSAPI.EXPECT().ChangeRecord(gomock.Any()).DoAndReturn(
			func(req *sgw.DNSChangeRequest) (*dnsvo.Domain, error) {
				assert.Equal(t, "UPSERT", req.Action)
				assert.Equal(t, "public", req.DNSType)
				assert.Equal(t, "a", req.EntryType)
				assert.Equal(t, strings.Join([]string{ip1, ip2}, "\n"), req.Value)

				return &dnsvo.Domain{}, nil
			}).Times(1)

		err := task.upsertPublicCentral()
		assert.NoError(t, err)
	})

	t.Run("cluster_domain_exists", func(t *testing.T) {
		t.Parallel()

		ip1 := gofakeit.IPv4Address()
		ip2 := gofakeit.IPv4Address()
		task.Meta.ECMPBGPWanVIPs = []string{ip1, ip2}

		domain := gofakeit.DomainName()

		// First expect GetByNode to return existing domain
		mockClusterAdapter.EXPECT().GetByUUID(gomock.Any()).
			Return(&albvo.Cluster{ALBClusterDomains: &albvo.ClusterDomains{
				WanDomain: domain,
			}}, nil).Times(1)

		// Then expect Query with the same domain
		mockDNSAPI.EXPECT().Query(domain).
			Return(&dnsvo.Domain{
				Name: domain,
				Records: []*dnsvo.Record{
					{
						Domain:    domain,
						DNSType:   "public",
						EntryType: "a",
						Value:     gofakeit.IPv4Address(),
					},
				},
			}, nil).Times(1)

		// Expect ChangeRecord to be called for DNS update
		mockDNSAPI.EXPECT().ChangeRecord(gomock.Any()).DoAndReturn(
			func(req *sgw.DNSChangeRequest) (*dnsvo.Domain, error) {
				assert.Equal(t, domain, req.Domain)
				assert.Equal(t, "UPSERT", req.Action)
				assert.Equal(t, "public", req.DNSType)
				assert.Equal(t, "a", req.EntryType)
				assert.Equal(t, strings.Join([]string{ip1, ip2}, "\n"), req.Value)

				return &dnsvo.Domain{}, nil
			}).Times(1)

		err := task.upsertDomain()
		assert.NoError(t, err)
	})
}
