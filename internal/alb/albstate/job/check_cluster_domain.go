package job

import (
	"fmt"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type CheckClusterDomainTask struct {
	*ClusterDomainTask

	Agent *TaskAgent
	Node  toc.TocexAdapter
}

func NewCheckClusterDomainTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *CheckClusterDomainTask {
	return &CheckClusterDomainTask{
		Agent:             NewTaskAgent(alb),
		ClusterDomainTask: NewClusterDomainTask(alb, svr),
		Node:              toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),
	}
}

func (t *CheckClusterDomainTask) Description() string {
	return ""
}

func (t *CheckClusterDomainTask) Name() string {
	return CheckClusterDomainTaskName
}

func (t *CheckClusterDomainTask) Run() error {
	domain, err := t.clusterDomain()
	if err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).WithError(err).Error("get_cluster_domain_for_check_failed")

		return errors.WithMessage(err, "get_cluster_domain_for_check_failed")
	}

	script := fmt.Sprintf(`timeout 5 curl http://%s/myip -H "Host: shopeemobile.com" -I`, domain)
	if _, err := t.Node.RunTask(script); err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"domain":  domain,
		}).Error("check_cluster_domain_failed")

		return errors.WithMessage(err, "check_cluster_domain_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"idc":     t.Agent.IDC,
		"az":      t.Agent.AZ,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
		"domain":  domain,
	}).Info("check_alb_cluster_domain_successfully")

	return nil
}
