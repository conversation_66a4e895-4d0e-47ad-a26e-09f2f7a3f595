package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/etcd/etcdvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
)

type DeleteEtcdNodeKeyTask struct {
	Agent *TaskAgent
	ALB   *v1alpha1.ALB
}

func NewDeleteEtcdNodeKeyTask(alb *v1alpha1.ALB) *DeleteEtcdNodeKeyTask {
	return &DeleteEtcdNodeKeyTask{
		Agent: NewTaskAgent(alb),
		ALB:   alb,
	}
}

func (t *DeleteEtcdNodeKeyTask) Description() string {
	return ""
}

func (t *DeleteEtcdNodeKeyTask) Name() string {
	return DeleteETCDNodeKeyTaskName
}

func (t *DeleteEtcdNodeKeyTask) Run() error {
	tocAdapter := toc.NewTocexAdapter(tocvo.NewTocNode(&t.ALB.Spec))
	sgwNode, err := toc.NewSGWNodeAdapter(tocAdapter)
	if err != nil {
		return errors.WithMessage(err, "fetch_sgw_node_failed")
	}

	etcdURL, err := sgwNode.SGWEtcd()
	if err != nil {
		return errors.WithMessage(err, "fetch_etcd_url_failed")
	}

	etcdMeta := &etcdvo.Meta{ClusterURL: etcdURL}
	endpoints, err := etcdMeta.Endpoints()
	if err != nil || len(endpoints) == 0 {
		return errors.WithMessage(err, "fetch_etcd_endpoints_failed")
	}

	script, err := tpl.ALBDeleteETCDNodeKeyScript(endpoints[0].Host, endpoints[0].Port, t.ALB.Spec.LanIP)
	if err != nil {
		return errors.WithMessage(err, "generate_delete_etcd_node_key_script_failed")
	}

	_, err = tocAdapter.RunTask(script)
	if err != nil {
		return errors.WithMessage(err, "action_delete_etcd_node_key_failed")
	}

	return nil
}
