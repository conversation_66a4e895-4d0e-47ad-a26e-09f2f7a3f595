package job

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

func TestUpdateServerStateTask_Run(t *testing.T) {
	node := sg90TestNode2()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	task := NewUpdateServerStateTask(node.ALB(t), svr, consts.StateOperating)
	task.Server = node.newServer(t)

	err = task.Run()
	assert.NoError(t, err)
}
