package job

import (
	"testing"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albprov"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

func TestDeleteProvisionTask_Run(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	t.Run("should handle NodeConfig error", func(t *testing.T) {
		task := newTestDeleteProvisionTask(t)
		if prov, ok := task.prov.(*albprov.MockProvision); ok {
			prov.EXPECT().NodeConfig(cmp.ComponentTypeUnSpec).Return(
				&toclib.ProvisionNodeConfig{},
				errors.New("config error"),
			)
		}

		err := task.Run()
		assert.ErrorContains(t, err, "fetch_provision_config_failed")
	})

	t.Run("should handle DeleteProvision error", func(t *testing.T) {
		task := newTestDeleteProvisionTask(t)
		if prov, ok := task.prov.(*albprov.MockProvision); ok {
			temps := make([]toclib.ProvisionNodeTemplate, 2)
			if err := gofakeit.Struct(&temps); err != nil {
				t.Fatal(err)
			}

			prov.EXPECT().NodeConfig(cmp.ComponentTypeUnSpec).Return(
				&toclib.ProvisionNodeConfig{
					Templates: temps,
				},
				nil,
			)
		}

		if node, ok := task.Node.(*toc.MockTocexAdapter); ok {
			node.EXPECT().DeleteProvision(gomock.Any()).
				Return(errors.New("delete error"))
		}

		err := task.Run()
		assert.ErrorContains(t, err, "delete_provision_failed")
	})

	t.Run("should delete provision successfully", func(t *testing.T) {
		task := newTestDeleteProvisionTask(t)
		if prov, ok := task.prov.(*albprov.MockProvision); ok {
			coms := make([]toclib.ProvisionNodeComponent, 3)
			if err := gofakeit.Struct(&coms); err != nil {
				t.Fatal(err)
			}

			prov.EXPECT().NodeConfig(cmp.ComponentTypeUnSpec).Return(
				&toclib.ProvisionNodeConfig{
					Components: coms,
				},
				nil,
			)
		}

		if node, ok := task.Node.(*toc.MockTocexAdapter); ok {
			node.EXPECT().DeleteProvision(gomock.Any()).Return(nil)
		}

		err := task.Run()
		assert.NoError(t, err)
	})
}

func newTestDeleteProvisionTask(t *testing.T) *DeleteProvisionTask {
	t.Helper()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	mockNode := toc.NewMockTocexAdapter(ctrl)
	mockProv := albprov.NewMockProvision(ctrl)

	return &DeleteProvisionTask{
		prov: mockProv,
		Node: mockNode,
		Agent: &TaskAgent{
			IP:      "*******",
			AZ:      "test-az",
			ALBName: "test-alb",
		},
	}
}
