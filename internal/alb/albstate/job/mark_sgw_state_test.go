package job

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
)

func TestMarkSGWStateTask_Run(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name     string
		sgwState string
		mockFn   func(*sgw.MockServiceStateAdapter)
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "mark ready success",
			sgwState: consts.SGWServiceUnitReady,
			mockFn: func(m *sgw.MockServiceStateAdapter) {
				m.EXPECT().MarkReady().Return(nil)
			},
			wantErr: false,
		},
		{
			name:     "mark ready failed",
			sgwState: consts.SGWServiceUnitReady,
			mockFn: func(m *sgw.MockServiceStateAdapter) {
				m.EXPECT().MarkReady().Return(errors.New("mark ready error"))
			},
			wantErr: true,
			errMsg:  "mark_sgw_ready_failed",
		},
		{
			name:     "mark not ready success",
			sgwState: consts.SGWServiceUnitNotReady,
			mockFn: func(m *sgw.MockServiceStateAdapter) {
				m.EXPECT().MarkNotReady().Return(nil)
			},
			wantErr: false,
		},
		{
			name:     "mark not ready failed",
			sgwState: consts.SGWServiceUnitNotReady,
			mockFn: func(m *sgw.MockServiceStateAdapter) {
				m.EXPECT().MarkNotReady().Return(errors.New("mark not ready error"))
			},
			wantErr: true,
			errMsg:  "mark_sgw_ready_failed",
		},
		{
			name:     "invalid state",
			sgwState: "invalid",
			mockFn:   func(m *sgw.MockServiceStateAdapter) {},
			wantErr:  true,
			errMsg:   "invalid sgw state",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockState := sgw.NewMockServiceStateAdapter(ctrl)
			tt.mockFn(mockState)

			task := &MarkSGWStateTask{
				sgwState:     tt.sgwState,
				ServiceState: mockState,
				Agent: &TaskAgent{
					IP:  "*******",
					Env: "test",
					UID: "test-uid",
					AZ:  "test-az",
				},
			}

			err := task.Run()
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
