package job

import (
	"fmt"
	"net"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/meta"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type UpsertEtcdEndpointTask struct {
	Agent      *TaskAgent
	Meta       *sgwvo.ALBClusterConfigMeta
	NodeServer *tocvo.ALBNodeServer
	Cluster    sgw.L7ClusterAdapter
	Sniffer    Sniffer

	AZInfo   *tocvo.AZ
	metaNode meta.NodeAdapter
}

func NewUpsertEtcdEndpointTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *UpsertEtcdEndpointTask {
	return &UpsertEtcdEndpointTask{
		Agent:      NewTaskAgent(alb),
		Meta:       svr.Meta,
		NodeServer: svr,
		Sniffer:    NewALBEtcdSniffer(),
		Cluster:    sgw.NewALBClusterAdapter(string(alb.UID)),
		metaNode:   meta.NewNodeAdapter(string(alb.UID)),
	}
}

func (t *UpsertEtcdEndpointTask) Description() string {
	return ""
}

func (t *UpsertEtcdEndpointTask) Name() string {
	return UpsertEtcdEndpointTaskName
}

func (t *UpsertEtcdEndpointTask) upsertPoints(points []string) error {
	// fetch cluster by IP, if not found etcd then add. if found and diff then update
	cls, err := t.Cluster.GetByNode(t.Agent.IP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("fetch_sgw_l7_cluster_by_node_failed")

		return errors.WithMessage(err, "fetch_sgw_l7_cluster_by_node_failed")
	}

	req := &sgw.L7ClusterEtcdUpdateRequest{
		UUID:      cls.UUID,
		Endpoints: points,
	}

	var changed bool

	if t.AZInfo.IsGeneral() && cls.IsEtcdEndpointWithURL() {
		// endpoint with URL at general AZ then skip change
		changed = false
	} else {
		pointMap := cls.EtcdEndpointMap() // cluster's etcd points

		for _, point := range points {
			if _, ok := pointMap[point]; !ok {
				// different
				changed = true
			}
		}
	}

	if !changed {
		log.Logger().WithFields(log.Fields{
			"ip":        t.Agent.IP,
			"idc":       t.Agent.IDC,
			"az":        t.Agent.AZ,
			"uid":       t.Agent.UID,
			"cluster":   t.Agent.ALBName,
			"etcd":      points,
			"endpoints": cls.ETCDEndpoints,
		}).Info("no_need_update_alb_etcd_endpoints")

		return nil
	}

	ok, err := t.Cluster.UpdateEtcdEndpoint(req)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"etcd":    points,
		}).Error("update_alb_etcd_endpoints_failed")

		return errors.WithMessage(err, "update_alb_etcd_endpoints_failed")
	}

	if !ok {
		err = fmt.Errorf("update_alb_%s_etcd_failed", cls.Name)
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"etcd":    points,
		}).Error("update_alb_etcd_endpoints_failed")

		return errors.WithMessage(err, "update_alb_etcd_endpoints_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"idc":     t.Agent.IDC,
		"az":      t.Agent.AZ,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
		"etcd":    points,
	}).Info("update_alb_etcd_endpoints_successfully")

	return nil
}

func (t *UpsertEtcdEndpointTask) Run() error {
	// etcd endpoint fetch from toc variables
	var points []string
	var err error

	azmeta := toc.NewMetaAdapter(t.Agent.UID)
	az, err := azmeta.AZ(t.Agent.AZ)
	if err != nil {
		return errors.WithMessage(err, "fetch_az_failed")
	}

	t.AZInfo = az

	if az.IsGeneral() {
		// general AZ
		etcdNode := meta.NewEtcdNodeAdapter(t.Agent.UID, consts.ALBEtcd, t.metaNode)
		points, err = etcdNode.Endpoints(t.Meta.RZ, t.Meta.Env)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":      t.Agent.IP,
				"idc":     t.Agent.IDC,
				"az":      t.Agent.AZ,
				"uid":     t.Agent.UID,
				"cluster": t.Agent.ALBName,
			}).Error("fetch_etcd_endpoints_failed")

			return errors.WithMessage(err, "fetch_etcd_endpoints_failed")
		}
	} else {
		// private AZ both dmz-inbound int through the same
		domain, err := t.Meta.SGWEtcdProxyDomain()
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":      t.Agent.IP,
				"idc":     t.Agent.IDC,
				"az":      t.Agent.AZ,
				"uid":     t.Agent.UID,
				"cluster": t.Agent.ALBName,
			}).Error("fetch_etcd_proxy_domain_failed")

			return errors.WithMessage(err, "fetch_etcd_proxy_domain_failed")
		}
		// check this domain access ok or not
		point := fmt.Sprintf("https://%s", net.JoinHostPort(domain, "443"))
		_, err = t.Sniffer.Probe(point)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":       t.Agent.IP,
				"idc":      t.Agent.IDC,
				"az":       t.Agent.AZ,
				"uid":      t.Agent.UID,
				"cluster":  t.Agent.ALBName,
				"endpoint": points,
			}).Error("probe_etcd_proxy_domain_failed")

			return errors.WithMessage(err, "probe_etcd_proxy_domain_failed")
		}

		points = []string{point}
	}

	if len(points) == 0 {
		log.Logger().WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("not_found_etcd_endpoints")

		return fmt.Errorf("not_found_etcd_endpoints")
	}

	log.Logger().WithFields(log.Fields{
		"ip":     t.Agent.IP,
		"idc":    t.Agent.IDC,
		"etcd":   points,
		"status": configs.Mgmt.Status.Host,
		"env":    t.Meta.Env,
	}).Warn("upsert_alb_etcd_endpoints")

	return t.upsertPoints(points)
}
