package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmdb/cmdbvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/cmdb"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/smap"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

type RegisterNodeTask struct {
	Agent *TaskAgent
	Meta  toc.MetaAdapter
}

func NewRegisterNodeTask(alb *v1alpha1.ALB) *RegisterNodeTask {
	server := &RegisterNodeTask{
		Agent: NewTaskAgent(alb),
		Meta:  toc.NewMetaAdapter(string(alb.UID)),
	}

	return server
}

func (t *RegisterNodeTask) Description() string {
	return ""
}

func (t *RegisterNodeTask) Name() string {
	return RegisterNodeTaskName
}

func (t *RegisterNodeTask) Run() error {
	az, err := t.Meta.AZ(t.Agent.AZ)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("fetch_az_failed_failed")

		return errors.WithMessage(err, "fetch_az_failed")
	}

	var sdu *cmdbvo.SDU
	if az.IsGeneral() {
		sdu = &cmdbvo.SDU{
			SDU:       t.Agent.SDU,
			Env:       t.Agent.Env,
			IDC:       t.Agent.IDC,
			ServiceID: configs.ALB.Nginx().ID,
			Hosts:     []string{t.Agent.IP},
			Exporters: configs.ALB.SMAP.Exporters(),
		}
	} else {
		central := configs.ALB.Service.Nginx.SDU.Central
		sdu = &cmdbvo.SDU{
			SDU:       central.Name,
			Env:       central.Env,
			IDC:       central.IDC,
			Hosts:     []string{t.Agent.IP},
			Exporters: configs.ALB.SMAP.Exporters(),
		}
	}

	cmdb, err := cmdb.NewServiceCMDBAdapter(t.Agent.UID)
	if err != nil {
		return errors.WithMessage(err, "fetch_cmdb_failed")
	}

	// ensure service and sdu binding
	err = cmdb.BindServiceSDU(sdu)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("ensure service_to_sdu_on_cmdb_error")

		return errors.WithMessage(err, "ensure_service_to_sdu_on_cmdb_error")
	}

	err = cmdb.BindServer(sdu)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"sdu":     t.Agent.SDU,
		}).Error("bind_server_to_sdu_on_cmdb_error")

		return errors.WithMessage(err, "bind_server_to_sdu_on_cmdb_error")
	}

	mon := smap.NewMonitorAdapter()
	err = mon.Bind(configs.ALB.Nginx().ID, t.Agent.SDU, configs.ALB.SMAP.PortConfigs())
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"sdu":     t.Agent.SDU,
		}).Error("create_monitor_on_space_error")

		return errors.WithMessage(err, "create_monitor_on_space_error")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"az":      t.Agent.AZ,
		"env":     t.Agent.Env,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
		"sdu":     t.Agent.SDU,
	}).Info("bind_server_to_sdu_on_cmdb_success")

	return nil
}
