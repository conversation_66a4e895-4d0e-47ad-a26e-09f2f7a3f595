package job

import (
	"strings"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albprov"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type CheckDeleteProvisionTask struct {
	Agent *TaskAgent
	ALB   *v1alpha1.ALB
	Node  toc.TocexAdapter

	prov   albprov.Provision
	server *tocvo.ALBNodeServer
}

func NewCheckDeleteProvisionTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *CheckDeleteProvisionTask {
	agent := NewTaskAgent(alb)
	traceID := agent.UID

	prov, err := albprov.NewNodeProvision(svr, traceID)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      agent.IP,
			"az":      agent.AZ,
			"env":     agent.Env,
			"uid":     agent.UID,
			"cluster": alb.Name,
		}).Error("toc_alb_provision_delete_error")
	}

	return &CheckDeleteProvisionTask{
		Agent: agent,
		ALB:   alb,
		Node:  toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),

		prov:   prov,
		server: svr,
	}
}

func (t *CheckDeleteProvisionTask) Description() string {
	return ""
}

func (t *CheckDeleteProvisionTask) Name() string {
	return CheckDeleteProvisionTaskName
}

func (t *CheckDeleteProvisionTask) Run() error {
	if err := t.ensureProvision(); err != nil {
		return errors.WithMessage(err, "provision_is_nil_during_task_init")
	}

	provision, err := t.prov.NodeReversedConfig(cmp.ComponentTypeUnSpec)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("toc_alb_provision_delete_check_error")

		return errors.WithMessage(err, "check_delete_provision_failed")
	}

	reservedComs := provision.Components
	if len(reservedComs) > 0 {
		names := slice.Map(reservedComs, func(_ int, com toclib.ProvisionNodeComponent) string {
			return com.Name
		})

		return errors.Errorf("delete_provision_failed_reserved_%s", strings.Join(names, "_"))
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"az":      t.Agent.AZ,
		"env":     t.Agent.Env,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
	}).Info("delete_provision_success")

	return nil
}

func (t *CheckDeleteProvisionTask) ensureProvision() error {
	if t.prov != nil {
		return nil
	}

	prov, err := albprov.NewNodeProvision(t.server, t.Agent.UID)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"az":  t.Agent.AZ,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
		}).Error("toc_alb_check_delete_provision_error")

		return errors.WithMessage(err, "toc_alb_check_delete_provision_error")
	}

	t.prov = prov

	return nil
}
