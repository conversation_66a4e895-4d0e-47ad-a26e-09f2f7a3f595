package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmdb/cmdbvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/cmdb"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

type UnregisterNodeTask struct {
	Agent *TaskAgent
	Meta  toc.MetaAdapter
}

func NewUnregisterNodeTask(alb *v1alpha1.ALB) *UnregisterNodeTask {
	server := &UnregisterNodeTask{
		Agent: NewTaskAgent(alb),
		Meta:  toc.NewMetaAdapter(string(alb.UID)),
	}

	return server
}

func (t *UnregisterNodeTask) Description() string {
	return ""
}

func (t *UnregisterNodeTask) Name() string {
	return UnregisterNodeTaskName
}

func (t *UnregisterNodeTask) Run() error {
	az, err := t.Meta.AZ(t.Agent.AZ)
	if err != nil {
		return errors.WithMessage(err, "fetch_az_failed")
	}

	var sdu *cmdbvo.SDU
	if az.IsGeneral() {
		sdu = &cmdbvo.SDU{
			SDU:   t.Agent.SDU,
			Env:   t.Agent.Env,
			IDC:   t.Agent.IDC,
			Hosts: []string{t.Agent.IP},
		}
	} else {
		central := configs.ALB.Service.Nginx.SDU.Central
		sdu = &cmdbvo.SDU{
			SDU:   central.Name,
			Env:   central.Env,
			IDC:   central.IDC,
			Hosts: []string{t.Agent.IP},
		}
	}

	cmdb, err := cmdb.NewServiceCMDBAdapter(t.Agent.UID)
	if err != nil {
		return errors.WithMessage(err, "fetch_cmdb_failed")
	}

	err = cmdb.UnBindServerToSDU(sdu)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"sdu":     t.Agent.SDU,
		}).Error("unbind_server_to_sdu_error")

		return errors.WithMessage(err, "unbind_server_to_sdu_error")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"az":      t.Agent.AZ,
		"env":     t.Agent.Env,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
		"sdu":     t.Agent.SDU,
	}).Info("unbind_server_to_sdu_success")

	return nil
}
