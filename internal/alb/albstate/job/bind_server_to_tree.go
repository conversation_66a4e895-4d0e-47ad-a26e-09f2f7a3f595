package job

import (
	"strings"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type BindServersToTreeTask struct {
	Agent      *TaskAgent
	ALB        *v1alpha1.ALB
	NodeServer *tocvo.ALBNodeServer
	ServerV3   toc.ServerV3Adapter
}

func NewBindServersToTreeTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *BindServersToTreeTask {
	return &BindServersToTreeTask{
		Agent:      NewTaskAgent(alb),
		ALB:        alb,
		NodeServer: svr,
		ServerV3:   toc.NewServerV3Adapter(string(alb.UID)),
	}
}

func (t *BindServersToTreeTask) Description() string {
	return ""
}

func (t *BindServersToTreeTask) Name() string {
	return BindServersToTreeTaskName
}

func (t *BindServersToTreeTask) Run() error {
	// skip non-seamoney and shopee
	if !t.NodeServer.ServerV3.IsClusterSupported() {
		log.Logger().WithFields(log.Fields{
			"ip":          t.Agent.IP,
			"env":         t.Agent.Env,
			"uid":         t.Agent.UID,
			"az":          t.Agent.AZ,
			"clusterName": t.ALB.Spec.ALBNode.ALBName,
		}).Info("bind_server_to_tree_non_supported_cluster")

		return nil
	}

	// skip non-az servers
	// if !strings.EqualFold(t.NodeServer.ServerV3.Platform.PlatformName, configs.ALB.Platform) {
	if ok := t.NodeServer.ServerV3.MatchedPlatform(configs.ALB.Platform); !ok {
		log.Logger().WithFields(log.Fields{
			"ip":          t.Agent.IP,
			"env":         t.Agent.Env,
			"uid":         t.Agent.UID,
			"az":          t.Agent.AZ,
			"clusterName": t.ALB.Spec.ALBNode.ALBName,
		}).Info("bind_server_to_tree_non_platform_az")

		return nil
	}

	for _, name := range configs.ALB.ResourceTree {
		if strings.HasPrefix(strings.ToLower(name), strings.ToLower(t.NodeServer.Cluster)) {
			resp, err := t.ServerV3.GetResourceTreeNodes(t.NodeServer.Cluster, name)
			if err != nil {
				log.Logger().WithError(err).WithFields(log.Fields{
					"ip":          t.Agent.IP,
					"env":         t.Agent.Env,
					"uid":         t.Agent.UID,
					"az":          t.Agent.AZ,
					"clusterName": t.ALB.Spec.ALBNode.ALBName,
				}).Error("bind_server_to_tree_failed")

				return errors.WithMessage(err, "bind_server_to_tree_failed")
			}

			for _, node := range resp.Data.Nodes {
				_, err = t.ServerV3.BindMainServerTree(t.ALB.Spec.LanIP, node.ID)
				if err != nil {
					log.Logger().WithError(err).WithFields(log.Fields{
						"ip":          t.Agent.IP,
						"env":         t.Agent.Env,
						"uid":         t.Agent.UID,
						"az":          t.Agent.AZ,
						"clusterName": t.ALB.Spec.ALBNode.ALBName,
					}).Error("bind_server_to_tree_failed")

					return errors.WithMessage(err, "bind_server_to_tree_failed")
				}
			}
		}
	}

	log.Logger().WithFields(log.Fields{
		"ip":          t.Agent.IP,
		"env":         t.Agent.Env,
		"uid":         t.Agent.UID,
		"az":          t.Agent.AZ,
		"clusterName": t.ALB.Spec.ALBNode.ALBName,
	}).Info("bind_server_to_tree_success")

	return nil
}
