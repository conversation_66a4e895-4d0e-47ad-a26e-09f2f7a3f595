package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/ops"
)

type MoveNodeIntoMAPoolTask struct {
	Agent *TaskAgent
}

func NewMoveNodeIntoMAPoolTask(alb *v1alpha1.ALB) *MoveNodeIntoMAPoolTask {
	return &MoveNodeIntoMAPoolTask{
		Agent: NewTaskAgent(alb),
	}
}

func (t *MoveNodeIntoMAPoolTask) Description() string {
	return ""
}

func (t *MoveNodeIntoMAPoolTask) Name() string {
	return MoveNodeIntoMAPool
}

func (t *MoveNodeIntoMAPoolTask) Run() error {
	machine := ops.NewMachineAdapter(t.Agent.UID)
	req := ops.MAPoolRequest{
		IP:            t.Agent.IP,
		TicketID:      t.Agent.TicketID,
		ApplicantUser: t.Agent.ApplicantUser,
	}

	if err := machine.MAPool(&req); err != nil {
		log.Logger().WithError(err).WithField("cluster", t.Agent.ALBName).WithField("ip", t.Agent.IP).
			WithField("idc", t.Agent.IDC).Error("move_node_ma_pool_success_error")

		return errors.WithMessage(err, "move_node_ma_pool_success_error")
	}

	log.Logger().WithField("ip", t.Agent.IP).WithField("idc", t.Agent.IDC).
		WithField("cluster", t.Agent.ALBName).Info("move_node_ma_pool_success")

	return nil
}
