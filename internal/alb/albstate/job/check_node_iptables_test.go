package job

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCheckNodeIPTablesTask_Run(t *testing.T) {
	node := sg90TestNode2()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)
	assert.NotNil(t, svr)

	alb := node.ALB(t)
	alb.Spec.ALBNode.TocAttributes = make(map[string]string)
	alb.Spec.ALBNode.TocAttributes["update_iptables_job_id"] = "32797268"

	task := NewCheckNodeIPTablesTask(alb, svr)
	assert.NoError(t, task.Run())
}
