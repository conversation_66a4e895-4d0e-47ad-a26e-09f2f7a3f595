package job

import (
	"fmt"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
)

type CheckNodeMgmtStatusTask struct {
	Agent        *TaskAgent
	TargetStatus string
}

func NewCheckNodeMgmtStatusTask(alb *v1alpha1.ALB, status string) *CheckNodeMgmtStatusTask {
	return &CheckNodeMgmtStatusTask{
		Agent:        NewTaskAgent(alb),
		TargetStatus: status,
	}
}

func (t *CheckNodeMgmtStatusTask) Description() string {
	return ""
}

func (t *CheckNodeMgmtStatusTask) Name() string {
	return CheckNodeMgmtStatusTaskName
}

func (t *CheckNodeMgmtStatusTask) Run() error {
	alb := sgw.NewALBClusterAdapter(t.Agent.UID)

	node, err := alb.GetNodeByIP(t.Agent.IP)
	if err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("check_alb_node_status_error")
	}

	if node == nil {
		log.Logger().WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Warn("not_found_node")

		return nil
	}

	if node.NodeStatus != t.TargetStatus {
		err = fmt.Errorf("%s_in_cluster_%s_status_invalid", t.Agent.IP, t.Agent.ALBName)
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":             t.Agent.IP,
			"az":             t.Agent.AZ,
			"env":            t.Agent.Env,
			"uid":            t.Agent.UID,
			"cluster":        t.Agent.ALBName,
			"target_status":  t.TargetStatus,
			"current_status": node.NodeStatus,
		}).Error("check_alb_node_status_error")

		return errors.WithMessage(err, "check_alb_node_status_error")
	}

	log.Logger().WithFields(log.Fields{
		"ip":             t.Agent.IP,
		"az":             t.Agent.AZ,
		"env":            t.Agent.Env,
		"uid":            t.Agent.UID,
		"cluster":        t.Agent.ALBName,
		"current_status": node.NodeStatus,
	}).Error("check_alb_node_status_success")

	return nil
}
