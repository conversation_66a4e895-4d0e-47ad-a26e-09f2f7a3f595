package job

import (
	"fmt"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type CheckNodeProvisionStatusTask struct {
	ALB   *v1alpha1.ALB
	Agent *TaskAgent
	Node  toc.TocexAdapter
}

func NewCheckNodeProvisionStatusTask(alb *v1alpha1.ALB) *CheckNodeProvisionStatusTask {
	return &CheckNodeProvisionStatusTask{
		Agent: NewTaskAgent(alb),
		ALB:   alb,
		Node:  toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),
	}
}

func (t *CheckNodeProvisionStatusTask) Description() string {
	return ""
}

func (t *CheckNodeProvisionStatusTask) Name() string {
	return NodeProvisionStatusTaskName
}

func (t *CheckNodeProvisionStatusTask) Run() error {
	resp, err := t.Node.GetNodeProvision()
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("check_toc_alb_provision_status_error")

		return errors.WithMessage(err, "fetch_provision_failed")
	}

	if len(resp.Templates) == 0 {
		err = fmt.Errorf("not found any templates")
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Warn("check_toc_alb_provision_status_error")

		return errors.WithMessage(err, "template_not_found")
	}

	if len(resp.Components) == 0 {
		err = fmt.Errorf("not found any components")
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Warn("check_toc_alb_provision_status_error")

		return errors.WithMessage(err, "component_not_found")
	}

	for _, item := range resp.Templates {
		if item.ErrMsg != "" {
			log.Logger().WithFields(log.Fields{
				"ip":      t.Agent.IP,
				"az":      t.Agent.AZ,
				"env":     t.Agent.Env,
				"uid":     t.Agent.UID,
				"cluster": t.Agent.ALBName,
				"path":    item.Path,
			}).Error("check_toc_alb_provision_status_error")

			return fmt.Errorf(item.ErrMsg)
		}
	}

	provisionComponents := make(map[string]toclib.ProvisionNodeComponent)
	for _, item := range resp.Components {
		name := item.Name
		if item.Type == cmp.ComponentTypeDocker {
			name = item.ServiceName
		}

		if _, ok := provisionComponents[name]; ok {
			// if the provision components repeated then pick first one drop others
			continue
		}

		if item.ErrMsg == "" {
			provisionComponents[name] = item
		} else {
			log.Logger().WithFields(log.Fields{
				"ip":           t.Agent.IP,
				"az":           t.Agent.AZ,
				"env":          t.Agent.Env,
				"uid":          t.Agent.UID,
				"cluster":      t.Agent.ALBName,
				"err_msg":      item.ErrMsg,
				"name":         item.Name,
				"version":      item.Version,
				"service_name": item.ServiceName,
			}).Error("check_toc_alb_provision_status_error")

			return fmt.Errorf(item.ErrMsg)
		}
	}

	for _, component := range cmp.ALBCoreComponents {
		if comp, ok := provisionComponents[component.Name]; !ok {
			log.Logger().WithFields(log.Fields{
				"ip":        t.Agent.IP,
				"az":        t.Agent.AZ,
				"env":       t.Agent.Env,
				"uid":       t.Agent.UID,
				"cluster":   t.Agent.ALBName,
				"component": component.Name,
			}).Warn("provision_still_in_progress_please_wait")

			return fmt.Errorf("component_%s_still_in_progress", component.Name)
		} else if component.VerifyVersion {
			ver := albvo.ALBNodeComponentVersion(component.Name, &t.ALB.Spec.ALBNode)
			if ver != "" && ver != comp.Version {
				log.Logger().WithFields(log.Fields{
					"ip":              t.Agent.IP,
					"az":              t.Agent.AZ,
					"env":             t.Agent.Env,
					"uid":             t.Agent.UID,
					"cluster":         t.Agent.ALBName,
					"component":       component.Name,
					"current_version": comp.Version,
					"target_version":  ver,
				}).Warn("provision_still_in_progress_please_wait")

				return fmt.Errorf("component_%s_still_in_progress", component.Name)
			}
		}
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"az":      t.Agent.AZ,
		"env":     t.Agent.Env,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
	}).Info("check_toc_alb_provision_status_success")

	return nil
}
