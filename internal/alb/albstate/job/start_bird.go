//nolint:dupl
package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
)

type StartBirdTask struct {
	BirdTask
	haMode string
}

func NewStartBirdTask(alb *v1alpha1.ALB, haMode string) *StartBirdTask {
	task := &StartBirdTask{
		haMode: haMode,
	}
	task.ALB = alb

	return task
}

func (t *StartBirdTask) Description() string {
	return ""
}

func (t *StartBirdTask) Name() string {
	return StartBirdTaskName
}

func (t *StartBirdTask) Run() error {
	if err := t.Start(); err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"az":      t.ALB.Spec.ALBNode.Zone,
			"ha_mode": t.haMode,
		}).WithError(err).Error("start_bird_failed")

		return errors.WithMessage(err, "start_bird_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.ALB.Spec.LanIP,
		"env":     t.ALB.Spec.ALBNode.Env,
		"idc":     t.ALB.Spec.ALBNode.IDC,
		"az":      t.ALB.Spec.ALBNode.Zone,
		"uid":     t.ALB.UID,
		"cluster": t.ALB.Spec.ALBNode.ALBName,
	}).Info("start_bird_success")

	return nil
}
