//nolint:dupl
package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type KeepalivedTask struct {
	ALB *v1alpha1.ALB
}

func (t *KeepalivedTask) action(script string) error {
	// if private then action on keepalived
	meta := toc.NewMetaAdapter(string(t.ALB.UID))
	az, err := meta.AZ(t.ALB.Spec.ALBNode.Zone)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
		}).Error("fetch_az_failed")

		return errors.WithMessage(err, "fetch_az_failed")
	}

	isGeneral, err := az.IsGeneralAZSeg(t.ALB.Spec.ALBNode.Segment)
	if err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
			"segment": t.ALB.Spec.ALBNode.Segment,
		}).WithError(err).Error("check_general_az_seg_failed_in_keepalived")

		return errors.WithMessage(err, "check_general_az_seg_failed")
	}

	if isGeneral {
		log.Logger().WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
			"cmd":     script,
		}).Warn("keepalived_on_general_az_skipped")

		return nil
	}

	node := toc.NewTocexAdapter(tocvo.NewTocNode(&t.ALB.Spec))
	_, err = node.RunTask(script)
	if err != nil {
		return errors.WithMessage(err, "action_keepalived_failed")
	}

	return nil
}

func (t *KeepalivedTask) Start() error {
	if err := t.action(consts.KeepalivedStartScript); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
		}).Error("start_keepalived_failed")

		return errors.WithMessage(err, "start_keepalived_failed")
	}

	return nil
}

func (t *KeepalivedTask) Stop() error {
	if err := t.action(consts.KeepalivedStopScript); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
		}).Error("stop_keepalived_failed")

		return errors.WithMessage(err, "stop_keepalived_failed")
	}

	return nil
}
