package job

import (
	"strings"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type CheckNodeListenerTask struct {
	Agent *TaskAgent
	Node  toc.TocexAdapter
}

func NewCheckNodeListenerTask(alb *v1alpha1.ALB) *CheckNodeListenerTask {
	return &CheckNodeListenerTask{
		Agent: NewTaskAgent(alb),
		Node:  toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),
	}
}

func (t *CheckNodeListenerTask) Description() string {
	return ""
}

func (t *CheckNodeListenerTask) Name() string {
	return CheckNodeListenerTaskName
}

func (t *CheckNodeListenerTask) Run() error {
	cluster := sgw.NewALBClusterAdapter(t.Agent.UID)

	cls, err := cluster.GetByNode(t.Agent.IP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).Error("fetch_cluster_by_node_failed")

		return errors.WithMessage(err, "fetch_cluster_failed")
	}

	cluster2, err := cluster.GetByUUID(cls.UUID)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).Error("fetch_cluster_by_uuid_failed")

		return errors.WithMessage(err, "fetch_cluster_failed")
	}

	// listener bound but still not released
	// same listeners may bind to multiple instances
	listenerCount := cluster2.ListenerCountFromInstances()

	if listenerCount == 0 {
		return nil
	}

	// listener bound and released
	instance := sgw.NewALBInstanceAdapter(t.Agent.UID)
	listeners := slice.Map(cluster2.Instances, func(_ int, ins *albvo.Instance) []string {
		req := &sgw.L7ListenerBoundRequest{
			InstanceID: ins.InstanceID,
		}
		req.Pager = consts.NewDefaultPager()
		bindings, err := instance.ListenerBindings(req)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":  t.Agent.IP,
				"env": t.Agent.Env,
				"uid": t.Agent.UID,
				"az":  t.Agent.AZ,
			}).Error("fetch_listener_bindings_by_instance_id_failed")

			return nil
		}

		return slice.Map(bindings, func(_ int, binding *albvo.InstanceListenerBinding) string {
			if binding == nil {
				return ""
			}

			if binding.Listener == nil || binding.Listener.DomainName == "" {
				return binding.DomainName
			}

			return binding.Listener.DomainName
		})
	})

	listeners2 := slice.FlatMap(listeners, func(_ int, listener []string) []string {
		return listener
	})
	listeners2 = slice.Unique(listeners2)

	files, err := t.Node.ListFiles("/etc/nginx/http-enabled/")
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).Error("check_node_listener_failed")

		return errors.WithMessage(err, "fetch_ngx_enabled_files_failed")
	}
	// filter hidden files
	filesAtNode := slice.Filter(files, func(_ int, f *toclib.FileInfo) bool {
		return !strings.HasPrefix(f.Name, ".")
	})

	filesAtNode2 := slice.Map(filesAtNode, func(_ int, f *toclib.FileInfo) string {
		return f.Name
	})

	listener3 := slice.Difference(listeners2, filesAtNode2)
	if len(listener3) != 0 {
		err = errors.Errorf("cluster_bound_listener_%d_not_at_node", len(listener3))
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":        t.Agent.IP,
			"env":       t.Agent.Env,
			"uid":       t.Agent.UID,
			"az":        t.Agent.AZ,
			"listeners": listener3,
		}).Error("check_node_listener_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":        t.Agent.IP,
		"env":       t.Agent.Env,
		"uid":       t.Agent.UID,
		"az":        t.Agent.AZ,
		"listeners": len(listeners2),
		"files":     len(filesAtNode),
	}).Info("check_node_listener_success")

	return nil
}
