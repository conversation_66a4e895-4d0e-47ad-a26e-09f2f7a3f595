package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type PersistStaticCacheTask struct {
	ALB *v1alpha1.ALB
}

func NewPersistStaticCacheTask(alb *v1alpha1.ALB) *PersistStaticCacheTask {
	task := &PersistStaticCacheTask{}
	task.ALB = alb

	return task
}

func (t *PersistStaticCacheTask) Description() string {
	return ""
}

func (t *PersistStaticCacheTask) Name() string {
	return PersistStaticCacheTaskName
}

func (t *PersistStaticCacheTask) Run() error {
	node := toc.NewTocexAdapter(tocvo.NewTocNode(&t.ALB.Spec))
	_, err := node.RunTask(consts.NginxSetSGWCDNProxyScript)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
		}).Error("persist_static_cache_failed")

		return errors.WithMessage(err, "persist_static_cache_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.ALB.Spec.LanIP,
		"env":     t.ALB.Spec.ALBNode.Env,
		"idc":     t.ALB.Spec.ALBNode.IDC,
		"az":      t.ALB.Spec.ALBNode.Zone,
		"uid":     t.ALB.UID,
		"cluster": t.ALB.Spec.ALBNode.ALBName,
	}).Info("persist_static_cache_success")

	return nil
}
