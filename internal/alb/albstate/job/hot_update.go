package job

import (
	"fmt"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type UpdateTocexTask struct {
	ALB  *v1alpha1.ALB
	Node toc.TocexAdapter
}

func NewHotUpdateTocexTask(alb *v1alpha1.ALB) *UpdateTocexTask {
	return &UpdateTocexTask{
		ALB:  alb,
		Node: toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),
	}
}

func (t *UpdateTocexTask) Description() string {
	return ""
}

func (t *UpdateTocexTask) Name() string {
	return HotUpdateTaskName
}

func (t *UpdateTocexTask) Run() error {
	switch t.ALB.Spec.ALBNode.HotUpdateConfig.Type {
	case consts.DeleteTocexItem:
		temps := tocvo.TocTemplates(t.ALB.Spec.ALBNode.HotUpdateConfig.Templates)
		comps := tocvo.TocComponents(t.ALB.Spec.ALBNode.HotUpdateConfig.Components)
		components := comps.ToNodeComponents()
		for i := 0; i < len(components); i++ {
			com := &components[i]
			com.Disabled = true
			com.EnsureServiceInactive = true
			com.DisableRestartService = true
			com.RemovePackageOnDisabled = true
			com.StopServiceOnDelete = true
		}

		err := t.Node.DeleteProvision(&toclib.ProvisionNodeConfig{
			HostIP:     t.ALB.Spec.LanIP,
			Components: components,
			Templates:  temps.ToNodeTemplates(),
		})
		if err != nil {
			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("type", consts.DeleteTocexItem).
				Error("hot_update_tocex_error")

			return errors.WithMessage(err, "delete_provision_failed")
		}

	case consts.UpdateTocexItem, consts.AddTocexItem:
		temps := tocvo.TocTemplates(t.ALB.Spec.ALBNode.HotUpdateConfig.Templates)
		comps := tocvo.TocComponents(t.ALB.Spec.ALBNode.HotUpdateConfig.Components)

		coms := comps.ToNodeComponents()
		tems := temps.ToNodeTemplates()
		err := t.Node.SetNodeProvision(&toclib.ProvisionNodeConfig{
			HostIP:     t.ALB.Spec.LanIP,
			Components: coms,
			Templates:  tems,
		})
		if err != nil {
			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				Error("hot_update_tocex_error")

			return errors.WithMessage(err, "provision_failed")
		}

		log.Logger().WithFields(log.Fields{
			"components": slice.Map(coms, func(_ int, item toclib.ProvisionNodeComponent) string {
				return fmt.Sprintf("%s:%s", item.Name, item.Version)
			}),
			"templates": slice.Map(tems, func(_ int, item toclib.ProvisionNodeTemplate) string {
				return item.Path
			}),
			"ip":   t.ALB.Spec.LanIP,
			"type": t.ALB.Spec.ALBNode.HotUpdateConfig.Type,
		}).Info("hot_update_tocex")

	default:
		log.Logger().WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
			WithField("ip", t.ALB.Spec.LanIP).Warn("update_tocex_config_invalid_type")

		return nil
	}

	log.Logger().WithField("ip", t.ALB.Spec.LanIP).WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
		Info("hot_update_tocex_success")

	return nil
}
