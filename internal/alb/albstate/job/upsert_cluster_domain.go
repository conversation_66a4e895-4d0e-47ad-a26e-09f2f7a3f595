package job

import (
	"fmt"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type UpsertClusterDomainTask struct {
	Agent      *TaskAgent
	NodeServer *tocvo.ALBNodeServer
	Meta       *sgwvo.ALBClusterConfigMeta
	Cluster    sgw.L7ClusterAdapter
}

func NewUpsertClusterDomainTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *UpsertClusterDomainTask {
	agent := NewTaskAgent(alb)

	return &UpsertClusterDomainTask{
		Agent:      agent,
		NodeServer: svr,
		Meta:       svr.Meta,
		Cluster:    sgw.NewALBClusterAdapter(agent.UID),
	}
}

func (t *UpsertClusterDomainTask) Description() string {
	return ""
}

func (t *UpsertClusterDomainTask) Name() string {
	return UpsertClusterDomainTaskName
}

func (t *UpsertClusterDomainTask) Run() error {
	// fetch cluster by IP, if not found etcd then add. if found and diff then update
	cls, err := t.Cluster.GetByNode(t.Agent.IP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("fetch_sgw_l7_cluster_by_node_failed")

		return errors.WithMessage(err, "fetch_sgw_l7_cluster_by_node_failed")
	}

	// etcd endpoint fetch from toc variables
	if cls.ALBClusterDomains != nil && cls.ALBClusterDomains.ExistsDomain() {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Info("cluster_domain_exist_no_need_to_update")

		return nil
	}

	prefix := t.Meta.RZ

	if t.NodeServer.IsClusterDomainWithNetworkZone() {
		zone := t.NodeServer.ShortenNetworkZone()
		p, err := t.Meta.PrefixWithNetworkZone(zone)
		if err != nil {
			return errors.WithMessage(err, "get_prefix_with_network_zone_failed")
		}
		prefix = p
	}

	req := &sgw.L7ClusterDomainUpdateRequest{
		UUID: cls.UUID,
		ClusterDomains: &sgw.ClusterDomains{WanDomain: &sgw.ClusterDomain{
			Prefix: prefix,
			Suffix: t.Meta.ClusterDomainSuffix(),
		}},
	}

	ok, err := t.Cluster.UpdateWANDomain(req)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"req":     req,
		}).Error("update_cluster_domain_failed")

		return errors.WithMessage(err, "update_cluster_domain_failed")
	}

	if !ok {
		err = fmt.Errorf("update_alb_%s_cluster_domain_failed", cls.Name)
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"idc":     t.Agent.IDC,
			"az":      t.Agent.AZ,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
			"req":     req,
		}).Error("update_cluster_domain_failed")

		return errors.WithMessage(err, "update_cluster_domain_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.Agent.IP,
		"idc":     t.Agent.IDC,
		"az":      t.Agent.AZ,
		"uid":     t.Agent.UID,
		"cluster": t.Agent.ALBName,
		"req":     req,
	}).Info("update_alb_cluster_domain_successfully")

	return nil
}
