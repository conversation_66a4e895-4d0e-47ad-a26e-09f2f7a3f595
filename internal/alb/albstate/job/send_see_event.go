package job

import (
	"context"
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/byteutils"
	"git.garena.com/shopee/go-shopeelib/json"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	leap "git.garena.com/shopee/devops/leap-apis/pkg/generated/clientset/versioned"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/see"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsinf/opscah"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

type SendSeeEventTask struct {
	ALB        *v1alpha1.ALB
	LeapClient leap.Interface
	Event      see.EventAdapter
}

func NewSendSeeEventTask(alb *v1alpha1.ALB, leapClient leap.Interface) *SendSeeEventTask {
	return &SendSeeEventTask{
		ALB:        alb,
		LeapClient: leapClient,
		Event:      see.NewEventAdapter(string(alb.UID)),
	}
}

func (t *SendSeeEventTask) Description() string {
	return ""
}

func (t *SendSeeEventTask) Name() string {
	return SendSeeEventTaskName
}

func (t *SendSeeEventTask) Run() error {
	ctx := context.Background()
	if t.ALB.Spec.ALBNode.SWPTicket == 0 {
		log.Logger().WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
		}).Warn("ticket_not_found")

		return nil
	}

	var tasks []string
	if vals, err := opscah.ElasticRedis.HGet(ctx, t.ALB.Name, t.ALB.Status.State).Bytes(); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
			"state":   t.ALB.Status.State,
		}).Error("fetch_state_task_failed")

		return errors.WithMessage(err, "fetch_state_task_failed")
	} else if err = json.Unmarshal(vals, &tasks); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
			"state":   t.ALB.Status.State,
		}).Error("unmarshal_state_task_failed")

		return errors.WithMessage(err, "unmarshal_state_task_failed")
	}

	if len(tasks) == 0 {
		return nil
	}

	tasks = slice.Filter(tasks, func(_ int, task string) bool {
		return !strings.EqualFold(task, t.Name())
	})

	var results []*sgwvo.TaskResult
	keyState := fmt.Sprintf("%s-%s", t.ALB.Name, t.ALB.Status.State)
	vals := opscah.ElasticRedis.HMGet(ctx, keyState, tasks...).Val()

	slice.ForEach(vals, func(i int, v interface{}) {
		val, ok := v.(string)
		if !ok {
			return
		}

		var result *sgwvo.TaskResult
		if err := json.Unmarshal(byteutils.ToBytes(val), &result); err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":      t.ALB.Spec.LanIP,
				"env":     t.ALB.Spec.ALBNode.Env,
				"idc":     t.ALB.Spec.ALBNode.IDC,
				"uid":     t.ALB.UID,
				"cluster": t.ALB.Spec.ALBNode.ALBName,
				"state":   t.ALB.Status.State,
			}).Error("unmarshal_state_task_failed")

			return
		}

		results = append(results, result)
	})

	var operator string
	if t.ALB.Spec.ALBNode.Options != nil {
		operator = t.ALB.Spec.ALBNode.Options["applicant_user"]
	}

	swpID := fmt.Sprintf("SWP-%d", t.ALB.Spec.ALBNode.SWPTicket)

	results2 := sgwvo.TaskResults(results)

	postCheck := t.Event.ALBNodePostCheck()
	postCheck.SetSourceGroup(swpID)
	postCheck.SetOperator(operator)
	postCheck.SetPriority(consts.HighPriority.Code)
	if strings.EqualFold(t.ALB.Status.State, consts.PreRunningState.Status) {
		postCheck.SetTitle(fmt.Sprintf("%s:Online ALB Node %s", swpID, t.ALB.Spec.LanIP))
	} else {
		postCheck.SetTitle(fmt.Sprintf("%s:Offline ALB Node %s", swpID, t.ALB.Spec.LanIP))
	}
	postCheck.SetStartTime(results2.FirstTime())
	postCheck.SetEndTime(results2.LastTime())
	postCheck.AddExtra("eventContent", results)
	postCheck.AddExtra("ip", t.ALB.Spec.LanIP)

	postCheck.AddTag("AZ", t.ALB.Spec.ALBNode.Zone)
	postCheck.AddTag("Segment", t.ALB.Spec.ALBNode.Segment)
	postCheck.AddTag("RZ", t.ALB.Spec.ALBNode.IDC)
	postCheck.AddTag("Env", t.ALB.Spec.ALBNode.Env)

	postCheck.Send()

	t.ALB.Spec.ALBNode.SWPTicket = 0

	ctx, cancel := context.WithTimeout(context.Background(), configs.Mgmt.Timeout())
	defer cancel()

	if _, err := t.LeapClient.MachineV1alpha1().ALBs().Update(ctx, t.ALB, metav1.UpdateOptions{}); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
			"swp_id":  t.ALB.Spec.ALBNode.SWPTicket,
		}).Error("update_alb_info_error")

		return errors.WithMessage(err, "update_alb_info_error")
	}

	return nil
}
