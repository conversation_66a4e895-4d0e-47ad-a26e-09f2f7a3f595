package job

import (
	"context"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	leap "git.garena.com/shopee/devops/leap-apis/pkg/generated/clientset/versioned"
)

type DeleteALBCRTask struct {
	ALB        *v1alpha1.ALB
	LeapClient leap.Interface
}

func NewDeleteALBCRTask(alb *v1alpha1.ALB, leapClientSet leap.Interface) *DeleteALBCRTask {
	return &DeleteALBCRTask{
		ALB:        alb,
		LeapClient: leapClientSet,
	}
}

func (t *DeleteALBCRTask) Description() string {
	return ""
}

func (t *DeleteALBCRTask) Name() string {
	return DeleteALBCRTaskName
}

func (t *DeleteALBCRTask) Run() error {
	err := t.LeapClient.MachineV1alpha1().ALBs().Delete(context.Background(), t.ALB.Name, metav1.DeleteOptions{})
	if err != nil {
		log.Logger().WithError(err).WithField("alb_name", t.ALB.Name).WithField("ip", t.ALB.Spec.LanIP).
			WithField("uid", t.ALB.UID).Error("delete_alb_cr_error")

		return errors.WithMessage(err, "delete_alb_cr_failed")
	}

	log.Logger().WithField("alb_name", t.ALB.Name).WithField("ip", t.ALB.Spec.LanIP).
		WithField("alb_cluster_name", t.ALB.Spec.ALBNode.ALBName).WithField("idc", t.ALB.Spec.ALBNode.IDC).
		WithField("uid", t.ALB.UID).WithField("env", t.ALB.Spec.ALBNode.Env).Info("delete_alb_cr_success")

	return nil
}
