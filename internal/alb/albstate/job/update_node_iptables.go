package job

import (
	"context"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	leap "git.garena.com/shopee/devops/leap-apis/pkg/generated/clientset/versioned"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type UpdateNodeIPTablesTask struct {
	Agent      *TaskAgent
	ALB        *v1alpha1.ALB
	NodeServer *tocvo.ALBNodeServer
	Playbook   toc.PlayBookerAdapter
	LeapClient leap.Interface
}

// NewUpdateNodeIPTablesTask creates a new UpdateNodeIPTablesTask
//
//nolint:nolintlint,lll
func NewUpdateNodeIPTablesTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer, leapClient leap.Interface) *UpdateNodeIPTablesTask {
	return &UpdateNodeIPTablesTask{
		Agent:      NewTaskAgent(alb),
		ALB:        alb,
		NodeServer: svr,
		Playbook:   toc.NewPlayBookAdapter(svr.Cluster, string(alb.UID)),
		LeapClient: leapClient,
	}
}

func (t *UpdateNodeIPTablesTask) Description() string {
	return ""
}

func (t *UpdateNodeIPTablesTask) Name() string {
	return UpdateNodeIPTablesTaskName
}

func (t *UpdateNodeIPTablesTask) Run() error {
	jobID, err := t.Playbook.UpdateIPTables([]string{t.ALB.Spec.LanIP})
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).Error("run_update_iptables_failed")

		return errors.WithMessage(err, "run_update_iptables_failed")
	}

	if t.ALB.Spec.ALBNode.TocAttributes == nil {
		t.ALB.Spec.ALBNode.TocAttributes = make(map[string]string)
	}
	t.ALB.Spec.ALBNode.TocAttributes["update_iptables_job_id"] = cast.ToString(jobID)

	_, err = t.LeapClient.MachineV1alpha1().ALBs().Update(context.Background(), t.ALB, metav1.UpdateOptions{})
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":     t.Agent.IP,
			"env":    t.Agent.Env,
			"uid":    t.Agent.UID,
			"az":     t.Agent.AZ,
			"job_id": jobID,
		}).Error("update_alb_job_id_failed")

		return errors.WithMessage(err, "update_alb_job_id_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":     t.Agent.IP,
		"env":    t.Agent.Env,
		"uid":    t.Agent.UID,
		"az":     t.Agent.AZ,
		"job_id": jobID,
	}).Info("run_update_iptables_success")

	return nil
}
