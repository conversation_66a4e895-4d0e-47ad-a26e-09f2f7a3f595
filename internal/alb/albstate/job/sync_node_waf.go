package job

import (
	"encoding/json"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/sourcegraph/conc/iter"

	"git.garena.com/shopee/go-shopeelib/byteutils"
	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/acl/aclvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type SyncNodeWAFTask struct {
	Agent   *TaskAgent
	ACL     sgw.ACLAdapter
	Tocex   toc.TocexAdapter
	TraceID string
	ALB     *v1alpha1.ALB
}

func NewSyncNodeWAFTask(alb *v1alpha1.ALB) *SyncNodeWAFTask {
	return &SyncNodeWAFTask{
		Agent: NewTaskAgent(alb),
		ACL:   sgw.NewACLAdapter(string(alb.UID)),
		Tocex: toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),

		TraceID: string(alb.UID),
		ALB:     alb,
	}
}

func (t *SyncNodeWAFTask) Description() string {
	return ""
}

func (t *SyncNodeWAFTask) Name() string {
	return SyncNodeWAFTaskName
}

func (t *SyncNodeWAFTask) fetchPolicyGaps() ([]string, error) {
	ipset, err := t.ACL.DumpIPSet()
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).Error("dump_ipset_failed")

		return nil, errors.WithMessage(err, "dump_ipset_failed")
	}

	node := toc.NewSGWNodeAdapter2(t.Tocex)
	files, err := node.ListWAFPolicy()
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).Error("list_waf_policy_files_failed")

		return nil, errors.WithMessage(err, "list_waf_policy_files_failed")
	}

	ipsets := slice.Map(ipset, func(_ int, ipset *aclvo.IPSet) string {
		return ipset.Name
	})
	policies := slice.Map(files, func(_ int, file *toclib.FileInfo) string {
		return file.Name
	})

	gaps := slice.Difference(ipsets, policies)

	log.Logger().WithFields(log.Fields{
		"ip":       t.Agent.IP,
		"env":      t.Agent.Env,
		"uid":      t.Agent.UID,
		"az":       t.Agent.AZ,
		"ipsets":   len(ipsets),
		"policies": len(policies),
		"gaps":     len(gaps),
	}).Info("dump_ipset_success")

	return gaps, nil
}

func (t *SyncNodeWAFTask) syncPolicy(ipsets []string) {
	log.Logger().WithFields(log.Fields{
		"ip":    t.Agent.IP,
		"ipset": len(ipsets),
	}).Info("sync_waf_start")

	start := time.Now()

	iter.ForEach(ipsets, func(ipset *string) {
		if ipset == nil {
			return
		}

		set := *ipset
		acl := sgw.NewACLAdapter(t.TraceID)
		ips, err := acl.GetIPs(set)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":  t.Agent.IP,
				"env": t.Agent.Env,
				"uid": t.Agent.UID,
				"az":  t.Agent.AZ,
			}).Error("get_ips_failed")

			return
		}
		ips = slice.Map(ips, func(_ int, ip *aclvo.IP) *aclvo.IP {
			if !strings.EqualFold(set, ip.IPSetName) {
				return nil
			}

			if ip.ExpiredAt == 0 {
				ip.ExpiredAt = 7920604800
			}

			return ip
		})

		ips = slice.Filter(ips, func(_ int, ip *aclvo.IP) bool {
			return ip != nil
		})

		if len(ips) == 0 {
			log.Logger().WithFields(log.Fields{
				"ip":    t.Agent.IP,
				"env":   t.Agent.Env,
				"uid":   t.Agent.UID,
				"az":    t.Agent.AZ,
				"ipset": ipset,
			}).Warn("no_ips_found")

			return
		}

		ipMap := convertor.ToMap(ips, func(ip *aclvo.IP) (string, int) {
			return ip.IP, ip.ExpiredAt
		})

		ret, err := json.Marshal(ipMap)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":    t.Agent.IP,
				"env":   t.Agent.Env,
				"uid":   t.Agent.UID,
				"az":    t.Agent.AZ,
				"ipset": ipset,
				"ips":   len(ipMap),
			}).Error("marshal_failed")

			return
		}

		tocex := toc.NewTocexAdapter(tocvo.NewTocNode(&t.ALB.Spec))
		node := toc.NewSGWNodeAdapter2(tocex)
		if err = node.UploadPolicy(set, byteutils.ToString(ret)); err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":    t.Agent.IP,
				"env":   t.Agent.Env,
				"uid":   t.Agent.UID,
				"az":    t.Agent.AZ,
				"ipset": ipset,
				"ips":   len(ipMap),
			}).Error("write_file_failed")
		}
	})

	log.Logger().WithFields(log.Fields{
		"ip":    t.Agent.IP,
		"env":   t.Agent.Env,
		"uid":   t.Agent.UID,
		"az":    t.Agent.AZ,
		"cost":  time.Since(start).Seconds(),
		"ipset": len(ipsets),
	}).Info("sync_waf_end")
}

func (t *SyncNodeWAFTask) Run() error {
	ipsets, err := t.fetchPolicyGaps()
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).Error("fetch_policy_gaps_failed")

		return errors.WithMessage(err, "fetch_policy_gaps_failed")
	}

	t.syncPolicy(ipsets)

	log.Logger().WithFields(log.Fields{
		"ip":     t.Agent.IP,
		"env":    t.Agent.Env,
		"uid":    t.Agent.UID,
		"az":     t.Agent.AZ,
		"ipsets": len(ipsets),
	}).Info("sync_waf_success")

	return nil
}
