package job

import (
	"context"
	"fmt"
	"net"
	"net/http"

	"github.com/asaskevich/govalidator"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
)

//go:generate mockgen -destination sniffer_mock.go -package job -source sniffer.go

type Sniffer interface {
	Probe(target string) (bool, error)
}

type ALBSniffer struct{}

func NewALBSniffer() Sniffer {
	return &ALBSniffer{}
}

func (p *ALBSniffer) Probe(ip string) (bool, error) {
	if !govalidator.IsIP(ip) {
		return false, errors.New("invalid ip")
	}

	dialer := &net.Dialer{
		Timeout: configs.Mgmt.Timeout(),
	}

	transport := http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			conn, err := dialer.DialContext(ctx, network, fmt.Sprintf("%s:443", ip))
			if err != nil {
				return nil, errors.WithMessage(err, "dial_conn_failed")
			}

			return conn, nil
		},
	}

	url := "https://shopeemobile.com/myip"
	resp, err := resty.New().SetTransport(&transport).R().Get(url)
	if err != nil {
		return false, errors.WithMessage(err, "probe_node_status_failed")
	}
	if resp.StatusCode() != http.StatusOK {
		return false, fmt.Errorf("unexpected response status %s", resp.Status())
	}

	return true, nil
}

type ALBEtcdSniffer struct{}

func NewALBEtcdSniffer() Sniffer {
	return &ALBEtcdSniffer{}
}

func (s *ALBEtcdSniffer) Probe(point string) (bool, error) {
	if !govalidator.IsURL(point) {
		return false, errors.New("invalid point")
	}

	_, err := resty.New().SetTimeout(configs.Mgmt.QuickTimeout()).R().Get(point)
	if err != nil {
		return false, errors.WithMessage(err, "probe_etcd_proxy_failed")
	}

	return true, nil
}
