package job

import (
	"strings"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

type CheckHotUpdateTask struct {
	ALB        *v1alpha1.ALB
	Node       toc.TocexAdapter
	templates  map[string]*toclib.ProvisionNodeTemplate
	components map[string]*toclib.ProvisionNodeComponent
}

func (t *CheckHotUpdateTask) nginxVersion() string {
	version, err := t.Node.RunTask(consts.NginxVersionScript)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"az":      t.ALB.Spec.ALBNode.Zone,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
		}).Error("fetch_version_failed")

		return consts.UnknownVersion
	}

	return strings.TrimSuffix(version, "\n")
}
