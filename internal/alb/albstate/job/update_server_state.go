package job

import (
	"strings"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type UpdateServerStateTask struct {
	ALB        *v1alpha1.ALB
	NodeServer *tocvo.ALBNodeServer
	ToState    string

	Server   toc.ServerAdapter
	ServerV3 toc.ServerV3Adapter
}

// NewUpdateServerStateTask creates a new instance of UpdateServerStateTask.
//
// Parameters:
// - alb: The alb parameter is of type *v1alpha1.ALB.
// - svr: The svr parameter is of type *tocvo.ALBNodeServer.
// - toState: The toState parameter is of type string.
//
// Returns:
// - *UpdateServerStateTask: The function returns a pointer to UpdateServerStateTask.
func NewUpdateServerStateTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer, toState string) *UpdateServerStateTask {
	return &UpdateServerStateTask{
		ALB:        alb,
		NodeServer: svr,
		ToState:    toState,

		Server:   toc.NewServerAdapter(string(alb.UID)),
		ServerV3: toc.NewServerV3Adapter(string(alb.UID)),
	}
}

// Description returns the description of the UpdateServerStateTask Go function.
//
// This function does not take any parameters.
// It returns a string.
func (t *UpdateServerStateTask) Description() string {
	return ""
}

// Name returns the name of the UpdateServerStateTask.
//
// No parameters.
// string.
func (t *UpdateServerStateTask) Name() string {
	return UpdateServerStateTaskName
}

// Run executes the UpdateServerStateTask.
//
// It compares the current state of the server with the desired state.
// If they are equal, the function returns nil.
// Otherwise, it calls the UpdateState method of the Server object to update the state.
// It logs an error message if the update fails and returns the error.
// The function returns nil if the update is successful.
func (t *UpdateServerStateTask) Run() error {
	if strings.EqualFold(t.ToState, t.NodeServer.ServerGroupVar.Server.ServerLabels.StateName) {
		return nil
	}

	server, err := t.ServerV3.Server(t.ALB.Spec.LanIP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":       t.ALB.Spec.LanIP,
			"env":      t.ALB.Spec.ALBNode.Env,
			"idc":      t.ALB.Spec.ALBNode.IDC,
			"uid":      t.ALB.UID,
			"cluster":  t.ALB.Spec.ALBNode.ALBName,
			"to_state": t.ToState,
		}).Error("fetch_server_by_ip_failed")

		return errors.WithMessage(err, "fetch_server_by_ip_failed")
	}

	fromState := t.NodeServer.ServerGroupVar.Server.ServerLabels.StateName
	_, err = t.Server.UpdateState(&toc.UpdateStateRequest{
		Cluster:   server.Cluster,
		FromState: fromState,
		ToState:   t.ToState,
		IgnoreJob: true,
		ServerIDs: []uint64{t.NodeServer.ServerGroupVar.Server.ServerLabels.ServerID},
	})
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":         t.ALB.Spec.LanIP,
			"env":        t.ALB.Spec.ALBNode.Env,
			"idc":        t.ALB.Spec.ALBNode.IDC,
			"uid":        t.ALB.UID,
			"cluster":    t.ALB.Spec.ALBNode.ALBName,
			"from_state": fromState,
			"to_state":   t.ToState,
		}).Error("update_server_state_failed")

		return errors.WithMessage(err, "update_server_state_failed")
	}

	return nil
}
