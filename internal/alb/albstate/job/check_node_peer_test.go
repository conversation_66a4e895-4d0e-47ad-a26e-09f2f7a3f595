package job

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

func TestCheckNodePeerTask_Run(t *testing.T) {
	node := my0StagingNode0()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	task := NewCheckNodePeerTask(node.ALB(t), svr)
	task.Node = node.Tocex
	assert.NoError(t, task.Run())
}

func TestCheckNodePeerTask_Run_OnlyReadyBrief(t *testing.T) {
	tests := []struct {
		name           string
		birdOutput     string
		haMode         consts.HighAvailableMode
		networkVersion string
		expectError    bool
		errorContains  string
	}{
		{
			name:           "Only ready brief without newline",
			birdOutput:     "BIRD 2.0.3 ready.",
			haMode:         consts.HAMNonStd2,
			networkVersion: "v3.0",
			expectError:    true,
			errorContains:  "no_bird_proto_brief",
		},
		{
			name:           "Only ready brief with newline",
			birdOutput:     "BIRD 2.0.3 ready.\n",
			haMode:         consts.HAMNonStd2,
			networkVersion: "v3.0",
			expectError:    true,
			errorContains:  "no_bird_proto_brief",
		},
		{
			name: "no BGP brief",
			birdOutput: `BIRD 2.0.3 ready.
			Name       Proto      Table      State  Since         Info
			device1    Device     ---        up     2023-03-13`,
			haMode:         consts.HAMNonStd2,
			networkVersion: "v3.0",
			expectError:    true,
			errorContains:  "no_bgp_peer",
		},
		{
			name: "BGP brief ok",
			birdOutput: `BIRD 2.0.3 ready.
			Name       Proto      Table      State  Since         Info
			device1    Device     ---        up     2023-03-13
			static1    Static     master4    up     2023-03-13
			ecmp_lan1_bgp BGP        ---        up     2023-03-13    Established
			ecmp_lan2_bgp BGP        ---        up     2023-03-13    Established`,
			haMode:         consts.HAMNonStd2,
			networkVersion: "v3.0",
			expectError:    false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockNode := toc.NewMockTocexAdapter(ctrl)
			mockNode.EXPECT().RunTask(consts.BirdShowProtocolScript).
				Return(tc.birdOutput, nil)

			mockServerV3 := tocvo.NewMockIServerV3(ctrl)
			mockServerV3.EXPECT().HAMode(gomock.Any()).
				Return(tc.haMode, nil)

			mockALBNodeServer := &tocvo.ALBNodeServer{
				Segment: &tocvo.Segment{
					NetworkVersion: tc.networkVersion,
				},
				ServerV3: mockServerV3,
			}

			// Construct a CheckNodePeerTask with dummy implementations
			task := &CheckNodePeerTask{
				Agent: &TaskAgent{
					// Fill in required fields for logging
					IP:  "*******",
					Env: "test",
					UID: "uid",
					AZ:  "az",
				},
				Node:       mockNode,
				NodeServer: mockALBNodeServer,
			}

			err := task.Run()

			if tc.expectError {
				require.Error(t, err)
				if tc.errorContains != "" {
					assert.Contains(t, err.Error(), tc.errorContains)
				}
			} else {
				require.NoError(t, err)
			}
		})
	}
}
