//nolint:dupl
package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
)

type StartKeepalivedTask struct {
	KeepalivedTask
	haMode string
}

func NewStartKeepalivedTask(alb *v1alpha1.ALB, haMode string) *StartKeepalivedTask {
	task := &StartKeepalivedTask{
		haMode: haMode,
	}
	task.ALB = alb

	return task
}

func (t *StartKeepalivedTask) Description() string {
	return ""
}

func (t *StartKeepalivedTask) Name() string {
	return StartKeepalivedTaskName
}

func (t *StartKeepalivedTask) Run() error {
	if err := t.Start(); err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"az":      t.ALB.Spec.ALBNode.Zone,
			"ha_mode": t.haMode,
		}).WithError(err).Error("start_keepalived_failed")

		return errors.WithMessage(err, "start_keepalived_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.ALB.Spec.LanIP,
		"env":     t.ALB.Spec.ALBNode.Env,
		"idc":     t.ALB.Spec.ALBNode.IDC,
		"az":      t.ALB.Spec.ALBNode.Zone,
		"uid":     t.ALB.UID,
		"cluster": t.ALB.Spec.ALBNode.ALBName,
	}).Info("start_keepalived_success")

	return nil
}
