package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albprov"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type NodeProvisionTask struct {
	ALB    *v1alpha1.ALB
	Agent  *TaskAgent
	Server *tocvo.ALBNodeServer
	Node   toc.TocexAdapter
	Apt    toc.AptAdapter
}

func NewNodeProvisionTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *NodeProvisionTask {
	node := toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec))

	return &NodeProvisionTask{
		Agent:  NewTaskAgent(alb),
		ALB:    alb,
		Server: svr,
		Node:   node,
		Apt:    toc.NewAptAdapter(node),
	}
}

func (t *NodeProvisionTask) Description() string {
	return ""
}

func (t *NodeProvisionTask) Name() string {
	return NodeProvisionTaskName
}

func (t *NodeProvisionTask) Run() error {
	var provision toclib.ProvisionNodeConfig

	provision.HostIP = t.Agent.IP
	prov, err := albprov.NewNodeProvision(t.Server, t.Agent.UID)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("toc_alb_provision_error")

		return errors.WithMessage(err, "toc_alb_provision_error")
	}

	templates, err := prov.Templates()
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("toc_alb_provision_template_error")

		return errors.WithMessage(err, "fetch_templates_failed")
	}

	version := albvo.ToNodeComponentVersion(&t.ALB.Spec.ALBNode)
	components, err := prov.Components(version)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("toc_alb_provision_component_error")

		return errors.WithMessage(err, "fetch_components_failed")
	}

	provision.Templates = templates
	provision.Components = components

	log.Logger().WithFields(log.Fields{
		"ip":         t.Agent.IP,
		"az":         t.Agent.AZ,
		"env":        t.Agent.Env,
		"uid":        t.Agent.UID,
		"cluster":    t.Agent.ALBName,
		"templates":  len(provision.Templates),
		"components": len(provision.Components),
	}).Info("toc_alb_set_provision")

	if err := t.Apt.CleanDpkgLock(); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("toc_alb_provision_cleanup_lock_error")

		return errors.WithMessage(err, "toc_alb_provision_cleanup_lock_error")
	}

	err = t.Node.SetNodeProvision(&provision)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":         t.Agent.IP,
			"az":         t.Agent.AZ,
			"env":        t.Agent.Env,
			"uid":        t.Agent.UID,
			"cluster":    t.Agent.ALBName,
			"templates":  len(provision.Templates),
			"components": len(provision.Components),
		}).Error("toc_alb_provision_server_error")

		return errors.WithMessage(err, "toc_alb_provision_server_error")
	}

	log.Logger().WithFields(log.Fields{
		"ip":         t.Agent.IP,
		"az":         t.Agent.AZ,
		"env":        t.Agent.Env,
		"uid":        t.Agent.UID,
		"cluster":    t.Agent.ALBName,
		"templates":  len(provision.Templates),
		"components": len(provision.Components),
	}).Info("toc_alb_provision_server_success")

	return nil
}
