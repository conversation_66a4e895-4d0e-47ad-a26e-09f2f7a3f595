package job

import (
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
)

func TestNewRegisterEtcdProxyDomainTask(t *testing.T) {
	t.Parallel()

	node := my0StagingNode0()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	task := NewRegisterEtcdProxyDomainTask(node.ALB(t), svr)
	task.DNSAPI = node.SGWDNSAPI(t)
	assert.NoError(t, task.Run())
}

func TestRegisterEtcdProxyDomainTask_clusterDomain(t *testing.T) {
	t.<PERSON>llel()

	node := my0StagingNode0()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	fakeit := gofakeit.New(time.Now().Unix())

	domain := fakeit.DomainName()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	mockDNSAPI := sgw.NewMockDNSAPIAdapter(ctrl)

	t.Run("wan_cluster", func(t *testing.T) {
		t.Parallel()

		// query cluster domain from agent first
		mockClusterAdapter := sgw.NewMockL7ClusterAdapter(ctrl)
		mockClusterAdapter.EXPECT().GetByUUID(gomock.Eq(node.ClusterUUID)).
			Return(&albvo.Cluster{
				ALBClusterDomains: &albvo.ClusterDomains{
					WanDomain: domain,
				},
				NetworkType: "WAN",
			}, nil).Times(1)

		task := &RegisterEtcdProxyDomainTask{
			Agent:      NewTaskAgent(node.ALB(t)),
			NodeServer: svr,
			DNSAPI:     mockDNSAPI,
			cluster:    mockClusterAdapter,
		}
		assert.Equal(t, domain, task.clusterDomain())
	})

	t.Run("lan_cluster_ok", func(t *testing.T) {
		t.Parallel()

		lanDomain := fakeit.DomainName()
		rz := fakeit.Word()
		env := fakeit.Word()
		az := fakeit.Word()
		seg := fakeit.Word()

		lanCluster := &albvo.Cluster{
			ALBClusterDomains: &albvo.ClusterDomains{
				WanDomain: lanDomain,
			},
			NetworkType: "LAN",
			RZ:          rz,
			Env:         env,
			AZ:          az,
			Segment:     seg,
		}
		wanCluster := &albvo.Cluster{
			ALBClusterDomains: &albvo.ClusterDomains{
				WanDomain: domain,
			},
			NetworkType: "WAN",
			RZ:          rz,
			Env:         env,
			AZ:          az,
			Segment:     seg,
		}
		var otherCluster *albvo.Cluster
		if err := fakeit.Struct(&otherCluster); err != nil {
			t.Fatal(err)
		}

		// query cluster domain from agent first
		mockClusterAdapter := sgw.NewMockL7ClusterAdapter(ctrl)
		mockClusterAdapter.EXPECT().GetByUUID(gomock.Eq(node.ClusterUUID)).
			Return(lanCluster, nil).Times(1)

		mockClusterAdapter.EXPECT().GetByRZ(gomock.Eq(rz)).
			Return([]*albvo.Cluster{
				lanCluster,
				wanCluster,
				otherCluster,
			}, nil).Times(1)

		task := &RegisterEtcdProxyDomainTask{
			Agent:      NewTaskAgent(node.ALB(t)),
			NodeServer: svr,
			DNSAPI:     mockDNSAPI,
			cluster:    mockClusterAdapter,
		}
		assert.Equal(t, domain, task.clusterDomain())
	})

	t.Run("lan_cluster_no_wan", func(t *testing.T) {
		t.Parallel()

		lanDomain := fakeit.DomainName()
		rz := fakeit.Word()
		env := fakeit.Word()
		az := fakeit.Word()
		seg := fakeit.Word()

		lanCluster := &albvo.Cluster{
			ALBClusterDomains: &albvo.ClusterDomains{
				WanDomain: lanDomain,
			},
			NetworkType: "LAN",
			RZ:          rz,
			Env:         env,
			AZ:          az,
			Segment:     seg,
		}
		var otherCluster *albvo.Cluster
		if err := fakeit.Struct(&otherCluster); err != nil {
			t.Fatal(err)
		}

		// query cluster domain from agent first
		mockClusterAdapter := sgw.NewMockL7ClusterAdapter(ctrl)
		mockClusterAdapter.EXPECT().GetByUUID(gomock.Eq(node.ClusterUUID)).
			Return(lanCluster, nil).Times(1)

		mockClusterAdapter.EXPECT().GetByRZ(gomock.Eq(rz)).
			Return([]*albvo.Cluster{
				lanCluster,
				otherCluster,
			}, nil).Times(1)

		svr.Meta.RZ = "IDC"
		svr.Meta.ClusterBaseDomain = "cluster.base.domain"

		task := &RegisterEtcdProxyDomainTask{
			Agent:      NewTaskAgent(node.ALB(t)),
			NodeServer: svr,
			Meta:       svr.Meta,
			DNSAPI:     mockDNSAPI,
			cluster:    mockClusterAdapter,
		}
		assert.Equal(t, "IDC.alb.sgw.cluster.base.domain", task.clusterDomain())
	})
}
