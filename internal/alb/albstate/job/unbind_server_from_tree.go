package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type UnbindServersFromTreeTask struct {
	Agent      *TaskAgent
	ALB        *v1alpha1.ALB
	NodeServer *tocvo.ALBNodeServer
	ServerV3   toc.ServerV3Adapter
}

func NewUnbindServersFromTreeTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *UnbindServersFromTreeTask {
	return &UnbindServersFromTreeTask{
		Agent:      NewTaskAgent(alb),
		ALB:        alb,
		NodeServer: svr,
		ServerV3:   toc.NewServerV3Adapter(string(alb.UID)),
	}
}

func (t *UnbindServersFromTreeTask) Description() string {
	return ""
}

func (t *UnbindServersFromTreeTask) Name() string {
	return UnbindServersFromTreeTaskName
}

func (t *UnbindServersFromTreeTask) Run() error {
	// skip non-seamoney and shopee
	if !t.NodeServer.ServerV3.IsClusterSupported() {
		log.Logger().WithFields(log.Fields{
			"ip":          t.Agent.IP,
			"env":         t.Agent.Env,
			"uid":         t.Agent.UID,
			"az":          t.Agent.AZ,
			"clusterName": t.ALB.Spec.ALBNode.ALBName,
		}).Info("unbounded_server_cluster_is_not_supported")

		return nil
	}

	// skip non-az servers
	if ok := t.NodeServer.ServerV3.MatchedPlatform(configs.ALB.Platform); !ok {
		log.Logger().WithFields(log.Fields{
			"ip":          t.Agent.IP,
			"env":         t.Agent.Env,
			"uid":         t.Agent.UID,
			"az":          t.Agent.AZ,
			"clusterName": t.ALB.Spec.ALBNode.ALBName,
		}).Info("unbounded_server_platform_is_not_az")

		return nil
	}

	for _, node := range t.NodeServer.ServerV3.GetResourceNodes() {
		if node.BindType == consts.MainNodeServerTreeType {
			continue
		}

		err := t.NodeServer.ServerV3.VerifyResourceNodesByName(consts.ALB, node.NodeName)
		if err != nil {
			log.Logger().WithFields(log.Fields{
				"ip":          t.Agent.IP,
				"env":         t.Agent.Env,
				"uid":         t.Agent.UID,
				"az":          t.Agent.AZ,
				"clusterName": t.ALB.Spec.ALBNode.ALBName,
			}).Info("node_name_is_not_az_traffic_scheduling_alb_nginx")

			continue
		}

		resp, err := t.ServerV3.GetResourceTreeNodes(t.NodeServer.Cluster, node.NodeName)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":          t.Agent.IP,
				"env":         t.Agent.Env,
				"uid":         t.Agent.UID,
				"az":          t.Agent.AZ,
				"clusterName": t.ALB.Spec.ALBNode.ALBName,
			}).Error("get_resource_tree_nodes_by_name_failed")

			return errors.WithMessage(err, "get_resource_tree_nodes_by_name_failed")
		}

		for _, node2 := range resp.Data.Nodes {
			if node2.NodeName == node.NodeName {
				_, err = t.ServerV3.UnbindServerTree(t.NodeServer.Cluster, t.ALB.Spec.LanIP, node2.ID)
				if err != nil {
					log.Logger().WithError(err).WithFields(log.Fields{
						"ip":          t.Agent.IP,
						"env":         t.Agent.Env,
						"uid":         t.Agent.UID,
						"az":          t.Agent.AZ,
						"clusterName": t.ALB.Spec.ALBNode.ALBName,
					}).Error("unbind_server_from_tree_failed")

					return errors.WithMessage(err, "unbind_server_from_tree_failed")
				}
			}
		}
	}

	log.Logger().WithFields(log.Fields{
		"ip":          t.Agent.IP,
		"env":         t.Agent.Env,
		"uid":         t.Agent.UID,
		"az":          t.Agent.AZ,
		"clusterName": t.ALB.Spec.ALBNode.ALBName,
	}).Info("unbind_server_from_tree_success")

	return nil
}
