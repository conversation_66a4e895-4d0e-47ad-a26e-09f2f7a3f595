package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
)

type CheckOSTask struct {
	Agent    *TaskAgent
	Node     toc.TocexAdapter
	GroupVar *tocvo.ALBGroupVar

	ext2cluster sgw.Ext2ClusterAdapter
}

func NewCheckOSTask(alb *v1alpha1.ALB, groupVar *tocvo.ALBGroupVar) *CheckOSTask {
	return &CheckOSTask{
		Agent:    NewTaskAgent(alb),
		Node:     toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),
		GroupVar: groupVar,

		ext2cluster: sgw.NewExt2Cluster(string(alb.UID)),
	}
}

func (t *CheckOSTask) Description() string {
	return ""
}

func (t *CheckOSTask) Name() string {
	return CheckOSTaskName
}

func (t *CheckOSTask) Run() error {
	ext2clusters, err := t.ext2cluster.DumpByRZ(t.GroupVar.IDC)
	if err != nil {
		return errors.WithMessage(err, "fetch_os_config_proxy_failed")
	}

	checkOSScript, err := tpl.CheckKernel(&tpl.HTTPTunnel{
		HTTPProxy:  ext2clusters.HTTPProxy(),
		HTTPSProxy: ext2clusters.HTTPSProxy(),
	})
	if err != nil {
		log.Logger().WithError(err).WithField("ip", t.Agent.IP).
			WithField("uid", t.Agent.UID).Error("fetch_check_kernel_script_failed")

		return errors.WithMessage(err, "check_alb_node_status_error")
	}

	_, err = t.Node.RunTask(checkOSScript)
	if err != nil {
		log.Logger().WithError(err).WithField("ip", t.Agent.IP).
			WithField("uid", t.Agent.UID).Error("alb_check_error")

		return errors.WithMessage(err, "run_check_os_script_failed")
	}

	log.Logger().WithField("ip", t.Agent.IP).
		WithField("uid", t.Agent.UID).Info("alb_check_success")

	return nil
}
