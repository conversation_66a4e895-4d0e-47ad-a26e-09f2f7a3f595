package job

const (
	CheckOSTaskName             = "checkOS"
	TagFormatTaskName           = "tagFormat"
	NodeProvisionTaskName       = "nodeProvision"
	NodeProvisionStatusTaskName = "nodeProvisionStatus"
	RegisterNodeTaskName        = "registerNode"
	UnregisterNodeTaskName      = "unregisterNode"

	CheckNodeStatusTaskName      = "checkNodeStatus"
	CheckNginxVersionTaskName    = "checkNginxVersion"
	CheckNodeListenerTaskName    = "checkNodeListener"
	CheckNodePeerTaskName        = "checkNodePeer"
	AddALBIntoClusterTaskName    = "addNodeIntoCluster"
	DeleteALBFromClusterTaskName = "deleteNodeFromCluster"
	DeleteETCDNodeKeyTaskName    = "deleteEtcdNodeKey"
	MoveNodeIntoMAPool           = "moveNodeIntoMAPool"
	AddClusterDomainTaskName     = "addClusterDomain"
	AddEtcdProxyDomainTask       = "addEtcdProxyDomain"
	AddNodeKVMetaTaskName        = "addNodeKVMetaTask"

	UpsertEtcdEndpointTaskName  = "upsertEtcdEndpoint"
	UpsertClusterDomainTaskName = "upsertClusterDomain"
	UpsertClusterIPsTaskName    = "upsertClusterIPs"
	UpdateServerStateTaskName   = "updateServerState"
	UpdateNodeIPTablesTaskName  = "updateNodeIPTables"
	CheckNodeIPTablesTaskName   = "checkNodeIPTables"
	SyncNodeWAFTaskName         = "syncNodeWAF"
	PersistStaticCacheTaskName  = "persistStaticCache"
	CheckClusterDomainTaskName  = "checkClusterDomain"
	CheckTicketStatusTaskName   = "checkTicketStatus"
	CheckNodeMgmtStatusTaskName = "checkNodeMgmtStatus"
	StartBirdTaskName           = "startBird"
	StopBirdTaskName            = "stopBird"
	StartKeepalivedTaskName     = "startKeepalived"
	StopKeepalivedTaskName      = "stopKeepalived"
	MarkSGWStateTaskName        = "markSGWState"
	CheckNICDriverTaskName      = "checkNICDriver"

	SendSeeEventTaskName      = "sendSeeEvent"
	RaiseNodeMaTicketTaskName = "raiseNodeMaTicket"
	GetMATicketTaskName       = "getMaTicket"

	DeleteProvisionTaskName       = "deleteProvision"
	CheckDeleteProvisionTaskName  = "checkDeleteProvision"
	DeleteALBCRTaskName           = "deleteALBCR"
	UnbindServersFromTreeTaskName = "unbindServerFromTree"
	BindServersToTreeTaskName     = "bindServerToTree"

	HotUpdateTaskName               = "hotUpdate"
	HotUpdateRollbackTaskName       = "hotUpdateRollback"
	CheckHotUpdateStatusTaskName    = "checkHotUpdateStatus"
	HotUpdateCleanTaskName          = "hotUpdateClean"
	HotUpdateRollbackStatusTaskName = "hotUpdateRollbackStatus"
)
