package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
)

type DeleteNodeFromClusterTask struct {
	Agent *TaskAgent
}

func NewDeleteNodeFromClusterTask(alb *v1alpha1.ALB) *DeleteNodeFromClusterTask {
	return &DeleteNodeFromClusterTask{
		Agent: NewTaskAgent(alb),
	}
}

func (t *DeleteNodeFromClusterTask) Description() string {
	return ""
}

func (t *DeleteNodeFromClusterTask) Name() string {
	return DeleteALBFromClusterTaskName
}

func (t *DeleteNodeFromClusterTask) Run() error {
	alb := sgw.NewALBClusterAdapter(t.Agent.UID)

	err := alb.DeleteNode(t.Agent.IP)
	if err != nil {
		log.Logger().WithError(err).With<PERSON>ield("cluster", t.Agent.ALBName).WithField("ip", t.Agent.IP).
			WithField("env", t.Agent.Env).WithField("idc", t.Agent.IDC).
			Error("delete_alb_node_from_cluster_error")

		return errors.WithMessage(err, "delete_alb_node_from_cluster_error")
	}

	log.Logger().WithField("ip", t.Agent.IP).WithField("idc", t.Agent.IDC).WithField("env", t.Agent.Env).
		WithField("cluster", t.Agent.ALBName).Info("delete_alb_node_from_cluster_success")

	return nil
}
