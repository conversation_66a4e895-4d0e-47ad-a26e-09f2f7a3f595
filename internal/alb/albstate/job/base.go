package job

import (
	"regexp"
	"strings"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

const (
	BirdProtoBriefLeastCols  = 5
	BirdProtoBriefSkipHeader = 2
)

var SpaceRegex = regexp.MustCompile(`\s+`)

type TaskAgent struct {
	Env     string
	IDC     string
	AZ      string
	UID     string
	IP      string // IP Addr
	ALBName string
	SegName string
	SDU     string

	ClusterUUID string

	TicketID      string
	ApplicantUser string
}

func (a *TaskAgent) IsNonLiveEnv() bool {
	return !strings.EqualFold(a.Env, consts.EnvLive)
}

func NewTaskAgent(alb *v1alpha1.ALB) *TaskAgent {
	return &TaskAgent{
		Env:     alb.Spec.ALBNode.Env,
		IDC:     alb.Spec.ALBNode.IDC,
		AZ:      alb.Spec.ALBNode.Zone,
		UID:     string(alb.UID),
		IP:      alb.Spec.LanIP,
		ALBName: alb.Spec.ALBNode.ALBName,
		SDU:     alb.Spec.ALBNode.SDU,

		ClusterUUID: alb.Spec.ALBNode.ClusterUUID,

		TicketID:      alb.Spec.ALBNode.Options["ticket_id"],
		ApplicantUser: alb.Spec.ALBNode.Options["applicant_user"],

		SegName: alb.Spec.ALBNode.Segment,
	}
}
