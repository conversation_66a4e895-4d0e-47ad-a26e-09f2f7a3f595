package job

import (
	"os"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v6"
	"github.com/duke-git/lancet/v2/cryptor"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/dns/dnsvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

var ciEnv bool

type node struct {
	NodeIP      string
	Env         string
	AZ          string
	RZ          string
	Segment     string
	ClusterUUID string

	ProductID uint64

	Private bool
	Tocex   toc.TocexAdapter
}

func init() {
	err := os.Setenv("ENV", "dev")
	if err != nil {
		return
	}

	log.InitConsole()

	err = configs.Init("")
	if err != nil {
		log.Logger().WithError(err).Error("init_config_failed")

		return
	}

	ciEnv = configs.CI()
}

func (n *node) ALB(t *testing.T) *v1alpha1.ALB {
	t.Helper()

	var albNode v1alpha1.ALBNode
	assert.NoError(t, gofakeit.Struct(&albNode))
	albNode.Env = n.Env
	albNode.IDC = n.RZ
	albNode.Zone = n.AZ
	albNode.ClusterUUID = n.ClusterUUID

	alb := v1alpha1.ALB{
		Spec: v1alpha1.ALBSpec{
			Type:    "ALB",
			LanIP:   n.NodeIP,
			ALBNode: albNode,
		},
	}

	return &alb
}

func my0StagingNode0() *node {
	return &node{
		NodeIP:  configs.E2E.ALB.Monee.Node,
		Env:     configs.E2E.ALB.Monee.Env,
		AZ:      configs.E2E.ALB.Monee.AZ,
		Segment: configs.E2E.ALB.Monee.Segment,
		Private: true,

		ClusterUUID: "b1b1b1b1-b1b1-b1b1-b1b1-b1b1b1b1b1b1",
	}
}

func sg90TestNode2() *node {
	return &node{
		NodeIP:      configs.E2E.ALB.Shopee.Node,
		ClusterUUID: configs.E2E.ALB.Shopee.UUID,
		Env:         configs.E2E.ALB.Shopee.Env,
		AZ:          configs.E2E.ALB.Shopee.AZ,
		RZ:          configs.E2E.ALB.Shopee.RZ,
		Segment:     configs.E2E.ALB.Shopee.Segment,
	}
}

func (n *node) withTocex(t *testing.T) error {
	t.Helper()

	if !ciEnv {
		tocex, err := toc.NewTocexAdapterWithIP(n.NodeIP, t.Name())
		if err != nil {
			return errors.WithMessage(err, "fetch_tocex_failed")
		}

		n.Tocex = tocex
	}

	fakeit := gofakeit.New(time.Now().Unix())

	tocex := toc.NewMockTocexAdapter(gomock.NewController(t))

	agentInfo := toclib.AgentInfo{}
	err := fakeit.Struct(&agentInfo)
	assert.NoError(t, err)

	agentInfo.Env = n.Env
	agentInfo.IP = n.NodeIP
	agentInfo.AZ = n.RZ
	agentInfo.AZv2 = n.AZ
	tocex.EXPECT().GetAgent().Return(&agentInfo, nil).AnyTimes()
	tocex.EXPECT().RunTask(gomock.Eq(consts.BirdShowProtocolScript)).
		Return(`		BIRD 2.0.3 ready.
		Name       Proto      Table      State  Since         Info
		device1    Device     ---        up     2023-03-13
		static1    Static     master4    up     2023-03-13
		ecmp_lan1_bgp BGP        ---        up     2023-03-13    Established
		ecmp_lan2_bgp BGP        ---        up     2023-03-13    Established`, nil).AnyTimes()
	tocex.EXPECT().ListFiles("/etc/nginx/http-enabled/").Return([]*toclib.FileInfo{
		{
			Name: "test.baidu.tw",
		},
		{
			Name: "test.shopee.ph",
		},
		{
			Name: "zhangkezk.test.test-shp.ee",
		},
	}, nil).AnyTimes()

	n.Tocex = tocex

	return nil
}

func (n *node) ALBNodeServer(t *testing.T) (*tocvo.ALBNodeServer, error) {
	t.Helper()

	if err := n.withTocex(t); err != nil {
		return nil, err
	}

	return n.ALBServer(t)
}

func (n *node) SGWL7Cluster(t *testing.T) sgw.L7ClusterAdapter {
	t.Helper()

	cluster := sgw.NewMockL7ClusterAdapter(gomock.NewController(t))

	cls := albvo.Cluster{}
	err := gofakeit.Struct(&cls)
	assert.NoError(t, err)

	cluster.EXPECT().GetByNode(gomock.Eq(n.NodeIP)).Return(&cls, nil).AnyTimes()
	cluster.EXPECT().UpdateWANDomain(gomock.Any()).Return(true, nil).AnyTimes()
	cluster.EXPECT().UpdateEtcdEndpoint(gomock.Any()).Return(true, nil).AnyTimes()

	return cluster
}

func (n *node) SGWDNSAPI(t *testing.T) sgw.DNSAPIAdapter {
	t.Helper()

	api := sgw.NewMockDNSAPIAdapter(gomock.NewController(t))

	domain := dnsvo.Domain{}
	assert.NoError(t, gofakeit.Struct(&domain))

	record := dnsvo.Record{}
	assert.NoError(t, gofakeit.Struct(&record))
	svr, err := n.ALBNodeServer(t)
	assert.NoError(t, err)

	record.Domain = svr.Meta.ClusterDomain()
	record.EntryType = "A"
	record.DNSType = "private"
	record.GeoLocation = "central"
	domain.Records = append(domain.Records, &record)

	api.EXPECT().Query(gomock.Any()).DoAndReturn(func(name string) (*dnsvo.Domain, error) {
		domain.Name = name

		return &domain, nil
	}).AnyTimes()
	api.EXPECT().ChangeRecord(gomock.Any()).DoAndReturn(func(req *sgw.DNSChangeRequest) (*sgw.DNSChangeResult, error) {
		return &sgw.DNSChangeResult{
			Domain:  req.Domain,
			Success: "true",
		}, nil
	}).AnyTimes()

	return api
}

func (n *node) Sniffer(t *testing.T) Sniffer {
	t.Helper()

	sniffer := NewMockSniffer(gomock.NewController(t))
	sniffer.EXPECT().Probe(gomock.Any()).Return(true, nil).AnyTimes()

	return sniffer
}

func (n *node) newServer(t *testing.T) toc.ServerAdapter {
	t.Helper()

	var server toc.ServerAdapter
	if !ciEnv {
		server = toc.NewServerAdapter(t.Name())
	} else {
		fakeit := gofakeit.New(time.Now().Unix())

		variable := tocvo.ALBServerAndVariables{}
		assert.NoError(t, fakeit.Struct(&variable))

		mockServer := toc.NewMockServerAdapter(gomock.NewController(t))
		mockServer.EXPECT().GetALBServerVariables(n.NodeIP).Return(&variable, nil).AnyTimes()
		mockServer.EXPECT().UpdateState(gomock.Any()).Return(true, nil).AnyTimes()

		server = mockServer
	}

	return server
}

func (n *node) newServerV3(t *testing.T) toc.ServerV3Adapter {
	t.Helper()

	fakeit := gofakeit.New(time.Now().Unix())

	var server toc.ServerV3Adapter
	if !ciEnv {
		server = toc.NewServerV3Adapter(t.Name())
	} else {
		mockServerV3 := toc.NewMockServerV3Adapter(gomock.NewController(t))

		variable := tocvo.ALBServerAndVariables{}
		assert.NoError(t, fakeit.Struct(&variable))

		variable.GroupVar.SegmentName = ""
		variable.Server.ServerLabels.ProductID = n.ProductID
		variable.Server.ServerLabels.Env = n.Env
		variable.Server.ServerLabels.AZ = n.AZ
		variable.Server.ServerLabels.ServerID = 88196
		variable.Server.ServerLabels.StateName = fakeit.RandomString([]string{"operating", "maintenance"})

		var tags []toclib.Tag
		fakeit.Slice(&tags)
		if n.Private {
			tags = append(tags, toclib.Tag{
				TagCategoryKey: "ham",
				Value:          "NonStd2",
			})
		}

		mockServerV3.EXPECT().Tags(n.NodeIP).Return(tags, nil).AnyTimes()
		mockServerV3.EXPECT().Server(n.NodeIP).DoAndReturn(func(ip string) (*toclib.ServerV3, error) {
			svr := toclib.ServerV3{}
			assert.NoError(t, fakeit.Struct(&svr))

			if n.Private {
				svr.Cluster = "seamoney"
			} else {
				svr.Cluster = "shopee"
			}

			return &svr, nil
		}).AnyTimes()

		server = mockServerV3
	}

	return server
}

func (n *node) ALBServer(t *testing.T) (*tocvo.ALBNodeServer, error) {
	t.Helper()

	fakeit := gofakeit.New(time.Now().Unix())

	server := n.newServer(t)
	serverV3 := n.newServerV3(t)

	serverVar, err := server.GetALBServerVariables(n.NodeIP)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_server_variable_failed")
	}

	if serverVar.GroupVar.SegmentName == "" {
		serverVar.GroupVar.SegmentName = fakeit.Name()

		serverVar.GroupVar.SegmentKey = cryptor.Md5String(serverVar.GroupVar.SegmentName)
	}

	tags, err := serverV3.Tags(n.NodeIP)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_tags_failed")
	}

	svrTocV3, err := serverV3.Server(n.NodeIP)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_server_failed")
	}

	svrV3 := tocvo.ServerV3(*svrTocV3)

	agent, err := n.Tocex.GetAgent()
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_agent_failed")
	}

	nodeSvr := &tocvo.ALBNodeServer{
		Cluster:        svrTocV3.Cluster,
		ServerGroupVar: serverVar,
		Meta: &sgwvo.ALBClusterConfigMeta{
			L7ClusterMeta: sgwvo.L7ClusterMeta{
				RZ:      n.RZ,
				AZ:      n.AZ,
				Env:     n.Env,
				Segment: n.Segment,
			},
		},
		Agent: agent,
		Tags:  tags,
		Segment: &tocvo.Segment{
			NetworkVersion: "v3.0",
		},
		ServerV3: &svrV3,
	}

	return nodeSvr, nil
}
