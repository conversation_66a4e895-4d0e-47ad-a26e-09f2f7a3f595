package job

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/etcd/etcdvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/ops"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/zk/zkvo"
)

func TestRegisterNodeKVTask_Run(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	testCases := []struct {
		name         string
		etcdMeta     *etcdvo.ClusterMeta
		etcdErr      error
		zkMeta       *zkvo.ClusterMeta
		zkErr        error
		cluster      *albvo.Cluster
		clusterErr   error
		updateResult bool
		updateErr    error
		expectErr    bool
	}{
		{
			name: "success_case_update_required",
			etcdMeta: &etcdvo.ClusterMeta{
				Meta: etcdvo.Meta{
					ClusterURL: "etcd://etcd1:2379,etcd2:2379/etcd",
				},
			},
			zkMeta: &zkvo.ClusterMeta{
				Meta: zkvo.Meta{
					ClusterURL: "zk://zk1:2181,zk2:2181/zk",
				},
			},
			cluster: &albvo.Cluster{
				NodeZK:   "",
				NodeEtcd: "",
			},
			updateResult: true,
			expectErr:    false,
		},
		{
			name: "success_case_no_update_required",
			etcdMeta: &etcdvo.ClusterMeta{
				Meta: etcdvo.Meta{
					ClusterURL: "etcd://etcd1:2379,etcd2:2379/etcd",
				},
			},
			zkMeta: &zkvo.ClusterMeta{
				Meta: zkvo.Meta{
					ClusterURL: "zk://zk1:2181,zk2:2181/zk",
				},
			},
			cluster: &albvo.Cluster{
				NodeZK:   "zk://zk1:2181,zk2:2181/zk",
				NodeEtcd: "etcd://etcd1:2379,etcd2:2379/etcd",
			},
			expectErr: false,
		},
		{
			name:      "etcd_error",
			etcdErr:   errors.New("etcd_error"),
			expectErr: true,
		},
		{
			name:      "zk_error",
			etcdMeta:  &etcdvo.ClusterMeta{},
			zkErr:     errors.New("zk_error"),
			expectErr: true,
		},
		{
			name:       "cluster_error",
			etcdMeta:   &etcdvo.ClusterMeta{},
			zkMeta:     &zkvo.ClusterMeta{},
			clusterErr: errors.New("cluster_error"),
			expectErr:  true,
		},
		{
			name: "update_failure",
			etcdMeta: &etcdvo.ClusterMeta{
				Meta: etcdvo.Meta{
					ClusterURL: "etcd://etcd1:2379,etcd2:2379/etcd",
				},
			},
			zkMeta: &zkvo.ClusterMeta{
				Meta: zkvo.Meta{
					ClusterURL: "zk://zk1:2181,zk2:2181/zk",
				},
			},
			cluster: &albvo.Cluster{
				NodeZK:   "",
				NodeEtcd: "",
			},
			updateResult: false,
			expectErr:    true,
		},
		{
			name: "update_error",
			etcdMeta: &etcdvo.ClusterMeta{
				Meta: etcdvo.Meta{
					ClusterURL: "etcd://etcd1:2379,etcd2:2379/etcd",
				},
			},
			zkMeta: &zkvo.ClusterMeta{
				Meta: zkvo.Meta{
					ClusterURL: "zk://zk1:2181,zk2:2181/zk",
				},
			},
			cluster: &albvo.Cluster{
				NodeZK:   "",
				NodeEtcd: "",
			},
			updateErr: errors.New("update_error"),
			expectErr: true,
		},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Setup mocks
			mockEtcd := ops.NewMockEtcdAdapter(ctrl)
			mockEtcd.EXPECT().ClusterMeta(gomock.Any(), gomock.Any()).
				Return(tc.etcdMeta, tc.etcdErr).AnyTimes()

			mockZK := ops.NewMockZKAdapter(ctrl)
			mockZK.EXPECT().ClusterMeta(gomock.Any(), gomock.Any()).
				Return(tc.zkMeta, tc.zkErr).AnyTimes()

			mockCluster := sgw.NewMockL7ClusterAdapter(ctrl)

			// Set up the task with the mocks
			task := &RegisterNodeKVTask{
				Agent: &TaskAgent{
					IDC:         "test-idc",
					Env:         "test-env",
					ClusterUUID: "test-uuid",
					IP:          "********",
					UID:         "test-uid",
					AZ:          "test-az",
				},
				etcd:    mockEtcd,
				zk:      mockZK,
				cluster: mockCluster,
			}

			// Setup expectations
			if tc.etcdErr == nil && tc.zkErr == nil {
				mockCluster.EXPECT().GetByUUID(gomock.Eq("test-uuid")).Return(tc.cluster, tc.clusterErr).AnyTimes()

				if tc.clusterErr == nil && (tc.cluster.NodeZK == "" || tc.cluster.NodeEtcd == "") {
					mockCluster.EXPECT().UpdateNodeKV(gomock.Any()).DoAndReturn(
						func(req *sgw.L7ClusterKVUpdateRequest) (bool, error) {
							assert.Equal(t, "test-uuid", req.UUID)
							assert.Equal(t, tc.etcdMeta.ClusterURL, req.NodeEtcd)
							assert.Equal(t, tc.zkMeta.ClusterURL, req.NodeZK)

							return tc.updateResult, tc.updateErr
						}).AnyTimes()
				}
			}

			// Execute the test
			err := task.Run()

			// Check results
			if tc.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
