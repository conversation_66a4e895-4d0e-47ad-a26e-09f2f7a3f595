package job

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	leap "git.garena.com/shopee/devops/leap-apis/pkg/generated/clientset/versioned"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/swp"
)

type CheckTicketStatusTask struct {
	RevertState string // previous state, return to when ticket cancel or reject
	ALB         *v1alpha1.ALB
	LeapClient  leap.Interface
	Ticket      swp.TicketAdapter
}

func NewCheckTicketStatusTask(alb *v1alpha1.ALB, leapClient leap.Interface, preState string) *CheckTicketStatusTask {
	return &CheckTicketStatusTask{
		ALB:         alb,
		LeapClient:  leapClient,
		RevertState: preState,
		Ticket:      swp.NewTicketAdapter(string(alb.UID)),
	}
}

func (t *CheckTicketStatusTask) Description() string {
	return ""
}

func (t *CheckTicketStatusTask) Name() string {
	return CheckTicketStatusTaskName
}

func (t *CheckTicketStatusTask) Run() error {
	if t.ALB.Spec.ALBNode.SWPTicket == 0 &&
		consts.MaintenanceState.EqualsString(t.ALB.Status.State) {
		log.Logger().WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"env":     t.ALB.Spec.ALBNode.Env,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"uid":     t.ALB.UID,
			"cluster": t.ALB.Spec.ALBNode.ALBName,
			"swp_id":  t.ALB.Spec.ALBNode.SWPTicket,
		}).Info("no_ma_ticket")

		return nil
	}

	resp, err := t.Ticket.Get(uint64(t.ALB.Spec.ALBNode.SWPTicket))
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":           t.ALB.Spec.LanIP,
			"env":          t.ALB.Spec.ALBNode.Env,
			"idc":          t.ALB.Spec.ALBNode.IDC,
			"uid":          t.ALB.UID,
			"cluster":      t.ALB.Spec.ALBNode.ALBName,
			"swp_id":       t.ALB.Spec.ALBNode.SWPTicket,
			"revert_state": t.RevertState,
		}).Error("check_alb_ticket_status_error")

		return errors.WithMessage(err, "check_alb_ticket_status_error")
	}

	if resp.WorkflowPhaseType != consts.TicketEndFailed && resp.WorkflowPhaseType != consts.TicketEndSuccess {
		err = fmt.Errorf("ticket_%d_in_status_%s_inprogress", t.ALB.Spec.ALBNode.SWPTicket, resp.WorkflowPhaseType)
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":           t.ALB.Spec.LanIP,
			"env":          t.ALB.Spec.ALBNode.Env,
			"idc":          t.ALB.Spec.ALBNode.IDC,
			"uid":          t.ALB.UID,
			"cluster":      t.ALB.Spec.ALBNode.ALBName,
			"swp_id":       t.ALB.Spec.ALBNode.SWPTicket,
			"revert_state": t.RevertState,
		}).Error("alb_ticket_still_in_progress")

		return errors.WithMessage(err, "alb_ticket_still_in_progress")
	}

	if consts.MaintenanceState.EqualsString(t.ALB.Status.State) {
		ctx, cancel := context.WithTimeout(context.Background(), configs.Mgmt.Timeout())
		defer cancel()

		t.ALB.Spec.ALBNode.SWPTicket = 0
		if _, err = t.LeapClient.MachineV1alpha1().ALBs().Update(ctx, t.ALB, metav1.UpdateOptions{}); err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":           t.ALB.Spec.LanIP,
				"env":          t.ALB.Spec.ALBNode.Env,
				"idc":          t.ALB.Spec.ALBNode.IDC,
				"uid":          t.ALB.UID,
				"cluster":      t.ALB.Spec.ALBNode.ALBName,
				"swp_id":       t.ALB.Spec.ALBNode.SWPTicket,
				"revert_state": t.RevertState,
			}).Error("update_alb_ticket_error")
		}

		return nil
	}

	// for MA ticket, whether ticket status is fail or success, goto next state
	if resp.WorkflowPhaseType == consts.TicketEndFailed {
		t.ALB.Status.State = t.RevertState
		t.ALB.Status.Reason = fmt.Sprintf("ticket_%d_cancel_return_to_prestate_%s", t.ALB.Spec.ALBNode.SWPTicket, t.RevertState)
		t.ALB.Spec.ALBNode.SWPTicket = 0

		ctx, cancel := context.WithTimeout(context.Background(), configs.Mgmt.Timeout())
		defer cancel()

		if _, err = t.LeapClient.MachineV1alpha1().ALBs().Update(ctx, t.ALB, metav1.UpdateOptions{}); err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"ip":           t.ALB.Spec.LanIP,
				"env":          t.ALB.Spec.ALBNode.Env,
				"idc":          t.ALB.Spec.ALBNode.IDC,
				"uid":          t.ALB.UID,
				"cluster":      t.ALB.Spec.ALBNode.ALBName,
				"swp_id":       t.ALB.Spec.ALBNode.SWPTicket,
				"revert_state": t.RevertState,
			}).Error("update_alb_info_error")

			return errors.WithMessage(err, "update_alb_info_error")
		}

		err = fmt.Errorf(t.ALB.Status.Reason)
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":           t.ALB.Spec.LanIP,
			"env":          t.ALB.Spec.ALBNode.Env,
			"idc":          t.ALB.Spec.ALBNode.IDC,
			"uid":          t.ALB.UID,
			"cluster":      t.ALB.Spec.ALBNode.ALBName,
			"swp_id":       t.ALB.Spec.ALBNode.SWPTicket,
			"revert_state": t.RevertState,
		}).Info("ticket_cancel_need_go_to_prestate")

		return err
	}

	// workflowPhaseType equals to TicketEndSuccess
	log.Logger().WithFields(log.Fields{
		"ip":           t.ALB.Spec.LanIP,
		"env":          t.ALB.Spec.ALBNode.Env,
		"idc":          t.ALB.Spec.ALBNode.IDC,
		"uid":          t.ALB.UID,
		"cluster":      t.ALB.Spec.ALBNode.ALBName,
		"swp_id":       t.ALB.Spec.ALBNode.SWPTicket,
		"revert_state": t.RevertState,
	}).Info("ticket_approve")

	return nil
}
