//nolint:dupl
package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
)

type StopKeepalivedTask struct {
	KeepalivedTask
	haMode string
}

func NewStopKeepalivedTask(alb *v1alpha1.ALB, haMode string) *StopKeepalivedTask {
	task := &StopKeepalivedTask{
		haMode: haMode,
	}
	task.ALB = alb

	return task
}

func (t *StopKeepalivedTask) Description() string {
	return ""
}

func (t *StopKeepalivedTask) Name() string {
	return StopKeepalivedTaskName
}

func (t *StopKeepalivedTask) Run() error {
	if err := t.Stop(); err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":      t.ALB.Spec.LanIP,
			"idc":     t.ALB.Spec.ALBNode.IDC,
			"az":      t.ALB.Spec.ALBNode.Zone,
			"ha_mode": t.haMode,
		}).WithError(err).Error("stop_keepalived_failed")

		return errors.WithMessage(err, "stop_keepalived_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":      t.ALB.Spec.LanIP,
		"env":     t.ALB.Spec.ALBNode.Env,
		"idc":     t.ALB.Spec.ALBNode.IDC,
		"az":      t.ALB.Spec.ALBNode.Zone,
		"uid":     t.ALB.UID,
		"cluster": t.ALB.Spec.ALBNode.ALBName,
	}).Info("stop_keepalived_success")

	return nil
}
