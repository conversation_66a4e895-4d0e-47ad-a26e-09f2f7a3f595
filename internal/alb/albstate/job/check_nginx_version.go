package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/tpl"
)

type CheckNginxVersionTask struct {
	ALB   *v1alpha1.ALB
	Agent *TaskAgent
	Node  toc.TocexAdapter
}

func NewCheckNginxVersionTask(alb *v1alpha1.ALB) *CheckNginxVersionTask {
	return &CheckNginxVersionTask{
		ALB:   alb,
		Agent: NewTaskAgent(alb),
		Node:  toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),
	}
}

func (t *CheckNginxVersionTask) Description() string {
	return ""
}

func (t *CheckNginxVersionTask) Name() string {
	return CheckNginxVersionTaskName
}

func (t *CheckNginxVersionTask) Run() error {
	_, err := t.Node.RunTask(tpl.PostCheckNginxVersionScript)
	if err != nil {
		log.Logger().WithError(err).WithField("ip", t.Agent.IP).WithField("env", t.Agent.Env).
			WithField("uid", t.Agent.UID).WithField("task", t.Name()).Error("check_nginx_version_failed")

		return errors.WithMessage(err, "check_ngx_version_failed")
	}

	log.Logger().WithField("task", t.Name()).WithField("ip", t.Agent.IP).
		WithField("uid", t.Agent.UID).
		WithField("env", t.Agent.Env).Info("check_nginx_version_success")

	return nil
}
