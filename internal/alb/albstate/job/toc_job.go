package job

import (
	"fmt"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

type TocJobTask struct {
	ALB     *v1alpha1.ALB
	Cluster string
}

func NewTocJobTask(cluster string, alb *v1alpha1.ALB) *TocJobTask {
	return &TocJobTask{
		ALB:     alb,
		Cluster: cluster,
	}
}

func (t *TocJobTask) CheckResult(jobID int) error {
	if jobID == 0 {
		log.Logger().WithFields(log.Fields{
			"ip":  t.ALB.Spec.LanIP,
			"env": t.ALB.Spec.ALBNode.Env,
			"idc": t.ALB.Spec.ALBNode.IDC,
			"uid": t.ALB.UID,
			"az":  t.ALB.Spec.ALBNode.Zone,
		}).Warn("job_id_not_found")

		return nil
	}

	job := toc.NewJobAdapter(t.Cluster, string(t.ALB.UID))

	result, err := job.Result(jobID)
	if err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":     t.ALB.Spec.LanIP,
			"env":    t.ALB.Spec.ALBNode.Env,
			"idc":    t.ALB.Spec.ALBNode.IDC,
			"uid":    t.ALB.UID,
			"az":     t.ALB.Spec.ALBNode.Zone,
			"job_id": jobID,
		}).WithError(err).Error("fetch_job_result_failed")

		return errors.WithMessage(err, "fetch_job_result_failed")
	}

	if !result.IsCompleted() {
		log.Logger().WithFields(log.Fields{
			"ip":     t.ALB.Spec.LanIP,
			"env":    t.ALB.Spec.ALBNode.Env,
			"idc":    t.ALB.Spec.ALBNode.IDC,
			"uid":    t.ALB.UID,
			"az":     t.ALB.Spec.ALBNode.Zone,
			"job_id": jobID,
		}).Warn("job_id_not_completed")

		return fmt.Errorf("job_%d_not_completed", jobID)
	}

	status := result.Status(t.ALB.Spec.LanIP)
	if status == nil {
		log.Logger().WithFields(log.Fields{
			"ip":     t.ALB.Spec.LanIP,
			"env":    t.ALB.Spec.ALBNode.Env,
			"idc":    t.ALB.Spec.ALBNode.IDC,
			"uid":    t.ALB.UID,
			"az":     t.ALB.Spec.ALBNode.Zone,
			"job_id": jobID,
		}).Error("job_status_not_found")

		return fmt.Errorf("job_%d_status_not_found", jobID)
	}

	if !status.IsSuccess() {
		log.Logger().WithFields(log.Fields{
			"ip":     t.ALB.Spec.LanIP,
			"env":    t.ALB.Spec.ALBNode.Env,
			"idc":    t.ALB.Spec.ALBNode.IDC,
			"uid":    t.ALB.UID,
			"az":     t.ALB.Spec.ALBNode.Zone,
			"job_id": jobID,
		}).Error("job_status_not_success")

		return fmt.Errorf("job_%d_status_not_success", jobID)
	}

	return nil
}
