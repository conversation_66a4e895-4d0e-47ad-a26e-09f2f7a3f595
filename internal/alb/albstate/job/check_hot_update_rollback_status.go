package job

import (
	"crypto/md5"
	"fmt"
	"strings"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type CheckHotUpdateRollbackStatusTask struct {
	CheckHotUpdateTask
}

func NewCheckHotUpdateRollbackStatusTask(alb *v1alpha1.ALB) *CheckHotUpdateRollbackStatusTask {
	task := &CheckHotUpdateRollbackStatusTask{}
	task.components = make(map[string]*toclib.ProvisionNodeComponent)
	task.templates = make(map[string]*toclib.ProvisionNodeTemplate)
	task.ALB = alb
	task.Node = toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec))

	return task
}

func (t *CheckHotUpdateRollbackStatusTask) Description() string {
	return ""
}

func (t *CheckHotUpdateRollbackStatusTask) Name() string {
	return HotUpdateRollbackStatusTaskName
}

func (t *CheckHotUpdateRollbackStatusTask) checkProvisionComponent() error {
	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.RollbackComponents {
		name := item.Name
		if item.Type == cmp.ComponentTypeDocker {
			name = item.ServiceName
		}

		if component, ok := t.components[name]; !ok {
			err := fmt.Errorf("component_name_%s_must_exist_wait_please", item.Name)
			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				Error("wait_component_to_be_add")

			return errors.WithMessage(err, "component_not_found")
		} else if component.ErrMsg != "" {
			err := fmt.Errorf(component.ErrMsg)
			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				Error("wait_component_to_be_added")

			return errors.WithMessage(err, "component_failed")
		} else if item.Version != component.Version {
			// nginx-shopee it must be inconsistent, cause upgrade-nginx component
			if strings.EqualFold(item.Name, cmp.NginxComponent.Name) {
				continue
			}

			err := fmt.Errorf("component_name_%s_version_%s_must_equal_to_version_%s",
				item.Name, component.Version, item.Version)

			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).WithField("component_name", item.Name).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				WithField("current_version", component.Version).WithField("target_version", item.Version).
				Error("wait_component_to_be_updated_or_added")

			return errors.WithMessage(err, "component_version_inconsistent")
		}

		if strings.EqualFold(item.Name, cmp.UpgradeNginxComponent.Name) {
			albvo.UpdateALBComponentVersion(&t.ALB.Spec.ALBNode, cmp.NginxComponent.Name, t.nginxVersion())
		} else {
			albvo.UpdateALBComponentVersion(&t.ALB.Spec.ALBNode, name, item.Version)
		}
	}

	return nil
}

func (t *CheckHotUpdateRollbackStatusTask) checkProvisionTemplate() error {
	for _, rollbackTemplate := range t.ALB.Spec.ALBNode.HotUpdateConfig.RollbackTemplates {
		if nodeTemplate, ok := t.templates[rollbackTemplate.Path]; !ok {
			err := fmt.Errorf("template_path_%s_must_exist_wait_please", rollbackTemplate.Path)

			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("path", rollbackTemplate.Path).WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				Error("wait_template_to_be_added")

			return errors.WithMessage(err, "template_not_found")
		} else if nodeTemplate.ErrMsg != "" {
			err := fmt.Errorf(nodeTemplate.ErrMsg)

			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				Error("wait_template_to_be_added")

			return errors.WithMessage(err, "template_failed")
		} else {
			rollbackMd5 := fmt.Sprintf("%x", md5.Sum([]byte(rollbackTemplate.Content)))
			nodeMd5 := fmt.Sprintf("%x", md5.Sum([]byte(nodeTemplate.Content)))
			if rollbackMd5 == nodeMd5 {
				return nil
			}

			err := fmt.Errorf("template_path_%s_content_md5_%s_must_equal_to_%s_please_wait",
				rollbackTemplate.Path, rollbackMd5, nodeMd5)

			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).WithField("path", rollbackTemplate.Path).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				WithField("current_md5", nodeMd5).WithField("target_md5", rollbackMd5).
				Error("wait_template_to_be_updated_or_added")

			return errors.WithMessage(err, "template_md5_inconsistent")
		}
	}

	return nil
}

func (t *CheckHotUpdateRollbackStatusTask) rollback() error {
	if err := t.checkProvisionComponent(); err != nil {
		return err
	}

	if err := t.checkProvisionTemplate(); err != nil {
		return err
	}

	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.RollbackTemplates {
		if t.ALB.Spec.ALBNode.TemplateMap == nil {
			t.ALB.Spec.ALBNode.TemplateMap = make(map[string]string)
		}

		t.ALB.Spec.ALBNode.TemplateMap[item.Path] = fmt.Sprintf("%x", md5.Sum([]byte(item.Content)))
	}

	for _, item := range t.ALB.Spec.ALBNode.HotUpdateConfig.RollbackComponents {
		if t.ALB.Spec.ALBNode.ComponentMap == nil {
			t.ALB.Spec.ALBNode.ComponentMap = make(map[string]string)
		}

		t.ALB.Spec.ALBNode.ComponentMap[item.Name] = item.Version
	}

	t.ALB.Spec.ALBNode.HotUpdateConfig.RollbackTemplates = nil
	t.ALB.Spec.ALBNode.HotUpdateConfig.RollbackComponents = nil
	t.ALB.Spec.ALBNode.HotUpdateConfig.Type = consts.CompleteTocexJob

	log.Logger().WithField("ip", t.ALB.Spec.LanIP).WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
		Info("check_hot_update_rollback_status_success")

	return nil
}

func (t *CheckHotUpdateRollbackStatusTask) Run() error {
	if !strings.EqualFold(t.ALB.Spec.ALBNode.HotUpdateConfig.Type, consts.RollbackTocexItem) {
		t.ALB.Spec.ALBNode.HotUpdateConfig.Type = consts.CompleteTocexJob
		log.Logger().WithField("ip", t.ALB.Spec.LanIP).WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
			Warn("check_hot_update_rollback_type_invalid")

		return nil
	}

	provision, err := t.Node.GetNodeProvision()
	if err != nil {
		log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
			Error("check_hot_update_rollback_status_error")

		return errors.WithMessage(err, "fetch_provision_failed")
	}

	for _, item := range provision.Templates {
		tem := item
		t.templates[item.Path] = &tem
	}

	for _, item := range provision.Components {
		com := item
		name := item.Name
		if item.Type == cmp.ComponentTypeDocker {
			name = item.ServiceName
		}
		t.components[name] = &com
	}

	if err = t.rollback(); err != nil {
		return errors.WithMessage(err, "rollback_failed")
	}

	return nil
}
