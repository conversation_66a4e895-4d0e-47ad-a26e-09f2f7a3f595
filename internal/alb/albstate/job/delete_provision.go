package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albdom/albprov"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type DeleteProvisionTask struct {
	Agent  *TaskAgent
	ALB    *v1alpha1.ALB
	Node   toc.TocexAdapter
	Server *tocvo.ALBNodeServer
	prov   albprov.Provision
}

func NewDeleteProvisionTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *DeleteProvisionTask {
	agent := NewTaskAgent(alb)
	traceID := agent.UID

	prov, err := albprov.NewNodeProvision(svr, traceID)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      agent.IP,
			"az":      agent.AZ,
			"env":     agent.Env,
			"uid":     agent.UID,
			"cluster": alb.Name,
		}).Error("toc_alb_provision_delete_error")
	}

	return &DeleteProvisionTask{
		Agent:  agent,
		ALB:    alb,
		Node:   toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),
		Server: svr,
		prov:   prov,
	}
}

func (t *DeleteProvisionTask) Description() string {
	return ""
}

func (t *DeleteProvisionTask) Name() string {
	return DeleteProvisionTaskName
}

func (t *DeleteProvisionTask) Run() error {
	if err := t.ensureProvision(); err != nil {
		return errors.WithMessage(err, "provision_is_nil_during_task_init")
	}

	provision, err := t.prov.NodeConfig(cmp.ComponentTypeUnSpec)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      t.Agent.IP,
			"az":      t.Agent.AZ,
			"env":     t.Agent.Env,
			"uid":     t.Agent.UID,
			"cluster": t.Agent.ALBName,
		}).Error("toc_alb_provision_delete_error")

		return errors.WithMessage(err, "fetch_provision_config_failed")
	}

	err = t.Node.DeleteProvision(provision)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":         t.Agent.IP,
			"az":         t.Agent.AZ,
			"env":        t.Agent.Env,
			"uid":        t.Agent.UID,
			"cluster":    t.Agent.ALBName,
			"templates":  len(provision.Templates),
			"components": len(provision.Components),
		}).Error("toc_alb_provision_delete_error")

		return errors.WithMessage(err, "delete_provision_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":         t.Agent.IP,
		"az":         t.Agent.AZ,
		"env":        t.Agent.Env,
		"uid":        t.Agent.UID,
		"cluster":    t.Agent.ALBName,
		"templates":  len(provision.Templates),
		"components": len(provision.Components),
	}).Info("toc_alb_delete_server_success")

	return nil
}

func (t *DeleteProvisionTask) ensureProvision() error {
	if t.prov != nil {
		return nil
	}

	prov, err := albprov.NewNodeProvision(t.Server, t.Agent.UID)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"az":  t.Agent.AZ,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
		}).Error("toc_alb_provision_delete_error")

		return errors.WithMessage(err, "toc_alb_provision_delete_error")
	}

	t.prov = prov

	return nil
}
