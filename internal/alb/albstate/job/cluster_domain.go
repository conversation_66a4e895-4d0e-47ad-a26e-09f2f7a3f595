package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type ClusterDomainTask struct {
	NodeServer *tocvo.ALBNodeServer
	Meta       *sgwvo.ALBClusterConfigMeta
	Agent      *TaskAgent

	cluster             sgw.L7ClusterAdapter
	cachedClusterDomain string
}

func NewClusterDomainTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *ClusterDomainTask {
	if svr == nil {
		log.Logger().Error("alb_node_server_is_nil")

		return nil
	}

	if alb == nil {
		log.Logger().Error("alb_is_nil")

		return nil
	}

	return &ClusterDomainTask{
		Agent:      NewTaskAgent(alb),
		NodeServer: svr,
		Meta:       svr.Meta,
		cluster:    sgw.NewALBClusterAdapter(svr.Agent.ID),
	}
}

/*
1. fetch tag if not empty
*/
func (t *ClusterDomainTask) clusterDomain() (string, error) {
	clusterDomain, err := t.agentClusterDomain()
	if err != nil {
		log.Logger().WithError(err).Error("get_cluster_domain_failed")
	}

	if clusterDomain != "" {
		return clusterDomain, nil
	}

	if t.NodeServer.IsClusterDomainWithNetworkZone() {
		zone := t.NodeServer.ShortenNetworkZone()
		domain, err := t.Meta.ClusterDomainWithNetworkZone(zone)
		if err != nil {
			return "", errors.WithMessage(err, "cluster_domain_with_network_zone_failed")
		}

		return domain, nil
	}

	return t.Meta.ClusterDomain(), nil
}

func (t *ClusterDomainTask) agentClusterDomain() (string, error) {
	if t.cachedClusterDomain != "" {
		return t.cachedClusterDomain, nil
	}

	if t.Agent == nil {
		return "", errors.New("task_agent_is_nil")
	}

	cluster, err := t.cluster.GetByUUID(t.Agent.ClusterUUID)
	if err != nil {
		return "", errors.WithMessage(err, "get_cluster_by_node_failed")
	}

	if cluster == nil {
		return "", errors.New("cluster_is_nil")
	}

	clusterDomains := cluster.ALBClusterDomains
	if clusterDomains == nil {
		return "", nil
	}

	if clusterDomains.ExistsDomain() {
		t.cachedClusterDomain = clusterDomains.GetDomain()

		return t.cachedClusterDomain, nil
	}

	return "", nil
}
