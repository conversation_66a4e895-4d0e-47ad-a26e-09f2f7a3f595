package job

import (
	"strings"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type HotUpdateRollbackTocexTask struct {
	ALB  *v1alpha1.ALB
	Node toc.TocexAdapter
}

func NewHotUpdateRollbackTocexTask(alb *v1alpha1.ALB) *HotUpdateRollbackTocexTask {
	return &HotUpdateRollbackTocexTask{
		ALB:  alb,
		Node: toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),
	}
}

func (t *HotUpdateRollbackTocexTask) Description() string {
	return ""
}

func (t *HotUpdateRollbackTocexTask) Name() string {
	return HotUpdateRollbackTaskName
}

func (t *HotUpdateRollbackTocexTask) Run() error {
	if strings.EqualFold(t.ALB.Spec.ALBNode.HotUpdateConfig.Type, consts.RollbackTocexItem) {
		temps := tocvo.TocTemplates(t.ALB.Spec.ALBNode.HotUpdateConfig.RollbackTemplates)
		comps := tocvo.TocComponents(t.ALB.Spec.ALBNode.HotUpdateConfig.RollbackComponents)

		err := t.Node.SetNodeProvision(&toclib.ProvisionNodeConfig{
			HostIP:     t.ALB.Spec.LanIP,
			Components: comps.ToNodeComponents(),
			Templates:  temps.ToNodeTemplates(),
		})
		if err != nil {
			log.Logger().WithError(err).WithField("ip", t.ALB.Spec.LanIP).
				WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
				Error("hot_update_rollback_tocex_error")

			return errors.WithMessage(err, "provision_failed")
		}
	} else {
		log.Logger().WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
			WithField("ip", t.ALB.Spec.LanIP).Warn("hot_update_rollback_tocex_invalid_type")

		return nil
	}

	log.Logger().WithField("ip", t.ALB.Spec.LanIP).WithField("type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
		Info("hot_update_rollback_success")

	return nil
}
