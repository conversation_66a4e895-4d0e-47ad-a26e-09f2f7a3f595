package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/toc/tocvo"
)

type CheckNodePeerTask struct {
	Agent      *TaskAgent
	Node       toc.TocexAdapter
	NodeServer *tocvo.ALBNodeServer
}

func NewCheckNodePeerTask(alb *v1alpha1.ALB, svr *tocvo.ALBNodeServer) *CheckNodePeerTask {
	return &CheckNodePeerTask{
		Agent:      NewTaskAgent(alb),
		Node:       toc.NewTocexAdapter(tocvo.NewTocNode(&alb.Spec)),
		NodeServer: svr,
	}
}

func (t *CheckNodePeerTask) Description() string {
	return ""
}

func (t *CheckNodePeerTask) Name() string {
	return CheckNodePeerTaskName
}

func (t *CheckNodePeerTask) fetchPeerBrief() (consts.BirdProtocolBriefs, error) {
	ret, err := t.Node.RunTask(consts.BirdShowProtocolScript)
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_bird_proto_brief_failed")
	}

	/*
		BIRD 2.0.3 ready.
		Name       Proto      Table      State  Since         Info
		device1    Device     ---        up     2023-03-13
		static1    Static     master4    up     2023-03-13
		ecmp_lan1_bgp BGP        ---        up     2023-03-13    Established
		ecmp_lan2_bgp BGP        ---        up     2023-03-13    Established
	*/

	briefs, err := consts.ParseBirdBriefs(ret)
	if err != nil {
		return nil, errors.WithMessage(err, "parse_bird_proto_brief_failed")
	}

	return briefs, nil
}

func (t *CheckNodePeerTask) Run() error {
	if !t.NodeServer.IsHAMNeedBGP() {
		return nil
	}

	briefs, err := t.fetchPeerBrief()
	if err != nil {
		return errors.WithMessage(err, "fetch_node_peer_brief_failed")
	}

	if err := briefs.AllBGPEstablished(); err != nil {
		return errors.WithMessage(err, "check_node_peer_failed")
	}

	log.Logger().WithFields(log.Fields{
		"ip":  t.Agent.IP,
		"env": t.Agent.Env,
		"uid": t.Agent.UID,
		"az":  t.Agent.AZ,
	}).Info("check_node_peer_success")

	return nil
}
