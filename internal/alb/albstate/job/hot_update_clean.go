package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type HotUpdateCleanTask struct {
	ALB *v1alpha1.ALB
}

func NewHotUpdateCleanTask(alb *v1alpha1.ALB) *HotUpdateCleanTask {
	return &HotUpdateCleanTask{
		ALB: alb,
	}
}

func (t *HotUpdateCleanTask) Description() string {
	return ""
}

func (t *HotUpdateCleanTask) Name() string {
	return HotUpdateCleanTaskName
}

func (t *HotUpdateCleanTask) Run() error {
	switch t.ALB.Spec.ALBNode.HotUpdateConfig.Type {
	case "", consts.CompleteTocexJob:
		t.ALB.Spec.ALBNode.HotUpdateConfig = v1alpha1.TocexHotUpdateConfig{
			Templates:          nil,
			Components:         nil,
			RollbackComponents: nil,
			RollbackTemplates:  nil,
		}

		log.Logger().WithField("ip", t.ALB.Spec.LanIP).WithField("alb_name", t.ALB.Name).
			Info("hot_update_clean_success")

		return nil
	default:
		log.Logger().WithField("ip", t.ALB.Spec.LanIP).
			WithField("leap_worker_name", t.ALB.Name).
			WithField("hot_update_type", t.ALB.Spec.ALBNode.HotUpdateConfig.Type).
			Warn("hot_update_clean_error_type_must_be_complete_or_empty")

		return errors.New("invalid_type")
	}
}
