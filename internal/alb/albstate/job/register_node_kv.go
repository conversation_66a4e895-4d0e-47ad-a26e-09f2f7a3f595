package job

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/ops"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
)

type RegisterNodeKVTask struct {
	Agent   *TaskAgent
	etcd    ops.EtcdAdapter
	zk      ops.ZKAdapter
	cluster sgw.L7ClusterAdapter
}

func NewRegisterNodeKVTask(alb *v1alpha1.ALB) *RegisterNodeKVTask {
	return &RegisterNodeKVTask{
		Agent:   NewTaskAgent(alb),
		etcd:    ops.NewEtcdAdapter(string(alb.UID)),
		zk:      ops.NewZKAdapter(string(alb.UID)),
		cluster: sgw.NewALBClusterAdapter(string(alb.UID)),
	}
}

func (t *RegisterNodeKVTask) Description() string {
	return ""
}

func (t *RegisterNodeKVTask) Name() string {
	return AddNodeKVMetaTaskName
}

func (t *RegisterNodeKVTask) Run() error {
	etcdMeta, err := t.etcd.ClusterMeta(t.Agent.IDC, t.Agent.Env)
	if err != nil {
		return errors.WithMessage(err, "fetch_etcd_meta_failed")
	}

	zkMeta, err := t.zk.ClusterMeta(t.Agent.IDC, t.Agent.Env)
	if err != nil {
		return errors.WithMessage(err, "fetch_zk_meta_failed")
	}

	cluster, err := t.cluster.GetByUUID(t.Agent.ClusterUUID)
	if err != nil {
		log.Logger().WithFields(log.Fields{
			"ip":           t.Agent.IP,
			"env":          t.Agent.Env,
			"uid":          t.Agent.UID,
			"cluster_uuid": t.Agent.ClusterUUID,
		}).WithError(err).Error("fetch_alb_cluster_failed")

		return errors.WithMessage(err, "fetch_alb_cluster_failed")
	}

	if cluster.NodeZK != "" && cluster.NodeEtcd != "" {
		log.Logger().WithFields(log.Fields{
			"ip":  t.Agent.IP,
			"env": t.Agent.Env,
			"uid": t.Agent.UID,
			"az":  t.Agent.AZ,
		}).Info("alb_node_kv_exist")

		return nil
	}

	ok, err := t.cluster.UpdateNodeKV(&sgw.L7ClusterKVUpdateRequest{
		UUID:     t.Agent.ClusterUUID,
		NodeEtcd: etcdMeta.ClusterURL,
		NodeZK:   zkMeta.ClusterURL,
	})
	if err != nil {
		return errors.WithMessage(err, "update_alb_node_kv_error")
	}

	if !ok {
		return errors.New("update_alb_node_kv_error")
	}

	log.Logger().WithFields(log.Fields{
		"ip":  t.Agent.IP,
		"env": t.Agent.Env,
		"uid": t.Agent.UID,
		"az":  t.Agent.AZ,
	}).Info("update_alb_node_kv_success")

	return nil
}
