package job

import (
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/meta"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/sgw"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/meta/metavo"
)

func TestUpsertEtcdEndpointTask_Run_Private(t *testing.T) {
	node := my0StagingNode0()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	task := NewUpsertEtcdEndpointTask(node.ALB(t), svr)
	task.Cluster = node.SGWL7Cluster(t)
	task.Sniffer = node.Sniffer(t)
	assert.NoError(t, task.Run())
}

func TestUpsertEtcdEndpointTask_Run_General(t *testing.T) {
	node := sg90TestNode2()
	svr, err := node.ALBNodeServer(t)
	assert.NoError(t, err)

	cls := sgw.NewALBClusterAdapter(t.Name())
	if _, err = cls.GetByNode(node.NodeIP); err != nil {
		if strings.Contains(err.Error(), "get_cluster_by_uuid_failed") {
			return
		}
	}
	assert.NoError(t, err)

	ctrl := gomock.NewController(t)
	mockMetaNode := meta.NewMockNodeAdapter(ctrl)
	etcdEndpointsTask := NewUpsertEtcdEndpointTask(node.ALB(t), svr)
	etcdEndpointsTask.metaNode = mockMetaNode

	mockMetaNode.EXPECT().DumpByResourceZone(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil, errors.New("not_found_etcd_default")).Times(1)
	err = etcdEndpointsTask.Run()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not_found_etcd_default")

	mockMetaNode.EXPECT().DumpByResourceZone(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(metavo.Nodes{}, nil).Times(1)
	err = etcdEndpointsTask.Run()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not_found_nodes")

	task := NewUpsertEtcdEndpointTask(node.ALB(t), svr)
	task.Sniffer = node.Sniffer(t)
	assert.NoError(t, task.Run())
}
