package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

type PostCheckStateRunner struct {
	StateBaseRunner
}

func NewPostCheckStateRunner() StateRunner {
	return &PostCheckStateRunner{StateBaseRunner{State: consts.PostCheckState}}
}

func (r *PostCheckStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *PostCheckStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	return ctl.CompareAndUpdateALBStatus(alb, consts.InitialisedState)
}

func (r *PostCheckStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	nodeServer, err := r.fetchALBNodeServer(alb)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"az":      alb.Spec.ALBNode.Zone,
			"uid":     alb.UID,
			"cluster": alb.Spec.ALBNode.ALBName,
			"state":   r.Current().Status,
		}).Error("fetch_node_server_failed")

		return errors.WithMessage(err, "fetch_node_server_failed")
	}

	meta := toc.NewMetaAdapter(string(alb.UID))
	az, err := meta.AZ(alb.Spec.ALBNode.Zone)
	if err != nil {
		return errors.WithMessage(err, "fetch_az_failed")
	}

	var tasks []core.Task
	tasks = append(tasks,
		job.NewCheckNginxVersionTask(alb),
		job.NewCheckNodeListenerTask(alb),
	)

	isGeneral, err := az.IsGeneralAZSeg(alb.Spec.ALBNode.Segment)
	if err != nil {
		log.Logger().WithFields(log.Fields{
			"alb_name": alb.Name,
			"node_ip":  alb.Spec.LanIP,
			"segment":  alb.Spec.ALBNode.Segment,
		}).WithError(err).Error("check_general_az_seg_failed_in_postcheck_state")

		return errors.WithMessage(err, "check_general_az_seg_failed")
	}

	if isGeneral {
		tasks = append(tasks,
			job.NewCheckNodeStatusTask(alb),
		)
	} else {
		tasks = append(tasks,
			job.NewCheckClusterDomainTask(alb, nodeServer),
		)
	}

	return r.doRun(alb, ctl.recorder, tasks)
}
