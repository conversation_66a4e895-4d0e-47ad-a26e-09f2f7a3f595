package albstate

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"time"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/workqueue"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"gorm.io/gorm"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"
	leap "git.garena.com/shopee/devops/leap-apis/pkg/generated/clientset/versioned"
	machineinformers "git.garena.com/shopee/devops/leap-apis/pkg/generated/informers/externalversions/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsdom/opsrepo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/ops/opsent"
)

var (
	NodeMap  sync.Map // name -> alb CR pointer
	albState map[string]StateRunner
)

func init() {
	albState = make(map[string]StateRunner)

	NewSpareStateRunner().Register()
	NewFormatStateRunner().Register()
	NewProvisionStateRunner().Register()
	NewInitialisingStateRunner().Register()
	NewPostCheckStateRunner().Register()
	NewInitialStateRunner().Register()
	NewInitialHotUpdateStateRunner().Register()
	NewInitialRollbackStateRunner().Register()
	NewPreRunningStateRunner().Register()
	NewRunningStateRunner().Register()
	NewRunningHotUpdateStateRunner().Register()
	NewRunningRollbackStateRunner().Register()
	NewPreOfflineStateRunner().Register()
	NewPreRetiringStateRunner().Register()
	NewRetiringStateRunner().Register()
	NewPreMAStateRunner().Register()
	NewMARunner().Register()
	NewSunsetStateRunner().Register()
}

type Controller struct {
	IsLeader bool

	workQueue      workqueue.RateLimitingInterface
	kubeClient     kubernetes.Interface
	leapClient     leap.Interface
	informer       machineinformers.ALBInformer
	informerSynced cache.InformerSynced
	recorder       record.EventRecorder

	event opsrepo.EventRepo
}

// NewController controller
func NewController(
	kubeClientSet kubernetes.Interface,
	leapClientSet leap.Interface,
	informer machineinformers.ALBInformer,
	recorder record.EventRecorder,
) *Controller {
	controller := &Controller{
		workQueue: workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(),
			configs.ALB.Kube.WorkerQueue.Name),
		leapClient:     leapClientSet,
		informer:       informer,
		informerSynced: informer.Informer().HasSynced,
		recorder:       recorder,
		kubeClient:     kubeClientSet,

		event: opsrepo.NewEventRepo(configs.ALB.Kube.WorkerQueue.Name),
	}

	informer.Informer().AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    controller.enqueue,
		UpdateFunc: controller.update,
		DeleteFunc: controller.delete,
	})

	return controller
}

func State(state string) StateRunner {
	return albState[state]
}

func (c *Controller) Running(alb *v1alpha1.ALB) error {
	if !consts.InitialisedState.Equals(alb.Status) {
		log.Logger().WithField("state", alb.Status.State).WithField("reason", alb.Status.Reason).
			WithField("name", alb.Name).Error("run_alb_error")

		return fmt.Errorf("alb_%s_not_ready_for_run", alb.Name)
	}

	return c.CompareAndUpdateALBStatus(alb, consts.PreRunningState)
}

func (c *Controller) Update(ctx context.Context, alb *v1alpha1.ALB) error {
	if _, err := c.leapClient.MachineV1alpha1().ALBs().Update(ctx, alb, metav1.UpdateOptions{}); err != nil {
		log.Logger().WithError(err).WithField("ip", alb.Spec.LanIP).WithField("name", alb.Name).
			Error("update_alb_info_error")

		return errors.WithMessage(err, "update_alb_info_error")
	}

	return nil
}

func (c *Controller) Delete(ctx context.Context, alb *v1alpha1.ALB) error {
	err := c.leapClient.MachineV1alpha1().ALBs().Delete(ctx, alb.Name, metav1.DeleteOptions{})
	if err != nil {
		log.Logger().WithError(err).WithField("name", alb.Name).Error("delete_alb_error")

		return errors.WithMessage(err, "delete_alb_error")
	}

	return nil
}

func (c *Controller) Create(ctx context.Context, alb *v1alpha1.ALB) error {
	oldALB, err := c.Get(ctx, alb.Name)
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
	}

	if oldALB != nil {
		log.Logger().WithField("name", oldALB.Name).Warn("alb_already_exist")

		return errors.New("alb_already_exist")
	}

	if _, err = c.leapClient.MachineV1alpha1().ALBs().Create(ctx, alb, metav1.CreateOptions{}); err != nil {
		log.Logger().WithError(err).WithField("name", alb.Name).Error("create_alb_error")

		return errors.WithMessage(err, "create_alb_error")
	}

	c.emitSingleEvent(consts.EventCreate, alb)

	return nil
}

func (c *Controller) Get(ctx context.Context, name string) (*v1alpha1.ALB, error) {
	alb, err := c.leapClient.MachineV1alpha1().ALBs().Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_alb_from_leap_client_failed")
	}

	return alb, nil
}

func (c *Controller) enqueue(obj interface{}) {
	key, err := cache.MetaNamespaceKeyFunc(obj)
	if err != nil {
		log.Logger().WithError(err).Error("enqueue_alb_queue_error")

		return
	}

	c.workQueue.Add(key)
}

func (c *Controller) delete(obj interface{}) {
	alb, ok := obj.(*v1alpha1.ALB)
	if !ok {
		log.Logger().Error("decoding_object_invalid_type_alb")

		return
	}

	NodeMap.Delete(alb.Name)

	c.emitSingleEvent(consts.EventDelete, alb)
}

func (c *Controller) update(origin, target interface{}) {
	oldALB, ok := origin.(*v1alpha1.ALB)
	if !ok {
		log.Logger().Error("decoding_object_invalid_alb_type")

		return
	}

	newALB, ok := target.(*v1alpha1.ALB)
	if !ok {
		log.Logger().Error("decoding_object_invalid_alb_type")

		return
	}

	if !reflect.DeepEqual(oldALB, newALB) {
		c.enqueue(target)
	}

	c.emitEvent(oldALB, newALB)
}

func (c *Controller) emitSingleEvent(eventType string, alb *v1alpha1.ALB) {
	event := &opsent.EventLog{
		NodeName:     alb.Spec.LanIP,
		BusinessType: consts.BizALBType,
		EventType:    eventType,
		Reason:       alb.Status.Reason,
		Message:      "",
		State:        alb.Status.State,
		TicketID:     cast.ToString(alb.Spec.ALBNode.SWPTicket),
		OperateUser:  cast.ToString(alb.Spec.ALBNode.Options["applicant_user"]),
		CreatedAt:    uint64(time.Now().Unix()),
		UpdatedAt:    uint64(time.Now().Unix()),
		DeletedAt:    gorm.DeletedAt{},
	}

	if _, err := c.event.Dispatch(event); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"alb":    alb.Name,
			"state":  alb.Status.State,
			"reason": alb.Status.Reason,
		}).Error("insert_event_failed")
	}
}

func (c *Controller) emitEvent(oldALB, newALB *v1alpha1.ALB) {
	if !c.IsLeader || oldALB.Status.State == newALB.Status.State {
		return
	}

	event := &opsent.EventLog{
		NodeName:     newALB.Spec.LanIP,
		BusinessType: consts.BizALBType,
		EventType:    consts.EventUpdate,
		Reason:       newALB.Status.Reason,
		Message:      fmt.Sprintf(`%s->%s`, oldALB.Status.State, newALB.Status.State),
		State:        newALB.Status.State,
		TicketID:     cast.ToString(newALB.Spec.ALBNode.SWPTicket),
		OperateUser:  cast.ToString(newALB.Spec.ALBNode.Options["applicant_user"]),
		CreatedAt:    uint64(time.Now().Unix()),
		UpdatedAt:    uint64(time.Now().Unix()),
		DeletedAt:    gorm.DeletedAt{},
	}

	if _, err := c.event.Dispatch(event); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"alb":        newALB.Name,
			"dst":        newALB.Status.State,
			"dst_reason": newALB.Status.Reason,
			"src":        oldALB.Status.State,
			"src_reason": oldALB.Status.Reason,
		}).Error("insert_event_failed")
	}
}

func (c *Controller) EnableLeader() {
	c.IsLeader = true
}

func (c *Controller) Run() {
	defer utilruntime.HandleCrash()
	//nolint:nolintlint,revive
	for c.processNext() {
	}
}

func (c *Controller) processNext() bool {
	obj, shutdown := c.workQueue.Get()

	if shutdown {
		log.Logger().Warn("alb_work_queue_already_shutdown")

		return false
	}
	defer c.workQueue.Done(obj)

	key, ok := obj.(string)
	if !ok {
		c.workQueue.Forget(obj)
		log.Logger().Error(fmt.Sprintf("expected_string_in_work_queue_but_got_%#v", obj))

		return true
	}

	if state, err := c.sync(key); err != nil {
		var interval time.Duration
		if consts.MaintenanceState.EqualsString(state) {
			interval = configs.ALB.Kube.Sync.MAInterval()
		} else {
			interval = configs.ALB.Kube.Sync.Interval()
		}

		log.Logger().WithError(err).WithFields(log.Fields{
			"key":      key,
			"state":    state,
			"interval": interval,
		}).Warn("alb_sync_error")

		c.workQueue.AddAfter(key, interval)
	}

	c.workQueue.Forget(obj)

	return true
}

func (c *Controller) sync(key string) (string, error) {
	_, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		log.Logger().WithField("key", key).WithError(err).Error("invalid_resource_key")

		return "", nil
	}

	alb, err := c.informer.Lister().Get(name)
	if err != nil {
		if apierrors.IsNotFound(err) {
			NodeMap.Delete(name)
			log.Logger().WithError(err).Warn("alb_does_not_exist_" + name)

			return "", nil
		}

		log.Logger().WithError(err).WithField("name", name).Error("get_alb_error")

		return "", errors.WithMessage(err, "get_alb_error")
	}

	NodeMap.Store(name, alb)

	return alb.Status.State, c.execute(alb)
}

func (c *Controller) InformerSynced() cache.InformerSynced {
	return c.informerSynced
}

func (c *Controller) SyncNodes() cache.InformerSynced {
	return func() bool {
		rets, err := c.informer.Lister().List(labels.Everything())
		if err != nil {
			log.Logger().WithError(err).Error("list_nodes_error")

			return false
		}

		for _, alb := range rets {
			NodeMap.Store(alb.Name, alb)
		}

		log.Logger().WithFields(log.Fields{
			"count": len(rets),
		}).Info("sync_nodes_success")

		return true
	}
}

func (c *Controller) SyncNodeMap(ctx context.Context) {
	rets, err := c.leapClient.MachineV1alpha1().ALBs().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Logger().WithError(err).Error("list_albs_failed")

		return
	}

	currents := make(map[string]struct{})
	slice.ForEach(rets.Items, func(_ int, alb v1alpha1.ALB) {
		currents[alb.Name] = struct{}{}
		NodeMap.Store(alb.Name, &alb)
	})

	NodeMap.Range(func(key, value interface{}) bool {
		name, ok := key.(string)
		if !ok {
			return true
		}
		if _, ok := currents[name]; !ok {
			NodeMap.Delete(name)
		}

		return true
	})

	log.Logger().WithFields(log.Fields{
		"count": len(rets.Items),
	}).Info("sync_alb_nodes_success")
}

func (c *Controller) CompareAndUpdateStatusWithContext(ctx context.Context, alb *v1alpha1.ALB, state consts.State) error {
	newALB := alb.DeepCopy()
	newALB.Status.State = state.Status
	newALB.Status.Reason = state.Reason
	newALB.Status.LastTransitionTime = metav1.Now()

	if reflect.DeepEqual(alb, newALB) {
		return nil
	}

	if _, err := c.leapClient.MachineV1alpha1().ALBs().Update(ctx, newALB, metav1.UpdateOptions{}); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"alb":        alb.Name,
			"dst":        state.Status,
			"dst_reason": state.Reason,
			"src":        alb.Status.State,
			"src_reason": alb.Status.Reason,
		}).Error("update_alb_node_failed")

		return errors.WithMessage(err, "update_alb_node_failed")
	}

	return nil
}

func (c *Controller) CompareAndUpdateALBStatus(alb *v1alpha1.ALB, state consts.State) error {
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancel()

	return c.CompareAndUpdateStatusWithContext(ctx, alb, state)
}

func (c *Controller) execute(alb *v1alpha1.ALB) error {
	if alb.Kind == "" {
		alb.Kind = configs.ALB.Kube.Kind
	}
	if alb.APIVersion == "" {
		alb.APIVersion = configs.ALB.Kube.APIVersion
	}

	if runner := albState[alb.Status.State]; runner != nil {
		if runner.Disable() {
			if err := runner.Run(alb, c); err != nil {
				return errors.WithMessage(err, fmt.Sprintf("%s_runner_run_failed", runner.Current().Status))
			}

			return nil
		}

		err := runner.Run(alb, c)
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("%s_runner_run_failed", runner.Current().Status))
		}

		if err = runner.Next(alb, c); err != nil {
			return errors.WithMessage(err, fmt.Sprintf("%s_runner_next_failed", runner.Current().Status))
		}

		return nil
	}

	return fmt.Errorf("state %s runner unsupported", alb.Status.State)
}

func (c *Controller) UpdateALBMATicketWithContext(ctx context.Context, alb *v1alpha1.ALB, ticketID int) error {
	newALB := alb.DeepCopy()
	newALB.Spec.ALBNode.SWPTicket = ticketID

	if reflect.DeepEqual(alb, newALB) {
		return nil
	}

	if _, err := c.leapClient.MachineV1alpha1().ALBs().Update(ctx, newALB, metav1.UpdateOptions{}); err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"alb": alb.Name,
			"dst": ticketID,
			"src": alb.Spec.ALBNode.SWPTicket,
		}).Error("update_alb_node_failed")

		return errors.WithMessage(err, "update_alb_node_failed")
	}

	return nil
}

func (c *Controller) GetALBList(ctx context.Context) (*v1alpha1.ALBList, error) {
	albs, err := c.leapClient.MachineV1alpha1().ALBs().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, errors.WithMessage(err, "fetch_alb_from_leap_client_failed")
	}

	return albs, nil
}
