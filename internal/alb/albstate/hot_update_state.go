package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type HotUpdateStateRunner struct {
	StateBaseRunner
}

func (r *HotUpdateStateRunner) hotUpdate(alb *v1alpha1.ALB, ctl *Controller) error {
	var tasks []core.Task

	for _, c := range alb.Spec.ALBNode.HotUpdateConfig.Components {
		switch c.Name {
		case cmp.SGWAgentComponent.Name:
			tasks = append(tasks,
				job.NewHotUpdateTocexTask(alb),
				job.NewCheckHotUpdateStatusTask(alb),
				job.NewCheckNginxVersionTask(alb),
				job.NewCheckNodeListenerTask(alb),
			)
		default:
			tasks = append(tasks,
				job.NewHotUpdateTocexTask(alb),
				job.NewCheckHotUpdateStatusTask(alb),
			)
		}
	}

	err := r.doRun(alb, ctl.recorder, tasks)
	if err != nil {
		return errors.WithMessage(err, "hot_update_run_failed")
	}

	// unlikely reach here
	if alb.Spec.ALBNode.HotUpdateConfig.Type != consts.CompleteTocexJob {
		alb.Spec.ALBNode.HotUpdateConfig.Type = consts.CompleteTocexJob
	}

	return nil
}
