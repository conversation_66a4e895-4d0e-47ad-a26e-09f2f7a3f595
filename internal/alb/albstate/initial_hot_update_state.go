package albstate

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type InitialHotUpdateStateRunner struct {
	HotUpdateStateRunner
}

func NewInitialHotUpdateStateRunner() StateRunner {
	return &InitialHotUpdateStateRunner{
		HotUpdateStateRunner: HotUpdateStateRunner{StateBaseRunner{State: consts.InitialisedHotUpdateState}},
	}
}

func (r *InitialHotUpdateStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *InitialHotUpdateStateRunner) Next(alb *v1alpha1.ALB, c *Controller) error {
	return c.CompareAndUpdateALBStatus(alb, consts.InitialisedState)
}

func (r *InitialHotUpdateStateRunner) Run(alb *v1alpha1.ALB, c *Controller) error {
	return r.hotUpdate(alb, c)
}
