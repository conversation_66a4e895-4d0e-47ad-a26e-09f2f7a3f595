package albstate

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type MARunner struct {
	StateBaseRunner
}

// NewMARunner returns a new instance of MARunner which implements the StateRunner interface.
//
// The function does not take any parameters.
// It returns a StateRunner.
func NewMARunner() StateRunner {
	return &MARunner{StateBaseRunner{State: consts.MaintenanceState}}
}

// Register registers the MARunner.
//
// This function does not take any parameters.
// It does not return anything.
func (r *MARunner) Register() {
	albState[r.State.Status] = r
}

// Next is a method of the MARunner struct that represents the implementation of the Next function.
//
// It takes in two parameters: _ *v1alpha1.ALB and _ *Controller, but does not use them.
// It returns an error.
func (r *MARunner) Next(_ *v1alpha1.ALB, _ *Controller) error {
	return nil
}

// Run executes the MARunner function.
//
// The Run function takes in two parameters: an ALB and a Controller.
// It returns an error.
func (r *MARunner) Run(_ *v1alpha1.ALB, _ *Controller) error {
	return nil
}
