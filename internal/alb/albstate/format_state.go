package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

type FormatStateRunner struct {
	StateBaseRunner
}

func NewFormatStateRunner() StateRunner {
	return &FormatStateRunner{
		StateBaseRunner{State: consts.FormatState},
	}
}

func (r *FormatStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *FormatStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	return ctl.CompareAndUpdateALBStatus(alb, consts.ProvisionState)
}

func (r *FormatStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	// private node no need to register into SMAP
	meta := toc.NewMetaAdapter(string(alb.UID))
	az, err := meta.AZ(alb.Spec.ALBNode.Zone)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"az":      alb.Spec.ALBNode.Zone,
			"uid":     alb.UID,
			"cluster": alb.Spec.ALBNode.ALBName,
			"state":   r.Current().Status,
		}).Error("fetch_az_failed_failed")

		return errors.WithMessage(err, "fetch_az_failed")
	}

	nodeServer, err := r.fetchALBNodeServer(alb)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"az":      alb.Spec.ALBNode.Zone,
			"uid":     alb.UID,
			"cluster": alb.Spec.ALBNode.ALBName,
			"state":   r.Current().Status,
		}).Error("fetch_node_server_failed")

		return errors.WithMessage(err, "fetch_node_server_failed")
	}

	var tasks []core.Task

	if az.IsGeneral() {
		tasks = append(
			tasks,
			job.NewBindServersToTreeTask(alb, nodeServer),
		)
	}

	return r.doRun(alb, ctl.recorder, tasks)
}
