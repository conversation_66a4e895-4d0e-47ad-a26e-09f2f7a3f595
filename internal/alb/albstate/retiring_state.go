package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

type RetiringStateRunner struct {
	StateBaseRunner
}

func NewRetiringStateRunner() StateRunner {
	return &RetiringStateRunner{StateBaseRunner{State: consts.RetiringState}}
}

func (r *RetiringStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *RetiringStateRunner) Next(alb *v1alpha1.ALB, c *Controller) error {
	return c.CompareAndUpdateALBStatus(alb, consts.SunsetState)
}

func (r *RetiringStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	nodeServer, err := r.fetchALBNodeServer(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_alb_node_server_failed")
	}

	var tasks []core.Task

	meta := toc.NewMetaAdapter(string(alb.UID))
	az, err := meta.AZ(alb.Spec.ALBNode.Zone)
	if err != nil {
		return errors.WithMessage(err, "fetch_az_failed")
	}

	tasks = append(
		tasks,
		job.NewDeleteNodeFromClusterTask(alb),
		job.NewDeleteEtcdNodeKeyTask(alb),
		job.NewDeleteProvisionTask(alb, nodeServer),
		job.NewCheckDeleteProvisionTask(alb, nodeServer),
		job.NewUnbindServersFromTreeTask(alb, nodeServer),
	)
	if az.IsGeneral() {
		tasks = append(tasks,
			job.NewUnregisterNodeTask(alb),
			job.NewMoveNodeIntoMAPoolTask(alb),
		)
	}
	// TODO unregister cluster domain, including etcd proxy

	return r.doRun(alb, ctl.recorder, tasks)
}
