package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/infra/toc"
)

type SpareStateRunner struct {
	StateBaseRunner
}

func NewSpareStateRunner() StateRunner {
	return &SpareStateRunner{StateBaseRunner{
		State: consts.SpareState,
	}}
}

func (r *SpareStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *SpareStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	return ctl.CompareAndUpdateALBStatus(alb, consts.FormatState)
}

func (r *SpareStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	server := toc.NewServerAdapter("")
	groupVar, err := server.GetALBGroupVar(alb.Spec.LanIP)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"ip":      alb.Spec.LanIP,
			"idc":     alb.Spec.ALBNode.IDC,
			"env":     alb.Spec.ALBNode.Env,
			"cluster": alb.Spec.ALBNode.ALBName,
			"uid":     alb.UID,
			"state":   r.State.Status,
		}).Error("fetch_server_group_var_failed")

		return errors.WithMessage(err, "fetch_server_group_var_failed")
	}

	var tasks []core.Task
	tasks = append(
		tasks,
		job.NewCheckOSTask(alb, groupVar),
	)

	if r.doRun(alb, ctl.recorder, tasks) != nil {
		if errors.Is(job.ErrCheck, err) {
			return ctl.CompareAndUpdateALBStatus(alb, consts.MaintenanceState)
		}

		return errors.WithMessage(err, "run_tasks_failed")
	}

	return nil
}
