package albstate

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type InitialRollbackStateRunner struct {
	*StateBaseRunner
	*HotUpdateRollbackStateRunner
}

func NewInitialRollbackStateRunner() StateRunner {
	return &InitialRollbackStateRunner{
		StateBaseRunner:              &StateBaseRunner{State: consts.InitialisedRollbackState},
		HotUpdateRollbackStateRunner: &HotUpdateRollbackStateRunner{},
	}
}

func (r *InitialRollbackStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *InitialRollbackStateRunner) Next(alb *v1alpha1.ALB, c *Controller) error {
	return c.CompareAndUpdateALBStatus(alb, consts.InitialisedState)
}

func (r *InitialRollbackStateRunner) Run(alb *v1alpha1.ALB, c *Controller) error {
	return r.hotUpdateRollback(alb, c)
}
