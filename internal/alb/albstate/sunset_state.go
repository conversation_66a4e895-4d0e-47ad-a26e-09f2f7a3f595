package albstate

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type SunsetStateRunner struct {
	StateBaseRunner
}

func NewSunsetStateRunner() StateRunner {
	return &SunsetStateRunner{StateBaseRunner{State: consts.SunsetState}}
}

func (r *SunsetStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *SunsetStateRunner) Next(_ *v1alpha1.ALB, _ *Controller) error {
	return nil
}

func (r *SunsetStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	var tasks []core.Task

	tasks = append(tasks,
		job.NewDeleteALBCRTask(alb, ctl.leapClient),
	)

	return r.doRun(alb, ctl.recorder, tasks)
}
