package albstate

import (
	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type RunningHotUpdateStateRunner struct {
	HotUpdateStateRunner
}

func NewRunningHotUpdateStateRunner() StateRunner {
	return &RunningHotUpdateStateRunner{
		HotUpdateStateRunner: HotUpdateStateRunner{StateBaseRunner{State: consts.RunningHotUpdateState}},
	}
}

func (r *RunningHotUpdateStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *RunningHotUpdateStateRunner) Next(alb *v1alpha1.ALB, c *Controller) error {
	return c.CompareAndUpdateALBStatus(alb, consts.RunningState)
}

func (r *RunningHotUpdateStateRunner) Run(alb *v1alpha1.ALB, c *Controller) error {
	return r.hotUpdate(alb, c)
}
