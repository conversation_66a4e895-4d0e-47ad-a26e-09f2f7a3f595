//nolint:nolintlint,dupl
package albstate

import (
	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/leap-apis/machine/v1alpha1"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albstate/job"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

type PreMAStateRunner struct {
	StateBaseRunner
}

// NewPreMAStateRunner creates a new PreMAStateRunner.
//
// It returns a StateRunner instance that implements the StateRunner interface.
func NewPreMAStateRunner() StateRunner {
	return &PreMAStateRunner{StateBaseRunner{State: consts.PreMaintenanceState}}
}

// Register is a method of the PreMAStateRunner struct.
//
// This function registers the PreMAStateRunner in the albState map
// using the status of the PreMAStateRunner as the key.
func (r *PreMAStateRunner) Register() {
	albState[r.State.Status] = r
}

func (r *PreMAStateRunner) Next(alb *v1alpha1.ALB, ctl *Controller) error {
	return ctl.CompareAndUpdateALBStatus(alb, consts.MaintenanceState)
}

// Run executes the PreMAStateRunner function.
//
// It takes in an alb object of type *v1alpha1.ALB and a ctl object of type *Controller.
// It returns an error.
func (r *PreMAStateRunner) Run(alb *v1alpha1.ALB, ctl *Controller) error {
	nodeServer, err := r.fetchALBNodeServer(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_alb_node_server_failed")
	}

	var tasks []core.Task
	tasks = append(tasks,
		// check ticket offline status
		job.NewCheckTicketStatusTask(alb, ctl.leapClient, consts.RunningState.Status),
		// check alb node status, make sure the alb node status is NOTREADY
		job.NewCheckNodeMgmtStatusTask(alb, consts.NodeStatusNotReady),
	)

	isPrivateAZ, err := r.isPrivate(alb)
	if err != nil {
		return errors.WithMessage(err, "fetch_is_general_failed")
	}

	if isPrivateAZ {
		ham := nodeServer.HAM()
		if ham.IsHAMWithECMP() {
			tasks = append(tasks, job.NewStopBirdTask(alb, ham.Name))
		} else {
			if !ham.IsHAMWithVRRP() {
				log.Logger().WithFields(log.Fields{
					"ip":  alb.Spec.LanIP,
					"ham": ham.Name,
				}).Error("invalid_ha_mode")
			}

			tasks = append(tasks, job.NewStopKeepalivedTask(alb, ham.Name))
		}

		tasks = append(tasks, job.NewMarkSGWStateTask(alb, consts.SGWServiceUnitNotReady))
	}

	tasks = append(
		tasks,
		// all the TOC cluster alr supported server state MA
		job.NewUpdateServerStateTask(alb, nodeServer, consts.StateMaintenance),
		job.NewSendSeeEventTask(alb, ctl.leapClient),
	)

	return r.doRun(alb, ctl.recorder, tasks)
}
