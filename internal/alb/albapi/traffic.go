package albapi

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

func SupportBlockingTrafficProductsAuthHandler(allowProducts map[string]struct{}) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var err error
		traceID := core.TraceID(ctx)
		var req albvo.ClusterTrafficRequest

		if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
			log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_control_cluster_traffic_ticket_param_invalid")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

			ctx.Abort()

			return
		}

		if _, ok := allowProducts[req.Product]; !ok {
			err = fmt.Errorf("product_isn't_in_the_allow_products")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

			ctx.Abort()

			return
		}

		ctx.Next()
	}
}

// BlockClusterTrafficTicketHandler godoc
// @Summary block alb cluster traffic
// @Description block alb cluster traffic, label: ShopeePay
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param rz body string true "blocked rz"
// @Success 200 {object} albvo.TicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/block/ [POST]
//
//nolint:nolintlint,dupl
func BlockClusterTrafficTicketHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.ClusterTrafficRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_block_cluster_traffic_ticket_param_invalid")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	req.Token = token

	ticket := albapp.NewTicketApp(traceID)
	response, err := ticket.Block(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// BlockClusterTrafficTicketCBHandler godoc
// @Summary block alb cluster traffic callback
// @Description block alb cluster traffic, label: ShopeePay
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param ips body string true "blocked node ips"
// @Param applicant_user body string true "applicant user id"
// @Success 200 {object} albvo.ControlTrafficTaskResultsResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/block/callback/ [POST]
//
//nolint:nolintlint,dupl
func BlockClusterTrafficTicketCBHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	var req albvo.ControlTrafficFormRequest
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("block_traffic_parameter_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	traffic := albapp.NewTrafficApp(traceID)
	resp, err := traffic.Block(&req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// OpenClusterTrafficTicketHandler godoc
// @Summary open alb cluster traffic
// @Description open alb cluster traffic; label: ShopeePay
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param rz body string true "opened rz"
// @Success 200 {object} albvo.TicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/open/ [POST]
//
//nolint:nolintlint,dupl
func OpenClusterTrafficTicketHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("token", token).Error("token_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.ClusterTrafficRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_open_cluster_traffic_ticket_param_invalid")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	req.Token = token

	ticket := albapp.NewTicketApp(traceID)

	response, err := ticket.Open(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// OpenClusterTrafficTicketCBHandler godoc
// @Summary open alb cluster traffic callback
// @Description open alb cluster traffic; label: ShopeePay
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param ips body string true "opened node ips"
// @Param applicant_user body string true "applicant user id"
// @Success 200 {object} albvo.ControlTrafficTaskResultsResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/open/callback/ [POST]
//
//nolint:nolintlint,dupl
func OpenClusterTrafficTicketCBHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	var req albvo.ControlTrafficFormRequest
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("open_traffic_parameter_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	traffic := albapp.NewTrafficApp(traceID)
	resp, err := traffic.Open(&req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// BlockTrafficByDomainsTicketHandler godoc
// @Summary block alb cluster traffic by domains
// @Description block alb cluster traffic by domains, label: ShopeePay
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param rz body string true "blocked rz"
// @Param network_type body string true "blocked network type"
// @Param domains body []string true "blocked domains"
// @Success 200 {object} albvo.TicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/block_by_domains/ [POST]
//
//nolint:nolintlint,dupl
func BlockTrafficByDomainsTicketHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.ClusterTrafficRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_block_traffic_by_domains_ticket_param_invalid")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	req.Token = token

	ticket := albapp.NewTicketApp(traceID)
	response, err := ticket.BlockByDomains(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// BlockTrafficByDomainsTicketCBHandler godoc
// @Summary block alb cluster traffic callback
// @Description block alb cluster traffic, label: ShopeePay
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param ips body string true "blocked node ips"
// @Param applicant_user body string true "applicant user id"
// @Param clusters body string true "blocked cluster names"
// @Param network_type body string true "blocked cluster network type"
// @Success 200 {object} albvo.ControlTrafficTaskResultsResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/block_by_domains/callback/ [POST]
//
//nolint:nolintlint,dupl
func BlockTrafficByDomainsTicketCBHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	var req albvo.ControlTrafficFormRequest
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("block_traffic_by_domains_parameter_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	traffic := albapp.NewTrafficApp(traceID)
	resp, err := traffic.BlockByDomains(&req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// OpenTrafficByDomainsTicketHandler godoc
// @Summary open alb cluster traffic
// @Description open alb cluster traffic; label: ShopeePay
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param rz body string true "opened rz"
// @Param network_type body string true "opened network type"
// @Success 200 {object} albvo.TicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/open_by_domain/ [POST]
//
//nolint:nolintlint,dupl
func OpenTrafficByDomainsTicketHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("token", token).Error("token_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.ClusterTrafficRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_open_cluster_traffic_ticket_param_invalid")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	req.Token = token

	ticket := albapp.NewTicketApp(traceID)

	response, err := ticket.OpenByDomains(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// OpenTrafficByDomainsTicketCBHandler godoc
// @Summary open alb cluster traffic by domains callback
// @Description open alb cluster traffic by domains; label: ShopeePay
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param ips body string true "opened node ips"
// @Success 200 {object} albvo.ControlTrafficTaskResultsResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/open_by_domains/callback/ [POST]
//
//nolint:nolintlint,dupl
func OpenTrafficByDomainsTicketCBHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	var req albvo.ControlTrafficFormRequest
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("open_traffic_parameter_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	traffic := albapp.NewTrafficApp(traceID)
	resp, err := traffic.OpenByDomains(&req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}
