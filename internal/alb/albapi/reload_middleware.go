package albapi

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// ALBReloadAuthMiddleware checks if the user is authorized for ALB reload operations
func ALBReloadAuthMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		traceID := core.TraceID(ctx)

		// Check if reload functionality is enabled
		if !configs.ALB.IsReloadEnabled() {
			log.Logger().WithField("trace_id", traceID).Error("alb_reload_functionality_disabled")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusServiceUnavailable, middlewares.ErrorServer, "ALB reload functionality is disabled"))
			ctx.Abort()
			return
		}

		// Get user information from context
		user, err := core.User(ctx)
		if err != nil {
			log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_get_user_from_context")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusUnauthorized, middlewares.ErrorHeader, "User authentication required"))
			ctx.Abort()
			return
		}

		// Check if user is mapped to a BU
		bu, found := configs.ALB.GetUserBU(user.Email)
		if !found {
			log.Logger().WithFields(log.Fields{
				"trace_id":   traceID,
				"user_email": user.Email,
			}).Error("user_not_authorized_for_alb_reload")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusForbidden, middlewares.ErrorPermissionDenied, 
				"User is not authorized for ALB reload operations"))
			ctx.Abort()
			return
		}

		// Store user BU in context for later use
		ctx.Set("user_bu", bu)
		ctx.Set("user_email", user.Email)

		log.Logger().WithFields(log.Fields{
			"trace_id":   traceID,
			"user_email": user.Email,
			"user_bu":    bu,
		}).Info("user_authorized_for_alb_reload")

		ctx.Next()
	}
}

// ALBClusterAccessMiddleware checks if the user can access the specified cluster
func ALBClusterAccessMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		traceID := core.TraceID(ctx)

		// Get user BU from context (set by ALBReloadAuthMiddleware)
		userBU, exists := ctx.Get("user_bu")
		if !exists {
			log.Logger().WithField("trace_id", traceID).Error("user_bu_not_found_in_context")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusInternalServerError, middlewares.ErrorServer, "User BU not found in context"))
			ctx.Abort()
			return
		}

		userEmail, exists := ctx.Get("user_email")
		if !exists {
			log.Logger().WithField("trace_id", traceID).Error("user_email_not_found_in_context")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusInternalServerError, middlewares.ErrorServer, "User email not found in context"))
			ctx.Abort()
			return
		}

		// Parse request to get cluster ID
		var req albvo.ReloadRequest
		if err := ctx.ShouldBindJSON(&req); err != nil {
			log.Logger().WithError(err).WithField("trace_id", traceID).Error("invalid_reload_request")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusBadRequest, middlewares.ErrorParams, "Invalid request format: "+err.Error()))
			ctx.Abort()
			return
		}

		// Additional validation
		if err := req.Validate(); err != nil {
			log.Logger().WithError(err).WithField("trace_id", traceID).Error("reload_request_validation_failed")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusBadRequest, middlewares.ErrorParams, "Request validation failed: "+err.Error()))
			ctx.Abort()
			return
		}

		// Check cluster access (for now, we'll allow access to all clusters for any BU)
		// In a real implementation, you would check if the cluster belongs to the user's BU
		clusterApp := albapp.NewClusterApp(traceID)
		clusterReq := &albvo.ClusterIDRequest{UUID: req.ClusterID}
		
		// Verify cluster exists and is accessible
		_, err := clusterApp.Node(ctx.Request.Context(), clusterReq)
		if err != nil {
			log.Logger().WithError(err).WithFields(log.Fields{
				"trace_id":   traceID,
				"cluster_id": req.ClusterID,
				"user_bu":    userBU,
			}).Error("cluster_not_accessible")
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusNotFound, middlewares.ErrorParams, "Cluster not found or not accessible"))
			ctx.Abort()
			return
		}

		// Store validated request in context
		ctx.Set("reload_request", req)

		log.Logger().WithFields(log.Fields{
			"trace_id":   traceID,
			"user_email": userEmail,
			"user_bu":    userBU,
			"cluster_id": req.ClusterID,
		}).Info("cluster_access_authorized")

		ctx.Next()
	}
}

// GetUserBUFromContext retrieves the user's BU from the gin context
func GetUserBUFromContext(ctx *gin.Context) (string, bool) {
	bu, exists := ctx.Get("user_bu")
	if !exists {
		return "", false
	}
	
	buStr, ok := bu.(string)
	return buStr, ok
}

// GetUserEmailFromContext retrieves the user's email from the gin context
func GetUserEmailFromContext(ctx *gin.Context) (string, bool) {
	email, exists := ctx.Get("user_email")
	if !exists {
		return "", false
	}
	
	emailStr, ok := email.(string)
	return emailStr, ok
}

// GetReloadRequestFromContext retrieves the validated reload request from the gin context
func GetReloadRequestFromContext(ctx *gin.Context) (*albvo.ReloadRequest, bool) {
	req, exists := ctx.Get("reload_request")
	if !exists {
		return nil, false
	}
	
	reloadReq, ok := req.(albvo.ReloadRequest)
	if !ok {
		return nil, false
	}
	
	return &reloadReq, true
}
