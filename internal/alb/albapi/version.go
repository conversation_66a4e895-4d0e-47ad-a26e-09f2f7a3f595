package albapi

import (
	"net/http"
	"runtime"

	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

// GetVersionHandler godoc
// @Summary get alb api version
// @Description get alb api version details
// @Tags ALBVersion
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Success 200 {object} map[string]string
// @Router /alb/api/version [GET]
func GetVersionHandler(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, map[string]string{
		"version":   consts.Ver.Version,
		"commit":    consts.Ver.CommitID,
		"built":     consts.Ver.Built,
		"goarch":    runtime.GOARCH,
		"goversion": runtime.Version(),
		"goos":      runtime.GOOS,
		"compiler":  runtime.Compiler,
	})
}
