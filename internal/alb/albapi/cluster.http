### cluster list
GET {{host}}/alb/v1/cluster/
Authorization: Bear<PERSON> {{auth_token}}

### cluster sync
PUT {{host}}/alb/v1/cluster/sync/
Authorization: Bearer {{auth_token}}

### cluster sync alb.opsplatform.sg2.test
PUT {{host}}/alb/v1/cluster/uuid/60ff0efb-b9f2-5a01-88fe-b78279a4db94/sync/
Authorization: Bearer {{auth_token}}

### alb.opsplatform.sg2.test node *************
PUT {{host}}/alb/v1/cluster/uuid/aef45efc-24d8-5745-b736-f0d2f2ca2525/sync/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "*************"
    ]
}

### alb.ops1.live node 	************** legacy
GET {{host}}/alb/v1/cluster/uuid/4d061e09-559d-5c03-960e-41d478969b54/node/nlb/listener/?ip=**************
Authorization: Bearer {{auth_token}}

### alb.stdlan1.sg11.live node **************
GET {{host}}/alb/v1/cluster/uuid/c2bc2357-18e1-54cc-9ae1-375afda3e1f5/node/nlb/listener/?ip=**************
Authorization: Bearer {{auth_token}}

### cluster node list alb.opsplatform.sg2.test
GET {{host}}/alb/v1/cluster/uuid/60ff0efb-b9f2-5a01-88fe-b78279a4db94/node/
Authorization: Bearer {{auth_token}}

### cluster node precheck
POST {{host}}/alb/v1/cluster/uuid/60ff0efb-b9f2-5a01-88fe-b78279a4db94/node/precheck/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "ips": [
    "**************"
  ]
}

### cluster config
GET {{host}}/alb/v1/cluster/uuid/a9364968-5805-5219-ab07-5e4fd1fe5d48/config/
Authorization: Bearer {{auth_token}}
