package albapi

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

// GetNodeListHandler godoc
// @Summary fetch node list
// @Description fetch node list with details, like state, tasks etc
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ips query string true "alb nodes' ip address list"
// @Success 200 {object} albvo.NodeInfosResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/ [GET]
//
//nolint:nolintlint,dupl
func GetNodeListHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindQuery(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("node_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.List(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// GetNodeSpecHandler godoc
// @Summary fetch node k8s runtime spec
// @Description fetch node runtime state
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ips query string true "alb nodes' ip address list"
// @Success 200 {object} albvo.NodeSpecResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/spec/ [GET]
//
//nolint:nolintlint,dupl
func GetNodeSpecHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindQuery(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("check_alb_node_state_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.Spec(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// UpdateNodeSpecHandler godoc
// @Summary update node k8s runtime spec
// @Description update node runtime spec
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body string true "alb nodes' ip address list"
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/spec/ [PUT]
//
//nolint:nolintlint,dupl
func UpdateNodeSpecHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("check_alb_node_state_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.UpdateSpec(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// GetNodeEventsHandler godoc
// @Summary fetch node events
// @Description fetch node events by paging
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ip query string true "alb node's ip address"
// @Param page query int true "page number"
// @Param limit query int true "expected how many records"
// @Success 200 {object} albvo.NodeEventResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/event/ [GET]
//
//nolint:nolintlint,dupl
func GetNodeEventsHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodePagerRequest
	if err = ctx.ShouldBindQuery(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("alb_node_event_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.Events(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// GetNodeTagVariableHandler godoc
// @Summary fetch node tag variables
// @Description fetch node tag variables
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ips query string true "alb nodes' ip address list"
// @Success 200 {object} albvo.NodeTagVariableResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/variable/ [GET]
//
//nolint:nolintlint,dupl
func GetNodeTagVariableHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindQuery(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("alb_node_tag_var_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.TagVariables(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// StateNodeListHandler godoc
// @Summary fetch nodes by state
// @Description fetch nodes by state
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Success 200 {object} sgwvo.StateNodesResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/state/{state}/ [GET]
func StateNodeListHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	req := sgwvo.StateNodesRequest{}

	if err = ctx.ShouldBindUri(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)
	resp, err := node.GetStateNodes(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// UpdateNodeMAHandler godoc
// @Summary set node maintenance ticket
// @Description set node maintenance ticket
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/ma/ [PATCH]
func UpdateNodeMAHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	req := sgwvo.UpdateNodesRequest{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.SetNodeMATicket(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ExpireNodeHandler godoc
// @Summary clean-up expired nodes
// @Description delete those expired nodes under Initialised or Running state
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/expire/ [PATCH]
func ExpireNodeHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	node := albapp.NewNodeApp(traceID)
	resp, err := node.ExpireNodes(ctx.Request.Context())
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}
