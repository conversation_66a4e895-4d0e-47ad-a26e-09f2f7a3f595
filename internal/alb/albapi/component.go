package albapi

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// ListComponentHandler godoc
// @Summary get alb component list
// @Description get alb component list
// @Tags ALBComponent
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Success 200 {object} albvo.ComponentListResponse
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/component/ [GET]
func ListComponentHandler(ctx *gin.Context) {
	traceID := core.TraceID(ctx)
	node := albapp.NewNodeApp(traceID)

	resp := node.Components()

	middlewares.SetResp(ctx, resp)
}

// ListComponentVersionHandler godoc
// @Summary fetch alb component versions
// @Description fetch alb related components' version list
// @Tags ALBComponent
// @Accept json
// @Produce json
// @Param name body string true "component name"
// @Param env body string true "environment,options: test, staging, stable, live"
// @Success 200 {object} albvo.ComponentVersionResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/component/version/ [GET]
func ListComponentVersionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.ComponentRequest
	if err = ctx.ShouldBindQuery(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	response, err := node.Versions(req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// GetNodeComponentHandler godoc
// @Summary fetch node components' version
// @Description fetch node's installed components via toc
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ip query string true "node ip address"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/component/ [GET]
//
//nolint:nolintlint,dupl
func GetNodeComponentHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeIPRequest
	if err = ctx.ShouldBindQuery(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.ListComponents(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// UpdateNodeComponentHandler godoc
// @Summary update node components' version
// @Description fetch node's installed components via toc then update into k8s runtime
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ip query string true "node ip address"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/component/ [PUT]
//
//nolint:nolintlint,dupl
func UpdateNodeComponentHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeIPRequest
	if err = ctx.ShouldBindQuery(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.UpdateComponents(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// UpdateNodeComponentVersionHandler godoc
// @Summary update node component's version
// @Description update specified version into k8s runtime
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ip query string true "node ip address"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/component/version/ [PUT]
//
//nolint:nolintlint,dupl
func UpdateNodeComponentVersionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeProvisionComponentRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.UpdateComponent(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}
