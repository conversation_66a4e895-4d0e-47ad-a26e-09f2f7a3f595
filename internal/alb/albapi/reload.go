package albapi

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// ReloadNginxHandler godoc
// @Summary Reload nginx on ALB cluster nodes
// @Description Triggers nginx reload operation using 'systemctl reload nginx' on ALB cluster nodes belonging to the requesting user's Business Unit (BU). Users must be authenticated and mapped to a BU. Rate limiting applies: 1 reload per BU every 5 minutes.
// @Tags ALBReload
// @Accept json
// @Produce json
// @Security BearerToken
// @Param request body albvo.ReloadRequest true "Reload request containing cluster ID and optional reason"
// @Success 200 {object} albvo.ReloadResponse "Successful reload operation"
// @Failure 400 {object} middlewares.Error "Invalid request format or validation failed"
// @Failure 401 {object} middlewares.Error "User authentication required"
// @Failure 403 {object} middlewares.Error "User not authorized for ALB reload operations or not mapped to a BU"
// @Failure 404 {object} middlewares.Error "Cluster not found or not accessible"
// @Failure 429 {object} middlewares.Error "Rate limit exceeded for the user's BU"
// @Failure 500 {object} middlewares.Error "Internal server error or reload operation failed"
// @Failure 503 {object} middlewares.Error "ALB reload functionality is disabled"
// @Router /alb/v1/reload [POST]
func ReloadNginxHandler(ctx *gin.Context) {
	traceID := core.TraceID(ctx)

	// Get user information from context (set by middleware)
	userEmail, ok := GetUserEmailFromContext(ctx)
	if !ok {
		log.Logger().WithField("trace_id", traceID).Error("user_email_not_found_in_context")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusInternalServerError, middlewares.ErrorServer, "User email not found in context"))
		return
	}

	// Get validated request from context (set by middleware)
	req, ok := GetReloadRequestFromContext(ctx)
	if !ok {
		log.Logger().WithField("trace_id", traceID).Error("reload_request_not_found_in_context")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusInternalServerError, middlewares.ErrorServer, "Reload request not found in context"))
		return
	}

	// Set requesting user if not provided
	if req.RequestedBy == "" {
		req.RequestedBy = userEmail
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":   traceID,
		"user_email": userEmail,
		"cluster_id": req.ClusterID,
		"reason":     req.Reason,
	}).Info("received_alb_reload_request")

	// Create reload service
	reloadService, err := albapp.NewReloadService(traceID)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_create_reload_service")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))
		return
	}
	defer func() {
		if closeErr := reloadService.Close(); closeErr != nil {
			log.Logger().WithError(closeErr).WithField("trace_id", traceID).Error("failed_to_close_reload_service")
		}
	}()

	// Process reload request
	response, err := reloadService.ProcessReloadRequest(ctx.Request.Context(), req, userEmail)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_process_reload_request")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))
		return
	}

	// Set response with appropriate status code
	ctx.JSON(response.Code, response)
}

// GetReloadStatusHandler godoc
// @Summary Get reload operation status
// @Description Get the status of a reload operation by request ID. Currently returns a placeholder response indicating that status tracking is not yet implemented. Check audit logs for request details.
// @Tags ALBReload
// @Accept json
// @Produce json
// @Security BearerToken
// @Param request_id path string true "Request ID (UUID format)"
// @Success 200 {object} core.BaseResponse "Status information (placeholder)"
// @Failure 400 {object} middlewares.Error "Invalid request ID format"
// @Failure 401 {object} middlewares.Error "User authentication required"
// @Failure 403 {object} middlewares.Error "User not authorized for ALB reload operations"
// @Failure 404 {object} middlewares.Error "Request ID not found"
// @Failure 500 {object} middlewares.Error "Internal server error"
// @Failure 503 {object} middlewares.Error "ALB reload functionality is disabled"
// @Router /alb/v1/reload/status/{request_id} [GET]
func GetReloadStatusHandler(ctx *gin.Context) {
	traceID := core.TraceID(ctx)
	requestID := ctx.Param("request_id")

	if requestID == "" {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, "Request ID is required"))
		return
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":   traceID,
		"request_id": requestID,
	}).Info("get_reload_status_request")

	// For now, return a placeholder response
	// In a full implementation, this would query a database or cache for the request status
	response := &core.BaseResponse{}
	response.Success()
	response.Message = "Status tracking not yet implemented. Check audit logs for request details."

	middlewares.SetResp(ctx, response)
}

// ListReloadHistoryHandler godoc
// @Summary List reload operation history
// @Description List recent reload operations for the requesting user's Business Unit (BU). Currently returns a placeholder response indicating that history listing is not yet implemented. Check audit log files for historical data.
// @Tags ALBReload
// @Accept json
// @Produce json
// @Security BearerToken
// @Param limit query int false "Number of records to return (1-100)" default(10) minimum(1) maximum(100)
// @Param offset query int false "Number of records to skip" default(0) minimum(0)
// @Success 200 {object} core.BaseResponse "History information (placeholder)"
// @Failure 400 {object} middlewares.Error "Invalid query parameters"
// @Failure 401 {object} middlewares.Error "User authentication required"
// @Failure 403 {object} middlewares.Error "User not authorized for ALB reload operations"
// @Failure 500 {object} middlewares.Error "Internal server error"
// @Failure 503 {object} middlewares.Error "ALB reload functionality is disabled"
// @Router /alb/v1/reload/history [GET]
func ListReloadHistoryHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	// Get user information from context
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_get_user_from_context")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, "User authentication required"))
		return
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":   traceID,
		"user_email": user.Email,
	}).Info("list_reload_history_request")

	// For now, return a placeholder response
	// In a full implementation, this would query audit logs or a database
	response := &core.BaseResponse{}
	response.Success()
	response.Message = "History listing not yet implemented. Check audit log files for historical data."

	middlewares.SetResp(ctx, response)
}

// GetReloadConfigHandler godoc
// @Summary Get reload configuration
// @Description Get the current reload configuration including rate limits and user permissions. Returns configuration information relevant to the authenticated user, including their assigned Business Unit (BU) if any.
// @Tags ALBReload
// @Accept json
// @Produce json
// @Security BearerToken
// @Success 200 {object} object{code=int,message=string,data=object{enabled=bool,rate_limit_window=string,max_requests=int,user_bu=string,audit_log_enabled=bool}} "Reload configuration information"
// @Failure 401 {object} middlewares.Error "User authentication required"
// @Failure 403 {object} middlewares.Error "User not authorized for ALB reload operations"
// @Failure 500 {object} middlewares.Error "Internal server error"
// @Failure 503 {object} middlewares.Error "ALB reload functionality is disabled"
// @Router /alb/v1/reload/config [GET]
func GetReloadConfigHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	// Get user information from context
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_get_user_from_context")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, "User authentication required"))
		return
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":   traceID,
		"user_email": user.Email,
	}).Info("get_reload_config_request")

	// Create a response with basic configuration info (without sensitive data)
	type ConfigResponse struct {
		core.BaseResponse
		Data struct {
			Enabled         bool   `json:"enabled"`
			RateLimitWindow string `json:"rate_limit_window"`
			MaxRequests     int    `json:"max_requests"`
			UserBU          string `json:"user_bu,omitempty"`
			AuditLogEnabled bool   `json:"audit_log_enabled"`
		} `json:"data"`
	}

	response := &ConfigResponse{}
	response.Success()

	// Get user's BU if they have one
	userBU, hasBU := albapp.GetUserBUFromConfig(user.Email)

	response.Data.Enabled = albapp.IsReloadEnabledFromConfig()
	response.Data.RateLimitWindow = albapp.GetRateLimitWindowFromConfig().String()
	response.Data.MaxRequests = albapp.GetMaxRequestsFromConfig()
	response.Data.AuditLogEnabled = albapp.IsAuditLogEnabledFromConfig()

	if hasBU {
		response.Data.UserBU = userBU
	}

	middlewares.SetResp(ctx, response)
}
