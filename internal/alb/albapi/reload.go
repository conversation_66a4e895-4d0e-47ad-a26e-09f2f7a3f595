package albapi

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// ReloadNginxHandler godoc
// @Summary Reload nginx on ALB cluster nodes
// @Description Triggers nginx reload operation on ALB cluster nodes belonging to the requesting user's BU
// @Tags ALBReload
// @Accept json
// @Produce json
// @Param request body albvo.ReloadRequest true "Reload request"
// @Success 200 {object} albvo.ReloadResponse
// @Failure 400 {object} middlewares.Error
// @Failure 401 {object} middlewares.Error
// @Failure 403 {object} middlewares.Error
// @Failure 429 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/reload [POST]
func ReloadNginxHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	// Get user information from context
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_get_user_from_context")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, "User authentication required"))
		return
	}

	// Parse request body
	var req albvo.ReloadRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("invalid_reload_request")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, "Invalid request format: "+err.Error()))
		return
	}

	// Additional validation
	if err = req.Validate(); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("reload_request_validation_failed")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, "Request validation failed: "+err.Error()))
		return
	}

	// Set requesting user if not provided
	if req.RequestedBy == "" {
		req.RequestedBy = user.Email
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":   traceID,
		"user_email": user.Email,
		"cluster_id": req.ClusterID,
		"reason":     req.Reason,
	}).Info("received_alb_reload_request")

	// Create reload service
	reloadService, err := albapp.NewReloadService(traceID)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_create_reload_service")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))
		return
	}
	defer func() {
		if closeErr := reloadService.Close(); closeErr != nil {
			log.Logger().WithError(closeErr).WithField("trace_id", traceID).Error("failed_to_close_reload_service")
		}
	}()

	// Process reload request
	response, err := reloadService.ProcessReloadRequest(ctx.Request.Context(), &req, user.Email)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_process_reload_request")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))
		return
	}

	// Set response with appropriate status code
	ctx.JSON(response.Code, response)
}

// GetReloadStatusHandler godoc
// @Summary Get reload operation status
// @Description Get the status of a reload operation by request ID (placeholder for future implementation)
// @Tags ALBReload
// @Accept json
// @Produce json
// @Param request_id path string true "Request ID"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 404 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/reload/status/{request_id} [GET]
func GetReloadStatusHandler(ctx *gin.Context) {
	traceID := core.TraceID(ctx)
	requestID := ctx.Param("request_id")

	if requestID == "" {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, "Request ID is required"))
		return
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":   traceID,
		"request_id": requestID,
	}).Info("get_reload_status_request")

	// For now, return a placeholder response
	// In a full implementation, this would query a database or cache for the request status
	response := &core.BaseResponse{}
	response.Success()
	response.Message = "Status tracking not yet implemented. Check audit logs for request details."

	middlewares.SetResp(ctx, response)
}

// ListReloadHistoryHandler godoc
// @Summary List reload operation history
// @Description List recent reload operations for the requesting user's BU (placeholder for future implementation)
// @Tags ALBReload
// @Accept json
// @Produce json
// @Param limit query int false "Number of records to return" default(10)
// @Param offset query int false "Number of records to skip" default(0)
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 401 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/reload/history [GET]
func ListReloadHistoryHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	// Get user information from context
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_get_user_from_context")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, "User authentication required"))
		return
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":   traceID,
		"user_email": user.Email,
	}).Info("list_reload_history_request")

	// For now, return a placeholder response
	// In a full implementation, this would query audit logs or a database
	response := &core.BaseResponse{}
	response.Success()
	response.Message = "History listing not yet implemented. Check audit log files for historical data."

	middlewares.SetResp(ctx, response)
}

// GetReloadConfigHandler godoc
// @Summary Get reload configuration
// @Description Get the current reload configuration including rate limits and user permissions
// @Tags ALBReload
// @Accept json
// @Produce json
// @Success 200 {object} core.BaseResponse
// @Failure 401 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/reload/config [GET]
func GetReloadConfigHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	// Get user information from context
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("failed_to_get_user_from_context")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, "User authentication required"))
		return
	}

	log.Logger().WithFields(log.Fields{
		"trace_id":   traceID,
		"user_email": user.Email,
	}).Info("get_reload_config_request")

	// Create a response with basic configuration info (without sensitive data)
	type ConfigResponse struct {
		core.BaseResponse
		Data struct {
			Enabled         bool   `json:"enabled"`
			RateLimitWindow string `json:"rate_limit_window"`
			MaxRequests     int    `json:"max_requests"`
			UserBU          string `json:"user_bu,omitempty"`
			AuditLogEnabled bool   `json:"audit_log_enabled"`
		} `json:"data"`
	}

	response := &ConfigResponse{}
	response.Success()

	// Get user's BU if they have one
	userBU, hasBU := albapp.GetUserBUFromConfig(user.Email)

	response.Data.Enabled = albapp.IsReloadEnabledFromConfig()
	response.Data.RateLimitWindow = albapp.GetRateLimitWindowFromConfig().String()
	response.Data.MaxRequests = albapp.GetMaxRequestsFromConfig()
	response.Data.AuditLogEnabled = albapp.IsAuditLogEnabledFromConfig()

	if hasBU {
		response.Data.UserBU = userBU
	}

	middlewares.SetResp(ctx, response)
}
