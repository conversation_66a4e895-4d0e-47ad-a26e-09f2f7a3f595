### node infos
GET {{host}}/alb/v1/node/?ips=**************&ips=*************
Authorization: Bearer {{auth_token}}
Content-Type: application/x-www-form-urlencoded

### node spec
GET {{host}}/alb/v1/node/spec/?ips=*************
Authorization: Bearer {{auth_token}}

### alb.opsplatform.sg2.test
GET {{host}}/alb/v1/node/spec/?ips=**************
Authorization: Bearer {{auth_token}}

### node provision config ***********
GET {{host}}/alb/v1/node/provision/?ips=***********
Authorization: Bearer {{auth_token}}

### fetch node component upgraded provision config
GET {{host}}/alb/v1/node/provision/upgrade/?ip=**************&component=nginx-shopee&version=1.21.3-1.5.4
Authorization: Bearer {{auth_token}}

### node provision state
GET {{host}}/alb/v1/node/state/?ip=**************
Authorization: Bearer {{auth_token}}

### node ************** delete provision templates
DELETE {{host}}/alb/v1/node/provision/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

[
    {
        "host_ip": "**************",
        "templates": [
            {
                "path": "/etc/hostname",
                "permission": 644
            },
            {
                "path": "/opt/trimlog/logrotate.sh",
                "permission": 755
            },
            {
                "path": "/etc/cron.d/logrotate",
                "permission": 644
            },
            {
                "path": "/etc/cron.d/clear-docker",
                "permission": 644
            },
            {
                "path": "/etc/systemd/.system.conf",
                "permission": 644
            },
            {
                "path": "/etc/sysctl.d/90-disable-rp_filter.conf",
                "permission": 644
            }
        ]
    }
]

### node delete provision component jq,driver-manager,linux-tools,linux-ethernet
DELETE {{host}}/alb/v1/node/provision/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

[
    {
        "host_ip": "***********",
        "components": [
            {
                "name": "bird-shopee",
                "type": "apt"
            },
            {
                "name": "bird_exporter",
                "type": "script"
            }
        ]
    }
]

### node *********** purge provision configs
DELETE {{host}}/alb/v1/node/provision/purge/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "***********"
    ]
}


### node *********** purge provision containers
DELETE {{host}}/alb/v1/node/provision/purge/container/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "***********"
    ]
}

### node ************** delete provision component
DELETE {{host}}/alb/v1/node/provision/component/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "**************"
    ],
    "components": [
        "pyOpenSSL"
    ]
}

### node ************** delete provision templates
DELETE {{host}}/alb/v1/node/provision/template/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "**************"
    ],
    "paths": [
        "/etc/systemd/.system.conf",
        "/etc/sysctl.d/90-disable-rp_filter.conf"
    ]
}

### node ************** delete provision templates
DELETE {{host}}/alb/v1/node/provision/template/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "**************"
    ],
    "paths": [
        "/opt/trimlog/logrotate.sh",
        "/etc/cron.d/logrotate",
        "/etc/cron.d/clear-docker",
        "/etc/hostname",
        "/etc/systemd/.system.conf",
        "/etc/sysctl.d/90-disable-rp_filter.conf"
    ]
}

### node provision spec
GET {{host}}/alb/v1/node/spec/?ips=*************
Authorization: Bearer {{auth_token}}

### node spec update
PUT {{host}}/alb/v1/node/spec/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "**************"
    ]
}

### node toc tag variables
GET {{host}}/alb/v1/node/variable/?ips=**************
Authorization: Bearer {{auth_token}}

### node component version sync
PUT {{host}}/alb/v1/node/component/?ip=*************
Authorization: Bearer {{auth_token}}

### node alb.stdlan1.sg11.live ************** component version sync
PUT {{host}}/alb/v1/node/component/?ip=**************
Authorization: Bearer {{auth_token}}

### node component version list
GET {{host}}/alb/v1/node/component/?ip=**************
Authorization: Bearer {{auth_token}}


### node ************** component version update
PUT {{host}}/alb/v1/node/component/version/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ip": "**************",
    "version": "v0.3.8",
    "component": "alb-waf"
}

### node alb.stdlan1.sg11.live ************** alb-metrics version update
PUT {{host}}/alb/v1/node/component/version/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ip": "**************",
    "version": "v1.1.1",
    "component": "alb-metrics"
}

### node ************** update provision state
PUT {{host}}/alb/v1/node/state/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ip": "**************",
    "state": "provision",
    "reason": "reprovision with fixing Nic"
}

### node *********** update MA state
PUT {{host}}/alb/v1/node/state/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ip": "***********",
    "state": "MA",
    "reason": "maintain"
}

### node ************** update Sunset state
PUT {{host}}/alb/v1/node/state/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ip": "**************",
    "state": "Sunset",
    "reason": "fix stuck"
}

### node ************** upgrade alb-metrics
PUT {{host}}/alb/v1/node/provision/upgrade/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "**************"
    ],
    "component": "alb-metrics",
    "version": "v1.1.3",
    "ticket_id": 462979
}

### node ************** rollback alb-metrics
PUT {{host}}/alb/v1/node/provision/rollback/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "**************"
    ],
    "component": "alb-metrics",
    "version": "v1.1.2",
    "ticket_id": 462979
}

### node ************** abort
PUT {{host}}/alb/v1/node/provision/abort/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "**************"
    ]
}

### node ************** recover
PUT {{host}}/alb/v1/node/provision/recover/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "**************"
    ]
}
