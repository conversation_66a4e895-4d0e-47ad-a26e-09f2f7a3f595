package albapi

import (
	"net/http"
	"strings"

	"github.com/asaskevich/govalidator"
	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// ClusterListHandler godoc
// @Summary fetch cluster lists
// @Description fetch cluster details from alb-cluster mgmt
// @Tags ALBCluster
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Success 200 {object} albvo.ClusterListResponse
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/cluster/ [GET]
func ClusterListHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	cluster := albapp.NewClusterApp(traceID)
	resp, err := cluster.List(ctx.Request.Context())
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ClusterNodeListHandler godoc
// @Summary fetch cluster details, includes node lists
// @Description fetch cluster node list by cluster uuid
// @Tags ALBCluster
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param uuid path string true "cluster uuid"
// @Success 200 {object} albvo.NodeInfosResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/cluster/uuid/{uuid}/node/ [GET]
func ClusterNodeListHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	req := albvo.ClusterIDRequest{}
	if err = ctx.ShouldBindUri(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	cluster := albapp.NewClusterApp(traceID)
	resp, err := cluster.Node(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ClusterConfigHandler godoc
// @Summary fetch cluster config
// @Description fetch cluster configuration store in azmeta
// @Tags ALBCluster
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param uuid path string true "cluster uuid"
// @Success 200 {object} sgwvo.ALBClusterConfigMeta
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/cluster/uuid/{uuid}/config/ [GET]
func ClusterConfigHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	req := albvo.ClusterIDRequest{}
	if err = ctx.ShouldBindUri(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	cluster := albapp.NewClusterApp(traceID)
	resp, err := cluster.Config(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ClusterMetaSyncHandler godoc
// @Summary fresh cluster config from tag vars
// @Description fresh cluster configuration into azmeta
// @Tags ALBCluster
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param uuid path string true "cluster uuid"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/cluster/uuid/{uuid}/meta/sync/ [PUT]
func ClusterMetaSyncHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	req := albvo.ClusterIDRequest{}
	if err = ctx.ShouldBindUri(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	cluster := albapp.NewClusterApp(traceID)
	resp, err := cluster.SyncConfigFromTagVars(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ClusterSyncHandler godoc
// @Summary sync cluster's nodes into k8s runtime
// @Description sync cluster node list by cluster uuid, only work at managed trigger
// @Tags ALBCluster
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param uuid path string true "cluster uuid"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/cluster/sync/ [PUT]
func ClusterSyncHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	cluster := albapp.NewClusterApp(traceID)
	resp, err := cluster.Sync(ctx.Request.Context())
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ClusterSyncByUUIDHandler godoc
// @Summary sync cluster's nodes into k8s runtime
// @Description sync cluster node list by cluster uuid, only work at managed trigger
// @Tags ALBCluster
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param uuid path string true "cluster uuid"
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/cluster/uuid/{uuid}/sync/ [PUT]
func ClusterSyncByUUIDHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.ClusterSyncRequest
	if err = ctx.ShouldBindUri(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("sync_alb_cluster_invalid_request")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if err = ctx.ShouldBindJSON(&req); err != nil {
		if !strings.EqualFold(err.Error(), "EOF") {
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

			return
		}
	}

	req.Token = token
	cluster := albapp.NewClusterApp(traceID)
	resp, err := cluster.SyncByUUID(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// checkClusterManaged return true when managed otherwise false
func checkClusterManaged(ctx *gin.Context, traceID, uuid string) bool {
	cluster := albapp.NewClusterApp(traceID)
	ok, err := cluster.Managed(ctx.Request.Context(), uuid)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusInternalServerError, middlewares.ErrorServer, err.Error()))

		return false
	} else if !ok {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusNotAcceptable, middlewares.ErrorPermissionDenied, "cluster unmanaged"))

		return false
	}

	return true
}

// ClusterManagedHandler godoc
func ClusterManagedHandler() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		traceID := core.TraceID(ctx)
		uuid := ctx.Param("uuid")
		if uuid == "" {
			uuid = ctx.Query("uuid")
		}

		if uuid == "" || !govalidator.IsUUID(uuid) {
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusBadRequest, middlewares.ErrorParams, "invalid uuid"))

			ctx.Abort()

			return
		}

		cluster := albapp.NewClusterApp(traceID)
		ok, err := cluster.Managed(ctx.Request.Context(), uuid)
		if err != nil {
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusInternalServerError, middlewares.ErrorServer, err.Error()))
			ctx.Abort()

			return
		}

		if !ok {
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusUnprocessableEntity, middlewares.ErrorPermissionDenied, "cluster unmanaged"))
			ctx.Abort()

			return
		}

		ctx.Next()
	}
}

// ClusterNodeNLBListenerListHandler godoc
// @Summary fetch cluster node's NLB listener list
// @Description fetch cluster node L4 listener by cluster uuid
// @Tags ALBCluster
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param uuid path string true "cluster uuid"
// @Param ip query string true "alb node's ip address"
// @Success 200 {object} albvo.ClusterNodeNLBListenerResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/cluster/uuid/{uuid}/node/nlb/listener/ [GET]
func ClusterNodeNLBListenerListHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	req := albvo.ClusterNodeIPRequest{}
	if err = ctx.ShouldBindQuery(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest,
			middlewares.ErrorParams, err.Error()))

		return
	}

	uuid := ctx.Param("uuid")

	cluster := albapp.NewClusterApp(traceID)
	resp, err := cluster.NodeNLBListeners(ctx.Request.Context(), uuid, req.IP)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ClusterNodePreCheckHandler godoc
// @Summary check cluster nodes' condition before online
// @Description check nodes' server state, iptables, nf_conntrack etc
// @Tags ALBCluster
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param uuid path string true "cluster uuid"
// @Param ips body []string true "nodes' ip address list"
// @Success 200 {object} albvo.ClusterNodePreCheckResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/cluster/uuid/{uuid}/node/precheck/ [POST]
func ClusterNodePreCheckHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.ClusterNodeIPsRequest
	uuid := ctx.Param("uuid")
	if err = ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}
	req.UUID = uuid

	cluster := albapp.NewClusterApp(traceID)
	resp, err := cluster.PreCheckNodes(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ClusterNodePreCheckOfflineHandler godoc
// @Summary check cluster nodes' condition before offline
// @Description check instances' advertise status etc
// @Tags ALBCluster
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param uuid path string true "cluster uuid"
// @Param ips body []string true "nodes' ip address list"
// @Success 200 {object} albvo.ClusterNodePreCheckResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/cluster/uuid/{uuid}/node/precheck_offline/ [POST]
func ClusterNodePreCheckOfflineHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.ClusterNodeIPsRequest
	uuid := ctx.Param("uuid")
	if err = ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}
	req.UUID = uuid

	cluster := albapp.NewClusterApp(traceID)
	resp, err := cluster.PreCheckOfflineNodes(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}
