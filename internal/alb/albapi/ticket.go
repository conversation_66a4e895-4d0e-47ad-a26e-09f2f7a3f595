package albapi

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"
	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwvo"
)

// AddNodeTicketHandler godoc
// @Summary create add node ticket
// @Description create a ticket to add nodes to alb cluster
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param ips body []string true "added nodes ip address"
// @Param type body string true "k8s resource type, option:ALB"
// @Param uuid body string true "alb cluster uuid"
// @Success 200 {object} albvo.ClusterNodeTicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/node/ [POST]
//
//nolint:nolintlint,dupl
func AddNodeTicketHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("token", token).Error("token_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": traceID,
		}).Error("user_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.ClusterNodeTicketRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_add_node_ticket_param_invalid")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if !checkClusterManaged(ctx, traceID, req.UUID) {
		return
	}

	req.Token = token
	req.ApplicantUser = user.Email

	ticket := albapp.NewTicketApp(traceID)
	response, err := ticket.Add(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// AddNodeTicketCBHandler godoc
// @Summary add nodes into alb cluster
// @Description add nodes into alb cluster by ticket info
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param form_data body  albvo.ClusterAddNodeCBRequest.ClusterAddNodeCBFormData true "added nodes and cluster info"
// @Param id body albvo.ClusterAddNodeCBRequest true "related ticket id"
// @Success 200 {object} albvo.ClusterNodeTicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/node/callback/ [POST]
//
//nolint:nolintlint,dupl
func AddNodeTicketCBHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.ClusterAddNodeCBRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("add_node_into_cluster_cb_param_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if !checkClusterManaged(ctx, traceID, req.FormData.UUID) {
		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.Add(ctx.Request.Context(), req.ToNodeRequest())
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// RetireNodeTicketHandler godoc
// @Summary create remove node ticket
// @Description create a ticket to remove nodes from alb cluster
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param ips body []string true "removed nodes ip address"
// @Param type body string true "k8s resource type, option:ALB"
// @Param uuid body string true "alb cluster uuid"
// @Success 200 {object} albvo.TicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/node/retire/ [POST]
//
//nolint:nolintlint,dupl
func RetireNodeTicketHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("token", token).Error("token_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": traceID,
		}).Error("user_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.ClusterNodeTicketRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_remove_node_ticket_param_invalid")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if !checkClusterManaged(ctx, traceID, req.UUID) {
		return
	}

	req.Token = token
	req.ApplicantUser = user.Email

	ticket := albapp.NewTicketApp(traceID)
	response, err := ticket.Retire(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// OnlineOfflineTicketHandler godoc
// @Summary create SWP ticket
// @Description create SWP ticket about ALB operation, online/offline
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param state body string false "ALB Node state, options:PreRunning PreOffline PreMA"
// @Param ips body []string true "related nodes ip address"
// @Param uuid body string true "cluster uuid, included in the form_data"
// @Param type body string false "ticket type, option:online offline ma"
// @Param reason body string true "ticket reason"
// @Success 200 {object} albvo.TicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/ [POST]
//
//nolint:nolintlint,dupl
func OnlineOfflineTicketHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("token", token).Error("token_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": traceID,
		}).Error("user_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))
	}

	var req albvo.TicketRequest
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_ticket_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if !checkClusterManaged(ctx, traceID, req.UUID) {
		return
	}

	if req.Type == "" {
		if err = req.ResetTypeByState(); err != nil {
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

			return
		}
	}

	req.Token = token
	req.ApplicantUser = user.Email

	ticket := albapp.NewTicketApp(traceID)
	response, err := ticket.OnlineOrOffline(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// AutoMAHandler godoc
// @Summary auto MA
// @Description auto MA
// @Tags ALBTicket
// @Accept json
// @Produce json
func AutoMAHandler(ctx *gin.Context) {
	traceID := core.TraceID(ctx)

	token, err := core.Token(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("token", token).Error("token_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": traceID,
		}).Error("user_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.AutoMARequest
	if err := ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("auto_ma_invalid_param")

		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	cluster := albapp.NewClusterApp(traceID)
	if err := cluster.AutoMAPreCheck(ctx.Request.Context(), req.IPs, req.UUID); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("auto_ma_invalid_param")

		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusInternalServerError, middlewares.ErrorServer, err.Error()))

		return
	}

	ticket := albapp.NewTicketApp(traceID)

	maReq := &albvo.TicketRequest{
		ApplicantUser: user.Email,
		Token:         token,
		Reason:        req.Reason,
		IPs:           req.IPs,
		Type:          consts.TicketMA,
		AutoSubmit:    true,
		UUID:          req.UUID,
	}

	resp, err := ticket.OnlineOrOffline(ctx.Request.Context(), maReq)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// OfflineHandler godoc
// @Summary check whether node can be offline
// @Tags ALBTicket
// @Description check whether node can be offline
// @Accept json
// @Produce json
// @Param ip body string true "ip"
// @Success 200 {object} albvo.TicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/offline/ [POST]
//
//nolint:nolintlint,dupl
func OfflineHandler(ctx *gin.Context) {
	traceID := core.TraceID(ctx)

	var req sgwvo.OfflineNodeRequest
	if err := ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("check_offline_invalid_param")

		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	ticket := albapp.NewTicketApp(traceID)
	resp, err := ticket.Offline(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// HotUpdateNodeTicketHandler godoc
// @Summary create hot update node ticket
// @Description create hot update node ticket
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param is_overall body bool false "cluster's all nodes"
// @Param uuid body string true "cluster uuid"
// @Param component body string true "component name"
// @param target_version body string true "update target version"
// @param rollback_version body string true "rollback version"
// @Param form_data body albvo.HotUpdateTicketFormData true "hot update ticket info"
// @Success 200 {object} albvo.TicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/node/config/ [POST]
//
//nolint:nolintlint,dupl
func HotUpdateNodeTicketHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		log.Logger().WithError(err).WithField("token", token).Error("token_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.HotUpdateTicketRequest

	req.FormData = &albvo.HotUpdateTicketFormData{Host: ctx.Request.Host}

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_hotupdate_ticket_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if !req.IsSupported() {
		err = fmt.Errorf("unsupported component %s", req.Component)
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_hotupdate_ticket_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if !checkClusterManaged(ctx, traceID, req.UUID) {
		return
	}

	req.Token = token

	// IsOverall all IPs at the cluster
	// IPs partial IPs by user
	if req.IsOverall && len(req.FormData.IPs) != 0 {
		err = fmt.Errorf("is_overall_and_ips_can_only_exist_one")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if req.IsOverall {
		// fetch nodes from cluster
		cluster := albapp.NewClusterApp(traceID)
		ips, err := cluster.NodeIPs(req.UUID)
		if err != nil {
			middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
				http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

			return
		}
		req.FormData.IPs = ips
	}

	if len(req.FormData.IPs) == 0 {
		err = fmt.Errorf("alb_worker_nodes_must_not_be_empty")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	req.FormData.SplitBatch()

	ticket := albapp.NewTicketApp(traceID)
	response, err := ticket.HotUpdate(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusBadRequest, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// HotUpdateNodeTicketCBHandler godoc
// @Summary hot update node ticket callback when gray stage or next
// @Description batch hot update node component version
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param baseRequest body albvo.HotUpdateNodeBaseRequest true "nodes base information"
// @Param operation_type body string true "operation type,one of update,delete,add"
// @Param target_config body v1alpha1.TocexHotUpdateConfig true "target config info"
// @Param rollback_config body v1alpha1.TocexHotUpdateConfig true "rollback config info"
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/node/config/callback/ [POST]
//
//nolint:nolintlint,dupl
func HotUpdateNodeTicketCBHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": traceID,
		}).Error("user_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))
	}

	var stage albvo.HotUpdateStageRequest
	if err = ctx.ShouldBindQuery(&stage); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("batch_hot_update_service_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	var req albvo.HotUpdateNodeRequest
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("batch_hot_update_service_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if strings.EqualFold(stage.Stage, "gray") {
		req.Nodes = req.GrayIPs
	} else {
		req.Nodes = req.OtherIPs
	}

	req.ApplicantUser = user.Email
	req.SetHotUpdateConfig()

	if len(req.HotUpdateConfig.Templates) == 0 && len(req.HotUpdateConfig.Components) == 0 {
		err = fmt.Errorf("toc_templates_and_toc_components_all_empty_at_the_same_time")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.HotUpdate(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// HotUpdateNodeTicketRollbackHandler godoc
// @Summary rollback node when gray stage rejected
// @Description batch rollback node component version
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param stage body string false "rollback stage"
// @Param business_type body string true "business type"
// @Param ips body string true "all node ip address"
// @Param version body string true "rollback config's version"
// @Param total_nodes body string true "total nodes number"
// @Param gray_ips body string true "gray rollback node ip address"
// @Param total_gray_nodes body string true "gray rollback nodes number"
// @Param total_other_nodes body string true "not rollback nodes number"
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/node/config/rollback/ [POST]
//
//nolint:nolintlint,dupl
func HotUpdateNodeTicketRollbackHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": traceID,
		}).Error("user_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))
	}

	var stage albvo.HotUpdateStageRequest
	if err = ctx.ShouldBindQuery(&stage); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("batch_hot_update_rollback_service_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	var req albvo.HotUpdateNodeBaseRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("batch_hot_update_rollback_service_error")

		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	req.ApplicantUser = user.Email
	if strings.EqualFold(stage.Stage, "gray") {
		req.Nodes = req.GrayIPs
	} else {
		req.Nodes = req.IPs
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.Rollback(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// HotUpdateNodeTicketResetHandler godoc
// @Summary reset node
// @Description set nodes HotUpdateConfig to nil
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param ips body string true "reset nodes ip address"
// @Param total_nodes body string true "number of reset nodes"
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/node/config/reset/ [POST]
//
//nolint:nolintlint,dupl
func HotUpdateNodeTicketResetHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": traceID,
		}).Error("user_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))
	}

	var req albvo.ResetNodeRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("hot_update_reset_service_error")

		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	req.ApplicantUser = user.Email
	resp, err := node.Reset(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// FreshClusterConfigTicketHandler godoc
// @Summary create fresh cluster config ticket
// @Description create a ticket to fresh cluster config
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param uuid body string true "cluster uuid"
// @Param config body sgwvo.L7ClusterConfig true "cluster config"
// @Success 200 {object} albvo.TicketResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/config/ [POST]
//
//nolint:nolintlint,dupl
func FreshClusterConfigTicketHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	token, err := core.Token(ctx)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": traceID,
		}).Error("user_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.ClusterConfigTicketRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("param_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	if !checkClusterManaged(ctx, traceID, req.UUID) {
		return
	}

	req.Token = token
	req.ApplicantUser = user.Email

	ticket := albapp.NewTicketApp(traceID)
	response, err := ticket.ClusterConfig(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}

// FreshClusterConfigTicketCBHandler godoc
// @Summary fresh cluster config ticket callback
// @Description ticket callback to fresh cluster config
// @Tags ALBTicket
// @Accept json
// @Produce json
// @Param uuid body string true "cluster uuid"
// @Param config body sgwvo.L7ClusterConfig true "cluster config"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/ticket/cluster/config/callback/ [POST]
//
//nolint:nolintlint,dupl
func FreshClusterConfigTicketCBHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	user, err := core.User(ctx)
	if err != nil {
		log.Logger().WithError(err).WithFields(log.Fields{
			"trace_id": traceID,
		}).Error("user_error")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusUnauthorized, middlewares.ErrorHeader, err.Error()))

		return
	}

	var req albvo.FreshClusterConfigRequest

	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	req.ApplicantUser = user.Email

	cluster := albapp.NewClusterApp(traceID)
	response, err := cluster.FreshConfig(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, response)
}
