package albapi

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// ListStateHandler godoc
// @Summary get alb state list
// @Description get alb state list
// @Tags ALBState
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Success 200 {object} albvo.GetStatesResponse
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/state/ [GET]
func ListStateHandler(ctx *gin.Context) {
	traceID := core.TraceID(ctx)
	node := albapp.NewNodeApp(traceID)

	resp := node.States()

	middlewares.SetResp(ctx, resp)
}

// ListStateTaskHandler godoc
// @Summary get alb state runner task list
// @Description get alb state runner task list
// @Tags ALBState
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Success 200 {object} albvo.GetStateTasksResponse
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/state/task/ [GET]
func ListStateTaskHandler(ctx *gin.Context) {
	traceID := core.TraceID(ctx)
	node := albapp.NewNodeApp(traceID)

	resp := node.StateTasks()

	middlewares.SetResp(ctx, resp)
}

// ListStateTaskResultHandler godoc
// @Summary get alb state runner task results
// @Description get alb state runner task results
// @Tags ALBState
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Success 200 {object} albvo.GetStateTasksResponse
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/state/task/result/ [GET]
func ListStateTaskResultHandler(ctx *gin.Context) {
	req := albvo.GetStateTaskResultsRequest{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	traceID := core.TraceID(ctx)
	node := albapp.NewNodeApp(traceID)

	resp, err := node.StateTaskResults(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusInternalServerError, middlewares.ErrorServer, err.Error()))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ReentrantNodeStateHandler godoc
// @Summary reentrant node state info
// @Description reentrant node state
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ip body string true "node ip address"
// @Param state body string true "node state,like spare,format etc."
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/state/ [PATCH]
//
//nolint:nolintlint,dupl
func ReentrantNodeStateHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	// Ensure validators are properly registered before binding
	if err := core.EnsureValidatorsRegistered(); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusInternalServerError, middlewares.ErrorInternal, "validator_registration_failed"))

		return
	}

	req := albvo.ReentrantNodeStateRequest{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.ReentrantState(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// GetNodeStateHandler godoc
// @Summary fetch node state info
// @Description fetch node state details
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ip query string true "node ip address"
// @Success 200 {object} albvo.GetNodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/state/ [GET]
//
//nolint:nolintlint,dupl
func GetNodeStateHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	req := albvo.NodeIPRequest{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.State(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// UpdateNodeStateHandler godoc
// @Summary update node state info
// @Description fetch node state details
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ip body string true "node ip address"
// @Param state body string true "node state,like spare,format etc."
// @Param reason body string true "state reason"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/state/ [PUT]
//
//nolint:nolintlint,dupl
func UpdateNodeStateHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	req := albvo.UpdateNodeStateRequest{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.UpdateState(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}
