### offline alb node
POST {{host}}/alb/v1/ticket/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "uuid": "fa4f3ea1-f562-52c8-a01d-2d8b04ab9911",
    "type": "offline",
    "state": "PreOffline",
    "ips": [
        "*************"
    ]
}

### ma alb node
POST {{host}}/alb/v1/ticket/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "uuid": "60ff0efb-b9f2-5a01-88fe-b78279a4db94",
    "type": "ma",
    "state": "PreMA",
    "ips": [
        "**************"
    ]
}


### hotupdate alb spcifiy nodes with grayscale
POST {{host}}/alb/v1/ticket/node/config/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "uuid": "60ff0efb-b9f2-5a01-88fe-b78279a4db94",
    "component": "nginx-shopee",
    "target_version": "1.21.3-1.5.4",
    "rollback_version": "1.21.3-1.5.3",
    "form_data": {
        "ips": [
            "**************"
        ],
        "operation_type": "update",
        "operation_description": "test",
        "gray_scale": 30
    }
}


### hotupdate delete scertms
POST {{host}}/alb/v1/ticket/node/config/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "uuid": "60ff0efb-b9f2-5a01-88fe-b78279a4db94",
    "component": "scertms",
    "target_version": "*********",
    "rollback_version": "*********",
    "form_data": {
        "ips": [
            "**************"
        ],
        "operation_type": "delete",
        "operation_description": "dev test",
        "gray_scale": 30
    }
}

### hotupdate alb overall nodes
POST {{host}}/alb/v1/ticket/node/config/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "is_overall": true,
    "uuid": "60ff0efb-b9f2-5a01-88fe-b78279a4db94",
    "form_data": {
        "operation_type": "update",
        "operation_description": "test",
        "gray_scale": 30
    }
}

### hotupdate nginx-lb
POST {{host}}/alb/v1/ticket/node/config/callback/?stage=gray
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "business_type": "alb",
    "ips": [
        "**************"
    ],
    "gray_ips": [
        "**************"
    ],
    "version": "96ad50c2-947b-4b00-9a05-46d2f858e016",
    "operation_type": "update",
    "target_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "harbor.test.shopeemobile.com/shopee/nginx-config-test-sg",
                "post_command": "",
                "pre_command": "#!/usr/bin/env bash\n\nif [ -d /usr/share/nginx/html ]; then\n    rm -rfv /usr/share/nginx/html\n    ln -sfnv /etc/nginx/html /usr/share/nginx/html\nfi\n\ncontainerID=$(docker ps -f \"name=mesos-nginx-lb\" -q)\nif [ -n \"${containerID}\" ]; then\n    docker stop \"${containerID}\"\n    docker rm \"${containerID}\"\nfi\n",
                "service_name": "mesos-nginx-lb",
                "type": "docker",
                "version": "v0.0.388",
                "script": "",
                "dependent_templates": [
                    "/etc/mesos/.zk",
                    "/etc/mesos/.ecpzk",
                    "/etc/sgw/.etcd",
                    "/etc/sgw/.env",
                    "/etc/mesos-lb/.idc",
                    "/etc/mesos-lb/.env"
                ],
                "dependent_components": [
                    "lxcfs-shopee",
                    "docker-shopee-dev",
                    "nginx-shopee"
                ],
                "service_options": [
                    "--restart=always",
                    "--privileged",
                    "--name=mesos-nginx-lb",
                    "--entrypoint=smb",
                    "--pid=host",
                    "--net=host",
                    "--volume=/usr/sbin/nginx:/usr/sbin/nginx",
                    "--volume=/var/run:/var/run",
                    "--volume=/usr/share/openresty:/usr/share/openresty",
                    "--volume=/etc/mesos-lb:/etc/mesos-lb",
                    "--volume=/etc/mesos:/etc/mesos",
                    "--volume=/etc/nginx:/etc/nginx",
                    "--volume=/etc/lualib:/etc/lualib",
                    "--volume=/etc/sgw:/etc/sgw",
                    "--volume=/etc/shopee-lb:/etc/shopee-lb",
                    "--volume=/etc/shopee-alb:/etc/shopee-alb",
                    "--volume=/var/log/nginx:/var/log/nginx",
                    "--volume=/data/nginx:/data/nginx",
                    "--volume=/data/shopee:/data/shopee",
                    "--volume=/var/lib:/var/lib",
                    "--volume=/usr/local/ssl/lib:/usr/local/ssl/lib",
                    "--volume=/usr/local/lib:/usr/local/lib",
                    "--volume=/usr/local/lib64:/usr/local/lib64",
                    "--env=SERVICE_NAME=nginx-config-test-sg",
                    "--env=MESOS_TASK_ID=nginx-config-test-sg.1",
                    "--env=MESOS_CONTAINER_NAME=nginx-config",
                    "--env=DEPLOYMENT=new",
                    "--env-file=/etc/sgw/env",
                    "--volume=/data/log/nginx-config-test-sg:/data/log/nginx-config-test-sg",
                    "--volume=/data/tmp/nginx-config-test-sg:/data/tmp"
                ],
                "service_args": [
                    "run",
                    "--env=test",
                    "--cid=sg",
                    "--idc=sg2"
                ],
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\nsleep 5 # wait for run.py launching\n\nif [ -a /etc/nginx/finished ]; then\n   echo \"run.py still running\" \u003e\u00262\n   exit 1\nfi\n\nnc -vz ************** 80 \u0026\u0026 nc -vz ************** 443\n",
                "err_msg": ""
            }
        ]
    },
    "rollback_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "harbor.test.shopeemobile.com/shopee/nginx-config-test-sg",
                "post_command": "",
                "pre_command": "#!/usr/bin/env bash\n\nif [ -d /usr/share/nginx/html ]; then\n    rm -rfv /usr/share/nginx/html\n    ln -sfnv /etc/nginx/html /usr/share/nginx/html\nfi\n\ncontainerID=$(docker ps -f \"name=mesos-nginx-lb\" -q)\nif [ -n \"${containerID}\" ]; then\n    docker stop \"${containerID}\"\n    docker rm \"${containerID}\"\nfi\n",
                "service_name": "mesos-nginx-lb",
                "type": "docker",
                "version": "v0.0.389",
                "script": "",
                "dependent_templates": [
                    "/etc/mesos/.zk",
                    "/etc/mesos/.ecpzk",
                    "/etc/sgw/.etcd",
                    "/etc/sgw/.env",
                    "/etc/mesos-lb/.idc",
                    "/etc/mesos-lb/.env"
                ],
                "dependent_components": [
                    "lxcfs-shopee",
                    "docker-shopee-dev",
                    "nginx-shopee"
                ],
                "service_options": [
                    "--restart=always",
                    "--privileged",
                    "--name=mesos-nginx-lb",
                    "--entrypoint=smb",
                    "--pid=host",
                    "--net=host",
                    "--volume=/usr/sbin/nginx:/usr/sbin/nginx",
                    "--volume=/var/run:/var/run",
                    "--volume=/usr/share/openresty:/usr/share/openresty",
                    "--volume=/etc/mesos-lb:/etc/mesos-lb",
                    "--volume=/etc/mesos:/etc/mesos",
                    "--volume=/etc/nginx:/etc/nginx",
                    "--volume=/etc/lualib:/etc/lualib",
                    "--volume=/etc/sgw:/etc/sgw",
                    "--volume=/etc/shopee-lb:/etc/shopee-lb",
                    "--volume=/etc/shopee-alb:/etc/shopee-alb",
                    "--volume=/var/log/nginx:/var/log/nginx",
                    "--volume=/data/nginx:/data/nginx",
                    "--volume=/data/shopee:/data/shopee",
                    "--volume=/var/lib:/var/lib",
                    "--volume=/usr/local/ssl/lib:/usr/local/ssl/lib",
                    "--volume=/usr/local/lib:/usr/local/lib",
                    "--volume=/usr/local/lib64:/usr/local/lib64",
                    "--env=SERVICE_NAME=nginx-config-test-sg",
                    "--env=MESOS_TASK_ID=nginx-config-test-sg.1",
                    "--env=MESOS_CONTAINER_NAME=nginx-config",
                    "--env=DEPLOYMENT=new",
                    "--env-file=/etc/sgw/env",
                    "--volume=/data/log/nginx-config-test-sg:/data/log/nginx-config-test-sg",
                    "--volume=/data/tmp/nginx-config-test-sg:/data/tmp"
                ],
                "service_args": [
                    "run",
                    "--env=test",
                    "--cid=sg",
                    "--idc=sg2"
                ],
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\nsleep 5 # wait for run.py launching\n\nif [ -a /etc/nginx/finished ]; then\n   echo \"run.py still running\" \u003e\u00262\n   exit 1\nfi\n\nnc -vz ************** 80 \u0026\u0026 nc -vz ************** 443\n",
                "err_msg": ""
            }
        ]
    }
}

### hotupdate alb-metrics
POST {{host}}/alb/v1/ticket/node/config/callback/?stage=gray
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "business_type": "alb",
    "ips": [
        "**************"
    ],
    "gray_ips": [
        "**************"
    ],
    "version": "de30026a-275c-4780-b4fd-91540372610f",
    "operation_type": "update",
    "target_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "harbor.test.shopeemobile.com/shopee/alb-metrics-test-sg",
                "post_command": "",
                "pre_command": "#!/usr/bin/env bash\n\ncontainerID=$(docker ps -f \"name=alb-metrics\" -q)\nif [ -n \"${containerID}\" ]; then\n    docker stop \"${containerID}\"\n    docker rm \"${containerID}\"\nfi\n",
                "service_name": "alb-metrics",
                "type": "docker",
                "version": "v1.1.3",
                "script": "",
                "dependent_templates": [
                    "/etc/mesos/.zk",
                    "/etc/mesos/.ecpzk",
                    "/etc/sgw/.etcd",
                    "/etc/sgw/.env",
                    "/etc/mesos-lb/.idc",
                    "/etc/mesos-lb/.env"
                ],
                "dependent_components": [
                    "cmds-exporter-shopee",
                    "harbor.test.shopeemobile.com/shopee/nginx-config-test-sg"
                ],
                "service_options": [
                    "--restart=always",
                    "--name=alb-metrics",
                    "--net=host",
                    "--cpus=2",
                    "--memory=6000M",
                    "--cap-add=SYS_PTRACE",
                    "--entrypoint=smb",
                    "--security-opt=seccomp:unconfined",
                    "--env=PORT=9116",
                    "--env=SERVICE_NAME=alb-metrics-test-sg",
                    "--env=MESOS_TASK_ID=alb-metrics-test-sg.1",
                    "--env=MESOS_CONTAINER_NAME=alb-metrics",
                    "--env-file=/etc/sgw/env",
                    "--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
                    "--volume=/etc/hosts:/etc/hosts:ro",
                    "--volume=/etc/mesos:/etc/mesos:ro",
                    "--volume=/etc/nginx/service_deps:/etc/nginx/service_deps",
                    "--volume=/etc/mesos-lb:/etc/mesos-lb",
                    "--volume=/etc/sgw:/etc/sgw",
                    "--volume=/data/log/alb-metrics-test-sg:/data/log/alb-metrics-test-sg",
                    "--volume=/data/tmp/alb-metrics-test-sg:/data/tmp"
                ],
                "service_args": [
                    "run",
                    "--env=test",
                    "--cid=sg",
                    "--idc=sg2"
                ],
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\ncurl -sS 127.0.0.1:9116/metrics -o /dev/null\n",
                "err_msg": ""
            }
        ]
    },
    "rollback_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "harbor.test.shopeemobile.com/shopee/alb-metrics-test-sg",
                "post_command": "",
                "pre_command": "#!/usr/bin/env bash\n\ncontainerID=$(docker ps -f \"name=alb-metrics\" -q)\nif [ -n \"${containerID}\" ]; then\n    docker stop \"${containerID}\"\n    docker rm \"${containerID}\"\nfi\n",
                "service_name": "alb-metrics",
                "type": "docker",
                "version": "v1.1.1",
                "script": "",
                "dependent_templates": [
                    "/etc/mesos/.zk",
                    "/etc/mesos/.ecpzk",
                    "/etc/sgw/.etcd",
                    "/etc/sgw/.env",
                    "/etc/mesos-lb/.idc",
                    "/etc/mesos-lb/.env"
                ],
                "dependent_components": [
                    "cmds-exporter-shopee",
                    "harbor.test.shopeemobile.com/shopee/nginx-config-test-sg"
                ],
                "service_options": [
                    "--restart=always",
                    "--name=alb-metrics",
                    "--net=host",
                    "--cpus=2",
                    "--memory=6000M",
                    "--cap-add=SYS_PTRACE",
                    "--entrypoint=smb",
                    "--security-opt=seccomp:unconfined",
                    "--env=PORT=9116",
                    "--env=SERVICE_NAME=alb-metrics-test-sg",
                    "--env=MESOS_TASK_ID=alb-metrics-test-sg.1",
                    "--env=MESOS_CONTAINER_NAME=alb-metrics",
                    "--env-file=/etc/sgw/env",
                    "--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
                    "--volume=/etc/hosts:/etc/hosts:ro",
                    "--volume=/etc/mesos:/etc/mesos:ro",
                    "--volume=/etc/nginx/service_deps:/etc/nginx/service_deps",
                    "--volume=/etc/mesos-lb:/etc/mesos-lb",
                    "--volume=/etc/sgw:/etc/sgw",
                    "--volume=/data/log/alb-metrics-test-sg:/data/log/alb-metrics-test-sg",
                    "--volume=/data/tmp/alb-metrics-test-sg:/data/tmp"
                ],
                "service_args": [
                    "run",
                    "--env=test",
                    "--cid=sg",
                    "--idc=sg2"
                ],
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\ncurl -sS 127.0.0.1:9116/metrics -o /dev/null\n",
                "err_msg": ""
            }
        ]
    }
}

### hotupdate alb-sd
POST {{host}}/alb/v1/ticket/node/config/callback/?stage=gray
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "business_type": "alb",
    "ips": [
        "**************"
    ],
    "gray_ips": [
        "**************"
    ],
    "version": "13e18de6-3c71-47df-ac0e-b2009a9d5041",
    "operation_type": "update",
    "target_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "harbor.test.shopeemobile.com/shopee/alb-sd-test-sg",
                "post_command": "",
                "pre_command": "#!/usr/bin/env bash\n\ncontainerID=$(docker ps -f \"name=alb-sd\" -q)\nif [ -n \"${containerID}\" ]; then\n    docker stop \"${containerID}\"\n    docker rm \"${containerID}\"\nfi\n",
                "service_name": "alb-sd",
                "type": "docker",
                "version": "v1.0.2",
                "script": "",
                "dependent_templates": [
                    "/etc/mesos/.zk",
                    "/etc/mesos/.ecpzk",
                    "/etc/sgw/.etcd",
                    "/etc/sgw/.env",
                    "/etc/mesos-lb/.idc",
                    "/etc/mesos-lb/.env"
                ],
                "dependent_components": [
                    "lxcfs-shopee",
                    "docker-shopee-dev",
                    "jq"
                ],
                "service_options": [
                    "--restart=always",
                    "--name=alb-sd",
                    "--net=host",
                    "--cpuset-cpus=53-61",
                    "--memory=6000M",
                    "--cap-add=SYS_PTRACE",
                    "--entrypoint=smb",
                    "--security-opt=seccomp:unconfined",
                    "--env=PORT=9118",
                    "--env=SERVICE_NAME=alb-sd-test-sg",
                    "--env=MESOS_TASK_ID=alb-sd-test-sg.1",
                    "--env=MESOS_CONTAINER_NAME=alb-sd",
                    "--env=NAMING_APPID=namingbva9xt4xw8",
                    "--env-file=/etc/sgw/env",
                    "--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
                    "--volume=/etc/hosts:/etc/hosts:ro",
                    "--volume=/etc/mesos:/etc/mesos:ro",
                    "--volume=/etc/sgw:/etc/sgw:ro",
                    "--volume=/etc/nginx/service_deps:/etc/nginx/service_deps",
                    "--volume=/etc/mesos-lb:/etc/mesos-lb",
                    "--volume=/data/log/alb-sd-test-sg:/data/log/alb-sd-test-sg",
                    "--volume=/data/tmp/alb-sd-test-sg:/data/tmp"
                ],
                "service_args": [
                    "run",
                    "--env=test",
                    "--cid=sg",
                    "--idc=sg2"
                ],
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\ncurl -sS 127.0.0.1:9118/ -o /dev/null\n",
                "err_msg": ""
            }
        ]
    },
    "rollback_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "harbor.test.shopeemobile.com/shopee/alb-sd-test-sg",
                "post_command": "",
                "pre_command": "#!/usr/bin/env bash\n\ncontainerID=$(docker ps -f \"name=alb-sd\" -q)\nif [ -n \"${containerID}\" ]; then\n    docker stop \"${containerID}\"\n    docker rm \"${containerID}\"\nfi\n",
                "service_name": "alb-sd",
                "type": "docker",
                "version": "v1.0.0",
                "script": "",
                "dependent_templates": [
                    "/etc/mesos/.zk",
                    "/etc/mesos/.ecpzk",
                    "/etc/sgw/.etcd",
                    "/etc/sgw/.env",
                    "/etc/mesos-lb/.idc",
                    "/etc/mesos-lb/.env"
                ],
                "dependent_components": [
                    "lxcfs-shopee",
                    "docker-shopee-dev",
                    "jq"
                ],
                "service_options": [
                    "--restart=always",
                    "--name=alb-sd",
                    "--net=host",
                    "--cpuset-cpus=53-61",
                    "--memory=6000M",
                    "--cap-add=SYS_PTRACE",
                    "--entrypoint=smb",
                    "--security-opt=seccomp:unconfined",
                    "--env=PORT=9118",
                    "--env=SERVICE_NAME=alb-sd-test-sg",
                    "--env=MESOS_TASK_ID=alb-sd-test-sg.1",
                    "--env=MESOS_CONTAINER_NAME=alb-sd",
                    "--env=NAMING_APPID=namingbva9xt4xw8",
                    "--env-file=/etc/sgw/env",
                    "--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
                    "--volume=/etc/hosts:/etc/hosts:ro",
                    "--volume=/etc/mesos:/etc/mesos:ro",
                    "--volume=/etc/sgw:/etc/sgw:ro",
                    "--volume=/etc/nginx/service_deps:/etc/nginx/service_deps",
                    "--volume=/etc/mesos-lb:/etc/mesos-lb",
                    "--volume=/data/log/alb-sd-test-sg:/data/log/alb-sd-test-sg",
                    "--volume=/data/tmp/alb-sd-test-sg:/data/tmp"
                ],
                "service_args": [
                    "run",
                    "--env=test",
                    "--cid=sg",
                    "--idc=sg2"
                ],
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\ncurl -sS 127.0.0.1:9118/ -o /dev/null\n",
                "err_msg": ""
            }
        ]
    }
}

### hotupdate alb-waf
POST {{host}}/alb/v1/ticket/node/config/callback/?stage=gray
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "business_type": "alb",
    "ips": [
        "**************"
    ],
    "gray_ips": [
        "**************"
    ],
    "version": "13e18de6-3c71-47df-ac0e-b2009a9d5041",
    "operation_type": "update",
    "target_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "harbor.test.shopeemobile.com/shopee/alb-waf-test-sg",
                "post_command": "",
                "pre_command": "#!/usr/bin/env bash\n\ncontainerID=$(docker ps -f \"name=alb-waf\" -q)\nif [ -n \"${containerID}\" ]; then\n    docker stop \"${containerID}\"\n    docker rm \"${containerID}\"\nfi\n",
                "service_name": "alb-waf",
                "type": "docker",
                "version": "v0.3.8_219",
                "script": "",
                "dependent_templates": [
                    "/etc/mesos/.zk",
                    "/etc/mesos/.ecpzk",
                    "/etc/sgw/.etcd",
                    "/etc/sgw/.env",
                    "/etc/mesos-lb/.idc",
                    "/etc/mesos-lb/.env"
                ],
                "dependent_components": [
                    "lxcfs-shopee",
                    "docker-shopee-dev",
                    "harbor.test.shopeemobile.com/shopee/nginx-config-test-sg"
                ],
                "service_options": [
                    "--restart=always",
                    "--name=alb-waf",
                    "--net=host",
                    "--cpuset-cpus=53-61",
                    "--cap-add=SYS_PTRACE",
                    "--entrypoint=smb",
                    "--security-opt=seccomp:unconfined",
                    "--env=PORT=8081",
                    "--env=SERVICE_NAME=alb-waf-test-sg",
                    "--env=MESOS_TASK_ID=alb-waf-test-sg.1",
                    "--env=MESOS_CONTAINER_NAME=alb-waf",
                    "--env-file=/etc/sgw/env",
                    "--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
                    "--volume=/etc/hosts:/etc/hosts:ro",
                    "--volume=/etc/mesos:/etc/mesos:ro",
                    "--volume=/etc/nginx/service_deps:/etc/nginx/service_deps",
                    "--volume=/etc/mesos-lb:/etc/mesos-lb",
                    "--volume=/data/log/alb-waf-test-sg:/data/log/alb-waf-test-sg",
                    "--volume=/data/tmp/alb-waf-test-sg:/data/tmp"
                ],
                "service_args": [
                    "run",
                    "--env=test",
                    "--cid=sg",
                    "--idc=sg2"
                ],
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\ncurl -sS 127.0.0.1:9117/dump -o /dev/null\n",
                "err_msg": ""
            }
        ]
    },
    "rollback_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "harbor.test.shopeemobile.com/shopee/alb-waf-test-sg",
                "post_command": "",
                "pre_command": "#!/usr/bin/env bash\n\ncontainerID=$(docker ps -f \"name=alb-waf\" -q)\nif [ -n \"${containerID}\" ]; then\n    docker stop \"${containerID}\"\n    docker rm \"${containerID}\"\nfi\n",
                "service_name": "alb-waf",
                "type": "docker",
                "version": "v0.3.8",
                "script": "",
                "dependent_templates": [
                    "/etc/mesos/.zk",
                    "/etc/mesos/.ecpzk",
                    "/etc/sgw/.etcd",
                    "/etc/sgw/.env",
                    "/etc/mesos-lb/.idc",
                    "/etc/mesos-lb/.env"
                ],
                "dependent_components": [
                    "lxcfs-shopee",
                    "docker-shopee-dev",
                    "harbor.test.shopeemobile.com/shopee/nginx-config-test-sg"
                ],
                "service_options": [
                    "--restart=always",
                    "--name=alb-waf",
                    "--net=host",
                    "--cpuset-cpus=53-61",
                    "--cap-add=SYS_PTRACE",
                    "--entrypoint=smb",
                    "--security-opt=seccomp:unconfined",
                    "--env=PORT=8081",
                    "--env=SERVICE_NAME=alb-waf-test-sg",
                    "--env=MESOS_TASK_ID=alb-waf-test-sg.1",
                    "--env=MESOS_CONTAINER_NAME=alb-waf",
                    "--env-file=/etc/sgw/env",
                    "--volume=/etc/resolv.conf:/etc/resolv.conf:ro",
                    "--volume=/etc/hosts:/etc/hosts:ro",
                    "--volume=/etc/mesos:/etc/mesos:ro",
                    "--volume=/etc/nginx/service_deps:/etc/nginx/service_deps",
                    "--volume=/etc/mesos-lb:/etc/mesos-lb",
                    "--volume=/data/log/alb-waf-test-sg:/data/log/alb-waf-test-sg",
                    "--volume=/data/tmp/alb-waf-test-sg:/data/tmp"
                ],
                "service_args": [
                    "run",
                    "--env=test",
                    "--cid=sg",
                    "--idc=sg2"
                ],
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\ncurl -sS 127.0.0.1:9117/dump -o /dev/null\n",
                "err_msg": ""
            }
        ]
    }
}

### hotupdate scertms
POST {{host}}/alb/v1/ticket/node/config/callback/?stage=gray
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "business_type": "alb",
    "ips": [
        "**************"
    ],
    "gray_ips": [
        "**************"
    ],
    "version": "9d315af2-bf26-4915-b71b-4cb234e6d444",
    "operation_type": "update",
    "target_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "scertms",
                "post_command": "install_scertmsd \u0026\u0026 install_certs",
                "pre_command": "",
                "service_name": "scertmsd",
                "type": "pip",
                "version": "0.0.143",
                "script": "",
                "dependent_templates": [
                    "/etc/scertms/.s3_configs.ini"
                ],
                "dependent_components": [
                    "nginx-shopee"
                ],
                "service_options": null,
                "service_args": null,
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\nprocess=$(ps aux | grep scertmsd | grep -v grep)\nif [ -z \"${process}\" ]; then\n    echo \"scertmsd not found\"\n    exit 2\nfi\n",
                "err_msg": ""
            }
        ]
    },
    "rollback_config": {
        "templates": null,
        "components": [
            {
                "is_service": true,
                "name": "scertms",
                "post_command": "install_scertmsd \u0026\u0026 install_certs",
                "pre_command": "",
                "service_name": "scertmsd",
                "type": "pip",
                "version": "0.0.141",
                "script": "",
                "dependent_templates": [
                    "/etc/scertms/.s3_configs.ini"
                ],
                "dependent_components": [
                    "nginx-shopee"
                ],
                "service_options": null,
                "service_args": null,
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\nprocess=$(ps aux | grep scertmsd | grep -v grep)\nif [ -z \"${process}\" ]; then\n    echo \"scertmsd not found\"\n    exit 2\nfi\n",
                "err_msg": ""
            }
        ]
    }
}

### hotupdate nginx
POST {{host}}/alb/v1/ticket/node/config/callback/?stage=gray
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "business_type": "alb",
  "ips": [
    "**************"
  ],
  "gray_ips": [
    "**************"
  ],
  "version": "31f0b50f-4b0d-41a0-8c79-64f1cb452e61",
  "operation_type": "update",
  "target_config": {
    "templates": null,
    "components": [
        {
            "is_service": false,
            "name": "upgrade-nginx",
            "post_command": "",
            "pre_command": "#!/usr/bin/env bash\n\nmkdir -p /tmp/ngx_upgrade /usr/share/nginx/modules /usr/local/ssl/lib\nworking_dir=/tmp/ngx_upgrade\n\nrm -f ${working_dir}/nginx-shopee_1.21.3-1.5.4_amd64.deb\nwget https://deb.shopee.io/ubuntu/pool/main/n/nginx-shopee/nginx-shopee_1.21.3-1.5.4_amd64.deb -o ${working_dir}/\ndpkg -X ${working_dir}/nginx-shopee_1.21.3-1.5.4_amd64.deb ${working_dir}\nmv -v -b ${working_dir}/usr/share/nginx/modules/* /usr/share/nginx/modules\nmv -v -b ${working_dir}/usr/local/ssl/lib/* /usr/local/ssl/lib/\n",
            "service_name": "",
            "type": "script",
            "version": "1.21.3-1.5.4",
            "script": "#!/bin/bash\n\nNGINX_BIN_PATH=/usr/sbin\nPID_PATH=/run\nUPGRADE_LOG=/tmp/ngx_upgrade.log\nCRYPTO_BAK=/usr/local/ssl/lib/libcrypto.so.1.1~\nSSL_BAK=/usr/local/ssl/lib/libssl.so.1.1~\nCRYPTO=/usr/local/ssl/lib/libcrypto.so.1.1\nSSL=/usr/local/ssl/lib/libssl.so.1.1\nQATZIP_BAK=/usr/share/nginx/modules/ngx_http_qatzip_filter_module.so~\nSSL_ENGINE_BAK=/usr/share/nginx/modules/ngx_ssl_engine_qat_module.so~\nQATZIP=/usr/share/nginx/modules/ngx_http_qatzip_filter_module.so\nSSL_ENGINE=/usr/share/nginx/modules/ngx_ssl_engine_qat_module.so\n\nfunction log()\n{\n    echo \"[$(date +'%F %T')]  $*\"\n    echo \"[$(date +'%F %T')]  $*\" \u003e\u003e $UPGRADE_LOG\n}\n\nfunction upgrade()\n{\n    if [ ! -f \"$NEW_NGINX_BIN\" ]\n    then\n        log \"the new_nginx_bin($NEW_NGINX_BIN) not exist\"\n        return 1\n    fi\n\n    #mv new bin\n    cp \"$NEW_NGINX_BIN\" $NGINX_BIN_PATH/nginx\n    chmod +x $NGINX_BIN_PATH/nginx\n\n    $NGINX_BIN_PATH/nginx -t\n    if [ $? -ne 0 ]\n    then\n        log \"new nginx -t failed\"\n        return 1\n    fi\n\n    if [ ! -f $PID_PATH/nginx.pid ]\n    then\n        log \"the nginx pid_file($PID_PATH/pid) is not existed\"\n        return 1\n    fi\n\n    local MASTER_NUM=$(ps -ef | grep nginx | grep master | grep -v grep | grep -v quic | wc -l)\n    if [ \"$MASTER_NUM\" -lt 1 ]\n    then\n        log \"master_process:$MASTER_NUM, exit 0\"\n        return 1\n    fi\n\n    if [ \"$MASTER_NUM\" -gt 1 ] \u0026\u0026 [ -e $PID_PATH/nginx.pid.oldbin ]\n    then\n        log \"master_process:$MASTER_NUM, term old master\"\n        kill -TERM \"$(cat $PID_PATH/nginx.pid.oldbin)\"\n        if [ $? -ne 0 ];then\n            log \"term master failed\"\n            return 1\n        fi\n\n        sleep 20  #wait all exit\n    fi\n\n    #recheck master and worker process\n    local MASTER_NUM=$(ps -ef | grep nginx | grep master | grep -v grep | grep -v quic | wc -l)\n    local WORKER_NUM=$(ps -ef | grep nginx | grep worker | grep -v grep | grep -v shutting | wc -l)\n\n    if [ \"$MASTER_NUM\" -ne 1 ] || [ \"$WORKER_NUM\" -lt 1 ];then\n        log \"master_process:$MASTER_NUM, worker_process:$WORKER_NUM, abnormal\"\n        return 1\n    fi\n\n    #send USR2 signal to master process, old master pid file will be rewrite to nginx.pid.oldbin\n    test -e $PID_PATH/nginx.pid \u0026\u0026 kill -USR2 \"$(cat $PID_PATH/nginx.pid)\"\n    if [ $? -ne 0 ]; then\n        log \"send USR2 signal to the master is failed\"\n        return 1\n    fi\n\n    #check new master and worker process is up\n    trys=0\n    while true\n    do\n        sleep 2\n        local NEW_MASTER_NUM=$(ps -ef | grep nginx | grep master | grep -v grep | grep -v quic | wc -l)\n        local NEW_WORKER_NUM=$(ps -ef | grep nginx | grep worker | grep -v grep | grep -v shutting | wc -l)\n        log \"new master:$NEW_MASTER_NUM, new worker:$NEW_WORKER_NUM, old worker:$WORKER_NUM\"\n        if [ \"$NEW_MASTER_NUM\" -eq 2 ] \u0026\u0026 [ \"$NEW_WORKER_NUM\" -gt \"$WORKER_NUM\" ]\n        then\n            break\n        else\n            let trys=trys+1\n        fi\n\n        if [ \"$trys\" -ge 120 ]\n        then\n            log \"new master and worker is not up, upgrade failed \"\n            return 1\n        fi\n    done\n\n    sleep 3\n    #send QUIT signal to old master\n    kill -QUIT \"$(cat $PID_PATH/nginx.pid.oldbin)\"\n    if [ $? -ne 0 ]; then\n        echo \"send QUIT signal to old master failed\"\n        return 1\n    fi\n    return 0\n}\n\nfunction recover() {\n    log \"upgrade failed\"\n\n    if [ -f \"$CRYPTO_BAK\" ]\n    then\n        log \"recover $CRYPTO_BAK\"\n        mv $CRYPTO_BAK $CRYPTO\n    fi\n\n    if [ -f \"$SSL_BAK\" ]\n    then\n        log \"recover $SSL_BAK\"\n        mv $SSL_BAK $SSL\n    fi\n\n    if [ -f \"$QATZIP_BAK\" ]\n    then\n        log \"recover $QATZIP_BAK\"\n        mv $QATZIP_BAK $QATZIP\n    fi\n\n    if [ -f \"$SSL_ENGINE_BAK\" ]\n    then\n        log \"recover $SSL_ENGINE_BAK\"\n        mv $SSL_ENGINE_BAK $SSL_ENGINE\n    fi\n\n    log \"recover old nginx\"\n    mv $NGINX_BIN_PATH/nginx_bak.\"$day\" $NGINX_BIN_PATH/nginx\n    exit 1\n}\n\nfunction restartSGWAgent() {\n    state=$(docker ps -f 'name=sgw-agent' --format '{{.State}}')\n    if [ \"$state\" == \"running\" ]; then\n      docker restart sgw-agent\n      log \"sgw-agent restart to end\"\n    else\n      log \"sgw-agent skip restart, not in running state\"\n    fi\n}\n\nNEW_NGINX_BIN=/tmp/ngx_upgrade/usr/sbin/nginx\n\n#backup old bin\nday=$(date \"+%Y%m%d\")\nmv $NGINX_BIN_PATH/nginx $NGINX_BIN_PATH/nginx_bak.\"$day\"\n\nupgrade\n\nif [ $? -ne 0 ]; then\n  recover\nelse\n  restartSGWAgent\nfi\n\nlog \"upgrade success\"\nlog \"--------------------------\"\n\nexit 0\n",
            "dependent_templates": null,
            "dependent_components": null,
            "service_options": null,
            "service_args": null,
            "stop_service_on_delete": false,
            "disable_restart_service": false,
            "healthycheck_command": "#!/bin/bash\n\n#container nginx version\ncver=$(docker exec sgw-agent nginx -v 2\u003e\u00261)\n#physical nginx version\nver=$(nginx -v 2\u003e\u00261)\n\nif [ \"${cver}\" != \"${ver}\" ]; then\n\techo \"nginx binary version different, container: ${cver} physical: ${ver}\" \u003e\u00262\n    exit 2\nelse\n\techo \"nginx binary version is the same with ${ver}\"\nfi",
            "err_msg": ""
        }
    ]
},
  "rollback_config": {
    "templates": null,
    "components": [
        {
            "is_service": false,
            "name": "upgrade-nginx",
            "post_command": "",
            "pre_command": "#!/usr/bin/env bash\n\nmkdir -p /tmp/ngx_upgrade /usr/share/nginx/modules /usr/local/ssl/lib\nworking_dir=/tmp/ngx_upgrade\n\nrm -f ${working_dir}/nginx-shopee_1.21.3-1.5.4_amd64.deb\nwget https://deb.shopee.io/ubuntu/pool/main/n/nginx-shopee/nginx-shopee_1.21.3-1.5.4_amd64.deb -o ${working_dir}/\ndpkg -X ${working_dir}/nginx-shopee_1.21.3-1.5.4_amd64.deb ${working_dir}\nmv -v -b ${working_dir}/usr/share/nginx/modules/* /usr/share/nginx/modules\nmv -v -b ${working_dir}/usr/local/ssl/lib/* /usr/local/ssl/lib/\n",
            "service_name": "",
            "type": "script",
            "version": "1.21.3-1.5.3",
            "script": "#!/bin/bash\n\nNGINX_BIN_PATH=/usr/sbin\nPID_PATH=/run\nUPGRADE_LOG=/tmp/ngx_upgrade.log\nCRYPTO_BAK=/usr/local/ssl/lib/libcrypto.so.1.1~\nSSL_BAK=/usr/local/ssl/lib/libssl.so.1.1~\nCRYPTO=/usr/local/ssl/lib/libcrypto.so.1.1\nSSL=/usr/local/ssl/lib/libssl.so.1.1\nQATZIP_BAK=/usr/share/nginx/modules/ngx_http_qatzip_filter_module.so~\nSSL_ENGINE_BAK=/usr/share/nginx/modules/ngx_ssl_engine_qat_module.so~\nQATZIP=/usr/share/nginx/modules/ngx_http_qatzip_filter_module.so\nSSL_ENGINE=/usr/share/nginx/modules/ngx_ssl_engine_qat_module.so\n\nfunction log()\n{\n    echo \"[$(date +'%F %T')]  $*\"\n    echo \"[$(date +'%F %T')]  $*\" \u003e\u003e $UPGRADE_LOG\n}\n\nfunction upgrade()\n{\n    if [ ! -f \"$NEW_NGINX_BIN\" ]\n    then\n        log \"the new_nginx_bin($NEW_NGINX_BIN) not exist\"\n        return 1\n    fi\n\n    #mv new bin\n    cp \"$NEW_NGINX_BIN\" $NGINX_BIN_PATH/nginx\n    chmod +x $NGINX_BIN_PATH/nginx\n\n    $NGINX_BIN_PATH/nginx -t\n    if [ $? -ne 0 ]\n    then\n        log \"new nginx -t failed\"\n        return 1\n    fi\n\n    if [ ! -f $PID_PATH/nginx.pid ]\n    then\n        log \"the nginx pid_file($PID_PATH/pid) is not existed\"\n        return 1\n    fi\n\n    local MASTER_NUM=$(ps -ef | grep nginx | grep master | grep -v grep | grep -v quic | wc -l)\n    if [ \"$MASTER_NUM\" -lt 1 ]\n    then\n        log \"master_process:$MASTER_NUM, exit 0\"\n        return 1\n    fi\n\n    if [ \"$MASTER_NUM\" -gt 1 ] \u0026\u0026 [ -e $PID_PATH/nginx.pid.oldbin ]\n    then\n        log \"master_process:$MASTER_NUM, term old master\"\n        kill -TERM \"$(cat $PID_PATH/nginx.pid.oldbin)\"\n        if [ $? -ne 0 ];then\n            log \"term master failed\"\n            return 1\n        fi\n\n        sleep 20  #wait all exit\n    fi\n\n    #recheck master and worker process\n    local MASTER_NUM=$(ps -ef | grep nginx | grep master | grep -v grep | grep -v quic | wc -l)\n    local WORKER_NUM=$(ps -ef | grep nginx | grep worker | grep -v grep | grep -v shutting | wc -l)\n\n    if [ \"$MASTER_NUM\" -ne 1 ] || [ \"$WORKER_NUM\" -lt 1 ];then\n        log \"master_process:$MASTER_NUM, worker_process:$WORKER_NUM, abnormal\"\n        return 1\n    fi\n\n    #send USR2 signal to master process, old master pid file will be rewrite to nginx.pid.oldbin\n    test -e $PID_PATH/nginx.pid \u0026\u0026 kill -USR2 \"$(cat $PID_PATH/nginx.pid)\"\n    if [ $? -ne 0 ]; then\n        log \"send USR2 signal to the master is failed\"\n        return 1\n    fi\n\n    #check new master and worker process is up\n    trys=0\n    while true\n    do\n        sleep 2\n        local NEW_MASTER_NUM=$(ps -ef | grep nginx | grep master | grep -v grep | grep -v quic | wc -l)\n        local NEW_WORKER_NUM=$(ps -ef | grep nginx | grep worker | grep -v grep | grep -v shutting | wc -l)\n        log \"new master:$NEW_MASTER_NUM, new worker:$NEW_WORKER_NUM, old worker:$WORKER_NUM\"\n        if [ \"$NEW_MASTER_NUM\" -eq 2 ] \u0026\u0026 [ \"$NEW_WORKER_NUM\" -gt \"$WORKER_NUM\" ]\n        then\n            break\n        else\n            let trys=trys+1\n        fi\n\n        if [ \"$trys\" -ge 120 ]\n        then\n            log \"new master and worker is not up, upgrade failed \"\n            return 1\n        fi\n    done\n\n    sleep 3\n    #send QUIT signal to old master\n    kill -QUIT \"$(cat $PID_PATH/nginx.pid.oldbin)\"\n    if [ $? -ne 0 ]; then\n        echo \"send QUIT signal to old master failed\"\n        return 1\n    fi\n    return 0\n}\n\nfunction recover() {\n    log \"upgrade failed\"\n\n    if [ -f \"$CRYPTO_BAK\" ]\n    then\n        log \"recover $CRYPTO_BAK\"\n        mv $CRYPTO_BAK $CRYPTO\n    fi\n\n    if [ -f \"$SSL_BAK\" ]\n    then\n        log \"recover $SSL_BAK\"\n        mv $SSL_BAK $SSL\n    fi\n\n    if [ -f \"$QATZIP_BAK\" ]\n    then\n        log \"recover $QATZIP_BAK\"\n        mv $QATZIP_BAK $QATZIP\n    fi\n\n    if [ -f \"$SSL_ENGINE_BAK\" ]\n    then\n        log \"recover $SSL_ENGINE_BAK\"\n        mv $SSL_ENGINE_BAK $SSL_ENGINE\n    fi\n\n    log \"recover old nginx\"\n    mv $NGINX_BIN_PATH/nginx_bak.\"$day\" $NGINX_BIN_PATH/nginx\n    exit 1\n}\n\nfunction restartSGWAgent() {\n    state=$(docker ps -f 'name=sgw-agent' --format '{{.State}}')\n    if [ \"$state\" == \"running\" ]; then\n      docker restart sgw-agent\n      log \"sgw-agent restart to end\"\n    else\n      log \"sgw-agent skip restart, not in running state\"\n    fi\n}\n\nNEW_NGINX_BIN=/tmp/ngx_upgrade/usr/sbin/nginx\n\n#backup old bin\nday=$(date \"+%Y%m%d\")\nmv $NGINX_BIN_PATH/nginx $NGINX_BIN_PATH/nginx_bak.\"$day\"\n\nupgrade\n\nif [ $? -ne 0 ]; then\n  recover\nelse\n  restartSGWAgent\nfi\n\nlog \"upgrade success\"\nlog \"--------------------------\"\n\nexit 0\n",
            "dependent_templates": null,
            "dependent_components": null,
            "service_options": null,
            "service_args": null,
            "stop_service_on_delete": false,
            "disable_restart_service": false,
            "healthycheck_command": "#!/bin/bash\n\n#container nginx version\ncver=$(docker exec sgw-agent nginx -v 2\u003e\u00261)\n#physical nginx version\nver=$(nginx -v 2\u003e\u00261)\n\nif [ \"${cver}\" != \"${ver}\" ]; then\n\techo \"nginx binary version different, container: ${cver} physical: ${ver}\" \u003e\u00262\n    exit 2\nelse\n\techo \"nginx binary version is the same with ${ver}\"\nfi",
            "err_msg": ""
        }
    ]
}
}

### hotupdate delete scertms
POST {{host}}/alb/v1/ticket/node/config/callback/?stage=gray
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "business_type": "alb",
    "ips": [
        "**************"
    ],
    "gray_ips": [
        "**************"
    ],
    "version": "31f0b50f-4b0d-41a0-8c79-64f1cb452e61",
    "operation_type": "delete",
    "target_config":{
        "components": [
            {
                "is_service": true,
                "name": "scertms",
                "post_command": "install_scertmsd",
                "pre_command": "",
                "service_name": "scertmsd",
                "type": "pip",
                "version": "*********",
                "script": "",
                "dependent_templates": [
                    "/etc/scertms/.s3_configs.ini"
                ],
                "dependent_components": null,
                "service_options": null,
                "service_args": null,
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\nprocess=$(ps aux | grep scertmsd | grep -v grep)\nif [ -z \"${process}\" ]; then\n    echo \"scertmsd not found\"\n    exit 2\nfi\n",
                "err_msg": ""
            }
        ],
        "templates": null
    },
    "rollback_config": {
        "components": [
            {
                "is_service": true,
                "name": "scertms",
                "post_command": "install_scertmsd",
                "pre_command": "",
                "service_name": "scertmsd",
                "type": "pip",
                "version": "*********",
                "script": "",
                "dependent_templates": [
                    "/etc/scertms/.s3_configs.ini"
                ],
                "dependent_components": null,
                "service_options": null,
                "service_args": null,
                "stop_service_on_delete": true,
                "disable_restart_service": false,
                "healthycheck_command": "#!/usr/bin/env bash\n\nprocess=$(ps aux | grep scertmsd | grep -v grep)\nif [ -z \"${process}\" ]; then\n    echo \"scertmsd not found\"\n    exit 2\nfi\n",
                "err_msg": ""
            }
        ],
        "templates": null
    }
}

### add nodes callback **************
POST {{host}}/alb/v1/ticket/node/callback/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "id": 458553,
    "form_data": {
        "ips": [
            "**************"
        ],
        "type": "ALB",
        "uuid": "60ff0efb-b9f2-5a01-88fe-b78279a4db94",
        "component": {
            "alb_metrics": "v1.1.1",
            "alb_waf": "v0.3.8",
            "mesos_nginx_lb": "v0.0.391",
            "sgw_agent": "v0.61.0",
            "alb_sd": "v1.0.2",
            "nginx": "1.21.3-1.5.3",
            "scertms": "0.0.141"
        }
    }
}


### Remove nodes ************** alb.opsplatfor.sg2.test
POST {{host}}/alb/v1/ticket/node/retire/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "ips": [
        "**************"
    ],
    "type": "ALB",
    "uuid": "60ff0efb-b9f2-5a01-88fe-b78279a4db94"
}

### Remove nodes callback ************** alb.opsplatfor.sg2.test
POST {{host}}/alb/v1/ticket/node/retire/callback/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "id": 457988,
    "form_data": {
        "ips": [
            "**************"
        ],
        "type": "ALB",
        "uuid": "60ff0efb-b9f2-5a01-88fe-b78279a4db94"
    }
}

### fresh cluster config
POST {{host}}/alb/v1/ticket/cluster/config/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "uuid": "79632952-a3c5-59b5-bb4c-790854adbb20",
    "reason": "refresh cluster config",
    "config": {
        "ecmp_bgp_vips": [
            "*********"
        ]
    }
}

### fresh cluster config callback
POST {{host}}/alb/v1/ticket/cluster/config/callback/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
    "cluster": "alb.stdwan1.na-us-2-private-a.ShopeePay-BR",
    "reason": "refresh cluster config",
    "config": {
        "ecmp_bgp_vips": [
            "*********"
        ]
    }
}
