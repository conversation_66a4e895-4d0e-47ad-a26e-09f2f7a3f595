package albapi

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/go-shopeelib/gin/middlewares"
	"git.garena.com/shopee/go-shopeelib/log"
	"git.garena.com/shopee/go-shopeelib/toclib"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albvo"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
)

// GetNodeProvisionHandler godoc
// @Summary fetch node provision config
// @Description fetch node provision configs, including template, component and events
// @Tags ALBNode
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param ips query string true "alb nodes' ip address list"
// @Success 200 {object} albvo.NodeProvisionResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/ [GET]
//
//nolint:nolintlint,dupl
func GetNodeProvisionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindQuery(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("fetch_provision_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	prov := albapp.NewNodeProvisionApp(traceID)
	resp, err := prov.Configs(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// GetNodeUpgradeProvisionHandler godoc
// @Summary generate node provision config
// @Description fetch node provision configs, including template, component
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ip body string true "node ip address"
// @Param component body string true "component name"
// @param version body string true "upgrade target version"
// @Success 200 {object} albvo.NodeProvisionConfigResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/upgrade/ [GET]
//
//nolint:nolintlint,dupl
func GetNodeUpgradeProvisionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeProvisionComponentRequest
	if err = ctx.ShouldBindQuery(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("create_provision_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	prov := albapp.NewNodeProvisionApp(traceID)
	resp, err := prov.GenConfigs(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// UpgradeNodeProvisionHandler godoc
// @Summary trigger node hot-update
// @Description auto-gen node provision configs, then update node state to hot-update
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body string true "nodes' ip address list"
// @Param component body string true "component name, options: alb-metrics, sgw-agent, alb-sd, mesos-nginx-lb, nginx-shopee"
// @param version body string true "upgrade target version"
// @Param env body string true "environment,options: test, staging, stable, live"
// @param ticket_id body int true "swp ticket id"
// @Success 200 {object} albvo.NodeProvisionConfigResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/upgrade/ [PUT]
//
//nolint:nolintlint,dupl
func UpgradeNodeProvisionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	user, err := core.User(ctx)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorInvalid, err.Error()))

		return
	}

	var req albvo.NodeHotUpdateProvisionRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	req.ApplicantUser = user.Email

	node := albapp.NewNodeApp(traceID)

	resp, err := node.UpgradeComponent(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// RollbackNodeProvisionHandler godoc
// @Summary trigger node hot-update rollback
// @Description auto-gen node provision configs, then update node state to hot-update rollback
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body string true "nodes' ip address list"
// @Param component body string true "component name, options: alb-metrics, sgw-agent, alb-sd, mesos-nginx-lb, nginx-shopee"
// @param version body string true "rollback target version"
// @Param env body string true "environment,options: test, staging, stable, live"
// @param ticket_id body int true "swp ticket id"
// @Success 200 {object} albvo.NodeProvisionConfigResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/rollback/ [PUT]
//
//nolint:nolintlint,dupl
func RollbackNodeProvisionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)
	user, err := core.User(ctx)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorInvalid, err.Error()))

		return
	}

	var req albvo.NodeHotUpdateProvisionRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	req.ApplicantUser = user.Email

	node := albapp.NewNodeApp(traceID)

	resp, err := node.RollbackComponent(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// AbortNodeProvisionHandler godoc
// @Summary abort node hot-update rollback
// @Description just update node state to Running or Initialised
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body []string true "nodes' ip address list"
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/abort/ [PUT]
//
//nolint:nolintlint,dupl
func AbortNodeProvisionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.AbortRollback(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// RecoverNodeProvisionHandler godoc
// @Summary recover node after MA, trigger recovery workflow
// @Description update node state to PostChecking, they must be in MA
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body []string true "nodes' ip address list"
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/recover/ [PUT]
//
//nolint:nolintlint,dupl
func RecoverNodeProvisionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.Recover(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// ReInitNodeProvisionHandler godoc
// @Summary re-init node before initialised, trigger provision workflow
// @Description update node state to Spare, they must be before initialised
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body []string true "nodes' ip address list"
// @Success 200 {object} albvo.NodeResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/reinit/ [PUT]
//
//nolint:nolintlint,dupl
func ReInitNodeProvisionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	node := albapp.NewNodeApp(traceID)

	resp, err := node.ReInit(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// DeleteNodeProvisionHandler godoc
// @Summary delete node provision config
// @Description delete node provision configs, whether template or component
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param configs body []toclib.ProvisionNodeConfig true "node provision configs"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/ [DELETE]
//
//nolint:nolintlint,dupl
func DeleteNodeProvisionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req []toclib.ProvisionNodeConfig
	if err = ctx.ShouldBindJSON(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("delete_provision_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	prov := albapp.NewNodeProvisionApp(traceID)
	resp, err := prov.Delete(ctx.Request.Context(), req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// DeleteNodeProvisionTemplateHandler godoc
// @Summary delete node provision template
// @Description delete node provision configs only templates
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body []string true "node IP address list"
// @Param paths body []string true "node provision templates"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/template/ [DELETE]
//
//nolint:nolintlint,dupl
func DeleteNodeProvisionTemplateHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeProvisionTemplateRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("delete_provision_template_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	prov := albapp.NewNodeProvisionApp(traceID)
	resp, err := prov.DeleteTemplate(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// DeleteNodeProvisionComponentHandler godoc
// @Summary delete node provision component
// @Description delete node provision configs only components
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body []string true "node IP address list"
// @Param components body []string true "node provision component list by name"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/component/ [DELETE]
//
//nolint:nolintlint,dupl
func DeleteNodeProvisionComponentHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeComponentRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).
			Error("delete_provision_component_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	prov := albapp.NewNodeProvisionApp(traceID)
	resp, err := prov.DeleteComponent(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// UpdateNodeProvisionComponentHandler godoc
// @Summary update node provision component
// @Description update node provision configs only components
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body []string true "node IP address list"
// @Param components body []string true "node provision component list by name"
// @Param disabled body bool true "node provision component disable or enable"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/component/ [PUT]
//
//nolint:nolintlint,dupl
func UpdateNodeProvisionComponentHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeUpdateComponentRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).
			Error("update_provision_component_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	prov := albapp.NewNodeProvisionApp(traceID)
	resp, err := prov.UpdateComponent(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// DeleteNodeProvisionContainerHandler godoc
// @Summary delete node provision docker container
// @Description delete node provision docker type component
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body []string true "node IP address list"
// @Param components body []string true "node provision component list by name"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/purge/container/ [DELETE]
//
//nolint:nolintlint,dupl
func DeleteNodeProvisionContainerHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("delete_provision_component_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	prov := albapp.NewNodeProvisionApp(traceID)
	resp, err := prov.PurgeContainers(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}

// PurgeNodeProvisionHandler godoc
// @Summary delete node provision total config
// @Description delete node provision configs, including defined templates and components
// @Tags ALBNode
// @Accept application/json
// @Produce json
// @Param ips body []albvo.NodeRequest true "node IP address list"
// @Success 200 {object} core.BaseResponse
// @Failure 400 {object} middlewares.Error
// @Failure 500 {object} middlewares.Error
// @Router /alb/v1/node/provision/purge/ [DELETE]
//
//nolint:nolintlint,dupl
func PurgeNodeProvisionHandler(ctx *gin.Context) {
	var err error
	traceID := core.TraceID(ctx)

	var req albvo.NodeRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		log.Logger().WithError(err).WithField("trace_id", traceID).Error("purge_provision_invalid_param")
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCode(
			http.StatusBadRequest, middlewares.ErrorParams, err.Error()))

		return
	}

	prov := albapp.NewNodeProvisionApp(traceID)
	resp, err := prov.Purge(ctx.Request.Context(), &req)
	if err != nil {
		middlewares.SetErr(ctx, middlewares.NewErrorWithStatusCodeAndTraceBack(
			http.StatusInternalServerError, middlewares.ErrorServer, err))

		return
	}

	middlewares.SetResp(ctx, resp)
}
