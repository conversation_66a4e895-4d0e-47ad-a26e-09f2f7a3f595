package aclvo

type IPSet struct {
	ID         int    `json:"id"`
	CreatedAt  int    `json:"created_at"`
	UpdatedAt  int    `json:"updated_at"`
	UsageRange int    `json:"usage_range"`
	Name       string `json:"name"`
	Services   []struct {
		ID           int    `json:"id"`
		CreatedAt    int    `json:"created_at"`
		UpdatedAt    int    `json:"updated_at"`
		ObjectType   string `json:"object_type"`
		ObjectKey    string `json:"object_key"`
		TreeNodeType int    `json:"tree_node_type"`
		TreeNodeName string `json:"tree_node_name"`
		TreeNodeKey  int    `json:"tree_node_key"`
	} `json:"services"`
	Comment       string `json:"comment"`
	ReleaseMethod string `json:"release_method"`
	SwpTicket     string `json:"swp_ticket"`
	CreatedBy     string `json:"created_by"`
	UpdatedBy     string `json:"updated_by"`
	ApprovedBy    string `json:"approved_by"`
	Status        string `json:"status"`
	LockID        string `json:"lock_id"`
	IsLocked      bool   `json:"is_locked"`
	Associated    struct {
		WAFRule     int `json:"waf_rule"`
		ALBListener int `json:"alb_listener"`
		NLBListener int `json:"nlb_listener"`
		SNAT        int `json:"snat"`
	} `json:"associated"`
	Version int `json:"version"`
}

type IP struct {
	ID        int    `json:"id"`
	CreatedAt int    `json:"created_at"`
	UpdatedAt int    `json:"updated_at"`
	ExpiredAt int    `json:"expired_at"`
	IPSetName string `json:"ipset_name"`
	IP        string `json:"ip"`
	UpdatedBy string `json:"updated_by"`
	Comment   string `json:"comment"`
}
