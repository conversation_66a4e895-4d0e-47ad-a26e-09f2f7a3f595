package sgw

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"

	"git.garena.com/shopee/go-shopeelib/log"

	"git.garena.com/shopee/devops/sgw-addon-operator/configs"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albapi"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/alb/albctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/cmdb/cmdbapi"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/core"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/sgw/sgwctl"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/smap/smapapp"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/swp/swpapi"
)

type ALBRouter struct{}

func (r *ALBRouter) Register(engine *gin.Engine) {
	group := engine.Group("/alb")
	albAPI := group.Group("/api")
	albAPI.GET("/version", albapi.GetVersionHandler)

	albV1 := group.Group("/v1")
	albV1.Use(core.AuthorizeHandler(), core.TraceHandler())

	r.registerRouterGroup(albV1)
}

func (r *ALBRouter) registerRouterGroup(albv1 *gin.RouterGroup) {
	group := albv1.Group("/")
	{
		node := group.Group("/node")
		{
			node.GET("/", albapi.GetNodeListHandler)
			node.GET("/spec/", albapi.GetNodeSpecHandler)
			node.PUT("/spec/", albapi.UpdateNodeSpecHandler)
			node.GET("/provision/", albapi.GetNodeProvisionHandler)
			node.GET("/provision/upgrade/", albapi.GetNodeUpgradeProvisionHandler)
			node.PUT("/provision/upgrade/", albapi.UpgradeNodeProvisionHandler)
			node.PUT("/provision/rollback/", albapi.RollbackNodeProvisionHandler)
			node.PUT("/provision/abort/", albapi.AbortNodeProvisionHandler)
			node.PUT("/provision/recover/", albapi.RecoverNodeProvisionHandler)
			node.PUT("/provision/reinit/", albapi.ReInitNodeProvisionHandler)
			node.DELETE("/provision/", albapi.DeleteNodeProvisionHandler)
			node.DELETE("/provision/template/", albapi.DeleteNodeProvisionTemplateHandler)
			node.DELETE("/provision/component/", albapi.DeleteNodeProvisionComponentHandler)
			node.PUT("/provision/component/", albapi.UpdateNodeProvisionComponentHandler)
			node.DELETE("/provision/purge/container/", albapi.DeleteNodeProvisionContainerHandler)
			node.DELETE("/provision/purge/", albapi.PurgeNodeProvisionHandler)
			node.GET("/event/", albapi.GetNodeEventsHandler)
			node.GET("/state/", albapi.GetNodeStateHandler)
			node.PUT("/state/", albapi.UpdateNodeStateHandler)
			node.PATCH("/state/", albapi.ReentrantNodeStateHandler)
			node.GET("/variable/", albapi.GetNodeTagVariableHandler)
			node.GET("/component/", albapi.GetNodeComponentHandler)
			node.PUT("/component/", albapi.UpdateNodeComponentHandler)
			node.PUT("/component/version/", albapi.UpdateNodeComponentVersionHandler)
			node.GET("/state/:state/", albapi.StateNodeListHandler)
			node.PATCH("/ma/", albapi.UpdateNodeMAHandler)
			node.PATCH("/expire/", albapi.ExpireNodeHandler)
		}
		cluster := group.Group("/cluster")
		{
			cluster.GET("/", albapi.ClusterListHandler)
			cluster.PUT("/sync/", albapi.ClusterSyncHandler)
			cluster.PUT("/uuid/:uuid/sync/", albapi.ClusterManagedHandler(), albapi.ClusterSyncByUUIDHandler)
			cluster.GET("/uuid/:uuid/node/", albapi.ClusterManagedHandler(), albapi.ClusterNodeListHandler)
			cluster.GET("/uuid/:uuid/config/", albapi.ClusterManagedHandler(), albapi.ClusterConfigHandler)
			cluster.PUT("/uuid/:uuid/meta/sync/", albapi.ClusterManagedHandler(), albapi.ClusterMetaSyncHandler)
			cluster.POST("/uuid/:uuid/node/precheck/", albapi.ClusterManagedHandler(), albapi.ClusterNodePreCheckHandler)
			cluster.POST("/uuid/:uuid/node/precheck_offline/",
				albapi.ClusterManagedHandler(), albapi.ClusterNodePreCheckOfflineHandler)
			cluster.GET("/uuid/:uuid/node/nlb/listener/",
				albapi.ClusterManagedHandler(), albapi.ClusterNodeNLBListenerListHandler)
		}
		sdu := group.Group("/sdu")
		{
			sdu.GET("/exporter/", cmdbapi.GetMonitorTagsHandler)
			sdu.POST("/server/", cmdbapi.BindServerHandler)
		}
		ticket := albv1.Group("/ticket")
		{
			ticket.POST("/", albapi.OnlineOfflineTicketHandler)
			ticket.POST("/offline/", albapi.OfflineHandler)
			ticket.POST("/automa/", core.SGWBotAuthHandler(), albapi.AutoMAHandler)
			ticket.GET("/id/:id/", swpapi.GetTicketIDHandler)
			ticket.POST("/node/", albapi.AddNodeTicketHandler)
			ticket.POST("/node/callback/", albapi.AddNodeTicketCBHandler)
			ticket.POST("/node/retire/", albapi.RetireNodeTicketHandler)
			ticket.POST("/node/config/", albapi.HotUpdateNodeTicketHandler)
			ticket.POST("/node/config/callback/", albapi.HotUpdateNodeTicketCBHandler)
			ticket.POST("/node/config/rollback/", albapi.HotUpdateNodeTicketRollbackHandler)
			ticket.POST("/node/config/reset/", albapi.HotUpdateNodeTicketResetHandler)
			ticket.POST("/cluster/block/", albapi.BlockClusterTrafficTicketHandler)
			ticket.POST("/cluster/block/callback/",
				core.OpsPlatformGroupsAuthHandler(configs.ALB.GetAllowBlockingGroups()),
				albapi.SupportBlockingTrafficProductsAuthHandler(configs.ALB.GetAllowBlockingProducts()),
				albapi.BlockClusterTrafficTicketCBHandler)
			ticket.POST("/cluster/open/", albapi.OpenClusterTrafficTicketHandler)
			ticket.POST("/cluster/open/callback/",
				core.OpsPlatformGroupsAuthHandler(configs.ALB.GetAllowBlockingGroups()),
				albapi.SupportBlockingTrafficProductsAuthHandler(configs.ALB.GetAllowBlockingProducts()),
				albapi.OpenClusterTrafficTicketCBHandler)
			ticket.POST("/cluster/block_by_domains/", albapi.BlockTrafficByDomainsTicketHandler)
			ticket.POST("/cluster/block_by_domains/callback/",
				core.OpsPlatformGroupsAuthHandler(configs.ALB.GetAllowBlockingGroups()),
				albapi.SupportBlockingTrafficProductsAuthHandler(configs.ALB.GetAllowBlockingProducts()),
				albapi.BlockTrafficByDomainsTicketCBHandler)
			ticket.POST("/cluster/open_by_domains/", albapi.OpenTrafficByDomainsTicketHandler)
			ticket.POST("/cluster/open_by_domains/callback/",
				core.OpsPlatformGroupsAuthHandler(configs.ALB.GetAllowBlockingGroups()),
				albapi.SupportBlockingTrafficProductsAuthHandler(configs.ALB.GetAllowBlockingProducts()),
				albapi.OpenTrafficByDomainsTicketCBHandler)
			ticket.POST("/cluster/config/", albapi.FreshClusterConfigTicketHandler)
			ticket.POST("/cluster/config/callback/", albapi.FreshClusterConfigTicketCBHandler)
		}
		comp := albv1.Group("/component")
		{
			comp.GET("/", albapi.ListComponentHandler)
			comp.GET("/version/", albapi.ListComponentVersionHandler)
		}
		state := albv1.Group("/state")
		{
			state.GET("/", albapi.ListStateHandler)
			state.GET("/task/", albapi.ListStateTaskHandler)
			state.GET("/task/result/", albapi.ListStateTaskResultHandler)
		}
		reload := albv1.Group("/reload")
		{
			reload.POST("/", albapi.ReloadNginxHandler)
			reload.GET("/status/:request_id", albapi.GetReloadStatusHandler)
			reload.GET("/history", albapi.ListReloadHistoryHandler)
			reload.GET("/config", albapi.GetReloadConfigHandler)
		}
	}
}

type ALBController struct{}

func (c *ALBController) Init(client *sgwctl.ControllerClient) error {
	albctl.ALBController = albctl.NewControllerApp(client)

	return nil
}

func (c *ALBController) Run(ctx context.Context) error {
	if albctl.ALBController != nil {
		if err := albctl.ALBController.Run(ctx); err != nil {
			log.Logger().WithError(err).Error("run_alb_operator_failed")

			return errors.WithMessage(err, "run_alb_controller_failed")
		}
	}

	return nil
}

func (c *ALBController) RegisterCollector() error {
	err := prometheus.Register(smapapp.NewALBCollector())
	if err != nil {
		return errors.WithMessage(err, "register_collector_failed")
	}

	return nil
}
