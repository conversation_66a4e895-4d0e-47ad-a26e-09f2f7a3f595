package adrvo

type Instance struct {
	InstanceID    string `json:"instance_id"`
	Type          string `json:"type"`
	IsCluster     bool   `json:"is_cluster"`
	IsLegacy      bool   `json:"is_legacy"`
	DefaultWeight int    `json:"default_weight"`
	Name          string `json:"name"`
	Domain        string `json:"domain"`
	Env           string `json:"env"`
	NetworkEnv    string `json:"network_env"`
	AZ            string `json:"az"`
	Segment       string `json:"segment"`
	RZ            string `json:"rz"`
	Region        string `json:"region"`
	NetworkType   string `json:"network_type"`
	Weight        int    `json:"weight"`
	VIPs          []*VIP `json:"vips"`
}

type VIP struct {
	VIP string `json:"vip"`
}
