package adrvo

type Group struct {
	IsService bool `json:"is_service"`

	ServiceID        int `json:"service_id"`
	DeploymentZoneID int `json:"deployment_zone_id"`
	CreatedAt        int `json:"created_at"`
	UpdatedAt        int `json:"updated_at"`

	Name               string      `json:"name"`
	GroupID            string      `json:"group_id"`
	NetworkType        string      `json:"network_type"`
	Domain             string      `json:"domain"`
	Status             string      `json:"status"`
	Remark             string      `json:"remark"`
	Type               string      `json:"type"`
	Owner              string      `json:"owner"`
	UpdatedBy          string      `json:"updated_by"`
	DeploymentZoneName string      `json:"deployment_zone_name"`
	ServiceDomains     []string    `json:"service_domains"`
	Instances          []*Instance `json:"instances"`
}
