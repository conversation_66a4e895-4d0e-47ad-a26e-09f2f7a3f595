package adrvo

type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "Pending"
	TaskStatusRunning   TaskStatus = "Running"
	TaskStatusCompleted TaskStatus = "Completed"
	TaskStatusFailed    TaskStatus = "Failed"
)

var TaskStatusOptions = []TaskStatus{TaskStatusPending, TaskStatusRunning, TaskStatusCompleted, TaskStatusFailed}

func (t *TaskStatus) IsDone() bool {
	return *t == TaskStatusCompleted || *t == TaskStatusFailed
}

type AsyncSwitchTaskResultDetail struct {
	Type   string `json:"type"`
	Key    string `json:"key"`
	Status string `TaskStatus:"status"`
}

type AsyncSwitchTaskResult struct {
	Status  TaskStatus                     `json:"status"`
	Details []*AsyncSwitchTaskResultDetail `json:"details"`
}

type AsyncSwitchTask struct {
	ID int `json:"async_task_id"`
}
