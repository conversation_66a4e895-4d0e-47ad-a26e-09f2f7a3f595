package configs

import (
	"embed"
	"fmt"
	"io"
	"io/fs"
	"os"
	"regexp"
	"strings"
	"sync"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"github.com/spf13/viper"

	"git.garena.com/shopee/go-shopeelib/byteutils"
	"git.garena.com/shopee/go-shopeelib/config"

	"git.garena.com/shopee/devops/sgw-addon-operator/deploy"
	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

var (
	SGW    *SGWConfig
	DNS    *DNSConfig
	NLB    *NLBConfig
	NAT    *NATConfig
	ALB    *ALBConfig
	LCS    *LCSConfig
	KLS    *KLSConfig
	Mgmt   *MgmtConfig
	BOT    *BotConfig
	E2E    *E2EConfig
	DB     *DBConfig
	ETCD   *ETCDConfig
	ZK     *ZKConfig
	Ext2   *Ext2Config
	DRC    *DRCConfig
	VGW    *VGWConfig
	EGW    *EGWConfig
	EIP    *EIPConfig
	EKS    *EKSConfig
	Anchor *AnchorConfig
	Remedy *RemedyConfig
	Redis  RedisConfigMap

	once sync.Once
)

//go:embed config.yaml
//go:embed config-*.yaml
var folder embed.FS

// GetEnv fetch env var ENV
func GetEnv() string {
	return os.Getenv("ENV")
}

// CI is under CI env
func CI() bool {
	return os.Getenv("CI") == "true"
}

// PrecheckConfig defines common precheck configuration for all business modules
type PrecheckConfig struct {
	MaxNodeWorkers    int `mapstructure:"max_node_workers"`
	MaxCheckerWorkers int `mapstructure:"max_checker_workers"`
}

// Config configuration
type Config struct {
	CI     bool           `mapstructure:"ci"`
	Mgmt   MgmtConfig     `mapstructure:"mgmt"`
	DNS    DNSConfig      `mapstructure:"dns"`
	ALB    ALBConfig      `mapstructure:"alb"`
	NLB    NLBConfig      `mapstructure:"nlb"`
	NAT    NATConfig      `mapstructure:"nat"`
	SGW    SGWConfig      `mapstructure:"sgw"`
	BOT    BotConfig      `mapstructure:"bot"`
	E2E    E2EConfig      `mapstructure:"e2e"`
	DB     DBConfig       `mapstructure:"databases"`
	ETCD   ETCDConfig     `mapstructure:"etcd"`
	ZK     ZKConfig       `mapstructure:"zk"`
	DRC    DRCConfig      `mapstructure:"drc"`
	Ext2   Ext2Config     `mapstructure:"ext2"`
	LCS    LCSConfig      `mapstructure:"lcs"`
	KLS    KLSConfig      `mapstructure:"kls"`
	VGW    VGWConfig      `mapstructure:"vgw"`
	EGW    EGWConfig      `mapstructure:"egw"`
	Anchor AnchorConfig   `mapstructure:"anchor"`
	EIP    EIPConfig      `mapstructure:"eip"`
	EKS    EKSConfig      `mapstructure:"eks"`
	Remedy RemedyConfig   `mapstructure:"remedy"`
	Redis  RedisConfigMap `mapstructure:"redis"`
}

// Cfg configuration
var Cfg = &Config{}

// Init configs initialization includes alb addon modules'
func Init(...string) error {
	var err error
	once.Do(func() {
		if err = deploy.SetEnvs(); err != nil {
			return
		}

		if err = initDefault("config"); err != nil {
			err = errors.Wrap(err, "init_default_config_failed")

			return
		}
		if err = initByEnv(); err != nil {
			err = errors.Wrap(err, "init_config_by_env_failed")

			return
		}

		if err = viper.Unmarshal(Cfg); err != nil {
			err = errors.Wrap(err, "viper.Unmarshal failed")

			return
		}

		verifier := validator.New()
		if err = verifier.Struct(Cfg); err != nil {
			err = errors.Wrap(err, "validator.Struct failed")

			return
		}

		Mgmt = &Cfg.Mgmt
		DNS = &Cfg.DNS
		NLB = &Cfg.NLB
		NAT = &Cfg.NAT
		ALB = &Cfg.ALB
		SGW = &Cfg.SGW
		BOT = &Cfg.BOT
		E2E = &Cfg.E2E
		DB = &Cfg.DB
		ETCD = &Cfg.ETCD
		ZK = &Cfg.ZK
		DRC = &Cfg.DRC
		Ext2 = &Cfg.Ext2
		LCS = &Cfg.LCS
		KLS = &Cfg.KLS
		VGW = &Cfg.VGW
		EGW = &Cfg.EGW
		EIP = &Cfg.EIP
		EKS = &Cfg.EKS
		Anchor = &Cfg.Anchor
		Remedy = &Cfg.Remedy
		Redis = Cfg.Redis
	})

	if err != nil {
		err = errors.Wrap(err, "init_configs_failed")

		return errors.WithMessage(err, "init_configs_failed")
	}

	return nil
}

func initByEnv() error {
	env := GetEnv()
	if env == "" {
		return nil
	}

	if env == consts.EnvDev {
		env = consts.EnvTest
	}

	name := fmt.Sprintf("config-%s", env)

	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.SetConfigType("yaml")
	viper.SetConfigFile(fmt.Sprintf("%s.yaml", name))

	cfgFile, err := folder.Open(viper.ConfigFileUsed())
	if err == nil {
		defer func(cfgFile fs.File) {
			err = cfgFile.Close()
			if err != nil {
				panic(err)
			}
		}(cfgFile)

		cfgs, err := io.ReadAll(cfgFile)
		if err != nil {
			return errors.WithMessage(err, "read_config_failed")
		}

		re := regexp.MustCompile(consts.DBPasswordRegex)

		cfgs = re.ReplaceAll(cfgs, byteutils.ToBytes(fmt.Sprintf("${1} %s${2}", os.Getenv("DBCONF_PASSWORD"))))

		config.InitByString(byteutils.ToString(cfgs), "yaml")
	}

	return nil
}

func initDefault(config string) error {
	vip := viper.New()
	vip.AutomaticEnv()
	vip.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	vip.SetConfigName(config)
	vip.SetConfigType("yaml")

	cfgFile, err := folder.Open(fmt.Sprintf("%s.yaml", config))
	if err == nil {
		defer func(cfgFile fs.File) {
			err = cfgFile.Close()
			if err != nil {
				panic(err)
			}
		}(cfgFile)

		if err = vip.ReadConfig(cfgFile); err != nil {
			return errors.WithMessage(err, "read_config_failed")
		}
	}

	for k, v := range vip.AllSettings() {
		viper.SetDefault(k, v)
	}

	return nil
}
