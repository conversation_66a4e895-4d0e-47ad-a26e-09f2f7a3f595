package configs

import (
	"github.com/duke-git/lancet/v2/slice"
)

type NLBConfig struct {
	Platform    string
	Application string
	Function    string
	Kube        Kube          `mapstructure:"kube"`
	Kernel      Kernel        `mapstructure:"kernel"`
	Service     ServiceConfig `mapstructure:"service"`
	SMAP        SMAP          `mapstructure:"smap"`
	SWP         struct {
		Template SWPTemplate
	}
	AddConfig struct {
		StatusChangeLimit int
		CtrlPollInterval  int
		CheckInterval     int
		TCPTimeout        int
		UDPTimeout        float64
		WanNic            string
		LanNic            string
		LipsAllocateType  string
		LipsAmount        int
	} `mapstructure:"addconfig"`
	Precheck       PrecheckConfig `mapstructure:"precheck"`
	VersionListLen int
	ResourceTree   []string `mapstructure:"tree"`
}

type Kernel struct {
	Versions []string `mapstructure:"versions"`
}

func (k *Kernel) IsSupported(version string) bool {
	return slice.Contain(k.Versions, version)
}

func (c *NLBConfig) Servers() *Service {
	return &c.Service.Servers
}
