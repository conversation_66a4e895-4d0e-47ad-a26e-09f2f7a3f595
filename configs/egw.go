package configs

import (
	"strings"
)

type EGWConfig struct {
	Platform  string
	Kube      Kube      `mapstructure:"kube"`
	Provision Provision `mapstructure:"provision"`
	SWP       struct {
		Template SWPTemplate
	}
	SMAP         SMAP          `mapstructure:"smap"`
	Service      ServiceConfig `mapstructure:"service"`
	ResourceTree []string      `mapstructure:"tree"`
}

// IsStateDisable specified state disable or not
func (c *EGWConfig) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

func (c *EGWConfig) Env() string {
	return strings.ToLower(GetEnv())
}

func (c *EGWConfig) IsLiveEnv() bool {
	return strings.EqualFold(c.Env(), "live")
}

func (c *EGWConfig) Servers() *Service {
	return &c.Service.Servers
}
