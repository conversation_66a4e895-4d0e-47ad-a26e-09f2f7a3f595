dns:
  service:
    dpdkdns:
      id: 11681
nlb:
  service:
    servers:
      id: 125304
  swp:
    template:
      cluster_online_node: "[nlb]_cluster_node_online"
      cluster_offline_node: "[nlb]_cluster_node_offline"
alb:
  kube:
    leader:
      concurrent: 1
  service:
    nginx:
      id: 11681
  swp:
    template:
      cluster_add_node: opsplatform_add_alb_nodes_test_qzd
      cluster_block_traffic: opsplatform_block_cluster_traffic
      cluster_open_traffic: opsplatform_open_cluster_traffic
      block_by_domains: opsplatform_block_traffic_by_domains
      open_by_domains: opsplatform_open_traffic_by_domains
  precheck:
    nginx_test_timeout: 60

  allow_blocking_groups:
    - SGW SRE
    - SPP SRE
    - APCC SRE
    - SPM SRE
  allow_blocking_products:
    - ShopeePay
    - SPM
    - APCC
  tree:
    - shopee.az.traffic_scheduling.alb.nginx
    - seamoney.az.traffic_scheduling.alb.nginx
    - id_insurance.az.traffic_scheduling.alb.nginx

lcs:
  service:
    nginx:
      id: 126832
  swp:
    template:
      cluster_add_node: opsplatform_add_lcs_nodes
      cluster_online_node: online_alb_node
      cluster_offline_node: offline_alb_node
      hotupdate_node: opsplatform_update_alb_tocex_configure_item_2
  tree:
    - shopee.az.traffic_scheduling.lcs.nginx
    - seamoney.az.traffic_scheduling.lcs.nginx
    - id_insurance.az.traffic_scheduling.lcs.nginx

ext2:
  adr:
    groupIDs:
      sg2: rg-cjyg6loz # ext2-sg
  platform: az
  application: external
  function: nginx
  swp:
    template:
      cluster_add_node: opsplatform_add_ext2_node
      cluster_remove_node: sgw_retire_ext2_node
      cluster_online_node: external2_cluster_node_online/offline
      cluster_offline_node: external2_cluster_node_online/offline
  tree:
    - shopee.az.traffic_scheduling.external.nginx
    - seamoney.az.traffic_scheduling.external.nginx
    - id_insurance.az.traffic_scheduling.external.nginx


kls:
  kube:
    leader:
      concurrent: 1
  service:
    nginx:
      id: 11681
  swp:
    template:
      cluster_add_node: opsplatform_add_kls_nodes
      cluster_remove_node: opsplatform_remove_kls_nodes
      hotupdate_node: opsplatform_hotupdate_kls_nodes
  tree:
    - shopee.az.traffic_scheduling.keyless.nginx
    - seamoney.az.traffic_scheduling.keyless.nginx
    - id_insurance.az.traffic_scheduling.keyless.nginx

nat:
  addconfig:
    StatusChangeLimit: 3
    CtrlPollInterval: 5
    CheckInterval: 5
    TcpTimeout: 3
    UdpTimeout: 0.5
    WanNic: "dpdk0"
    LanNic: "dpdk1"

anchor:
  swp:
    template:
      cluster_deploy_node: opsplatform_add_anchor_nodes_1
      cluster_remove_node: opsplatform_retire_anchor_nodes
      cluster_online_node: opsplatform_online_anchor_nodes
      cluster_offline_node: opsplatform_offline_anchor_nodes

mgmt:
  space: &space
    host: https://space.test.shopee.io
  anchor:
    <<: *space
  # ndmp:
  #   <<: *space
  arti:
    <<: *space
  seatalk:
    host: https://openapi.seatalk.io
    bot:
      sre: XkJceeEgRPWt9-IGF8RThg
      mre: rjs6XuCgQqui8Ryd4dCC2g
  alb:
    host: https://alb.test.shopeemobile.com
  nlb:
    host: https://nlb.test.shopeemobile.com
    addconfig:
      StatusChangeLimit: 3
      CtrlPollInterval: 5
      CheckInterval: 5
      TcpTimeout: 3
      UdpTimeout: 0.5
      WanNic: "dpdk0"
      LanNic: "dpdk1"
  nat:
    host: https://nlb.test.shopeemobile.com
  lcs:
    host: https://alb.test.shopeemobile.com
  cert:
    <<: *space
  dns:
    host: https://api.dns.test.shopeemobile.com
    bot:
      user: Shopee_Gateway
      password:
      email: <EMAIL>
  adr:
    <<: *space
  ops:
    host: https://opsplatform.sto.test.shopee.io
  status:
    host: https://status.sto.test.shopee.io
    token:
  timeout: 60
  smap:
    host: https://query.smap.test.shopee.io
  see:
    host: https://see.test.shopee.io
  res:
    host: https://tageditor.sto.test.shopee.io
  bot:
    users:
      - Shopee_Gateway
      - opsplatform
    group:
      allow:
        - "[DNS] DNS Admins"
        - "DNS Test Owner Group"

db-conf: &db-conf
  name: shopee_ops_platform_db
  port: 6606
  user: sg_sto_test
  password:
  MaxIdleConns: 10
  MaxOpenConns: 10
  DialTimeout: 5s
databases:
  ops.write:
    address: master.ed8a297a00fa4130.mysql.cloud.test.shopee.io:6606
    <<: *db-conf
  ops.read:
    address: shadow.ed8a297a00fa4130.mysql.cloud.test.shopee.io:6606
    <<: *db-conf

redis:
  opsplatform:
    addr: 8lyza.elasticredis.cloud.shopee.io:13846

e2e:
  dns:
    node: **************
    cluster: dns-unbound-dev-sg2
    az: ap-sg-1-general-x
    segment: General
    env: test
    cluster_id: 1
  nlb:
    node: **************
    node2: **************
    shopee:
      node: **************
      cluster: NLB.StdFourBondMix1.test.ops90
      uuid: 5a962fff-be21-5cf7-8c62-1d0664bec1a5
      az: ap-sg-1-general-z
      segment: General
      env: test
      rz: sg90
  nat:
    shopee:
      node: **************
      az: ap-sg-1-general-z
      segment: General
      env: test
      rz: sg90
      uuid: e9326d1b-c0fa-5e2e-91c7-007ba7adbe89
      cluster: phy_cluster_for_qa_test
  alb:
    node: ************ #alb.stdwan2.ap-sg-1-private-y.SeaMoney-Nonlive
    node2: ********** #alb.opsplatform.ap-sg-1-general-z.General.sg90.test
    cluster2: alb.opsplatform.ap-sg-1-general-z.General.sg90.test
    uuid2: 2711aeb5-348f-5e65-b660-72d5b8ea56db
    shopee:
      node: **********
      cluster: alb.opsplatform.ap-sg-1-general-z.General.sg90.test
      uuid: 2711aeb5-348f-5e65-b660-72d5b8ea56db
      az: ap-sg-1-general-z
      segment: General
      env: test
      rz: sg90
    monee:
      node: 10.60.1.227
      cluster: alb.internal.ap-sg-1-private-g.SI-SEA
      uuid: 2e63d55b-82bc-5692-b219-b81d293ec334
      az: ap-my-1-private-a
      segment: ShopeePay-MY
      env: staging
      rz: my0
    idfs:
      node: ************
      cluster: alb.stdwan11.id5.live
      uuid: a9364968-5805-5219-ab07-5e4fd1fe5d48
      az: ap-id-1-private-g
      segment: ShopeePay-ID
      env: live
      rz: id5

  etcd:
    node: 10.214.87.70 #etcd.ct4.anchor
    node2: 10.71.1.110 #etcd.opsplatform.sg90.test node1
    cluster2: opsplatform
    uuid2: 138c9a5a-dd26-5f75-a0b0-0f17f2d3a648
    shopee:
      node: 10.71.1.110
      cluster: opsplatform
      uuid: 138c9a5a-dd26-5f75-a0b0-0f17f2d3a648
      az: ap-sg-1-general-z
      segment: General
      env: test
      rz: sg90
  ext2:
    node: ************
    az: ap-sg-1-general-z
    segment: General
    env: test
    rz: sg90
    uuid: a48fc715-5a07-5f12-ae0e-0cfd68fe5e50
    cluster: ext2.opsplatform.ap-sg-1-general-z.General

  zk:
    node: ********** #zk.opsplatform.sg90.test node1
    cluster: opsplatform
  vgw:
    node2: ************ #spare node
    cluster2: anchor-ap-sg-1-general-b-General-ct4 # test env cluster
    uuid2: ee4e3355-8e79-4b41-b272-d8d1053d3db4 #test env uuid
  egw:
    node2: *************
    cluster2: ops1
  eip:
    node2: ************
    cluster2: test-eip-01
    uuid2: test-eip-01
  anchor:
    shopee:
      node: *************
      cluster: anchor-ap-sg-1-general-c-General-ct1
      uuid: bcac1b45-7afb-4d51-8cb0-1b7180f11b3b
      az: ap-sg-1-general-b
      segment: General
      env: live
      rz: ct1
  lcs:
    node2: ***********
    cluster2: lcs.opsplatform.sg90.test
    uuid2: 8c339772-a670-5edd-b2f7-5a89bc89a0ee
