package configs

import (
	"strings"
)

type VGWConfig struct {
	Platform  string
	Kube      Kube      `mapstructure:"kube"`
	Provision Provision `mapstructure:"provision"`
	SWP       struct {
		Template SWPTemplate
	}
	SMAP         SMAP          `mapstructure:"smap"`
	Service      ServiceConfig `mapstructure:"service"`
	ResourceTree []string      `mapstructure:"tree"`
}

// IsStateDisable specified state disable or not
func (c *VGWConfig) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

func (c *VGWConfig) Env() string {
	return strings.ToLower(GetEnv())
}

func (c *VGWConfig) IsLiveEnv() bool {
	return strings.EqualFold(c.Env(), "live")
}

func (c *VGWConfig) Servers() *Service {
	return &c.Service.Servers
}
