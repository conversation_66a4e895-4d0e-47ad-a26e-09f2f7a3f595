package configs

// SWPTemplate is a template list on SWP
type SWPTemplate struct {
	ClusterAddNode     string `mapstructure:"cluster_add_node"`
	ClusterRefresh     string `mapstructure:"cluster_refresh"`
	ClusterDeployNode  string `mapstructure:"cluster_deploy_node"`
	ClusterRemoveNode  string `mapstructure:"cluster_remove_node"`
	HotUpdateNode      string `mapstructure:"hotupdate_node"`
	ClusterOnlineNode  string `mapstructure:"cluster_online_node"`
	ClusterOfflineNode string `mapstructure:"cluster_offline_node"`
	MAProcess          string `mapstructure:"ma_process"`
}

type DNSSWPTemplate struct {
	ClusterAddNode     string `mapstructure:"cluster_add_node"`
	ClusterDeployNode  string `mapstructure:"cluster_deploy_node"`
	ClusterRemoveNode  string `mapstructure:"cluster_remove_node"`
	ClusterOnlineNode  string `mapstructure:"cluster_online_node"`
	ClusterOfflineNode string `mapstructure:"cluster_offline_node"`
	HotUpdateNode      string `mapstructure:"hotupdate_node"`
}

type ALBSWPTemplate struct {
	ClusterAddNode      string `mapstructure:"cluster_add_node"`
	ClusterFreshConfig  string `mapstructure:"cluster_fresh_config"`
	ClusterBlockTraffic string `mapstructure:"cluster_block_traffic"`
	ClusterOpenTraffic  string `mapstructure:"cluster_open_traffic"`
	ClusterRemoveNode   string `mapstructure:"cluster_remove_node"`
	HotUpdateNode       string `mapstructure:"hotupdate_node"`
	ClusterOnlineNode   string `mapstructure:"cluster_online_node"`
	ClusterOfflineNode  string `mapstructure:"cluster_offline_node"`
	BlockByDomains      string `mapstructure:"block_by_domains"`
	OpenByDomains       string `mapstructure:"open_by_domains"`
}

// LCSSWPTemplate is a template list on SWP
type LCSSWPTemplate struct {
	ClusterAddNode     string `mapstructure:"cluster_add_node"`
	ClusterRemoveNode  string `mapstructure:"cluster_remove_node"`
	ClusterOnlineNode  string `mapstructure:"cluster_online_node"`
	ClusterOfflineNode string `mapstructure:"cluster_offline_node"`
	HotUpdateNode      string `mapstructure:"hotupdate_node"`
}

type KLSSWPTemplate struct {
	ClusterAddNode    string `mapstructure:"cluster_add_node"`
	ClusterRemoveNode string `mapstructure:"cluster_remove_node"`
	HotUpdateNode     string `mapstructure:"hotupdate_node"`
}
