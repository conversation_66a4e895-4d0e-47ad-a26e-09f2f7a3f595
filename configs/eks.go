package configs

import "time"

// EKSConfig represents EKS configuration
type EKSConfig struct {
	Platform EKSPlatformConfig `mapstructure:"platform"`
}

// EKSPlatformConfig represents EKS Platform API configuration
type EKSPlatformConfig struct {
	BaseURL string        `mapstructure:"base_url"`
	Timeout time.Duration `mapstructure:"timeout"`
	Auth    EKSAuthConfig `mapstructure:"auth"`
}

// EKSAuthConfig represents EKS authentication configuration
type EKSAuthConfig struct {
	Type  string `mapstructure:"type"`  // "bearer" or "basic"
	Token string `mapstructure:"token"` // Bearer token or basic auth credentials
}

// GetEKSConfig returns the EKS configuration
func GetEKSConfig() *EKSConfig {
	return &Cfg.EKS
}
