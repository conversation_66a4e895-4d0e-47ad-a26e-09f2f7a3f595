package configs

import (
	"strings"
)

type Ext2Config struct {
	Platform    string
	Application string
	Function    string
	Verify      struct {
		Disable bool
	}
	<PERSON>be      <PERSON>be          `mapstructure:"kube"`
	Provision Provision     `mapstructure:"provision"`
	Service   ServiceConfig `mapstructure:"service"`
	Business  struct {
		SGW Business `mapstructure:"sgw"`
	} `mapstructure:"business"`
	SWP struct {
		Template SWPTemplate
	}
	SMAP      SMAP `mapstructure:"smap"`
	HotUpdate struct {
		Concurrent int
	} `mapstructure:"hotupdate"`
	Rollback struct {
		Concurrent int
	} `mapstructure:"rollback"`
	ADR struct {
		GroupIDs map[string]string `mapstructure:"groupIDs"`
	}
	Precheck                   PrecheckConfig `mapstructure:"precheck"`
	ResourceTree               []string       `mapstructure:"tree"`
	DefaultPrivateEtcdEndpoint string         `mapstructure:"defaultPrivateEtcdEndpoints"`
}

// IsStateDisable specified state disable or not
func (c *Ext2Config) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

func (c *Ext2Config) Env() string {
	return strings.ToLower(GetEnv())
}

func (c *Ext2Config) IsLiveEnv() bool {
	return strings.EqualFold(c.Env(), "live")
}
