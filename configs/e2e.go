package configs

type E2EConfig struct {
	DNS    E2ENodeConfig   `mapstructure:"dns"`
	NLB    NLBE2EConfig    `mapstructure:"nlb"`
	NAT    NATE2EConfig    `mapstructure:"nat"`
	ALB    ALBE2EConfig    `mapstructure:"alb"`
	ETCD   ETCDE2EConfig   `mapstructure:"etcd"`
	ZK     E2ENodeConfig   `mapstructure:"zk"`
	Anchor AnchorE2EConfig `mapstructure:"anchor"`
	VGW    E2ENodeConfig   `mapstructure:"vgw"`
	EGW    E2ENodeConfig   `mapstructure:"egw"`
	EIP    E2ENodeConfig   `mapstructure:"eip"`
	LCS    E2ENodeConfig   `mapstructure:"lcs"`
	Ext2   E2ENodeConfig   `mapstructure:"ext2"`
}

type E2ENodeConfig struct {
	Node      string // seamoney private az
	Node2     string // shopee general az
	Cluster2  string
	UUID      string
	UUID2     string
	Cluster   string
	ClusterID int
	Segment   string
	AZ        string
	RZ        string
	Env       string
}

type NodeConfig struct {
	Cluster string
	UUID    string
	Node    string
	AZ      string
	Segment string
	RZ      string
	Env     string
}

type ALBE2EConfig struct {
	Node     string // seamoney private az
	Node2    string // shopee general az
	Cluster2 string
	UUID     string
	UUID2    string

	Shopee NodeConfig `mapstructure:"shopee"`
	Monee  NodeConfig `mapstructure:"monee"`
	IDFS   NodeConfig `mapstructure:"idfs"`
}

type NLBE2EConfig struct {
	Node  string // seamoney private az
	Node2 string // shopee general az

	Shopee NodeConfig `mapstructure:"shopee"`
	Monee  NodeConfig `mapstructure:"monee"`
}

type NATE2EConfig struct {
	Shopee NodeConfig `mapstructure:"shopee"`
}

type ETCDE2EConfig struct {
	Node     string // CT4
	Node2    string // shopee general az
	Cluster2 string
	UUID     string
	UUID2    string

	Shopee NodeConfig `mapstructure:"shopee"`
	Monee  NodeConfig `mapstructure:"monee"`
}

type AnchorE2EConfig struct {
	Shopee NodeConfig `mapstructure:"shopee"`
}
