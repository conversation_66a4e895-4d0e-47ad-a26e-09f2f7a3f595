package configs

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/cast"

	"git.garena.com/shopee/go-shopeelib/spacelib/models/monitor"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type SGWConfig struct {
	Port      uint      `mapstructure:"port"`
	TLS       TLS       `mapstructure:"tls"`
	Kube      Kube      `mapstructure:"kube"`
	Submodule Submodule `mapstructure:"submodule"`
	Bot       Bot       `mapstructure:"bot"`
}

// TLS struct for TLS
type TLS struct {
	Enable bool
	Cert   string
	Key    string
}

// IsTLSEnable enable TLS or not
func (c *SGWConfig) IsTLSEnable() bool {
	return c.TLS.Enable
}

type Submodule struct {
	ALB  string `mapstructure:"alb"`
	Etcd string `mapstructure:"etcd"`
	Bot  string `mapstructure:"bot"`
}

// ServePort return HTTP server port
func (c *SGWConfig) ServePort(product string) int {
	port := int(c.Port)
	if port == 0 {
		switch product {
		case consts.ALB:
			port = cast.ToInt(os.Getenv(fmt.Sprintf("PORT_%s", c.Submodule.ALB)))
		case consts.ETCD:
			port = cast.ToInt(os.Getenv(fmt.Sprintf("PORT_%s", c.Submodule.Etcd)))
		case consts.BOT:
			port = cast.ToInt(os.Getenv(fmt.Sprintf("PORT_%s", c.Submodule.Bot)))
		}
		if port == 0 {
			port = cast.ToInt(os.Getenv("PORT"))
			if port == 0 {
				port = 8080
			}
		}
	}

	return port
}

func (c *SGWConfig) Env() string {
	return strings.ToLower(GetEnv())
}

// IsTestMode current env is in Test or not
func (c *SGWConfig) IsTestMode() bool {
	return c.Env() == "test"
}

func (c *SGWConfig) IsDevMode() bool {
	return c.Env() == "dev"
}

type ServiceConfig struct {
	Nginx   Service `mapstructure:"nginx"`
	Servers Service `mapstructure:"servers"`
	DpdkDNS Service `mapstructure:"dpdkdns"`
}

type Service struct {
	Name string
	ID   uint64
	SDU  struct {
		Central SDU
	}
}

type SDU struct {
	Name string
	Env  string
	IDC  string
}

type SMAP struct {
	Exporter map[string]SMAPExporterConfig
}

func (s *SMAP) PortConfigs() []monitor.PortConfig {
	var cfgs []monitor.PortConfig

	for name, exp := range s.Exporter {
		cfg := monitor.PortConfig{
			Name:           name,
			MetricsPath:    "",
			MetricsScheme:  exp.Scheme,
			ScrapeInterval: exp.Interval,
			Token:          "",
			Port:           exp.Port,
		}

		cfgs = append(cfgs, cfg)
	}

	return cfgs
}

func (s *SMAP) Exporters() map[string]map[string]int {
	exporters := make(map[string]int)
	for _, cfg := range s.PortConfigs() {
		exporters[cfg.Name] = cfg.Port
	}

	ports := make(map[string]map[string]int)
	ports["port"] = exporters

	return ports
}
