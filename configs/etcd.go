package configs

import (
	"strings"
)

type ETCDConfig struct {
	Platform     string
	ResourceTree []string  `mapstructure:"tree"`
	Kube         Kube      `mapstructure:"kube"`
	Provision    Provision `mapstructure:"provision"`
	Service      struct {
		ETCD Service `mapstructure:"etcd"`
	} `mapstructure:"service"`
	SWP struct {
		Template SWPTemplate
	}
	Precheck PrecheckConfig `mapstructure:"precheck"`
}

// IsStateDisable specified state disable or not
func (c *ETCDConfig) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

func (c *ETCDConfig) Env() string {
	return strings.ToLower(GetEnv())
}

func (c *ETCDConfig) IsLiveEnv() bool {
	return strings.EqualFold(c.Env(), "live")
}

func (c *ETCDConfig) ETCD() *Service {
	return &c.Service.ETCD
}
