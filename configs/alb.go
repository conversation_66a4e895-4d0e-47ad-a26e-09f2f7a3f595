package configs

import (
	"strings"
	"time"
)

type ALBConfig struct {
	Platform    string
	Application string
	Function    string
	Verify      struct {
		Disable bool
	}
	Kube      Kube          `mapstructure:"kube"`
	Provision Provision     `mapstructure:"provision"`
	Service   ServiceConfig `mapstructure:"service"`
	Business  SGWBusiness   `mapstructure:"business"`
	Cert      struct {
		Etcd     ALBCertEtcd `mapstructure:"etcd"`
		Scertmsd Scertmsd    `mapstructure:"scertmsd"`
	}
	SWP struct {
		Template ALBSWPTemplate
	}
	SMAP      SMAP `mapstructure:"smap"`
	HotUpdate struct {
		Concurrent int
	} `mapstructure:"hotupdate"`
	Rollback struct {
		Concurrent int
	} `mapstructure:"rollback"`
	Precheck struct {
		PrecheckConfig          `mapstructure:",squash"`
		NginxTestTimeout        int `mapstructure:"nginx_test_timeout"`
		RestartSleepTimeSeconds int `mapstructure:"restart_sleep_time_seconds"`
		OverallTimeout          int `mapstructure:"overall_timeout"`
	} `mapstructure:"precheck"`
	AllowBlockingGroups   []string `mapstructure:"allow_blocking_groups"`
	AllowBlockingProducts []string `mapstructure:"allow_blocking_products"`

	ResourceTree []string `mapstructure:"tree"`

	Version struct {
		Etcdctl string `mapstructure:"etcdctl"`
	} `mapstructure:"version"`
}

type SGWBusiness struct {
	SGW Business `mapstructure:"sgw"`
}

type Provision struct {
	Disable []string
}

// IsStateDisable specified state disable or not
func (c *ALBConfig) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

func (c *ALBConfig) Env() string {
	return strings.ToLower(GetEnv())
}

func (c *ALBConfig) IsLiveEnv() bool {
	return strings.EqualFold(c.Env(), "live")
}

func (c *ALBConfig) NginxTestTimeout() time.Duration {
	return time.Duration(c.Precheck.NginxTestTimeout) * time.Second
}

// RestartSleepTime returns the restart sleep time duration
func (c *ALBConfig) RestartSleepTime() time.Duration {
	const defaultSleepTime = 30 * time.Second
	if c.Precheck.RestartSleepTimeSeconds > 0 {
		return time.Duration(c.Precheck.RestartSleepTimeSeconds) * time.Second
	}

	return defaultSleepTime
}

func (c *ALBConfig) PrecheckOverallTimeout() time.Duration {
	const multiplier = 5
	if c.Precheck.OverallTimeout == 0 {
		// 5 times of default timeout
		return time.Duration(c.Precheck.NginxTestTimeout * multiplier)
	}

	return time.Duration(c.Precheck.OverallTimeout) * time.Second
}

func (c *ALBConfig) GetAllowBlockingGroups() map[string]struct{} {
	m := make(map[string]struct{})
	for _, g := range c.AllowBlockingGroups {
		m[g] = struct{}{}
	}

	return m
}

func (c *ALBConfig) GetAllowBlockingProducts() map[string]struct{} {
	m := make(map[string]struct{})
	for _, g := range c.AllowBlockingProducts {
		m[g] = struct{}{}
	}

	return m
}

type KubeLease struct {
	Name      string
	Namespace string
}

type KubeLimit struct {
	QPS   float32
	Burst int
}

type KubeLeader struct {
	Name             string
	LeaseSec         uint `mapstructure:"lease_duration"`
	RenewDeadlineSec uint `mapstructure:"renew_deadline"`
	RetryPeriodSec   uint `mapstructure:"retry_period"`
	LaunchTimeoutSec uint `mapstructure:"launch_timeout"`
	Concurrent       int  `mapstructure:"concurrent"`
}

func (k *KubeLeader) LeaseDuration() time.Duration {
	return time.Duration(k.LeaseSec) * time.Second
}

func (k *KubeLeader) RenewDeadline() time.Duration {
	return time.Duration(k.RenewDeadlineSec) * time.Second
}

func (k *KubeLeader) RetryPeriod() time.Duration {
	return time.Duration(k.RetryPeriodSec) * time.Second
}

func (k *KubeLeader) LaunchTimeout() time.Duration {
	return time.Duration(k.LaunchTimeoutSec) * time.Second
}

type KubeSync struct {
	IntervalSec     uint `mapstructure:"interval"`
	MAIntervalSec   uint `mapstructure:"ma_interval"`
	SyncIntervalSec uint `mapstructure:"sync_interval"`
}

func (k *KubeSync) Interval() time.Duration {
	return time.Duration(k.IntervalSec) * time.Second
}

func (k *KubeSync) MAInterval() time.Duration {
	return time.Duration(k.MAIntervalSec) * time.Second
}

func (k *KubeSync) SyncInterval() time.Duration {
	return time.Duration(k.SyncIntervalSec) * time.Second
}

type KubeWorkerQueue struct {
	Name string
}

type Kube struct {
	Disable     bool
	Kind        string
	APIVersion  string `mapstructure:"api_version"`
	Lease       KubeLease
	Limit       KubeLimit
	Leader      KubeLeader
	Sync        KubeSync
	WorkerQueue KubeWorkerQueue
}

func (c *ALBConfig) Nginx() *Service {
	return &c.Service.Nginx
}

type Business struct {
	Name string
}

func (c *ALBConfig) BizSGW() *Business {
	return &c.Business.SGW
}

type SMAPExporterConfig struct {
	Port     int
	Scheme   string
	Interval int
}

type ALBCertEtcd struct {
	Live    string
	NonLive string `mapstructure:"nonlive"`
}

func (c *ALBConfig) EtcdCert() *ALBCertEtcd {
	return &c.Cert.Etcd
}

type Scertmsd struct {
	AccessKey string `mapstructure:"accessKey"`
	SecretKey string `mapstructure:"secretKey"`
	Host      string `mapstructure:"host"`
}
