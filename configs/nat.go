package configs

type NATConfig struct {
	Platform    string
	Application string
	Function    string
	Kube        <PERSON>be          `mapstructure:"kube"`
	Kernel      Kernel        `mapstructure:"kernel"`
	Service     ServiceConfig `mapstructure:"service"`
	SMAP        SMAP          `mapstructure:"smap"`
	SWP         struct {
		Template SWPTemplate
	}
	AddConfig struct {
		StatusChangeLimit int
		CtrlPollInterval  int
		CheckInterval     int
		TCPTimeout        int
		UDPTimeout        float64
		WanNic            string
		LanNic            string
	} `mapstructure:"addconfig"`
	Precheck       PrecheckConfig `mapstructure:"precheck"`
	VersionListLen int
	ResourceTree   []string `mapstructure:"tree"`
}

func (c *NATConfig) Servers() *Service {
	return &c.Service.Servers
}
