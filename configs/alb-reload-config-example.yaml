# ALB Reload Configuration Example
# This file shows how to configure the ALB nginx reload functionality

alb:
  # Enable or disable the ALB reload functionality
  reload:
    enable: true
    
    # User to Business Unit (BU) mapping
    # Each user email is mapped to their respective BU
    # Only users listed here can perform reload operations
    user_bu_map:
      - email: "<EMAIL>"
        bu: "payments"
      - email: "<EMAIL>"
        bu: "checkout"
      - email: "<EMAIL>"
        bu: "search"
      - email: "<EMAIL>"
        bu: "recommendations"
      - email: "<EMAIL>"
        bu: "payments"
      - email: "<EMAIL>"
        bu: "infrastructure"
    
    # Rate limiting configuration
    rate_limit:
      # Time window in minutes for rate limiting
      window_minutes: 5
      # Maximum number of reload requests allowed per BU within the window
      max_requests: 1
    
    # Audit logging configuration
    audit_log:
      # Enable or disable audit logging
      enable: true
      # Path to the audit log file
      file_path: "/var/log/sgw-addon-operator/alb-reload-audit.log"
      # Maximum size of log file in MB before rotation
      max_size_mb: 100
      # Maximum age of log files in days before deletion
      max_age_days: 30
    
    # Nginx command configuration
    nginx_command:
      # The command to execute for nginx reload
      # Only predefined safe commands are allowed for security
      command: "nginx -s reload"
      # Timeout for the command execution in seconds
      timeout_seconds: 30

# Example of additional ALB configuration sections
# (These are existing configurations, shown for context)

# ALB general configuration
platform: "alb"
application: "alb"
function: "lb"

# Verification settings
verify:
  disable: false

# Service configuration
service:
  nginx:
    port: 80
    ssl_port: 443

# Business configuration
business:
  sgw:
    name: "sgw"

# Precheck configuration
precheck:
  nginx_test_timeout: 30
  restart_sleep_time_seconds: 30
  overall_timeout: 150

# Hot update configuration
hotupdate:
  concurrent: 5

# Rollback configuration
rollback:
  concurrent: 3

# Version configuration
version:
  etcdctl: "v3.5.0"

# Resource tree
tree:
  - "alb"
  - "nginx"
  - "metrics"

# Notes:
# 1. The reload functionality must be explicitly enabled by setting reload.enable to true
# 2. Users must be explicitly mapped to their BUs in the user_bu_map section
# 3. Rate limiting prevents abuse by allowing only 1 request per BU every 5 minutes by default
# 4. Audit logging records all reload operations for security and compliance
# 5. Only safe nginx commands are allowed to prevent command injection attacks
# 6. All reload operations are executed using tocex on the target ALB nodes
# 7. Users can only reload nginx on nodes belonging to their assigned BU
