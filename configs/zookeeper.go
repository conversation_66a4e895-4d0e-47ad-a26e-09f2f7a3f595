package configs

import (
	"strings"
)

type ZKConfig struct {
	Platform     string
	ResourceTree []string  `mapstructure:"tree"`
	Kube         Kube      `mapstructure:"kube"`
	Provision    Provision `mapstructure:"provision"`
	Service      struct {
		ZK Service `mapstructure:"zk"`
	} `mapstructure:"service"`
	SWP struct {
		Template SWPTemplate
	}
	Metrics  SMAPExporterConfig `mapstructure:"metrics"`
	Precheck PrecheckConfig     `mapstructure:"precheck"`
}

// IsStateDisable specified state disable or not
func (c *ZKConfig) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

func (c *ZKConfig) Env() string {
	return strings.ToLower(GetEnv())
}

func (c *ZKConfig) IsLiveEnv() bool {
	return strings.EqualFold(c.Env(), "live")
}

func (c *ZKConfig) ZK() *Service {
	return &c.Service.ZK
}
