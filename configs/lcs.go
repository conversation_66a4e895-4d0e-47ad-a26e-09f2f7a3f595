package configs

import "strings"

type LCSConfig struct {
	Platform    string
	Application string
	Function    string
	Verify      struct {
		Disable bool
	}
	Kube      Kube          `mapstructure:"kube"`
	Provision Provision     `mapstructure:"provision"`
	Service   ServiceConfig `mapstructure:"service"`
	Business  struct {
		SGW Business `mapstructure:"sgw"`
	} `mapstructure:"business"`
	Cert struct {
		Etcd     LCSCertEtcd `mapstructure:"etcd"`
		Scertmsd LCSScertmsd `mapstructure:"scertmsd"`
	}
	SWP struct {
		Template LCSSWPTemplate
	}
	SMAP      SMAP `mapstructure:"smap"`
	HotUpdate struct {
		Concurrent int
	} `mapstructure:"hotupdate"`
	Rollback struct {
		Concurrent int
	} `mapstructure:"rollback"`

	Precheck     PrecheckConfig `mapstructure:"precheck"`
	ResourceTree []string       `mapstructure:"tree"`

	Version struct {
		Etcdctl string `mapstructure:"etcdctl"`
	} `mapstructure:"version"`
}

type LCSCertEtcd struct {
	Live    string
	NonLive string `mapstructure:"nonlive"`
}

type LCSScertmsd struct {
	AccessKey string `mapstructure:"accessKey"`
	SecretKey string `mapstructure:"secretKey"`
	Host      string `mapstructure:"host"`
}

// IsStateDisable specified state disable or not
func (c *LCSConfig) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

func (c *LCSConfig) BizSGW() *Business {
	return &c.Business.SGW
}

func (c *LCSConfig) Nginx() *Service {
	return &c.Service.Nginx
}

func (c *LCSConfig) EtcdCert() *LCSCertEtcd {
	return &c.Cert.Etcd
}

func (c *LCSConfig) IsLiveEnv() bool {
	return strings.EqualFold(c.Env(), "live")
}

func (c *LCSConfig) Env() string {
	return strings.ToLower(GetEnv())
}
