package configs

import "strings"

type DNSConfig struct {
	Platform    string
	Application string
	Function    string
	Kube        Kube      `mapstructure:"kube"`
	Provision   Provision `mapstructure:"provision"`
	Service     struct {
		DpdkDNS Service `mapstructure:"dpdkdns"`
	} `mapstructure:"service"`
	Business SGWBusiness          `mapstructure:"business"`
	BGP      map[string]BgpConfig `mapstructure:"bgp"`
	SMAP     SMAP                 `mapstructure:"smap"`
	SWP      struct {
		Template DNSSWPTemplate
	}
	Config       DpdkDNSConfig  `mapstructure:"config"`
	Precheck     PrecheckConfig `mapstructure:"precheck"`
	ResourceTree []string       `mapstructure:"tree"`
}

func (c *DNSConfig) BizSGW() *Business {
	return &c.Business.SGW
}

func (c *DNSConfig) DpdkDNS() *Service {
	return &c.Service.DpdkDNS
}

// IsStateDisable specified state disable or not
func (c *DNSConfig) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

type DpdkDNSConfig struct {
	Path struct {
		Template string
		Values   string
	}
	Agent struct {
		BaseURLWAN string   `mapstructure:"base_url_wan"`
		BaseURLLAN string   `mapstructure:"base_url_lan"`
		SDULAN     []string `mapstructure:"sdu_lan"`
		MinBackup  int      `mapstructure:"min_backup"`
		MaxBackup  int      `mapstructure:"max_backup"`
		Metrics    struct {
			Port        int `mapstructure:"port"`
			PrivatePort int `mapstructure:"private_port"`
		}
	}
	Unbound struct {
		Port    int    `mapstructure:"port"`
		Forward string `mapstructure:"forward"`
	}
}
