mgmt:
  cloud:
    az:
      - ap-sg-3-general-m
      - ap-hk-2-general-m
    rz:
      - aws0
      - gcphk
  sentry:
    host: https://langley.razes.shopee.io/3
    user: b216508e16164e3ea21de8854a3c69fc
    password:
  space: &space
    host: https://space.shopee.io
  anchor:
    <<: *space
  ndmp:
    <<: *space
  ipam:
    <<: *space
  dod:
    host: https://dod.shopee.io
    teamid:
      mre:
        seamoney: 246
        az: 252
      sre:
        az: 88
  seatalk:
    host: https://openapi.seatalk.io
    bot:
      sre: XkJceeEgRPWt9-IGF8RThg
      mre: gRQyQYsASJOKndgCF2YIJA
    app:
      id: MDE4MjY1MzAwMjEx
      secret:
  alb:
    host: https://alb.shopeemobile.com
  nlb:
    host: https://nlb.shopeemobile.com
  nat:
    host: https://nlb.shopeemobile.com
  lcs:
    host: https://alb.shopeemobile.com
  dns:
    host: https://api.dns.shopeemobile.com
    bot:
      user: DNSBot
      password:
      email: <EMAIL>
  adr:
    <<: *space
  aptly:
    <<: *space
  cert:
    <<: *space
  sgw:
    bot:
      user: sgw_sre
      password:
      email: <EMAIL>
  eks:
    bot:
      user: opsplatform
      password:
      email: <EMAIL>
  ops:
    host: https://opsplatform.sto.shopee.io
    bot:
      user: opsplatform
      password:
      email: <EMAIL>
    event: &event
      pool_size: 5
      consumer: 2
      limit: 200
    task:
      <<: *event
  harbor:
    live:
      domain: harbor.shopeemobile.com
      host: https://origin.harbor.shopeemobile.com
      password:
      user: robot$opsplatform
      auth:
    test:
      domain: harbor.test.shopeemobile.com
      host: https://origin.harbor.test.shopeemobile.com
      password:
      user: robot$opsplatform
      auth:
  arti:
    <<: *space
  status:
    host: https://status.sto.shopee.io
    token:
  gdrc:
    secret:
    mode:
      auto:
        secret:
      manual:
        secret:
  see:
    host: https://see.shopee.io
  res:
    host: https://tageditor.sto.shopee.io
  jira:
    host: https://jira.shopee.io
  timeout: 80
  timeout2: 3
  retries: 3
  tocex:
    token:
      live:
      nonlive:
  rc:
    host: https://rc.infra.garenanow.com
    token:
  smap:
    host: https://query.smap.shopee.io
    step: 15
  shadowcat:
    host: https://shadowcat.razes.shopee.io/vm-portal/universal-auth/select/0/prometheus
    step: 15
    username: sgw_sre
    password:
  bot:
    user: goshopeelibbot
    password:
    email: <EMAIL>
    group:
      allow:
        - SGW SRE
    noti:
      user: noti.bot
      email: <EMAIL>
      password:
  netplan:
    - /etc/netplan/02-wanip.yaml
    - /etc/netplan/02-netcfg.yaml
    - /etc/netplan/01-netcfg.yaml
  docker:
    port: 7070
  bgp:
    local_as: 65000
    remote_as: 4200080000
    hold_time: 60
    keepalive_time: 20
  office:
    time:
      start: 09:30am
      end: 07:00pm

redis:
  opsplatform:
    addr:
    username:
    password:
    db: 0
    max_retries: 1
    pool_size: 10
    min_idle_conns: 3
    max_conn_age: 100

sgw:
  kube: &kube
    disable: false
    limit:
      qps: 1000
      burst: 1000
    api_version: machine.shopee.io/v1alpha1
    sync:
      interval: 20
      ma_interval: 600
      sync_interval: 600
  leader: &leader
    lease_duration: 5
    renew_deadline: 3
    retry_period: 1
    launch_timeout: 1
    concurrent: 10
  submodule:
    alb: albcontroller
    etcd: etcdcontroller
    bot: chatbot
  business: &business
    sgw:
      name: engineering_infra.infra_products.sgw
  bot:
    user: sgw_doctor
    email: <EMAIL>
    password:

vgw:
  kube:
    <<: *kube
    kind: VGW
    lease:
      name: vgw-operator-lease
      namespace: kube-system
    leader:
      name: vgw-operator
      <<: *leader
    workerqueue:
      name: vgw
  swp:
    template:
      cluster_deploy_node: opsplatform_add_vgw_nodes
      cluster_remove_node: opsplatform_retire_vgw_nodes
      cluster_online_node: opsplatform_online_vgw_nodes
      cluster_offline_node: opsplatform_offline_vgw_nodes
  service:
    servers:
      id: 18814
      name: shopee.engineering_infra.ndre.virtual_network.anchor.gateway
  smap:
    exporter:
      node:
        port: 9100
      anchor-gateway:
        port: 8081
      anchor-gateway-exporter:
        port: 8082
      gateway-monitor:
        port: 8083
  platform: az
  application: anchor
  function: vgw
  tree:
    - shopee.az.sdn.anchor.vgw
    - shopee.az.bare_metal.engineering_infra_ndre.anchor
    - shopee.ndre.server_pool
    - seamoney.ndre.anchor
    - seamoney.az.sdn.anchor.vgw
    - seamoney.ndre.server_pool
    - id_insurance.az.sdn._main

egw:
  kube:
    <<: *kube
    kind: EGW
    lease:
      name: egw-operator-lease
      namespace: kube-system
    leader:
      name: egw-operator
      <<: *leader
    workerqueue:
      name: egw
  swp:
    template:
      cluster_deploy_node: opsplatform_add_egw_nodes
      cluster_remove_node: opsplatform_retire_egw_nodes
      cluster_online_node: opsplatform_online_egw_nodes
      cluster_offline_node: opsplatform_offline_egw_nodes
  service:
    servers:
      id: 18814
      name: shopee.engineering_infra.ndre.virtual_network.anchor.gateway
  smap:
    exporter:
      node:
        port: 9100
      anchor-gateway:
        port: 8081
      anchor-gateway-exporter:
        port: 8082
      gateway-monitor:
        port: 8083
  platform: az
  application: anchor
  function: anchor
  tree:
    - shopee.az.sdn.anchor.vgw
    - shopee.ndre.server_pool
    - seamoney.ndre.anchor
    - seamoney.ndre.server_pool
    - seamoney.az.sdn.anchor.vgw
    - id_insurance.az.sdn._main

eip:
  kube:
    <<: *kube
    kind: EIP
    lease:
      name: eip-operator-lease
      namespace: kube-system
    leader:
      name: eip-operator
      <<: *leader
    workerqueue:
      name: eip
  swp:
    template:
      cluster_deploy_node: opsplatform_add_eip_nodes
      cluster_remove_node: opsplatform_retire_eip_nodes
      cluster_online_node: opsplatform_online_eip_nodes
      cluster_offline_node: opsplatform_offline_eip_nodes
  service:
    servers:
      id: 66398
      name: shopee.engineering_infra.ndre.virtual_network.eip
  smap:
    exporter:
      node:
        port: 9100
      eip:
        port: 9652
      eip-proxy:
        port: 9662
      eip-eds:
        port: 9720
      host-networking-manager:
        port: 9126
  platform: az
  application: anchor
  function: eip
  tree:
    - shopee.az.sdn.anchor.control
    - shopee.ndre.server_pool
    - shopee.az.traffic_scheduling.etcd
    - shopee.az.bare_metal.engineering_infra_ndre.anchor

eks:
  platform:
    base_url: "https://eks.eks-platform.shopee.io"
    timeout: 30s
    auth:
      type: "bearer"
      token: ""

anchor:
  kube:
    <<: *kube
    kind: Anchor
    lease:
      name: anchor-operator-lease
      namespace: kube-system
    leader:
      name: anchor-operator
      <<: *leader
    workerqueue:
      name: anchor
  swp:
    template:
      cluster_deploy_node: opsplatform_add_anchor_nodes
      cluster_remove_node: opsplatform_retire_anchor_nodes
      cluster_online_node: opsplatform_online_anchor_nodes
      cluster_offline_node: opsplatform_offline_anchor_nodes
  service:
    servers:
      id: 18128
      name: shopee.engineering_infra.ndre.virtual_network.anchor.anchor_api
  smap:
    exporter:
      node:
        port: 9100
      anchor:
        port: 9626
      anchor-eds:
        port: 9710
      anchor-pinger:
        port: 8891
      host-networking-manager:
        port: 9126
  platform: az
  application: anchor
  function: anchor
  tree:
    - shopee.az.sdn.anchor.control
    - shopee.ndre.server_pool
    - shopee.az.traffic_scheduling.etcd
    - shopee.az.bare_metal.engineering_infra_ndre.anchor
    - seamoney.ndre.anchor
    - seamoney.ndre.server_pool
    - seamoney.az.sdn.anchor.control
    - seamoney.az.traffic_scheduling.etcd
    - id_insurance.az.sdn._main

dns:
  kube:
    <<: *kube
    kind: DNS
    lease:
      name: gslb-operator-lease
      namespace: kube-system
    leader:
      name: gslb-operator
      <<: *leader
    workerqueue:
      name: dns
  service:
    dpdkdns:
      id: 36392
      name: shopee.engineering_infra.infra_products.sgw.dns.dpdkdns
  business:
    <<: *business
  bgp:
    base:
      local_as: 65000
      remote_as: 4200080000
      hold_time: 14
      keepalive_time: 3
    vn:
      local_as: 64516
      remote_as: 65009
      hold_time: 14
      keepalive_time: 3
      password:
      neighbor:
        "10_84_243_44":
          - "10.84.242.10"
        "10_84_243_45":
          - "10.84.242.8"
        "10_84_243_46":
          - "10.84.242.6"
        "10_84_243_49":
          - "10.84.242.4"
      next_hop:
        "10_84_243_44": "10.84.242.11"
        "10_84_243_45": "10.84.242.9"
        "10_84_243_46": "10.84.242.7"
        "10_84_243_49": "10.84.242.5"
    tw1:
      local_as: 64516
      remote_as: 65009
      hold_time: 14
      keepalive_time: 3
      password:
      neighbor:
        "10_59_242_4":
          - "10.59.242.5"
        "10_59_243_196":
          - "10.59.242.7"
        "10_59_243_200":
          - "10.59.242.11"
        "10_59_243_202":
          - "10.59.242.13"
      next_hop:
        "10_59_242_4": "10.59.242.4"
        "10_59_243_196": "10.59.242.6"
        "10_59_243_200": "10.59.242.10"
        "10_59_243_202": "10.59.242.12"
  swp:
    template:
      cluster_add_node: opsplatform_add_dns_nodes
      cluster_deploy_node: opsplatform_deploy_dns_nodes
      cluster_remove_node: opsplatform_remove_dns_nodes
      cluster_online_node: opsplatform_online_dns_nodes
      cluster_offline_node: opsplatform_offline_dns_nodes
      hotupdate_node: opsplatform_hotupdate_dns_nodes
  smap:
    exporter:
      node:
        port: 9100
      cmds:
        port: 9105
      unbound:
        port: 9167
  config:
    Path:
      template: /etc/dpdkdns-config/templates
      values: /etc/dpdkdns-config/values
    agent:
      base_url_wan: https://143.92.88.8
      base_url_lan: https://10.250.0.19
      sdu_lan:
        - dns-unbound-live-apsg1gc87int
      min_backup: 100
      max_backup: 200
      metrics:
        port: 8090
        private_port: 9169
    unbound:
      port: 53
      forward: 8.8.8.8
  platform: az
  application: dns
  function: unbound
  tree:
    - shopee.az.traffic_scheduling.dns.unbound
    - seamoney.az.traffic_scheduling.dns.unbound
    - id_insurance.az.traffic_scheduling.dns.unbound

nlb:
  kube:
    <<: *kube
    kind: NLB
    lease:
      name: nlb-operator-lease
      namespace: kube-system
    leader:
      name: nlb-operator
      <<: *leader
    workerqueue:
      name: nlb
  service:
    servers:
      id: 6990
      name: shopee.engineering_infra.infra_products.sgw.nlb.servers
  swp:
    template:
      cluster_add_node: opsplatform_add_nlb_nodes
      cluster_remove_node: opsplatform_remove_nlb_nodes
      cluster_online_node: nlb_cluster_node_online
      cluster_offline_node: nlb_cluster_node_offline
      hotupdate_node: opsplatform_update_nlb_tocex_configure_item_1
  smap:
    exporter:
      node:
        port: 9100
      cmds:
        port: 9105
      sd:
        port: 9121
      dpvs:
        port: 9101
  kernel:
    versions:
      - 4.15.0
      - 5.4.0
      - 6.6.0
  platform: az
  application: nlb
  function: dpvs
  addconfig:
    StatusChangeLimit: 3
    CtrlPollInterval: 5
    CheckInterval: 5
    TcpTimeout: 3
    UdpTimeout: 0.5
    WanNic: "dpdk0"
    LanNic: "dpdk1"
    LipsAllocateType: "auto"
    LipsAmount: "8"
  VersionListLen: 200
  tree:
    - shopee.az.traffic_scheduling.nlb.dpvs
    - seamoney.az.traffic_scheduling.nlb.dpvs
    - id_insurance.az.traffic_scheduling.nlb.dpvs

nat:
  kube:
    <<: *kube
    kind: NAT
    lease:
      name: nat-operator-lease
      namespace: kube-system
    leader:
      name: nat-operator
      <<: *leader
    workerqueue:
      name: nat
  platform: az
  service:
    servers:
      id: 6990
      name: shopee.engineering_infra.infra_products.sgw.nat.servers
  swp:
    template:
      cluster_add_node: opsplatform_add_nat_nodes
      cluster_remove_node: opsplatform_remove_nat_nodes
      cluster_online_node: snat_node_online
      cluster_offline_node: snat_node_offline
      hotupdate_node: opsplatform_update_nat_tocex_configure_item_1
  smap:
    exporter:
      node:
        port: 9100
      cmds:
        port: 9105
      sd:
        port: 9121
      dpvs:
        port: 9101
  kernel:
    versions:
      - 4.15.0
      - 5.4.0
      - 6.6.0
  function: dpvs_sdn
  application: nat
  addconfig:
    StatusChangeLimit: 3
    CtrlPollInterval: 5
    CheckInterval: 5
    TcpTimeout: 3
    UdpTimeout: 0.5
    WanNic: "dpdk0"
    LanNic: "dpdk1"
  VersionListLen: 30
  tree:
    - shopee.az.traffic_scheduling.nat.dpvs
    - seamoney.az.traffic_scheduling.nat.dpvs
    - id_insurance.az.traffic_scheduling.nat.dpvs

alb:
  kube:
    <<: *kube
    kind: ALB
    lease:
      name: alb-operator-lease
      namespace: kube-system
    leader:
      name: alb-operator
      <<: *leader
    workerqueue:
      name: alb
  precheck:
    enable_node_worker_pool: true
    max_node_workers: 32
    max_checker_workers: 5
    node_queue_size: 128
    restart_sleep_time_seconds: 60
    overall_timeout: 899
  service:
    nginx:
      id: 6487
      name: shopee.engineering_infra.infra_products.sgw.alb.nginx
      sdu:
        central:
          name: alb-seamoney-live-central
          env: live
          idc: central
  business:
    <<: *business
  cert:
    etcd:
      live: wildcard.live.etcd-sgw-shopee-io.api.ISRG
      nonlive: wildcard.nonlive.etcd-sgw-shopee-io.api.ISRG
    scertmsd:
      accessKey:
      secretKey:
      host: proxy.uss.s3.sz.shopee.io
  swp:
    template:
      cluster_add_node: opsplatform_add_alb_nodes
      cluster_remove_node: opsplatform_remove_alb_nodes
      cluster_fresh_config: opsplatform_refresh_alb_cluster_config
      hotupdate_node: opsplatform_update_alb_tocex_configure_item
      cluster_block_traffic: opsplatform_block_cluster_traffic
      cluster_open_traffic: opsplatform_open_cluster_traffic
      block_by_domains: opsplatform_block_traffic_by_domains
      open_by_domains: opsplatform_open_traffic_by_domains
  smap:
    exporter:
      node:
        port: 9100
      cmds:
        port: 9105
      metrics:
        port: 9116
      sd:
        port: 9118
      sgw:
        port: 9119
      kernel:
        port: 9153
      host-networking-manager:
        port: 9126
        scheme: http
        interval: 15
  platform: az
  application: alb
  function: nginx
  hotupdate:
    concurrent: 10
  rollback:
    concurrent: 10
  instance:

  allow_blocking_groups:
    - SGW SRE
    - SPP SRE
    - APCC SRE
    - SPM SRE
  allow_blocking_products:
    - ShopeePay
    - SPM
    - APCC
  tree:
    - shopee.az.traffic_scheduling.alb.nginx
    - seamoney.az.traffic_scheduling.alb.nginx
    - id_insurance.az.traffic_scheduling.alb.nginx
  version:
    etcdctl: 3.4.30

kls:
  kube:
    <<: *kube
    kind: KLS
    lease:
      name: kls-operator-lease
      namespace: kube-system
    leader:
      name: kls-operator
      <<: *leader
    workerqueue:
      name: kls
  service:
    nginx:
      id: 67922
      name: shopee.engineering_infra.infra_products.sgw.keyless.nginx
      sdu:
  business:
    <<: *business
  cert:
    etcd:
      live: wildcard.live.etcd-sgw-shopee-io.api.ISRG
      nonlive: wildcard.nonlive.etcd-sgw-shopee-io.api.ISRG
    scertmsd:
      accessKey:
      secretKey:
      host: proxy.uss.s3.sz.shopee.io
  swp:
    template:
      cluster_add_node: opsplatform_add_kls_nodes
      cluster_remove_node: opsplatform_remove_kls_nodes
      hotupdate_node: opsplatform_hotupdate_kls_nodes


  smap:
    exporter:
      node:
        port: 9100
      cmds:
        port: 9105
      metrics:
        port: 9116
      sd:
        port: 9118
      sgw:
        port: 9119
      kernel:
        port: 9153
      host-networking-manager:
        port: 9126
        scheme: http
        interval: 15
  platform: az
  application: keyless
  function: nginx
  hotupdate:
    concurrent: 10
  rollback:
    concurrent: 10
  instance:

  tree:
    - shopee.az.traffic_scheduling.keyless.nginx
    - seamoney.az.traffic_scheduling.keyless.nginx
    - id_insurance.az.traffic_scheduling.keyless.nginx


etcd:
  kube:
    <<: *kube
    kind: Etcd
    lease:
      name: etcd-operator-lease
      namespace: kube-system
    leader:
      name: etcd-operator
      <<: *leader
    workerqueue:
      name: etcd
  swp:
    template:
      cluster_add_node: opsplatform_add_etcd_nodes
  platform: az
  tree:
    - shopee.az.traffic_scheduling.etcd
    - seamoney.az.traffic_scheduling.etcd
    - id_insurance.az.traffic_scheduling.etcd
    - shopee.az.traffic_scheduling.alb.etcd
    - seamoney.az.traffic_scheduling.alb.etcd
    - id_insurance.az.traffic_scheduling.alb.etcd

zk:
  kube:
    <<: *kube
    kind: Zookeeper
    lease:
      name: zk-operator-lease
      namespace: kube-system
    leader:
      name: zk-operator
      <<: *leader
    workerqueue:
      name: zk
  swp:
    template:
      cluster_add_node: opsplatform_add_zookeeper_nodes
  platform: az
  tree:
    - shopee.az.traffic_scheduling.zk
    - seamoney.az.traffic_scheduling.zk
    - id_insurance.az.traffic_scheduling.zk

ext2:
  kube:
    <<: *kube
    kind: Ext2
    lease:
      name: ext2-operator-lease
      namespace: kube-system
    leader:
      name: ext2-operator
      <<: *leader
    workerqueue:
      name: ext2
  platform: az
  application: external
  function: nginx
  adr:
    groupIDs:
      sg5: rg-f3c5j51o
      sg7: rg-f3c5j51o
  swp:
    template:
      cluster_add_node: opsplatform_add_ext2_node
      cluster_remove_node: sgw_retire_ext2_node
      cluster_online_node: external2_cluster_node_online/offline
      cluster_offline_node: external2_cluster_node_online/offline
  tree:
    - shopee.az.traffic_scheduling.external.nginx
    - seamoney.az.traffic_scheduling.external.nginx
    - id_insurance.az.traffic_scheduling.external.nginx

  defaultPrivateEtcdEndpoints: https://private-ext2-proxy.etcd.sgw.shopee.io:443

drc:
  kube:
    <<: *kube
    kind: SGWDR
    lease:
      name: drc-operator-lease
      namespace: kube-system
    leader:
      name: drc-operator
      <<: *leader
    workerqueue:
      name: drc
  fireDrill:
    size: 100
    cooldown: 120
    interval: 5
  cnproxy:
    nodes:
      - 10.178.225.33
      - 10.178.225.34
      - 10.178.225.35

lcs:
  kube:
    <<: *kube
    kind: LCS
    lease:
      name: lcs-operator-lease
      namespace: kube-system
    leader:
      name: lcs-operator
      <<: *leader
    workerqueue:
      name: lcs
  service:
    nginx:
      id: 24775
      name: shopee.engineering_infra.infra_products.sgw.longconnsvc.nginx
  business:
    sgw:
      name: engineering_infra.infra_products.sgw
  cert:
    etcd:
      live: wildcard.live.etcd-sgw-shopee-io.api
      nonlive: wildcard.nonlive.etcd-sgw-shopee-io.api
    scertmsd:
      accessKey:
      secretKey:
      host: proxy.uss.s3.sz.shopee.io
  swp:
    template:
      cluster_add_node: opsplatform_add_lcs_nodes
      cluster_online_node: online_alb_node
      cluster_offline_node: offline_alb_node
      cluster_remove_node: opsplatform_remove_lcs_nodes
      hotupdate_node: opsplatform_update_alb_tocex_configure_item_2
  smap:
    exporter:
      node:
        port: 9100
      cmds:
        port: 9105
      metrics:
        port: 9116
  platform: az
  application: longconnsvc
  function: nginx
  hotupdate:
    concurrent: 10
  rollback:
    concurrent: 10
  instance:
  tree:
    - shopee.az.traffic_scheduling.lcs.nginx
    - seamoney.az.traffic_scheduling.lcs.nginx
    - id_insurance.az.traffic_scheduling.lcs.nginx
  version:
    etcdctl: 3.4.30

remedy:
  swp:
    template:
      ma_process: server_ma_processing

bot:
  author:
    - name: kwanhur
      email: <EMAIL>
    - name: zhongding.qin
      email: <EMAIL>
  nlb:
    wiki:
      - name: faq
        title: "[NLB] FAQ"
        link: https://confluence.shopee.io/x/Sa4RMQ
      - name: permission
        title: "[NLB] Permission"
        link: https://confluence.shopee.io/x/4BXFMg
      - name: instance
        title: "[NLB] Instance"
        link: https://confluence.shopee.io/x/srwRMQ
      - name: listener
        title: "[NLB] Listener"
        link: https://confluence.shopee.io/x/uAfFMg
      - name: healthcheck
        alias:
          - health
        title: "[NLB] Healthcheck"
        link: https://confluence.shopee.io/x/MdInYw
      - name: replica
        title: "[NLB] Replica"
        link: https://confluence.shopee.io/x/PFUjQ
  alb:
    wiki:
      - name: acl
        title: "[ALB] Access List Control Best Practice"
        link: https://confluence.shopee.io/x/zaLwTQ
      - name: pfb
        title: "[ALB] ALB Support PFB v1 and v2 Behavior"
        link: https://confluence.shopee.io/x/J8rfQw
      - name: ssl
        alias:
          - tls
        title: "[ALB] Custom ssl_protocols and ssl_ciphers"
        link: https://confluence.shopee.io/x/36DwTQ
      - name: default
        title: "[ALB] Default Behavior"
        link: https://confluence.shopee.io/x/V6HwTQ
      - name: tss
        alias:
          - anticrawler
          - anti-crawler
        title: "[ALB] Enable Integration With Anti Crawler Decision Center"
        link: https://confluence.shopee.io/x/AqHwTQ
      - name: http3
        alias:
          - http/3
        title: "[ALB] HTTP3"
        link: https://confluence.shopee.io/x/bKHwTQ
      - name: status
        alias:
          - code
          - statuscode
          - status-code
        title: "[ALB] Status Code"
        link: https://confluence.shopee.io/x/EKLwTQ
      - name: directive
        title: "[ALB] Supported Directive List"
        link: https://confluence.shopee.io/x/paLwTQ
      - name: officeip
        alias:
          - office-ip
          - office_ip
        title: "[ALB] Add office_ip whitelist"
        link: https://confluence.shopee.io/x/MqHwTQ
      - name: log
        alias:
          - access
        title: "[ALB] Access Log (Integrated with Log Platform)"
        link: https://confluence.shopee.io/x/tqHwTQ
      - name: permission
        title: "[ALB] Permissions including listener,instance,release-center etc"
        link: https://confluence.shopee.io/x/w6PwTQ
      - name: setup
        title: Shopee Gateway Operation
        link: https://confluence.shopee.io/x/7bOpSw
      - name: cluster
        title: "[ALB] Clusters"
        link: https://confluence.shopee.io/x/405MN
      - name: instance
        title: "[ALB] Instances including create,delete,update,bind,unbind etc"
        link: https://confluence.shopee.io/x/R3XOOw
      - name: listener
        title: "[ALB] Listeners including create,delete,update,rollback,history etc"
        link: https://confluence.shopee.io/x/7mCFTQ
      - name: release
        alias:
          - release-center
        title: "[ALB] Release Center"
        link: https://confluence.shopee.io/x/mrvdCg
      - name: reaction
        alias:
          - reaction-plan
        title: "[ALB] Reaction Plan"
        link: https://confluence.shopee.io/x/yGvBSw
      - name: entrytask
        alias:
          - entry-task
        title: "[ALB] EntryTask"
        links:
          - DataPlane https://confluence.shopee.io/x/UbIaNg
          - SRE https://confluence.shopee.io/x/BsY-Cg
      - name: arch
        title: "[ALB] Architecture and Design"
        link: https://confluence.shopee.io/x/7Y2sLw
      - name: ham
        title: "[ALB] High Availability Mode"
        link: https://confluence.shopee.io/x/sxf3Vg
      - name: tech
        alias:
          - techdoc
          - tech-doc
        title: "[ALB] Tech Doc"
        links:
          - ControlPlane https://confluence.shopee.io/x/s0DONg
          - DataPlane https://confluence.shopee.io/x/To6sLw
  lcs:
    wiki:
      - name: mode
        title: "[LCS] Mode Introduction of LCS Listener"
        link: https://confluence.shopee.io/x/PsJiVg
  ext2:
    wiki:
      - name: overview
        title: Shopee External2 Overview
        link: https://confluence.shopee.io/x/JTa0Bg
      - name: arch
        title: Shopee External2 Architecture
        link: https://confluence.shopee.io/x/_SrGGg
      - name: fm
        alias:
          - failure
          - failure-mode
        title: Shopee External2 Failure Mode Analysis
        link: https://confluence.shopee.io/x/nAvGGg
      - name: cn-proxy
        alias:
          - cnproxy
        title: Shopee CN-Proxy User Guide
        link: https://confluence.shopee.io/x/QsrlYg
      - name: operation
        alias:
          - ops
        title: Shopee External2 Operation Guide
        link: https://confluence.shopee.io/x/nJx-DQ
  dns:
    wiki:
      - name: domain
        title: "[DNS] Domains"
        link: https://confluence.shopee.io/x/YhmtBw
      - name: record
        title: "[DNS] Records"
        link: https://confluence.shopee.io/x/BN7oBw
      - name: permission
        title: "[DNS] Permissions"
        link: https://confluence.shopee.io/x/cpUoEQ
      - name: healthcheck
        alias:
          - health-check
        title: "[DNS] HealthCheck"
        link: https://confluence.shopee.io/x/-w3PKQ
