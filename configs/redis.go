package configs

// RedisConfig Redis configuration
type RedisConfig struct {
	Addr         string `mapstructure:"addr"`
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
	DB           int    `mapstructure:"db"`
	MaxRetries   int    `mapstructure:"max_retries"`
	PoolSize     int    `mapstructure:"pool_size"`
	MinIdleConns int    `mapstructure:"min_idle_conns"`
	MaxConnAge   int    `mapstructure:"max_conn_age"`
}

type RedisConfigMap map[string]RedisConfig

func (r RedisConfigMap) GetByName(name string) (*RedisConfig, bool) {
	if r == nil {
		return nil, false
	}

	if config, ok := r[name]; ok {
		return &config, true
	}

	return nil, false
}
