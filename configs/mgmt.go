package configs

import (
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"

	"git.garena.com/shopee/devops/sgw-addon-operator/internal/consts"
)

type MgmtConfig struct {
	Cloud       Cloud             `mapstructure:"cloud"`
	Anchor      MGMT              `mapstructure:"anchor"`
	ALB         MGMT              `mapstructure:"alb"`
	NLB         MGMT              `mapstructure:"nlb"`
	NAT         MGMT              `mapstructure:"nat"`
	LCS         MGMT              `mapstructure:"lcs"`
	DNS         MgmtDNS           `mapstructure:"dns"`
	ADR         MGMT              `mapstructure:"adr"`
	Space       MGMT              `mapstructure:"space"`
	Aptly       MGMT              `mapstructure:"aptly"`
	NDMP        MGMT              `mapstructure:"ndmp"`
	Seatalk     MgmtSeatalk       `mapstructure:"seatalk"`
	IPAM        MGMT              `mapstructure:"ipam"`
	Dod         MgmtDod           `mapstructure:"dod"`
	Arti        MGMT              `mapstructure:"arti"`
	Cert        MGMT              `mapstructure:"cert"`
	Smap        MgmtSmap          `mapstructure:"smap"`
	Shadowcat   MgmtShadowcat     `mapstructure:"shadowcat"`
	GDRC        MgmtGdrc          `mapstructure:"gdrc"`
	SGW         MgmtSGW           `mapstructure:"sgw"`
	EKS         MgmtEKS           `mapstructure:"eks"`
	Ops         Ops               `mapstructure:"ops"`
	SEE         MGMT              `mapstructure:"see"`
	Res         MGMT              `mapstructure:"res"`
	Jira        MGMT              `mapstructure:"jira"`
	Harbor      map[string]Harbor `mapstructure:"harbor"`
	TimeoutSec  uint              `mapstructure:"timeout"`
	Timeout2Sec uint              `mapstructure:"timeout2"`
	Retries     int               `mapstructure:"retries"`
	TOCEx       TOCex             `mapstructure:"tocex"`
	Bot         Bot               `mapstructure:"bot"`
	Status      Server            `mapstructure:"status"`
	RC          Server            `mapstructure:"rc"`
	NetPlan     []string          `mapstructure:"netplan"`
	Sentry      Sentry            `mapstructure:"sentry"`
	Docker      DockerConfig      `mapstructure:"docker"`
	BGP         BgpConfig         `mapstructure:"bgp"`
	Office      OfficeConfig      `mapstructure:"office"`
}

type MGMT struct {
	Host string
}

type MgmtDod struct {
	Host   string `mapstructure:"host"`
	TeamID struct {
		MRE struct {
			Seamoney int `mapstructure:"seamoney"`
			AZ       int `mapstructure:"az"`
		} `mapstructure:"mre"`
		Sre struct {
			AZ int `mapstructure:"az"`
		} `mapstructure:"sre"`
	} `mapstructure:"teamid"`
}

type Cloud struct {
	AZs []string `mapstructure:"az"`
	RZs []string `mapstructure:"rz"`
}

type MgmtDNS struct {
	Host string
	Bot  Account
}

type MgmtSeatalk struct {
	Host string
	Bot  struct {
		Mre string
		Sre string
	}
	App struct {
		ID     string
		Secret string
	}
}

type MgmtSmap struct {
	Host    string
	StepSec int `mapstructure:"step"`
}

func (m *MgmtSmap) Step() time.Duration {
	return time.Duration(m.StepSec) * time.Second
}

type MgmtShadowcat struct {
	Host     string
	StepSec  int    `mapstructure:"step"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

func (m *MgmtShadowcat) Step() time.Duration {
	return time.Duration(m.StepSec) * time.Second
}

type MgmtGdrc struct {
	Secret string
	Mode   struct {
		Auto struct {
			Secret string `mapstructure:"secret"`
		}
		Manual struct {
			Secret string `mapstructure:"secret"`
		}
	} `mapstructure:"mode"`
}

func (m *MgmtGdrc) SecretKey(mode consts.Mode) string {
	switch mode.Code {
	case consts.ModeManual.Code:
		return m.Mode.Manual.Secret
	default:
		return m.Mode.Auto.Secret
	}
}

type BotGroup struct {
	Allow []string
	Deny  []string
}

func (g *BotGroup) AllowMap() map[string]struct{} {
	m := make(map[string]struct{})
	for _, s := range g.Allow {
		m[s] = struct{}{}
	}

	return m
}

func (g *BotGroup) DenyMap() map[string]struct{} {
	m := make(map[string]struct{})
	for _, s := range g.Deny {
		m[s] = struct{}{}
	}

	return m
}

type Account struct {
	User     string
	Password string
	Email    string
}

type Bot struct {
	User     string
	Password string
	Email    string
	Users    []string
	Group    BotGroup
	Noti     Account
}

type Event struct {
	PoolSize int `mapstructure:"pool_size"`
	Consumer int
	Limit    int
}

func (c *MgmtConfig) IsNonLiveEnv() bool {
	return !strings.EqualFold(GetEnv(), "live")
}

func (c *MgmtConfig) IsLiveEnv() bool {
	return strings.EqualFold(GetEnv(), "live")
}

func (c *MgmtConfig) IsUserAllow(user string) bool {
	for _, u := range c.Bot.Users {
		if u == user {
			return true
		}
	}

	return false
}

func (c *MgmtConfig) IsGroupAllow(groups []string) bool {
	group := c.Bot.Group
	deny := group.DenyMap()
	allow := group.AllowMap()

	for _, group := range groups {
		if _, ok := deny[group]; ok {
			return false
		}
		if _, ok := allow[group]; ok {
			return true
		}
	}

	return false
}

func (c *MgmtConfig) IsInAllowGroup(groups []string, allowGroup map[string]struct{}) bool {
	for _, group := range groups {
		if _, ok := allowGroup[group]; ok {
			return true
		}
	}

	return false
}

type MgmtSGW struct {
	Bot Bot
}

type MgmtEKS struct {
	Bot Bot
}

type Ops struct {
	Host  string
	Bot   Bot
	Event Event
	Task  Event
}

// Timeout unit:second
func (c *MgmtConfig) Timeout() time.Duration {
	return time.Duration(c.TimeoutSec) * time.Second
}

// QuickTimeout unit:second
func (c *MgmtConfig) QuickTimeout() time.Duration {
	return time.Duration(c.Timeout2Sec) * time.Second
}

type Harbor struct {
	Host     string
	User     string
	Password string
	Domain   string
	Auth     string
}

func (c *MgmtConfig) HarborDomain(env string) string {
	switch env {
	case "test", "staging", "stable", "uat":
		return c.Harbor["test"].Domain
	default:
		return c.Harbor["live"].Domain
	}
}

type TOCex struct {
	Token struct {
		Live    string `validate:"required"`
		NonLive string `mapstructure:"nonlive" validate:"required"`
	}
}

func (c *MgmtConfig) TocexToken(env string) string {
	if strings.EqualFold(env, "live") {
		return c.TOCEx.Token.Live
	}

	return c.TOCEx.Token.NonLive
}

type Server struct {
	Host  string
	Token string
}

type Sentry struct {
	Host     string `mapstructure:"host" validate:"required,url"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password" validate:"required"`
}

func (s *Sentry) DSN() string {
	return fmt.Sprintf("https://%s:%s@%s", s.User, s.Password, strings.TrimPrefix(s.Host, "https://"))
}

type OfficeConfig struct {
	TimeConfig struct {
		Start string
		End   string
	} `mapstructure:"time"`
}

func (c *OfficeConfig) IsWorkingHours() (bool, error) {
	now := time.Now()
	weekday := now.Weekday()
	if weekday == time.Saturday || weekday == time.Sunday {
		return false, nil
	}

	currentDate := now.Format("2006-01-02")

	startTime, err := time.ParseInLocation("2006-01-02 03:04am", currentDate+" "+c.TimeConfig.Start, now.Location())
	if err != nil {
		return false, errors.WithMessage(err, "parse_start_time_failed")
	}

	endTime, err := time.ParseInLocation("2006-01-02 03:04pm", currentDate+" "+c.TimeConfig.End, now.Location())
	if err != nil {
		return false, errors.WithMessage(err, "parse_end_time_failed")
	}

	return startTime.Before(now) && endTime.After(now), nil
}
