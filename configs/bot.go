package configs

import (
	"fmt"
	"strings"

	"github.com/urfave/cli/v2"
)

type BotConfig struct {
	Author []Author `mapstructure:"author"`
	NLB    Wiki     `mapstructure:"nlb"`
	ALB    Wiki     `mapstructure:"alb"`
	LCS    Wiki     `mapstructure:"lcs"`
	Ext2   Wiki     `mapstructure:"ext2"`
	DNS    Wiki     `mapstructure:"dns"`
}

type Author struct {
	Name  string
	Email string
}

type Wiki struct {
	Wiki []WikiCmd `mapstructure:"wiki"`
}

type WikiCmd struct {
	Name  string   `mapstructure:"name"`
	Alias []string `mapstructure:"alias"`
	Title string   `mapstructure:"title"`
	Link  string   `mapstructure:"link"`
	Links []string `mapstructure:"links"`
}

func (c *WikiCmd) Content() string {
	var link string
	if len(c.Links) != 0 {
		link = strings.Join(c.Links, "\n")
	} else {
		link = c.Link
	}

	return fmt.Sprintf(`%s

%s`, c.Title, link)
}

func (c *BotConfig) Authors() []*cli.Author {
	var authors []*cli.Author
	for _, a := range c.Author {
		authors = append(authors, &cli.Author{
			Name:  a.Name,
			Email: a.Email,
		})
	}

	return authors
}
