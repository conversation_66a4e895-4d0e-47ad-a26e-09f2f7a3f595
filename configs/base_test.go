package configs

import (
	"log"
	"os"
	"testing"
	"time"

	"github.com/asaskevich/govalidator"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func init() {
	err := os.Setenv("ENV", "dev")
	if err != nil {
		log.Fatal(err)
	}

	if err = Init(""); err != nil {
		log.Fatal(err)
	}

	log.Println("init done")
}

func Test_CI(t *testing.T) {
	t.Setenv("CI", "true")
	assert.True(t, CI())

	t.Setenv("CI", "false")
	assert.False(t, CI())
}

func TestReadFile_config(t *testing.T) {
	sgw, err := folder.ReadFile("config.yaml")
	assert.NoError(t, err)
	assert.NotEmpty(t, sgw)
}

func TestInit_initConfig_nonlive(t *testing.T) {
	t.Setenv("ENV", "dev")
	err := initByEnv()
	assert.NoError(t, err)
	assert.Equal(t, 11681, viper.GetInt("alb.service.nginx.id"))
}

func TestInit_initConfig_no_env(t *testing.T) {
	t.Setenv("ENV", "")

	err := initByEnv()
	assert.NoError(t, err)
}

func TestALBConfig_SMAPPorts(t *testing.T) {
	assert.NotEmpty(t, ALB.SMAP.Exporters()["port"])
}

func TestALBConfig_SMAPConfigs(t *testing.T) {
	assert.Greater(t, len(ALB.SMAP.PortConfigs()), 0)
}

func TestMgmtConfig_DnsHost(t *testing.T) {
	assert.NotEmpty(t, Mgmt.DNS.Host)
}

func TestMgmtConfig_Anchor(t *testing.T) {
	assert.NotEmpty(t, Mgmt.Anchor.Host)
}

func TestMgmtConfig_Harbor(t *testing.T) {
	assert.NotEmpty(t, Mgmt.Harbor["live"].Host)
	assert.NotEmpty(t, Mgmt.Harbor["live"].Password)
	assert.NotEmpty(t, Mgmt.Harbor["test"].Host)
	assert.NotEmpty(t, Mgmt.Harbor["test"].Password)
}

func TestBotConfig_Authors(t *testing.T) {
	assert.Greater(t, len(BOT.Authors()), 0)
}

func TestSentry_DSN(t *testing.T) {
	dsn := Mgmt.Sentry.DSN()
	assert.NotEmpty(t, dsn)
	assert.True(t, govalidator.IsURL(dsn))
	assert.NotEmpty(t, Mgmt.Sentry.Password)
}

func TestAnchorConfig_ResourceTree(t *testing.T) {
	assert.NotEmpty(t, Anchor.ResourceTree)
	assert.Greater(t, len(Anchor.ResourceTree), 0)
	assert.Contains(t, Anchor.ResourceTree, "shopee.ndre.server_pool")
	assert.Contains(t, Anchor.ResourceTree, "seamoney.az.traffic_scheduling.etcd")
}

func TestDNSConfig_BGP(t *testing.T) {
	bgp := Mgmt.BGP
	assert.NotZero(t, bgp.LocalAS)
	assert.NotZero(t, bgp.RemoteAS)

	bgp = DNS.BGP["vn"]
	assert.NotNil(t, bgp.Neighbor)
	assert.Greater(t, len(bgp.Neighbor), 0)
	assert.NotEmpty(t, bgp.NextHop)
	assert.NotEmpty(t, bgp.Password)
}

func TestOfficeConfig_IsWorkingHours(t *testing.T) {
	Office := Mgmt.Office

	ok, err := Office.IsWorkingHours()
	require.NoError(t, err)

	now := time.Now()
	if now.Weekday() == time.Saturday || now.Weekday() == time.Sunday {
		assert.False(t, ok)
	}

	currentDate := now.Format("2006-01-02")

	t1, err := time.ParseInLocation("2006-01-02 03:04am", currentDate+" "+Office.TimeConfig.Start, now.Location())
	assert.NoError(t, err)

	t2, err := time.ParseInLocation("2006-01-02 03:04pm", currentDate+" "+Office.TimeConfig.End, now.Location())
	assert.NoError(t, err)

	// Expected behavior:
	// 1. If today is a weekend (Saturday or Sunday), it's not working hours
	// 2. If the current time is between start and end times on a weekday, it is working hours
	expectedWorkingHours := false
	if now.Weekday() != time.Saturday && now.Weekday() != time.Sunday {
		// It's a weekday, check if we're within working hours
		expectedWorkingHours = now.After(t1) && now.Before(t2)
	}

	// Assert that the function's return matches our expected result
	assert.Equal(t, expectedWorkingHours, ok)
}

func TestE2E_ALB(t *testing.T) {
	assert.NotEmpty(t, E2E.ALB.Shopee)
	assert.NotEmpty(t, E2E.ALB.Monee)
}
