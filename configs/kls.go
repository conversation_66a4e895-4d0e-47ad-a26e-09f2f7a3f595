package configs

import (
	"strings"
	"time"
)

type KLSConfig struct {
	Platform    string
	Application string
	Function    string
	Verify      struct {
		Disable bool
	}
	Kube      <PERSON>be          `mapstructure:"kube"`
	Provision Provision     `mapstructure:"provision"`
	Service   ServiceConfig `mapstructure:"service"`
	Business  SGWBusiness   `mapstructure:"business"`
	Cert      struct {
		Etcd     KLSCertEtcd `mapstructure:"etcd"`
		Scertmsd Scertmsd    `mapstructure:"scertmsd"`
	}
	SWP struct {
		Template KLSSWPTemplate
	}
	SMAP      SMAP `mapstructure:"smap"`
	HotUpdate struct {
		Concurrent int
	} `mapstructure:"hotupdate"`
	Rollback struct {
		Concurrent int
	} `mapstructure:"rollback"`
	Precheck struct {
		PrecheckConfig   `mapstructure:",squash"`
		NginxTestTimeout int `mapstructure:"nginx_test_timeout"`
	} `mapstructure:"precheck"`

	ResourceTree []string `mapstructure:"tree"`
}

// IsStateDisable specified state disable or not
func (c *KLSConfig) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

func (c *KLSConfig) Env() string {
	return strings.ToLower(GetEnv())
}

func (c *KLSConfig) IsLiveEnv() bool {
	return strings.EqualFold(c.Env(), "live")
}

func (c *KLSConfig) NginxTestTimeout() time.Duration {
	return time.Duration(c.Precheck.NginxTestTimeout) * time.Second
}

func (c *KLSConfig) BizSGW() *Business {
	return &c.Business.SGW
}

type KLSCertEtcd struct {
	Live    string
	NonLive string `mapstructure:"nonlive"`
}

func (c *KLSConfig) EtcdCert() *KLSCertEtcd {
	return &c.Cert.Etcd
}
