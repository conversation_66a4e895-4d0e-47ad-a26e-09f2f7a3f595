package configs

type BgpConfig struct {
	LocalAS       uint16              `yaml:"local_as" mapstructure:"local_as"`
	RemoteAS      int64               `yaml:"remote_as" mapstructure:"remote_as"`
	Password      string              `yaml:"password"`
	HoldTime      int                 `yaml:"hold_time" mapstructure:"hold_time"`
	KeepaliveTime int                 `yaml:"keepalive_time" mapstructure:"keepalive_time"`
	NextHop       map[string]string   `yaml:"next_hop" mapstructure:"next_hop"`
	Neighbor      map[string][]string `yaml:"neighbor"`
}
