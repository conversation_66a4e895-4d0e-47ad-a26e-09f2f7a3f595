package configs

import (
	"strings"
)

type EIPConfig struct {
	Platform  string
	Kube      Kube      `mapstructure:"kube"`
	Provision Provision `mapstructure:"provision"`
	SWP       struct {
		Template SWPTemplate
	}
	SMAP         SMAP          `mapstructure:"smap"`
	Service      ServiceConfig `mapstructure:"service"`
	ResourceTree []string      `mapstructure:"tree"`
}

// IsStateDisable specified state disable or not
func (c *EIPConfig) IsStateDisable(state string) bool {
	s := strings.ToLower(state)
	for _, st := range c.Provision.Disable {
		if s == strings.ToLower(st) {
			return true
		}
	}

	return false
}

func (c *EIPConfig) Env() string {
	return strings.ToLower(GetEnv())
}

func (c *EIPConfig) IsLiveEnv() bool {
	return strings.EqualFold(c.Env(), "live")
}

func (c *EIPConfig) Servers() *Service {
	return &c.Service.Servers
}
