{"#": "Kubernetes ALB Controller", "project_name": "leap", "module_name": "albcontroller", "build": {"commands": ["make bin/alb-controller", "make bin/sgwctl", "mv -v ./bin/alb-controller /tmp/alb-controller", "mv -v ./bin/sgwctl /tmp/sgwctl", "mv -v ./configs/config*.yaml /tmp/", "mv -v ./deploy/alb.json /tmp/", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/alb-controller ./bin/controller", "mv -v /tmp/sgwctl ./bin/sgwctl", "mv -v /tmp/config*.yaml ./configs/", "mv -v /tmp/alb.json ./deploy/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}}, "run": {"register_zk": true, "command": "./bin/controller --application alb", "prometheus_job_name": "leap-albcontroller", "enable_prometheus": true, "enable_service_account": true, "smoke": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/smoke"}, "check": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/healthz"}, "port_definitions": [{"submodule": "albcontroller"}]}}