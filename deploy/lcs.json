{"#": "Kubernetes LCS Controller", "project_name": "leap", "module_name": "lcscontroller", "build": {"commands": ["make bin/lcs-controller", "mv -v ./bin/lcs-controller /tmp/lcs-controller", "mv -v ./configs/config*.yaml /tmp/", "mv -v ./deploy/lcs.json /tmp/", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/lcs-controller ./bin/controller", "mv -v /tmp/config*.yaml ./configs/", "mv -v /tmp/lcs.json ./deploy/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}}, "run": {"register_zk": true, "command": "./bin/controller --application lcs", "prometheus_job_name": "leap-lcscontroller", "enable_prometheus": true, "enable_service_account": true, "smoke": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/smoke"}, "check": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/healthz"}, "port_definitions": [{"submodule": "lcscontroller"}]}}