{"#": "SGW Viz API Docs", "project_name": "opsplatform", "module_name": "sgwviz", "build": {"commands": ["make bin/sgwviz", "mv -v ./bin/sgwviz /tmp/sgwviz", "mv -v ./configs/config*.yaml /tmp/", "mv -v ./deploy/viz.json /tmp/", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/sgwviz ./bin/controller", "mv -v /tmp/config*.yaml ./configs/", "mv -v /tmp/viz.json ./deploy/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}}, "run": {"register_zk": true, "command": "./bin/controller --application viz", "prometheus_job_name": "opsplatform-sgwviz", "enable_prometheus": true, "enable_service_account": false, "smoke": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/smoke"}, "check": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/healthz"}, "port_definitions": [{"submodule": "sgwviz"}]}}