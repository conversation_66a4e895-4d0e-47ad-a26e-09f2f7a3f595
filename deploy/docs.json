{"#": "SGW Operator API Docs", "project_name": "opsplatform", "module_name": "docs", "build": {"commands": ["make bin/docs", "mv -v ./bin/docs /tmp/docs", "mv -v ./configs/config*.yaml /tmp/", "mv -v ./deploy/docs.json /tmp/", "mv -v ./docs /tmp/docs-origin", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/docs ./bin/controller", "mv -v /tmp/config*.yaml ./configs/", "mv -v /tmp/docs.json ./deploy/", "mv -v /tmp/docs-origin ./docs", "rm -frv ./docs/*.go"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}}, "run": {"register_zk": true, "command": "./bin/controller --application docs", "prometheus_job_name": "opsplatform-docs", "enable_prometheus": true, "enable_service_account": false, "smoke": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/smoke"}, "check": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/healthz"}, "port_definitions": [{"submodule": "docs"}]}}