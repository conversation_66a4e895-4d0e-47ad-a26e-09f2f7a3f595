package deploy

import (
	"embed"
	"os"

	"github.com/pkg/errors"

	"git.garena.com/shopee/go-shopeelib/json"
)

//go:embed env.json
var folder embed.FS

type EnvItem struct {
	Key   string
	Value string
}

func envs() (map[string][]EnvItem, error) {
	kvs, err := folder.ReadFile("env.json")
	if err != nil {
		return nil, errors.WithMessagef(err, "read env file failed")
	}

	var envs map[string][]EnvItem

	err = json.Unmarshal(kvs, &envs)
	if err != nil {
		return nil, errors.WithMessagef(err, "unmarshal env file failed")
	}

	return envs, nil
}

func SetEnvs() error {
	envs, err := envs()
	if err != nil {
		return errors.WithMessagef(err, "fetch envs failed")
	}

	for _, v := range envs["default"] {
		err = os.Setenv(v.Key, v.Value)
		if err != nil {
			return errors.WithMessagef(err, "set default env failed")
		}
	}

	env := os.Getenv("ENV")
	if env == "dev" {
		env = "test"
	}

	for _, v := range envs[env] {
		err = os.Setenv(v.Key, v.Value)
		if err != nil {
			return errors.WithMessagef(err, "set env failed")
		}
	}

	return nil
}

func TestEnvs() ([]EnvItem, error) {
	envs, err := envs()
	if err != nil {
		return nil, errors.WithMessagef(err, "fetch envs failed")
	}

	return envs["test"], nil
}
