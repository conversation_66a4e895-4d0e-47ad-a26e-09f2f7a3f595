{"#": "Kubernetes Anchor Controller", "project_name": "opsplatform", "module_name": "anchorcontroller", "build": {"commands": ["make bin/anchor-controller", "mv -v ./bin/anchor-controller /tmp/anchor-controller", "mv -v ./configs/config*.yaml /tmp/", "mv -v ./deploy/anchor.json /tmp/", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/anchor-controller ./bin/controller", "mv -v /tmp/config*.yaml ./configs/", "mv -v /tmp/anchor.json ./deploy/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}}, "run": {"register_zk": true, "command": "./bin/controller --application anchor", "prometheus_job_name": "opsplatform-anchorcontroller", "enable_prometheus": true, "enable_service_account": true, "smoke": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/smoke"}, "check": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/healthz"}, "port_definitions": [{"submodule": "anchorcontroller"}]}}