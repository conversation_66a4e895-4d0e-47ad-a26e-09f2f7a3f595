{"#": "SGW ChatBot", "project_name": "chatbot", "module_name": "chatbot", "build": {"commands": ["make bin/chatbot", "mv -v ./bin/chatbot /tmp/chatbot", "mv -v ./configs/config*.yaml /tmp/", "mv -v ./deploy/bot.json /tmp/", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/chatbot ./bin/controller", "setcap cap_net_raw=+ep ./bin/controller", "mv -v /tmp/config*.yaml ./configs/", "mv -v /tmp/bot.json ./deploy/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}}, "run": {"register_zk": true, "command": "./bin/controller --application bot", "prometheus_job_name": "chatbot-chatbot", "enable_prometheus": true, "enable_service_account": false, "smoke": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/smoke"}, "check": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/healthz"}, "port_definitions": [{"submodule": "chatbot"}]}}