{"#": "SGW Open API Docs", "project_name": "opsplatform", "module_name": "openapi", "build": {"commands": ["make bin/openapi", "mv -v ./bin/openapi /tmp/openapi", "mv -v ./configs/config*.yaml /tmp/", "mv -v ./deploy/openapi.json /tmp/", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/openapi ./bin/controller", "mv -v /tmp/config*.yaml ./configs/", "mv -v /tmp/openapi.json ./deploy/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}}, "run": {"register_zk": true, "command": "./bin/controller --application openapi", "prometheus_job_name": "opsplatform-openapi", "enable_prometheus": true, "enable_service_account": false, "smoke": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/smoke"}, "check": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/healthz"}, "port_definitions": [{"submodule": "openapi"}]}}