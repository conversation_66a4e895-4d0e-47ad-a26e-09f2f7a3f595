{"#": "Kubernetes Ext2 Controller", "project_name": "leap", "module_name": "ext2controller", "build": {"commands": ["make bin/ext2-controller", "make bin/sgwctl", "mv -v ./bin/ext2-controller /tmp/ext2-controller", "mv -v ./bin/sgwctl /tmp/sgwctl", "mv -v ./configs/config*.yaml /tmp/", "mv -v ./deploy/ext2.json /tmp/", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/ext2-controller ./bin/controller", "mv -v /tmp/sgwctl ./bin/sgwctl", "mv -v /tmp/config*.yaml ./configs/", "mv -v /tmp/ext2.json ./deploy/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}}, "run": {"register_zk": true, "command": "./bin/controller --application ext2", "prometheus_job_name": "leap-ext2controller", "enable_prometheus": true, "enable_service_account": true, "smoke": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/smoke"}, "check": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/healthz"}, "port_definitions": [{"submodule": "ext2controller"}]}}