{"#": "Kubernetes EGW Controller", "project_name": "opsplatform", "module_name": "egwcontroller", "build": {"commands": ["make bin/egw-controller", "mv -v ./bin/egw-controller /tmp/egw-controller", "mv -v ./configs/config*.yaml /tmp/", "mv -v ./deploy/egw.json /tmp/", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/egw-controller ./bin/controller", "mv -v /tmp/config*.yaml ./configs/", "mv -v /tmp/egw.json ./deploy/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}}, "run": {"register_zk": true, "command": "./bin/controller --application egw", "prometheus_job_name": "opsplatform-egwcontroller", "enable_prometheus": true, "enable_service_account": true, "smoke": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/smoke"}, "check": {"protocol": "HTTP", "timeout": 5, "retry": 10, "endpoint": "/healthz"}, "port_definitions": [{"submodule": "egwcontroller"}]}}