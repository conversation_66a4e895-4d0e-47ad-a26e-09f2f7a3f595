{"#": "sgwctl remedy", "project_name": "leap", "module_name": "sgwremedy", "build": {"commands": ["make bin/sgwctl", "mv -v ./bin/sgwctl /tmp/sgwctl", "mv -v ./configs/config*.yaml /tmp/", "rm -frv ./*", "mkdir -p ./bin ./configs ./deploy", "mv -v /tmp/sgwctl ./bin/sgwctl", "mv -v /tmp/config*.yaml ./configs/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.21.3-20", "enable_multi_stage_golang_image": true, "dependent_libraries_files": ["go.mod", "go.sum"], "run_commands": ["go env -w GO111MODULE=on", "go env -w GOPRIVATE=*.garena.com", "go mod download"]}, "force_upload_image": true}}