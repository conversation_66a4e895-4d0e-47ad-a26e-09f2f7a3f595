@startuml

title SGW DR Controller x Global Controller

participant Bob
autonumber

Bob -> Global: Register Task Executor
note right: setup sgw dr controller task acceptor

Global -[#green]> Bob: Success

Bob -> Global: Trigger DR Task

Global -> SGWDR: Transparent Task
SGWDR --[#red]>Global: Validate Data Failure

SGWDR -> DB: Save Task
DB --[#green]> SGWDR: Success

SGWDR -> K8S: Create Task CR under `firing`
K8S --[#green]> SGWDR: Success

SGWDR --> Global: Success

K8S -> SGWDR: StateMachine Rotate

SGWDR -> AZMeta: Fetch AZ&RZ
AZMeta --[#green]> SGWDR: Success

SGWDR -> ADR: Switch affected RZs
ADR --[#green]> SGWDR: Success
SGWDR -> Global: Callback on success or failure
Global --[#green]> SGWDR: Success
SGWDR -> K8S: Save Switch Result
K8S --[#green]> SGWDR: Success

SGWDR -> ALB: Fetch Clusters
SGWDR -> ADR: Switch affected Domains
ADR --[#green]> SGWDR: Success
SGWDR -> Global: Callback on success or failure
Global --[#green]> SGWDR: Success
SGWDR -> K8S: Save Switch Result
K8S --[#green]> SGWDR: Success

SGWDR -> DB: Save Task Result
DB --[#green]> SGWDR: Success

SGWDR -> K8S: Delete Task CR
K8S --[#green]> SGWDR: Success

SGWDR -> Global: Done
Global -> Bob: DR Task Finish

@enduml
