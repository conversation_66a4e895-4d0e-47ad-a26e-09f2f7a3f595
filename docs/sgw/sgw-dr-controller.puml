@startuml
'https://plantuml.com/state-diagram

state "SGW DR" as SG<PERSON><PERSON> {

Firing: 1.NA

Analysis: 1.CheckDisasterScenario

Recovery: 1. SwitchAffectedAZ
Recovery: 2. SwitchAffectedRZ
Recovery: 3. SwitchAffectedDomains
Recovery: 3.1 SwitchALBListeners
Recovery: 3.2 SwitchNLBInstances
Recovery: 3.3 SwitchExt2Clusters

Inspect: 1. CheckRZSwitch
Inspect: 2. CheckALBSwitch
Inspect: 2.1 ReloadALBNginx
Inspect: 3. CheckNLBSwitch
Inspect: 3.1 ResetNLBConnection
Inspect: 4. CheckExt2Switch
Inspect: 4. CheckDummySwitch

Scaling: 1.NA

Mark: 1. SplitAffectedDomainsByBatch
Mark: 2. MarkALBClusters

Sweeping: 1. ReloadALBNginx
Sweeping: 2. ReloadCNProxyNginx
Sweeping: 3. ReportFireDrillResult

OutFire: 1.SaveDRCTask

Tidy: 1.DeleteDRCCRTask

    [*] --> Firing

    Firing --> Analysis

    Analysis --> Recovery: Default: Disaster
    Analysis --> Mark: FireDrill

    Mark --> Switching

    Switching --> Sweeping

    Sweeping --> OutFire

    Recovery --> Inspect

    Inspect --> Scaling

    Scaling --> OutFire

    OutFire --> Tidy

    Tidy --> [*]
}

state Recovery {
    [*] --> AZ

    AZ -> RZ
    note on link
    0. list RZ by AZ
    1. switch by RZ
    end note

    RZ -> Domain
    note on link
    1. switch by rz
    end note

    Domain -> [*]
    note on link
    0. list Biz domains via ALB Cluster
    1. list ADR domains via NLB Cluster
    2. switch by domain
    end note
}

state Scaling {
    [*] --> Servers

    Servers -> Cluster
    note on link
      split by product

      DNS NLB NAT ALB LCS Ext2
      Etcd Zookeeper
    end note

    Cluster -> Misc
    note on link
      1. only partial nodes in cluster then offline
      2. if offlined nodes percent greater
          than 50% then try to scale-up
      3. if only etcd then try replacement
    end note

    Misc -> Data
    note on link
      0. if only zookeeper
      1. nlb-sd&alb-sd downgrade
      2. data fix
    end note

    Data --> [*]
}

'note right of Misc: ALB Etcd / Zookeeper
'note top of Data:data fixing like etcd/zk

note right of Recovery: mostly action as switch

note right of Scaling: main action: offline and scale-up

State Switching {

    [*] --> ManualSwitch: Mark as Manual
    [*] --> AutoSwitch: Mark as Auto

    State ManualSwitch {
        [*] --> ManualBatch1

        ManualBatch1 -> ManualBatch2
        note on link
          1.Waiting for manual check
          2.Continue via Ops
        end note

        ManualBatch2 -> ManualBatchN
        note on link
          1.Waiting for manual check
          2.Continue via Ops
        end note

        ManualBatchN --> [*]
    }

    note left of ManualSwitch: Manual Switching

    ManualSwitch --> Paused: Auto Pause on Batch

    Paused --> ManualSwitch: Manual Continue via Ops

    State AutoSwitch {
        [*] --> Batch1

        Batch1 -> Batch2
        note on link
          1.Waiting for 120s
          2.Continue by Auto
        end note

        Batch2 -> BatchN
        note on link
          1.Waiting for 120s
          2.Continue by Auto
        end note

        BatchN --> [*]
    }

    note right of AutoSwitch: Automatic Switching
}

@enduml
