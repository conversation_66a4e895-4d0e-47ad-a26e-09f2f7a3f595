# auto-retrieve node's BGP peer

## background

because some nodes in private AZ, distribute in different network segments, so their route gateway will also be different.
we try to retrieve BGP peer mgmt IP address via route gateway.

## condition

only work on no `calico_bgp_peer_as_mapping` values.

## verification

1. retrieve default gateway with `ip route show default` for every node. if not found, then verify failed.

    - under HAM-NonStd2, default gateway must be public IP address.
    - under HAM-NonStd3 or above, default gateway must be private IP address.

2. check 2 peer switches' port `179` connectivity which mgmt IP addresses are default gateway's `-1` and `-2`.
if unconnected, then verify failed.

## provision

1. retrieve node's default gateway.
2. set node's `ALBGroupVar.BGPASMapping` with kv like `"gateway  IP address -1":"4200080000"`
