# how to support frontend(OpsPlatform)

## Target

To support OpsPlatform integrate with controller locally

## how to reach

in OpsPlatform frontend project, the traffic path is `https://space.test.shopee.io/apis/az_ops/ → https://opsplatform.sto.test.shopee.io/`

at local, it'll be `http://localhost:8081/apis/az_ops/ -> localhost:8080/`

so we'll use nginx as a proxy for the backend with rewrite url. the config `nginx.conf` as below

```nginx

#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

pid        logs/nginx.pid;


events {
    worker_connections  256;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    server {
        listen       8081;
        server_name  localhost;
        root   html;

        #charset koi8-r;

        access_log  logs/host.access.log  main;

        location / {
            proxy_set_header Host space.test.shopee.io;
            proxy_pass https://space.test.shopee.io;
        }

        location /apis/az_ops/ {
            rewrite /apis/az_ops/(.*) /$1 break;
            proxy_pass http://localhost:8080;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        error_page   404  /40x.html;
        error_page   404  /400.html;
        location = /50x.html {
            root   html;
        }
    }
    include servers/*;
}
```

## Q&A

### Q1: how to install `nginx`

A: under MacOS, install as `brew install nginx` `brew services run nginx`

tips: after change `nginx.conf`, need to reload nginx like `brew services reload nginx`

### Q2: how to locate `nginx`

A: locate service as `brew service info nginx`

locate installation as `brew info nginx`
