# Swagger documentation for ALB Reload functionality
# This file contains additional Swagger definitions for the ALB reload API

# Security definitions
securityDefinitions:
  BearerToken:
    type: apiKey
    name: Authorization
    in: header
    description: "Bearer token for user authentication. Format: 'Bearer <token>'"

# Global security requirement
security:
  - BearerToken: []

# Additional model definitions
definitions:
  ALBReloadRequest:
    type: object
    required:
      - cluster_id
    properties:
      cluster_id:
        type: string
        format: uuid
        description: "UUID of the ALB cluster to reload nginx on"
        example: "550e8400-e29b-41d4-a716-************"
      requested_by:
        type: string
        format: email
        description: "Email of the user requesting the reload (optional, will be set automatically if not provided)"
        example: "<EMAIL>"
      reason:
        type: string
        maxLength: 500
        description: "Reason for the reload operation (optional, max 500 characters)"
        example: "Updated SSL certificates"

  ALBReloadResponse:
    type: object
    properties:
      code:
        type: integer
        description: "HTTP status code"
        example: 200
      message:
        type: string
        description: "Response message"
        example: "OK"
      data:
        $ref: '#/definitions/ALBReloadResponseData'

  ALBReloadResponseData:
    type: object
    properties:
      request_id:
        type: string
        format: uuid
        description: "Unique identifier for this reload request"
        example: "123e4567-e89b-12d3-a456-************"
      status:
        type: string
        enum: ["pending", "in_progress", "success", "failed", "rate_limited"]
        description: "Status of the reload operation"
        example: "success"
      message:
        type: string
        description: "Human-readable message describing the operation result"
        example: "Successfully reloaded nginx on 3 nodes"
      cluster_id:
        type: string
        format: uuid
        description: "UUID of the ALB cluster that was targeted"
        example: "550e8400-e29b-41d4-a716-************"
      bu:
        type: string
        description: "Business Unit of the requesting user"
        example: "payments"
      target_nodes:
        type: array
        items:
          type: string
          format: ipv4
        description: "List of node IP addresses that were targeted for reload"
        example: ["*********", "*********", "*********"]
      start_time:
        type: string
        format: date-time
        description: "Timestamp when the reload operation started"
        example: "2025-01-08T10:30:00Z"
      end_time:
        type: string
        format: date-time
        description: "Timestamp when the reload operation completed (null if still in progress)"
        example: "2025-01-08T10:30:15Z"

  ALBReloadConfigResponse:
    type: object
    properties:
      code:
        type: integer
        description: "HTTP status code"
        example: 200
      message:
        type: string
        description: "Response message"
        example: "OK"
      data:
        $ref: '#/definitions/ALBReloadConfigData'

  ALBReloadConfigData:
    type: object
    properties:
      enabled:
        type: boolean
        description: "Whether ALB reload functionality is enabled"
        example: true
      rate_limit_window:
        type: string
        description: "Rate limit window duration"
        example: "5m0s"
      max_requests:
        type: integer
        description: "Maximum number of requests allowed per BU within the rate limit window"
        example: 1
      user_bu:
        type: string
        description: "Business Unit assigned to the authenticated user (if any)"
        example: "payments"
      audit_log_enabled:
        type: boolean
        description: "Whether audit logging is enabled"
        example: true

  ALBReloadError:
    type: object
    properties:
      code:
        type: integer
        description: "HTTP status code"
        example: 400
      message:
        type: string
        description: "Error message"
        example: "Invalid request format"
      error:
        type: string
        description: "Detailed error information"
        example: "cluster_id is required"
      trace_id:
        type: string
        description: "Request trace ID for debugging"
        example: "abc123def456"

# Tags for grouping endpoints
tags:
  - name: ALBReload
    description: "ALB nginx reload operations"
    externalDocs:
      description: "Find out more about ALB reload functionality"
      url: "https://internal-docs.company.com/alb-reload"

# Example responses for common error cases
responses:
  BadRequest:
    description: "Invalid request format or validation failed"
    schema:
      $ref: '#/definitions/ALBReloadError'
    examples:
      application/json:
        code: 400
        message: "Invalid request format"
        error: "cluster_id must be a valid UUID"
        trace_id: "abc123def456"

  Unauthorized:
    description: "User authentication required"
    schema:
      $ref: '#/definitions/ALBReloadError'
    examples:
      application/json:
        code: 401
        message: "Unauthorized"
        error: "User authentication required"
        trace_id: "abc123def456"

  Forbidden:
    description: "User not authorized for ALB reload operations"
    schema:
      $ref: '#/definitions/ALBReloadError'
    examples:
      application/json:
        code: 403
        message: "Forbidden"
        error: "User is not authorized for ALB reload operations"
        trace_id: "abc123def456"

  NotFound:
    description: "Cluster not found or not accessible"
    schema:
      $ref: '#/definitions/ALBReloadError'
    examples:
      application/json:
        code: 404
        message: "Not Found"
        error: "Cluster not found or not accessible"
        trace_id: "abc123def456"

  RateLimited:
    description: "Rate limit exceeded for the user's BU"
    schema:
      $ref: '#/definitions/ALBReloadError'
    examples:
      application/json:
        code: 429
        message: "Too Many Requests"
        error: "Rate limit exceeded for BU payments. Next request allowed in 3m45s"
        trace_id: "abc123def456"

  InternalServerError:
    description: "Internal server error or reload operation failed"
    schema:
      $ref: '#/definitions/ALBReloadError'
    examples:
      application/json:
        code: 500
        message: "Internal Server Error"
        error: "Failed to execute reload operation"
        trace_id: "abc123def456"

  ServiceUnavailable:
    description: "ALB reload functionality is disabled"
    schema:
      $ref: '#/definitions/ALBReloadError'
    examples:
      application/json:
        code: 503
        message: "Service Unavailable"
        error: "ALB reload functionality is disabled"
        trace_id: "abc123def456"
