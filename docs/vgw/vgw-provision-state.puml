@startuml name Anchor VGW State
title Anchor VGW State

' Will be done through playbooks at razes/sdn-control

Spare: 1.AddNodeToClusterTask

Initialising: 1.BindServersToTreeTask

Provision: 1.InitVGWTask
Provision: 2.CheckInitVGWTask

Postcheck: 1.CheckIPTablesTask
Postcheck: 2.CheckVGWImagesTask

PreMA: 1.CheckMATicketTask
PreMA: 2.StopVGWTask
PreMA: 3.CheckStopVGWTask
PreMA: 4.UpdateNodeServerStateTask
PreMA: 5.RaiseNodeMATicketTask

PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.CheckAnchorControllerDNSTask
PreRunning: 3.CheckAnchorControllerPortTask
PreRunning: 4.CheckAnchorETCDClusterHealthyTask
PreRunning: 5.StartVGWTask
PreRunning: 6.UpdateComponentsVersionTask
PreRunning: 7.SendSeeEventTask

PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.StopVGWTask
PreOffline: 3.CheckStopVGWTask

PreRetire: 1.CheckRetireTicketTask

Retiring: 1.RemoveVGWTask
Retiring: 2.CheckRemoveVGWTask
Retiring: 3.RemoveNodeFromClusterTask
Retiring: 4.UnbindServerFromTreeTask

Sunset: 1.DeleteVGWCRTask

Initialised:

Running:

[*] -> Spare

Spare --> Initialising: RunSuccessfully

Initialising --> Provision: RunSuccessfully

Provision --> Postcheck: RunSuccessfully
Provision --> Provision: RunFailed
Initialising --> Initialising: RunFailed

Initialised --> PreRunning: Raise Online Ticket

Postcheck --> Initialised: RunSuccessfully
Postcheck --> Postcheck: RunFailed

PreOffline --> Initialised: RunSuccessfully
PreOffline --> PreOffline: RunFailed

PreRunning --> PreRunning: RunFailed
PreRunning --> Running: RunSuccessfully

Running --> PreOffline: Raise Offline Ticket
Running --> PreMA: Raise MA Ticket
Running --> Running: HotUpdateType

PreMA --> Running: Rejected or cancelled
PreMA --> PreMA: RunFailed
PreMA --> MA: Approved

MA --> MA: Wait for Repair
MA --> Postcheck: Recover

Initialised --> PreRetire: Raise Retire Ticket

PreRetire --> PreRetire: WaitForApproval or RunFailed
PreRetire --> Retiring: Approved

Retiring --> Retiring: RunFailed
Retiring --> Sunset: RunSuccessfully

Sunset --> Sunset: RunFailed
Sunset --> [*]: RunSuccessfully
@enduml
