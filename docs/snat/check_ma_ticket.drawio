<mxfile host="65bd71144e">
    <diagram id="InTlwstxTB53JOaFAa5E" name="Page-1">
        <mxGraphModel dx="959" dy="659" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="16" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;" parent="1" source="2" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="PreMA_state" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" parent="1" vertex="1">
                    <mxGeometry x="10" y="210" width="190" height="86" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="2" vertex="1">
                    <mxGeometry y="26" width="190" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="+ Run(): " style="text;strokeColor=default;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="2" vertex="1">
                    <mxGeometry y="34" width="190" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="+ Next():  -&gt; to MA_state" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="2" vertex="1">
                    <mxGeometry y="60" width="190" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="23" style="edgeStyle=none;html=1;dashed=1;" parent="1" source="7" target="18" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="MA_state" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" parent="1" vertex="1">
                    <mxGeometry x="10" y="390" width="190" height="86" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="7" vertex="1">
                    <mxGeometry y="26" width="190" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="+ Run(): " style="text;strokeColor=default;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="7" vertex="1">
                    <mxGeometry y="34" width="190" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="+ Next():  -&gt; to PostCheck_state" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="7" vertex="1">
                    <mxGeometry y="60" width="190" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="+ task: raise_ma_ticket&#10;&#10;1.call API node/v1/ticket/maprocess&#10;2.save ticket ID into CRD" style="text;strokeColor=default;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="1" vertex="1">
                    <mxGeometry x="290" y="220" width="270" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="13" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="5" target="12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="+ task: check ticket status&#10;&#10;approval: Next()&#10;fail : raise event &#10;        在processNextNAT() 中 &#10;        c.natWorkQueue.AddAfter 更长的时间间隔" style="text;strokeColor=default;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="1" vertex="1">
                    <mxGeometry x="290" y="383.5" width="270" height="107" as="geometry"/>
                </mxCell>
                <mxCell id="15" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="10" target="14" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="if success" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="100" y="330" width="80" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="PostCheck_state" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" parent="1" vertex="1">
                    <mxGeometry x="10" y="560" width="190" height="86" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="18" vertex="1">
                    <mxGeometry y="26" width="190" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="+ Run(): " style="text;strokeColor=default;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="18" vertex="1">
                    <mxGeometry y="34" width="190" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="+ Next():  -&gt; to Initialised_state" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="18" vertex="1">
                    <mxGeometry y="60" width="190" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="if success" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="100" y="510" width="80" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
