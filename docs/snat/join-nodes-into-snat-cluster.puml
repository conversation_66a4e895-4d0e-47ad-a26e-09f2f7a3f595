@startuml

title Join <PERSON><PERSON>T Nodes Request

participant Bob
autonumber

Bob -> SNATCluster: Create Cluster
note right: Bind SDU

Bob -> SWP: Approval Add Nodes
SWP -> SNATController: Approve

SNATController -> SNATCluster: Fetch Cluster Meta
return

SNATController --> SWP: <PERSON><PERSON> on failure
SWP --[#red]> Bob: Show Alert Tips

SNATController -> SWP: Fetch Ticket Meta
return

SNATController -> SNATController: Verify Ticket
SNATController --> SWP: Alert on failure
SWP --[#red]> Bob: Show Alert Tips

SNATController -> SNATCluster: Fetch Cluster Meta
return

SNATController -> SNATController: Verify Cluster SDU
SNATController --> SWP: Alert on no binding
SWP --[#red]> Bob: Show Alert Tips

SNATController -> SNATController: Verify Cluster Meta
note left: Verify zookeeper\n Connectivity on Nodes
SNATController --> SWP: Alert on unconnected
SWP --[#red]> Bob: Show Alert Tips

SNATController -> NDMP: Verify Switch
note left: Verify switch existence
SNATController --> SWP: Alert on invalid switch
SWP --[#red]> Bob: Show Alert Tips

SNATController -> TOC: Fetch Servers Tags
return

SNATController -> SNATController: Verify Servers Tags
note right: general: `environment` `application:snat`\n `function:dpvs` `idc`
SNATController --> SWP: Alert on illegal tag
SWP --[#red]> Bob: Show Alert Tips

SNATController -> TOC: Fetch Server Tag Variables
return

SNATController -> SNATController: Verify Tag Variables
note right: common: `func_main` `func_sub`\n `zk Connectivity on node`

SNATController -> K8S: Add Nodes into Queue
note right: node state `Spare`
return

SNATController -> SWP: Success
SWP -> Bob: Done

@enduml
