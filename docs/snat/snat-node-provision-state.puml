@startuml

title SNAT Node Provision State

'https://confluence.shopee.io/x/jK2vdg
Spare: 1.CheckConfigFileTask
Spare: 2.AddNodeIntoClusterTask

Format:
Format: 1.BindServerTreeMainNodeTask

Provision: 1.ProvisionTask

Initialising: 1.CheckNodeProvisionStatusTask
Initialising: 2.InstallMlnxofedTask

PostInitialising: 1.CheckNetplanTask
PostInitialising: 2.CheckSnatIPTask
PostInitialising: 3.RebootServerTask

PreInitialised: 1.StartServiceTask
PreInitialised: 2.CheckServiceTask
PreInitialised: 3.EnableServiceTask
PreInitialised: 4.CheckInnerIPTask
PreInitialised: 5.AddLogRotateCrontab

note left of PreInitialised: Service: dpvs

state Initialised{
    [*] -> Initial

   Initial --> InitialisedHotUpdate: RaiseHotUpdateTicket
   InitialisedHotUpdate -> InitialisedHotUpdate: RunFailed
   InitialisedHotUpdate --> Initial: RunSuccessfully

   Initial --> InitialisedRollback: RaiseRollbackTicket
   InitialisedRollback -> InitialisedRollback: RunFailed
   InitialisedRollback --> Initial: RunSuccessfully
}
note left of Initialised: Initial done and Wait for Approval of `Online`

InitialisedHotUpdate: 1.HotUpdateProvisionTask
InitialisedHotUpdate: 2.CheckHotUpdateProvisionStatusTask

InitialisedRollback: 1.RollbackProvisionTask
InitialisedRollback: 2.CheckRollbackProvisionStatusTask

PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.CheckNodeMgmtStatusTask
PreRunning: 3.UpdateNodeServerStateTask
PreRunning: 4.SendSeeEventTask

state Running{
    [*] -> Run

    Run --> RunningHotUpdate: RaiseHotUpdateTicket
    RunningHotUpdate -> RunningHotUpdate: RunFailed
    RunningHotUpdate --> Run: RunSuccessfully

    Run --> RunningRollback: RaiseRollbackTicket
    RunningRollback -> RunningRollback: RunFailed
    RunningRollback --> Run: RunSuccessfully
}
note right of Running: Node in Serving Traffic

RunningHotUpdate: 1.HotUpdateProvisionTask
RunningHotUpdate: 2.CheckHotUpdateProvisionStatusTask
RunningHotUpdate: 3.CheckHotUpdateStatusTask
RunningHotUpdate: 4.(dpvs-metrics)PostCheckDpvsMetricsSnatTask
RunningHotUpdate: 4.(flowlog-agent)AddLogRotateCrontab
RunningHotUpdate:
RunningHotUpdate: (snat-agent)CheckSnatAgentStatusTask
RunningHotUpdate: (snat-agent)HotUpdateConfigTask
RunningHotUpdate: (snat-agent)HotUpdateTask
RunningHotUpdate: (snat-agent)CheckHotUpdateStatusTask
RunningHotUpdate: (snat-agent)RecordDpvsServiceLinesTask
RunningHotUpdate: (snat-agent)StartSnatAgentTask
RunningHotUpdate: (snat-agent)PostCheckDpvsSvcLinesTask
RunningHotUpdate: (snat-agent)PostCheckStaticRouteTask
RunningHotUpdate:
RunningHotUpdate: (dpvs)NewUpdateDpvsPrecheckTask
RunningHotUpdate: (dpvs)NewRecordDpvsServiceLinesTask
RunningHotUpdate: (dpvs)NewRecordDpvsConnTask
RunningHotUpdate: (dpvs)NewCheckSnatAgentStatusTask
RunningHotUpdate: (dpvs)NewCheckTrafficTask
RunningHotUpdate: (dpvs)NewTouchUpgradeFileTask
RunningHotUpdate: (dpvs)NewUnreadyNodeTask
RunningHotUpdate: (dpvs)NewCheckNodeMgmtStatusTask
RunningHotUpdate: (dpvs)NewUpgradeDpvsStopServiceTask
RunningHotUpdate: (dpvs)NewStopBirdTask
RunningHotUpdate: (dpvs)NewCheckTrafficTask
RunningHotUpdate: (dpvs)NewUninstallDpvsTask
RunningHotUpdate: (dpvs)NewHotUpdateConfigTask
RunningHotUpdate: (dpvs)NewHotUpdateTask
RunningHotUpdate: (dpvs)NewCheckHotUpdateStatusTask
RunningHotUpdate: (dpvs)NewInstallMlnxofedTask
RunningHotUpdate: (dpvs)NewStartDpvsServiceTask
RunningHotUpdate: (dpvs)NewCheckStartDpvsResultTask
RunningHotUpdate: (dpvs)NewRecordDpvsServiceLinesTask
RunningHotUpdate: (dpvs)NewStartSnatAgentTask
RunningHotUpdate: (dpvs)NewConnSyncModeTask
RunningHotUpdate: (dpvs)NewReadyNodeTask
RunningHotUpdate: (dpvs)NewRemoveUpgradeFileTask

RunningRollback: 1.RollbackProvisionTask
RunningRollback: 2.CheckRollbackProvisionStatusTask

PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.CheckNodeMgmtStatusTask
PreOffline: 3.SendSeeEventTask

PreMA: 1.CheckOfflineTicketTask
PreMA: 2.CheckNodeMgmtStatusTask
PreMA: 3.UpdateNodeServerStateTask
PreMA: 4.SendSeeEventTask

MA: 1.WaitForRepairManually

PreRetiring: 1.CheckRetiringTicketTask

Retiring: 1.CheckTrafficStatusTask
Retiring: 2.StopNodeServiceTask
Retiring: 3.DeleteProvisionTask
Retiring: 4.RemoveNodeTask
Retiring: 5.MoveNodeMAPoolTask
Retiring: 6.UnbindServersFromTreeTask

Sunset: 1.DeleteSNATCRTask

[*] -> Spare
Spare -> Spare: RunFailed

Spare --> Format: RunSuccessfully

Format -> Format: RunFailed
Format --> Provision: RunSuccessfully

Provision -> Provision: RunFailed
Provision --> Initialising: RunSuccessfully

Initialising -> Initialising: RunFailed
Initialising --> PostInitialising: RunSuccessfully

PostInitialising --> PostInitialising: RunFailed
PostInitialising --> PreInitialised: RunSuccessfully

PreInitialised --> PreInitialised: RunFailed
PreInitialised --> PostCheck: RunSuccessfully

PostCheck -> PostCheck: RunFailed
PostCheck --> Initialised: RunSuccessfully

Initialised -> Initialised: HotUpdateType

Initialised --> PreRunning: RaiseOnlineTicket

PreRunning -> PreRunning: WaitForApproval or RunFailed
PreRunning -> Running: Approved

Running -> Running: HotUpdateType

Running --> PreOffline: RaiseOfflineTicket
Running --> PreMA: RaiseMATicket

PreMA --> Running: Rejected or Canceled
PreMA --> PreMA: RunFailed
PreMA --> MA: Approved

MA --> MA: WaitForRepair
MA --> PostCheck: Recover

PreOffline -> PreOffline: WaitForApproval or RunFailed
PreOffline --> Initialised: Approved

Initialised --> PreRetiring: RaiseRetiringTicket

PreRetiring -> PreRetiring: WaitForApproval or RunFailed
PreRetiring --> Retiring: Approved

Retiring -> Retiring: RunFailed
Retiring --> Sunset: RunSuccessfully

Sunset -> Sunset: RunFailed
Sunset --> [*]: RunSuccessfully

@enduml
