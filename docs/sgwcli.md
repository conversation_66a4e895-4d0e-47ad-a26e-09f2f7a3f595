# SGWCLI Documentation

## Overview

`sgwcli` is a command-line interface tool for SGW (Service Gateway) node-local operations. It provides utilities for Docker container management and other node-level operations within the SGW ecosystem.

## Installation

### From Source
```bash
# Build the binary
make bin/sgwcli

# Or run directly
go run cmd/sgwcli/main.go [commands]
```

### Install Globally
```bash
go install git.garena.com/shopee/devops/sgw-addon-operator/cmd/sgwcli@latest
```

## Usage

```bash
sgwcli [global options] command [command options] [arguments...]
```

### Global Options
- `--version`: Show version information
- `--help`: Show help information

## Commands

### Docker Commands

The `docker` command provides utilities for Docker container management.

#### Generate Docker Run Command

Generate a `docker run` command from an existing running container or inspect file.

```bash
sgwcli docker generate-run <container-name>
sgwcli docker generate-run --file <inspect-file.json>
```

**Options:**
- `-f, --file`: Generate from JSON file instead of running container

**Examples:**
```bash
# Generate run command from running container
sgwcli docker generate-run nginx-proxy

# Generate run command from inspect file
sgwcli docker generate-run -f /tmp/nginx-proxy.json
```

**Output:**
The command outputs a formatted `docker run` command with all the necessary flags and options to recreate the container, including:
- Container name
- Network settings
- Volume mounts
- Environment variables
- Resource limits
- Security options
- Restart policies

#### Recreate Container

Stop, remove, and recreate a container using its current configuration.

```bash
sgwcli docker recreate <container-name>
```

**What it does:**
1. Inspects the running container and saves configuration to `/tmp/<container-name>.json`
2. Generates the docker run command and saves it to `/tmp/<container-name>.sh`
3. Stops the existing container
4. Removes the existing container
5. Recreates the container using the generated command

**Examples:**
```bash
# Recreate a container
sgwcli docker recreate nginx-proxy
```

**Backup Files:**
- `/tmp/<container-name>.json`: Container inspect output
- `/tmp/<container-name>.sh`: Generated docker run command script

**Error Handling:**
If any step fails, the backup files are preserved, and you can manually run the generated script to recreate the container.

## Version Information

Use the `--version` flag to display version information with ASCII art:

```bash
sgwcli --version
```

This will show:
- Tool name and version
- Build information (Go version, OS, architecture, compiler)
- ASCII art banner

## Configuration

`sgwcli` is designed for node-local operations and doesn't require external configuration files. All operations are performed directly on the local Docker daemon and file system.

## Use Cases

### Container Migration
Use `generate-run` to capture container configurations for migration between environments:
```bash
sgwcli docker generate-run production-app > production-app-config.sh
```

### Container Updates
Use `recreate` to safely update containers while preserving their configuration:
```bash
sgwcli docker recreate my-service
```

### Debugging
Use `generate-run` with backup files to troubleshoot container startup issues:
```bash
sgwcli docker generate-run -f /tmp/failed-container.json
```

### Keepalived Commands

The `keepalived` command provides utilities for parsing and analyzing keepalived configuration files.

#### Parse Keepalived Configuration

Parse keepalived configuration file and output structured JSON.

```bash
sgwcli keepalived parse
sgwcli keepalived parse --config /path/to/keepalived.conf
cat keepalived.conf | sgwcli keepalived parse --config -
```

**Options:**
- `-c, --config`: Path to keepalived configuration file (default: `/etc/keepalived/keepalived.conf`)
  - Use `-` to read from stdin

**Examples:**
```bash
# Parse default keepalived configuration
sgwcli keepalived parse

# Parse specific configuration file
sgwcli keepalived parse -c /etc/keepalived/keepalived.conf.bak

# Parse from stdin
cat /etc/keepalived/keepalived.conf | sgwcli keepalived parse -c -
```

**Output:**
The command outputs JSON with the following structure:
```json
{
  "keepalived_instances": {
    "VI_1": {
      "interface": "eth0",
      "state": "MASTER",
      "virtual_router_id": 51,
      "priority": 100,
      "vip": "**********",
      "without_auth": false
    },
    "VI_2": {
      "interface": "eth1",
      "state": "BACKUP",
      "virtual_router_id": 52,
      "priority": 50,
      "vip": "**********",
      "without_auth": true
    }
  }
}
```

**Fields:**
- `interface`: Network interface name
- `state`: VRRP state (MASTER or BACKUP)
- `virtual_router_id`: Virtual router ID
- `priority`: Instance priority
- `vip`: Virtual IP address (first VIP if multiple)
- `without_auth`: Whether authentication is configured

### Use Cases for Keepalived Parse

#### Configuration Validation
Validate keepalived configuration structure:
```bash
sgwcli keepalived parse && echo "Configuration is valid"
```

#### Extract VIP Information
Extract all VIPs from configuration:
```bash
sgwcli keepalived parse | jq -r '.keepalived_instances[].vip'
```

#### Check Instance States
List all MASTER instances:
```bash
sgwcli keepalived parse | jq -r '.keepalived_instances | to_entries[] | select(.value.state == "MASTER") | .key'
```

#### Compare Configurations
Compare keepalived configurations between nodes:
```bash
# On node1
sgwcli keepalived parse > node1-keepalived.json

# On node2
sgwcli keepalived parse > node2-keepalived.json

# Compare
diff node1-keepalived.json node2-keepalived.json
```

## Development

### Project Structure
- `cmd/sgwcli/main.go`: Application entry point
- `cmd/sgwcli/operator.go`: Core operator structure and version display
- `cmd/sgwcli/docker.go`: Docker-related commands and utilities
- `cmd/sgwcli/keepalived.go`: Keepalived configuration parsing utilities
- `cmd/sgwcli/version`: Version file embedded in binary

### Adding New Commands
To add new commands to `sgwcli`, extend the `Commands()` method in `operator.go` and create corresponding handler functions.

## Troubleshooting

### Common Issues

**Container not found:**
```
Error: failed to inspect container 'container-name': exit status 1
```
- Verify the container name is correct
- Ensure the container is running: `docker ps`

**Permission denied:**
```
Error: permission denied while trying to connect to Docker daemon
```
- Ensure your user is in the docker group: `sudo usermod -aG docker $USER`
- Or run with sudo: `sudo sgwcli docker recreate container-name`

**Backup file creation failed:**
- Check `/tmp` directory permissions
- Ensure sufficient disk space

### Getting Help

For command-specific help:
```bash
sgwcli help
sgwcli docker --help
sgwcli docker generate-run --help
```

## Related Tools

- `sgwctl`: Main SGW control CLI for cluster-level operations
- `docker`: Docker CLI for direct container management
- SGW Addon Operator: Kubernetes operator for SGW services
