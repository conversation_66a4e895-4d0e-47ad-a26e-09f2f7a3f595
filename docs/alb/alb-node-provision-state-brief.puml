@startuml

title ALB Node Provision State

Spare: Tasks

Format: Tasks

Provision: Tasks

Initialising: Tasks

PostCheck: Tasks

state Initialised{
    [*] -> Initial

   Initial --> InitialisedHotUpdate: RaiseTicket
   InitialisedHotUpdate -> InitialisedHotUpdate: RunFailed
   InitialisedHotUpdate --> Initial: RunSuccessfully

   Initial --> InitialisedRollback: RaiseTicket
   InitialisedRollback -> InitialisedRollback: RunFailed
   InitialisedRollback --> Initial: RunSuccessfully
}
note left of Initialised: Wait for Approval of `Online`

InitialisedHotUpdate: Tasks

InitialisedRollback: Tasks

PreRunning: Tasks

state Running{
    [*] -> Run

    Run --> RunningHotUpdate: RaiseTicket
    RunningHotUpdate -> RunningHotUpdate: RunFailed
    RunningHotUpdate --> Run: RunSuccessfully

    Run --> RunningRollback: RaiseTicket
    RunningRollback -> RunningRollback: RunFailed
    RunningRollback --> Run: RunSuccessfully
}
note right of Running: Node in Serving Traffic

RunningHotUpdate: Tasks

RunningRollback: Tasks

PreOffline: Tasks

PreMA: Tasks

MA: Tasks

[*] -> Spare
Spare -> Spare: RunFailed

Spare -> MA: CheckFailed
MA --> Spare: ManualRepair

Spare --> Format: RunSuccessfully

Format -> Format: RunFailed
Format --> Provision: RunSuccessfully

Provision -> Provision: RunFailed
Provision --> Initialising: RunSuccessfully

Initialising -> Initialising: RunFailed
Initialising --> PostCheck: RunSuccessfully

PostCheck -> PostCheck: RunFailed
PostCheck --> Initialised: RunSuccessfully

Initialised -> Initialised: HotUpdateType

Initialised --> PreRunning: RaiseOnlineTicket

PreRunning -> PreRunning: WaitForApproval or RunFailed
PreRunning -> Running: Approved

Running -> Running: HotUpdateType

Running --> PreOffline: RaiseOfflineTicket
Running --> PreMA: RaiseMATicket

PreMA --> Running: Rejected or Canceled
PreMA --> PreMA: RunFailed
PreMA --> MA: Approved

MA --> MA: WaitForRepair
MA --> PostCheck: Recover

PreOffline -> PreOffline: WaitForApproval or RunFailed
PreOffline --> Initialised: Approved

@enduml
