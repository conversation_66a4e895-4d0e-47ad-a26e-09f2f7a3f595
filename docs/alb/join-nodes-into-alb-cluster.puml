@startuml

title Join <PERSON>B Nodes Request

participant Bob
autonumber

Bob -> SWP: Approval Add Nodes
SWP -> ALBController: Approve

ALBController -> ALBCluster: Fetch Cluster Meta
return

ALBController -> ALBController: Verify Managed
note left: Managed by NonECP
ALBController --> SWP: <PERSON><PERSON> on failure
SWP --[#red]> Bob: Show Alert Tips

ALBController -> SWP: Fetch Ticket Meta
return

ALBController -> ALBController: Verify Ticket
ALBController --> SWP: Alert on failure
SWP --[#red]> Bob: Show Alert Tips

ALBController -> ALBCluster: Fetch Cluster Meta
return

ALBController -> ALBController: Verify Cluster SDU
ALBController --> SWP: Alert on no binding
SWP --[#red]> Bob: Show Alert Tips

ALBController -> ALBController: Verify Cluster Meta
note left: Verify ETCD Endpoint\nConnectivity on Nodes
ALBController --> SWP: Al<PERSON> on unconnected
SWP --[#red]> Bob: Show Alert Tips

ALBController -> TOC: Fetch Servers Tags
return

ALBController -> ALBController: Verify Servers Tags
note right: general: `environment` `application:alb`\n `function:nginx` `idc`\nprivate:`environment` `application:alb`\n `function:nginx` `az` `segment`\n `Network Zone` `HighAvailabilityMode`
ALBController --> SWP: Alert on illegal tag
SWP --[#red]> Bob: Show Alert Tips

ALBController -> TOC: Fetch Server Tag Variables
return

ALBController -> ALBController: Verify Tag Variables
note right: common: `func_main` `func_sub`\n `etcd_clusters` `alb_provision`\n `etcd|zk Connectivity on node`

ALBController -> ALBController: Verify HA Tag Variables
note right: `VIP` on HAM, BGP Peer on HAM, WAN VIP\nHTTP Tunnel
ALBController --> SWP: Alert on illegal variable
SWP --[#red]> Bob: Show Alert Tips

ALBController -> K8S: Add Nodes into Queue
note right: node state `Spare`
return

ALBController -> SWP: Success
SWP -> Bob: Done

@enduml
