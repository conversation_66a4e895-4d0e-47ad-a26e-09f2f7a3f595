@startuml

title ALB Node Provision State

Spare: 1.CheckOSTask

Format: Private: Nothing
Format: General: 1.RegisterNodeIntoSDU

Provision: 1.ProvisionTask
Provision: 2.UpdateIPTablesTask

Initialising: Private(HAM):
Initialising: 1.CheckNodeProvisionStatusTask
Initialising: 2.CheckNodeIPTablesTask
Initialising: 3.SyncNodeWAFTask
Initialising: 4.RegisterClusterDomainTask
Initialising: 5.RegisterEtcdProxyDomainTask
Initialising: 6.UpsertClusterDomainTask
Initialising: 7.UpsertClusterIPsTask
Initialising: 8.UpsertEtcdEndpointTask
Initialising: 9.AddNodeIntoClusterTask
Initialising: 10.RegisterNodeKVTask
Initialising: General:
Initialising: 1.CheckNodeProvisionStatusTask
Initialising: 2.CheckNodeIPTablesTask
Initialising: 3.SyncNodeWAFTask
Initialising: 4.PersistStaticCacheTask
Initialising: 5.UpsertEtcdEndpointTask
Initialising: 6.AddNodeIntoClusterTask
Initialising: 7.RegisterNodeKVTask

PostCheck: Private:
PostCheck: 1.CheckNginxVersionTask
PostCheck: 2.CheckNodeListenerTask
PostCheck: 3.CheckNodePeerTask
PostCheck: 4.CheckClusterDomainTask
PostCheck: General:
PostCheck: 1.CheckNginxVersionTask
PostCheck: 2.CheckNodeListenerTask
PostCheck: 3.CheckNodeStatusTask

state Initialised{
    [*] -> Initial

   Initial --> InitialisedHotUpdate: RaiseHotUpdateTicket
   InitialisedHotUpdate -> InitialisedHotUpdate: RunFailed
   InitialisedHotUpdate --> Initial: RunSuccessfully

   Initial --> InitialisedRollback: RaiseRollbackTicket
   InitialisedRollback -> InitialisedRollback: RunFailed
   InitialisedRollback --> Initial: RunSuccessfully
}
note left of Initialised: Initial done and Wait for Approval of `Online`

InitialisedHotUpdate: Default:
InitialisedHotUpdate: 1.HotUpdateProvisionTask
InitialisedHotUpdate: 2.CheckHotUpdateProvisionStatusTask
InitialisedHotUpdate: SGW-Agent:
InitialisedHotUpdate: 1.HotUpdateProvisionTask
InitialisedHotUpdate: 2.CheckHotUpdateProvisionStatusTask
InitialisedHotUpdate: 3.CheckNginxVersionTask
InitialisedHotUpdate: 4.CheckNodeListenerTask

InitialisedRollback: 1.RollbackProvisionTask
InitialisedRollback: 2.CheckRollbackProvisionStatusTask

PreRunning: Private:
PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.CheckNodeMgmtStatusTask
PreRunning: 3.StartBirdTask
PreRunning: General:
PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.CheckNodeMgmtStatusTask
PreRunning: 3.UpdateNodeServerStateTask

state Running{
    [*] -> Run

    Run --> RunningHotUpdate: RaiseHotUpdateTicket
    RunningHotUpdate -> RunningHotUpdate: RunFailed
    RunningHotUpdate --> Run: RunSuccessfully

    Run --> RunningRollback: RaiseRollbackTicket
    RunningRollback -> RunningRollback: RunFailed
    RunningRollback --> Run: RunSuccessfully
}
note right of Running: Node in Serving Traffic

RunningHotUpdate: Default:
RunningHotUpdate: 1.HotUpdateProvisionTask
RunningHotUpdate: 2.CheckHotUpdateProvisionStatusTask
RunningHotUpdate: SGW-Agent:
RunningHotUpdate: 1.HotUpdateProvisionTask
RunningHotUpdate: 2.CheckHotUpdateProvisionStatusTask
RunningHotUpdate: 3.CheckNginxVersionTask
RunningHotUpdate: 4.CheckNodeListenerTask

RunningRollback: 1.RollbackProvisionTask
RunningRollback: 2.CheckRollbackProvisionStatusTask

PreOffline: Private:
PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.CheckNodeMgmtStatusTask
PreOffline: 3.StopBirdTask
PreOffline: General:
PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.CheckNodeMgmtStatusTask

PreMA: 1.CheckOfflineTicketTask
PreMA: 2.CheckNodeMgmtStatusTask
PreMA: 3.UpdateNodeServerStateTask
PreMA: 4.RaiseNodeMATicketTask

MA: 1.WaitForRepairManually

PreRetiring: 1.CheckRetiringTicketTask

Retiring: General:
Retiring: 1.DeleteNodeFromClusterTask
Retiring: 2.DeleteProvisionTask
Retiring: 3.UnRegisterNodeTask
Retiring: 4.MoveNodeMAPoolTask
Retiring: Private:
Retiring: 1.DeleteNodeFromClusterTask
Retiring: 2.DeleteProvisionTask
Retiring: 3.UnRegisterEtcdProxyDomainTask
Retiring: 4.UnRegisterClusterDomainTask

Sunset: 1.DeleteALBCRTask

[*] -> Spare
Spare -> Spare: RunFailed

Spare -> MA: CheckFailed
MA --> Spare: ManualRepair

Spare --> Format: RunSuccessfully

Format -> Format: RunFailed
Format --> Provision: RunSuccessfully

Provision -> Provision: RunFailed
Provision --> Initialising: RunSuccessfully

Initialising -> Initialising: RunFailed
Initialising --> PostCheck: RunSuccessfully

PostCheck -> PostCheck: RunFailed
PostCheck --> Initialised: RunSuccessfully

Initialised -> Initialised: HotUpdateType

Initialised --> PreRunning: RaiseOnlineTicket

PreRunning -> PreRunning: WaitForApproval or RunFailed
PreRunning -> Running: Approved

Running -> Running: HotUpdateType

Running --> PreOffline: RaiseOfflineTicket
Running --> PreMA: RaiseMATicket

PreMA --> Running: Rejected or Canceled
PreMA --> PreMA: RunFailed
PreMA --> MA: Approved

MA --> MA: WaitForRepair
MA --> PostCheck: Recover

PreOffline -> PreOffline: WaitForApproval or RunFailed
PreOffline --> Initialised: Approved

Initialised --> PreRetiring: RaiseRetiringTicket

PreRetiring -> PreRetiring: WaitForApproval or RunFailed
PreRetiring --> Retiring: Approved

Retiring -> Retiring: RunFailed
Retiring --> Sunset: RunSuccessfully

Sunset -> Sunset: RunFailed
Sunset --> [*]: RunSuccessfully

@enduml
