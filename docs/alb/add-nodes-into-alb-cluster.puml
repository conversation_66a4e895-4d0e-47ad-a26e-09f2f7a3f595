@startuml

title Submit Add ALB Nodes Request

participant Bob
autonumber

Bob -> OpsPlat: Submit Add Nodes
note left: Pick a target ALB Cluster

OpsPlat -> ALBController: Request Raise a ticket of\n `Add SGW L7 Nodes`
ALBController -> ALBCluster: Fetch Cluster Meta
return

ALBController -> ALBController: Verify Managed
note left: Managed by NonECP
ALBController --> OpsPlat: Alert on failure
OpsPlat --[#red]> Bob: Show Alert Tips

ALBController -> ALBCluster: Fetch Nodes
return

ALBController -> ALBController: Verify Nodes Existed
ALBController --> OpsPlat: Alert on existed
OpsPlat --[#red]> Bob: Show Alert Tips

ALBController -> ALBController: Verify Cluster SDU
ALBController --> OpsPlat: Alert on no binding
OpsPlat --[#red]> Bob: Show Alert Tips

ALBController -> TOC: Fetch Servers
return

ALBController -> ALBController: Verify Servers BizAssign
ALBController --> OpsPlat: Alert on no assign at general
OpsPlat --[#red]> Bob: Show Alert Tips

ALBController -> ALBController: Verify Servers Tags
note right: general: `environment` `application:alb`\n `function:nginx` `idc`\nprivate:`environment` `application:alb`\n `function:nginx` `az` `segment`\n `Network Zone` `HighAvailabilityMode`
ALBController --> OpsPlat: Alert on illegal tag
OpsPlat --[#red]> Bob: Show Alert Tips

ALBController -> ALBController: Fetch Component Version

ALBController -> SWP: Create Ticket
return

ALBController -> OpsPlat: Response SWP Ticket
OpsPlat -> Bob: Show Ticket Tips

@enduml
