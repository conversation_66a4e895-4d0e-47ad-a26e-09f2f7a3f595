# how-to-revert-provision

## purge docker containers

```http request
### node purge provision containers
DELETE {{host}}/alb/v1/node/provision/purge/container/
```

## purge legacy configs

it contains components and dependent templates

```http request
### node purge provision configs
DELETE {{host}}/alb/v1/node/provision/purge/
```

## double check provision config after purge

```http request
### node provision config ***********
GET {{host}}/alb/v1/node/provision/?ips=***********
```

## recover `iptables` and `hostname`

1. run playbook `AZ Server Init Common V2`
2. check `/etc/sysconfig/iptables` `/etc/hostname`

## generate ALB node provision config to review

```http request
### gen node provision config
POST {{host}}/alb/v1/node/provision/
```

## reset ALB node state to provision

```http request
### node ************** update provision state
PUT {{host}}/alb/v1/node/state/?ip=***********&state=provision&reason=reprovision
```

## check provision config

```http request
### node provision config ***********
GET {{host}}/alb/v1/node/provision/?ips=***********
```
