# ALB Provision for Release Platform

## Summary

Provide 3 APIs to upgrade/rollback/check node provision progress.

### upgrade

1. request parameter including nodes IP address list, swp ticket id, component name, version.
2. validation

    2.1 swp must be approved

    2.2 component name must be alb core component list

    2.3 version must be not the same as the CR one

    2.4 node's state must be `Initialised` or `Running`

    2.5 foreach node ip list then generate provision config for every node

    2.6 update node state `InitialisedHotUpdate` or `RunningHotUpdate` with provision config

### rollback

1. the same as upgrade

### check

1. request parameter including node IP address list
2. fetch node state from CR and task result from cachecloud
