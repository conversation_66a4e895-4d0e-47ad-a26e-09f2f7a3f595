@startuml name Anchor Controller State
title Anchor Controller State

' Will be done through playbooks at razes/sdn-control

Spare: 1.AddNodeToClusterTask

Initialising: 1.BindServersToTreeTask

Provision: 1.UpdateIPTablesTask
Provision: 2.DeployControllerTask
Provision: 3.DeployEDSTask
Provision: 4.DeployExporterTask
Provision: 5.DeployStatusAPITask
Provision: 6.DeployReleaseAPITask

' Pinger is only deployed on 1 host
Provision: Pinger Host
Provision: 7.DeployPingerTask
Provision: 8.DeployETCDBackupTask

Provision: 9.CheckAnchorDeploymentTask

Postcheck: 1.CheckIPTablesTask
Postcheck: 2.CheckAnchorImagesTask

PreMA: 1.CheckMATicketTask
PreMA: 2.StopControllerTask
PreMA: 3.CheckStopControllerTask
PreMA: 3.UpdateNodeServerStateTask
PreMA: 4.RaiseNodeMATicketTask

PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.CheckAnchorControllerDNSTask
PreRunning: 3.CheckAnchorControllerPortTask
PreRunning: 4.CheckAnchorETCDClusterHealthyTask
PreRunning: 5.StartControllerTask
PreRunning: 6.UpdateComponentsVersionTask
PreRunning: 7.SendSeeEventTask

PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.StopControllerTask
PreOffline: 3.CheckStopControllerTask

PreRetire: 1.CheckRetireTicketTask

Retiring: 1.RemoveAnchorTask
Retiring: 2.CheckRemoveAnchorTask
Retiring: 3.RemoveNodeFromClusterTask
Retiring: 4.UnbindServerFromTreeTask

Sunset: 1.DeleteAnchorCRTask

Initialised:

Running:

[*] -> Spare

Spare --> Initialising: RunSuccessfully

Initialising --> Provision: RunSuccessfully

Provision --> Postcheck: RunSuccessfully
Provision --> Provision: RunFailed
Initialising --> Initialising: RunFailed

Initialised --> PreRunning: Raise Online Ticket

Postcheck --> Initialised: RunSuccessfully
Postcheck --> Postcheck: RunFailed

PreOffline --> Initialised: RunSuccessfully
PreOffline --> PreOffline: RunFailed

PreRunning --> PreRunning: RunFailed
PreRunning --> Running: RunSuccessfully

Running --> PreOffline: Raise Offline Ticket
Running --> PreMA: Raise MA Ticket
Running --> Running: HotUpdateType

PreMA --> Running: Rejected or cancelled
PreMA --> PreMA: RunFailed
PreMA --> MA: Approved

MA --> MA: Wait for Repair
MA --> Postcheck: Recover

Initialised --> PreRetire: Raise Retire Ticket

PreRetire --> PreRetire: WaitForApproval or RunFailed
PreRetire --> Retiring: Approved

Retiring --> Retiring: RunFailed
Retiring --> Sunset: RunSuccessfully

Sunset --> Sunset: RunFailed
Sunset --> [*]: RunSuccessfully
@enduml
