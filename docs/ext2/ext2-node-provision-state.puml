@startuml
title Ext2 Node Provision State

Spare: 1.CheckOSTask

MA: 1.WaitForRepairManually

Format:  1.RegisterNodeIntoSDU

Provision: 1.ProvisionTask
Provision: 2.UpdateIPTablesTask


Initialising: 1.CheckNodeProvisionStatusTask
Initialising: 2.CheckNodeIPTablesTask


PostCheck: 1.CheckNginxTask
PostCheck: 2.CheckSGWAgentTask
PostCheck: 3.CheckExt2MetricsTask
PostCheck: 4.CheckDNSTask
PostCheck: 5.CheckNLBListenerTask
PostCheck: 6.CheckSnatRoutingTable

state Initialised{
    [*] -> Initial

   Initial --> InitialisedHotUpdate: RaiseHotUpdateTicket
   InitialisedHotUpdate -> InitialisedHotUpdate: RunFailed
   InitialisedHotUpdate --> Initial: RunSuccessfully

   Initial --> InitialisedRollback: RaiseRollbackTicket
   InitialisedRollback -> InitialisedRollback: RunFailed
   InitialisedRollback --> Initial: RunSuccessfully
}
note left of Initialised: Initial done and Wait for Approval of `Online`


InitialisedHotUpdate: 1.HotUpdateProvisionTask
InitialisedHotUpdate: 2.CheckHotUpdateProvisionStatusTask
InitialisedHotUpdate: 3.CheckNginxTask
InitialisedHotUpdate: 4.CheckSGWAgentTask
InitialisedHotUpdate: 5.CheckExt2MetricsTask
InitialisedHotUpdate: 6.CheckDNSTask
InitialisedHotUpdate: 7.CheckNLBListenerTask
InitialisedHotUpdate: 8.CheckSnatRoutingTable

InitialisedRollback: 1.RollbackProvisionTask
InitialisedRollback: 2.CheckRollbackProvisionStatusTask


PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.CheckNodeMgmtStatusTask

state Running{
    [*] -> Run

    Run --> RunningHotUpdate: RaiseHotUpdateTicket
    RunningHotUpdate -> RunningHotUpdate: RunFailed
    RunningHotUpdate --> Run: RunSuccessfully

    Run --> RunningRollback: RaiseRollbackTicket
    RunningRollback -> RunningRollback: RunFailed
    RunningRollback --> Run: RunSuccessfully
}
note right of Running: Node in Serving Traffic

RunningHotUpdate: 1.HotUpdateProvisionTask
RunningHotUpdate: 2.CheckHotUpdateProvisionStatusTask
RunningHotUpdate: 3.CheckNginxTask
RunningHotUpdate: 4.CheckSGWAgentTask
RunningHotUpdate: 5.CheckExt2MetricsTask
RunningHotUpdate: 6.CheckDNSTask
RunningHotUpdate: 7.CheckNLBListenerTask
RunningHotUpdate: 8.CheckSnatRoutingTable

RunningRollback: 1.RollbackProvisionTask
RunningRollback: 2.CheckRollbackProvisionStatusTask


PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.CheckNodeMgmtStatusTask

PreRetiring: 1.CheckRemovingTicketTask


Retiring: 1.DeleteNodeFromClusterTask
Retiring: 2.DeleteProvisionTask

Sunset: 1.DeleteExt2CRTask

[*] -> Spare
Spare -> Spare: RunFailed

Spare -> MA: CheckFailed
MA --> Spare: ManualRepair

Spare --> Format: RunSuccessfully

Format -> Format: RunFailed
Format --> Provision: RunSuccessfully

Provision -> Provision: RunFailed
Provision --> Initialising: RunSuccessfully

Initialising -> Initialising: RunFailed
Initialising --> PostCheck: RunSuccessfully

PostCheck -> PostCheck: RunFailed
PostCheck --> Initialised: RunSuccessfully

Initialised -> Initialised: HotUpdateType

Initialised --> PreRunning: RaiseOnlineTicket

PreRunning -> PreRunning: WaitForApproval or RunFailed
PreRunning -> Running: Approved

Running -> Running: HotUpdateType

Running --> PreOffline: RaiseOfflineTicket

PreOffline -> PreOffline: WaitForApproval or RunFailed
PreOffline --> Initialised: Approved

Initialised --> PreRetiring: RaiseRemovingTicket

PreRetiring -> PreRetiring: WaitForApproval or RunFailed
PreRetiring --> Retiring: Approved

Retiring -> Retiring: RunFailed
Retiring --> Sunset: RunSuccessfully

Sunset -> Sunset: RunFailed
Sunset --> [*]: RunSuccessfully
@enduml
