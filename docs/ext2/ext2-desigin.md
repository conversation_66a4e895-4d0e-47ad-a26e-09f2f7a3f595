# Ext2 K8s Controller Technical Design

The ext2 controller is a k8s controller that manages the ext2 components.

## Architecture

this section describes the architecture of the ext2 controller.

```mermaid

graph LR
    A[HTTP API] --> B[Ext2 Controller]
    B --> C[TOC API]
    C --> D[TOC Agent]
    D --> E[Ext2 Node]
```

TOC is our internal platform, we use the TOC API to do the task on the node.

## State Machine

- we use the state machine to manage the ext2 node's lifecycle. the state machine is as follow table.
- foreach state, we have a task to do, the controller will call TOC api to do the task on the node.
- we can use the shell script to do the task, the shell script is stored in the `internal/tpl` directory.

| 状态                     | 任务                                                                                                                                                                                                                |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Spare**                | 1. CheckOSTask                                                                                                                                                                                                      |
| **MA**                   | 1. WaitForRepairManually                                                                                                                                                                                            |
| **Format**               | 1. RegisterNodeIntoSDU                                                                                                                                                                                              |
| **Provision**            | 1. ProvisionTask<br>2. UpdateIPTablesTask                                                                                                                                                                           |
| **Initialising**         | 1. CheckNodeProvisionStatusTask<br>2. CheckNodeIPTablesTask                                                                                                                                                         |
| **PostCheck**            | 1. CheckNginxTask<br>2. CheckSGWAgentTask<br>3. CheckExt2MetricsTask<br>4. CheckDNSTask<br>5. CheckNLBListenerTask<br>6. CheckSnatRoutingTable                                                                      |
| **Initialised**          | Initial done and Wait for Approval of `Online`                                                                                                                                                                      |
| **InitialisedHotUpdate** | 1. HotUpdateProvisionTask<br>2. CheckHotUpdateProvisionStatusTask<br>3. CheckNginxTask<br>4. CheckSGWAgentTask<br>5. CheckExt2MetricsTask<br>6. CheckDNSTask<br>7. CheckNLBListenerTask<br>8. CheckSnatRoutingTable |
| **InitialisedRollback**  | 1. RollbackProvisionTask<br>2. CheckRollbackProvisionStatusTask                                                                                                                                                     |
| **PreRunning**           | 1. CheckOnlineTicketTask<br>2. CheckNodeMgmtStatusTask                                                                                                                                                              |
| **Running**              | Node in Serving Traffic                                                                                                                                                                                             |
| **RunningHotUpdate**     | 1. HotUpdateProvisionTask<br>2. CheckHotUpdateProvisionStatusTask<br>3. CheckNginxTask<br>4. CheckSGWAgentTask<br>5. CheckExt2MetricsTask<br>6. CheckDNSTask<br>7. CheckNLBListenerTask<br>8. CheckSnatRoutingTable |
| **RunningRollback**      | 1. RollbackProvisionTask<br>2. CheckRollbackProvisionStatusTask                                                                                                                                                     |
| **PreOffline**           | 1. CheckOfflineTicketTask<br>2. CheckNodeMgmtStatusTask                                                                                                                                                             |
| **PreRetiring**          | 1. CheckRemovingTicketTask                                                                                                                                                                                          |
| **Retiring**             | 1. DeleteNodeFromClusterTask<br>2. DeleteProvisionTask                                                                                                                                                              |
| **Sunset**               | 1. DeleteExt2CRTask                                                                                                                                                                                                 |

### Task Description

- CheckOSTask

## Internal Design

### How to deploy the ext2 components

1. We using the docker image to deploy the ext2 components on physical machine or VM
2. The images is stored in the docker registry. harbor.shopeemobile.com

#### High Availability Modes

| Mode       | Description                      |
| ---------- | -------------------------------- |
| single     | single node,                     |
| Keepalived | keepalived for high availability |
| ECMP-BGP   | ECMP-BGP for high availability   |

### Ext2 Components

this section describes the components of the ext2.

| Name         | Description                               | Ports                        |
| ------------ | ----------------------------------------- | ---------------------------- |
| swg-agent    | automatically syn configure file to local |                              |
| ext2-metrics | collect ext2's metrics                    |                              |
| ext2-nginx   | traffic proxy service                     | `1080` `2280` `3280` `10701` |

Tips:

- only private AZ expose the 10701 port, the other AZs don't expose the 10701 port. we according to the `az_type` tag to determine the AZ type.

## User Manual

the user manual describes how to use the ext2 controller. the Ext2 controller support the following operations.

### 1.Add Node

- before to add Node, you need to add following labels to the node

| Name    | Description             |
| ------- | ----------------------- |
| idc     | the idc of the ext2     |
| ha-mode | the ha mode of the ext2 |

### 2. Online Node

TODO

### 3. Offline Node

TODO

### 4. Upgrade components version

TODO
