@startuml
title ETCD Node Provision State

Spare: 1.AddAZMetaNodeTask

Format: 1.BindServersToTreeTask

Provision: 1.ProvisionTask
Provision: 2.UpdateIPTablesTask
Provision: TLS:
Provision: 1.GenerateEtcdCertsTask
Provision: Private:
Provision: 1.EtcdMetricsProvisionTask

PostProvision: 1.RemoveOldMetricsTask
PostProvision: 2.DeleteOldMetricsProvisionTask
PostProvision: 3.MetricsProvisionTask
PostProvision: 4.CheckMetricsProvisionTask

Initialising: 1.CheckNodeProvisionTask
Initialising: 2.BindTreeNodeTask
Initialising: Private:
Initialising: 1.CheckMetricsProvisionTask

Postcheck: 1.CheckIPTablesTask
Postcheck: 2.CheckEtcdBinaryTask
Postcheck: TLS:
Postcheck: 1.CheckCertificatesTask

Sunset: 1.DeleteETCDCRTask

PreMA: 1.CheckMATicketTask
PreMA: 2.StopEtcdTask
PreMA: 3.UpdateNodeServerStateTask
PreMA: 4.RaiseNodeMATicketTask

Initialised:

Running:

PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.StartEtcdTask
PreRunning: 3.CheckETCDExistingTask


PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.StopEtcdTask

PreRetire: 1.CheckRetireTicketTask

Retiring: 1.KickEtcdMemberTask
Retiring: 2.DeleteEtcdFilesTask
Retiring: 3.DeleteProvisionTask
Retiring: 4.RemoveETCDHostTask
Retiring: 5.RemoveAZMetaNodeTask
Retiring: 6.UnbindTreeNodeTask

[*] -> Spare

Spare --> Format: RunSuccessfully

Format --> Provision: RunSuccessfully

Provision --> Initialising: RunSuccessfully

Initialising --> Postcheck: RunSuccessfully

Provision --> Provision: RunFailed

Initialising --> Initialising: RunFailed

Initialised --> PreRunning: Raise Online Ticket

Postcheck --> Initialised: RunSuccessfully

Postcheck --> Postcheck: RunFailed

PreOffline --> Initialised: RunSuccessfully

PreOffline --> PreOffline: RunFailed

PreRunning --> PreRunning: RunFailed

PreRunning --> Running: RunSuccessfully

Running --> PreOffline: Raise Offline Ticket

Running --> PreMA: Raise MA Ticket

Running --> PostProvision: API

PostProvision --> Running: RunSuccessfully

PostProvision --> PostProvision: RunFailed

PreMA --> Running: Rejected or cancelled

PreMA --> PreMA: RunFailed

PreMA --> MA: Approved

MA --> MA: Wait for Repair

MA --> Postcheck: Recover

Initialised --> PreRetire: Raise Retire Ticket

PreRetire --> PreRetire: WaitForApproval or RunFailed

PreRetire --> Retiring: Approved

Retiring --> Retiring: RunFailed

Retiring --> Sunset: RunSuccessfully

Sunset --> Sunset: RunFailed

Sunset --> [*]: RunSuccessfully
@enduml
