@startuml
title CheckETCDExistingTask Decision Tree

EtcdClusterHealthy: CheckHealth()

EtcdInMemberList: CheckMemberList()

AddEtcdMember: AddMember()

CheckEtcdMemberStarted: CheckIfStarted()

CheckEtcdMemberLearner: CheckIfLearner()

PromoteEtcdMember: Promote()

Error: CheckETCDLogs

EtcdClusterHealthy --> EtcdInMemberList: Yes

EtcdClusterHealthy --> Skip: No

EtcdInMemberList --> CheckEtcdMemberStarted: Yes

CheckEtcdMemberStarted --> CheckEtcdMemberLearner: Yes

CheckEtcdMemberStarted --> Error: No

CheckEtcdMemberLearner --> Skip: No

CheckEtcdMemberLearner --> PromoteEtcdMember: Yes

PromoteEtcdMember --> [*]: RunSuccessfully

EtcdInMemberList --> AddEtcdMember: No

AddEtcdMember --> AddEtcdMember: RunFailed

AddEtcdMember --> CheckEtcdMemberStarted: RunSuccessfully

Skip --> [*]

@enduml
