# OpsPlatform API
OpsPlatform Backend web server

## Version: 1.0

### Terms of service
<http://swagger.io/terms/>

**Contact information:**  
AZ SRE Devs  
<https://confluence.shopee.io/x/mB_0Bg>  
<EMAIL>  

**License:** [Apache 2.0](http://www.apache.org/licenses/LICENSE-2.0.html)

### Security
**ApiKeyAuth**  

| apiKey | *API Key* |
| ------ | --------- |
| Description | Using JWT as an access key |
| In | header |
| Name | authorization |

**BearerToken**  

| basic | *Basic* |
| ----- | ------- |

---
### /alb/api/version

#### GET
##### Summary

get alb api version

##### Description

get alb api version details

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | object |

---
### /alb/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from alb-cluster mgmt

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ClusterListResponse](#albvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/cluster/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/cluster/uuid/{uuid}/config/

#### GET
##### Summary

fetch cluster config

##### Description

fetch cluster configuration store in azmeta

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [sgwvo.ALBClusterConfigMeta](#sgwvoalbclusterconfigmeta) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/cluster/uuid/{uuid}/meta/sync/

#### PUT
##### Summary

fresh cluster config from tag vars

##### Description

fresh cluster configuration into azmeta

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/cluster/uuid/{uuid}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeInfosResponse](#albvonodeinfosresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/cluster/uuid/{uuid}/node/nlb/listener/

#### GET
##### Summary

fetch cluster node's NLB listener list

##### Description

fetch cluster node L4 listener by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |
| ip | query | alb node's ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ClusterNodeNLBListenerResponse](#albvoclusternodenlblistenerresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/cluster/uuid/{uuid}/node/precheck/

#### POST
##### Summary

check cluster nodes' condition before online

##### Description

check nodes' server state, iptables, nf_conntrack etc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ClusterNodePreCheckResponse](#albvoclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/cluster/uuid/{uuid}/node/precheck_offline/

#### POST
##### Summary

check cluster nodes' condition before offline

##### Description

check instances' advertise status etc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ClusterNodePreCheckResponse](#albvoclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/cluster/uuid/{uuid}/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/cluster/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/cluster/uuid/{uuid}/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeResponse](#ext2vonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /alb/v1/component/

#### GET
##### Summary

get alb component list

##### Description

get alb component list

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ComponentListResponse](#albvocomponentlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/component/version/

#### GET
##### Summary

fetch alb component versions

##### Description

fetch alb related components' version list

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| name | body | component name | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ComponentVersionResponse](#albvocomponentversionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /alb/v1/node/

#### GET
##### Summary

fetch node list

##### Description

fetch node list with details, like state, tasks etc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | alb nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeInfosResponse](#albvonodeinfosresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/component/

#### GET
##### Summary

fetch node components' version

##### Description

fetch node's installed components via toc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node components' version

##### Description

fetch node's installed components via toc then update into k8s runtime

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/component/version/

#### PUT
##### Summary

update node component's version

##### Description

update specified version into k8s runtime

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | alb node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeEventResponse](#albvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/expire/

#### PATCH
##### Summary

clean-up expired nodes

##### Description

delete those expired nodes under Initialised or Running state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/ma/

#### PATCH
##### Summary

set node maintenance ticket

##### Description

set node maintenance ticket

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/

#### DELETE
##### Summary

delete node provision config

##### Description

delete node provision configs, whether template or component

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| configs | body | node provision configs | Yes | [ [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### GET
##### Summary

fetch node provision config

##### Description

fetch node provision configs, including template, component and events

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | alb nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeProvisionResponse](#albvonodeprovisionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/abort/

#### PUT
##### Summary

abort node hot-update rollback

##### Description

just update node state to Running or Initialised

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/component/

#### DELETE
##### Summary

delete node provision component

##### Description

delete node provision configs only components

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | node IP address list | Yes | [ string ] |
| components | body | node provision component list by name | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node provision component

##### Description

update node provision configs only components

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | node IP address list | Yes | [ string ] |
| components | body | node provision component list by name | Yes | [ string ] |
| disabled | body | node provision component disable or enable | Yes | boolean |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/purge/

#### DELETE
##### Summary

delete node provision total config

##### Description

delete node provision configs, including defined templates and components

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | node IP address list | Yes | [ [albvo.NodeRequest](#albvonoderequest) ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/purge/container/

#### DELETE
##### Summary

delete node provision docker container

##### Description

delete node provision docker type component

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | node IP address list | Yes | [ string ] |
| components | body | node provision component list by name | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/reinit/

#### PUT
##### Summary

re-init node before initialised, trigger provision workflow

##### Description

update node state to Spare, they must be before initialised

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/rollback/

#### PUT
##### Summary

trigger node hot-update rollback

##### Description

auto-gen node provision configs, then update node state to hot-update rollback

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | string |
| component | body | component name, options: alb-metrics, sgw-agent, alb-sd, mesos-nginx-lb, nginx-shopee | Yes | string |
| version | body | rollback target version | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |
| ticket_id | body | swp ticket id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeProvisionConfigResponse](#albvonodeprovisionconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/template/

#### DELETE
##### Summary

delete node provision template

##### Description

delete node provision configs only templates

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | node IP address list | Yes | [ string ] |
| paths | body | node provision templates | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/provision/upgrade/

#### GET
##### Summary

generate node provision config

##### Description

fetch node provision configs, including template, component

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| component | body | component name | Yes | string |
| version | body | upgrade target version | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeProvisionConfigResponse](#albvonodeprovisionconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

trigger node hot-update

##### Description

auto-gen node provision configs, then update node state to hot-update

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | string |
| component | body | component name, options: alb-metrics, sgw-agent, alb-sd, mesos-nginx-lb, nginx-shopee | Yes | string |
| version | body | upgrade target version | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |
| ticket_id | body | swp ticket id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeProvisionConfigResponse](#albvonodeprovisionconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | alb nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeSpecResponse](#albvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | alb nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/state/

#### GET
##### Summary

fetch node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.GetNodeResponse](#albvogetnoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PATCH
##### Summary

reentrant node state info

##### Description

reentrant node state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/state/{state}/

#### GET
##### Summary

fetch nodes by state

##### Description

fetch nodes by state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [sgwvo.StateNodesResponse](#sgwvostatenodesresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | alb nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeTagVariableResponse](#albvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/node/provision/reinit/

#### PUT
##### Summary

re-init node before initialised, trigger provision workflow

##### Description

update node state to Spare, they must be before initialised

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeResponse](#ext2vonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /alb/v1/sdu/exporter/

#### GET
##### Summary

get monitor tags

##### Description

get monitor tags by serviceName and sdu

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| service | query | serviceName | Yes | string |
| sdu | query | sdu | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [cmdbvo.GetMonitorTagsResponse](#cmdbvogetmonitortagsresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/sdu/server/

#### POST
##### Summary

bind servers into sdu

##### Description

bind servers with IP addresses into specified SDU

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| req | body | request body | Yes | [cmdbvo.BindServerRequest](#cmdbvobindserverrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /alb/v1/state/

#### GET
##### Summary

get alb state list

##### Description

get alb state list

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.GetStatesResponse](#albvogetstatesresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/state/task/

#### GET
##### Summary

get alb state runner task list

##### Description

get alb state runner task list

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.GetStateTasksResponse](#albvogetstatetasksresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/state/task/result/

#### GET
##### Summary

get alb state runner task results

##### Description

get alb state runner task results

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.GetStateTasksResponse](#albvogetstatetasksresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /alb/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about ALB operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | ALB Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| uuid | body | cluster uuid, included in the form_data | Yes | string |
| type | body | ticket type, option:online offline ma | No | string |
| reason | body | ticket reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.TicketResponse](#albvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/block/

#### POST
##### Summary

block alb cluster traffic

##### Description

block alb cluster traffic, label: ShopeePay

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| rz | body | blocked rz | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.TicketResponse](#albvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/block/callback/

#### POST
##### Summary

block alb cluster traffic callback

##### Description

block alb cluster traffic, label: ShopeePay

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | blocked node ips | Yes | string |
| applicant_user | body | applicant user id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ControlTrafficTaskResultsResponse](#albvocontroltraffictaskresultsresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/block_by_domains/

#### POST
##### Summary

block alb cluster traffic by domains

##### Description

block alb cluster traffic by domains, label: ShopeePay

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| rz | body | blocked rz | Yes | string |
| network_type | body | blocked network type | Yes | string |
| domains | body | blocked domains | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.TicketResponse](#albvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/block_by_domains/callback/

#### POST
##### Summary

block alb cluster traffic callback

##### Description

block alb cluster traffic, label: ShopeePay

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | blocked node ips | Yes | string |
| applicant_user | body | applicant user id | Yes | string |
| clusters | body | blocked cluster names | Yes | string |
| network_type | body | blocked cluster network type | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ControlTrafficTaskResultsResponse](#albvocontroltraffictaskresultsresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/config/

#### POST
##### Summary

create fresh cluster config ticket

##### Description

create a ticket to fresh cluster config

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | body | cluster uuid | Yes | string |
| config | body | cluster config | Yes | [sgwvo.L7ClusterConfig](#sgwvol7clusterconfig) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.TicketResponse](#albvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/config/callback/

#### POST
##### Summary

fresh cluster config ticket callback

##### Description

ticket callback to fresh cluster config

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | body | cluster uuid | Yes | string |
| config | body | cluster config | Yes | [sgwvo.L7ClusterConfig](#sgwvol7clusterconfig) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/open/

#### POST
##### Summary

open alb cluster traffic

##### Description

open alb cluster traffic; label: ShopeePay

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| rz | body | opened rz | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.TicketResponse](#albvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/open/callback/

#### POST
##### Summary

open alb cluster traffic callback

##### Description

open alb cluster traffic; label: ShopeePay

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | opened node ips | Yes | string |
| applicant_user | body | applicant user id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ControlTrafficTaskResultsResponse](#albvocontroltraffictaskresultsresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/open_by_domain/

#### POST
##### Summary

open alb cluster traffic

##### Description

open alb cluster traffic; label: ShopeePay

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| rz | body | opened rz | Yes | string |
| network_type | body | opened network type | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.TicketResponse](#albvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/cluster/open_by_domains/callback/

#### POST
##### Summary

open alb cluster traffic by domains callback

##### Description

open alb cluster traffic by domains; label: ShopeePay

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | opened node ips | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ControlTrafficTaskResultsResponse](#albvocontroltraffictaskresultsresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/id/{id}/

#### GET
##### Summary

get ticket

##### Description

get ticket by ticket id

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | query | swp ticket id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [swpvo.TicketGetResp](#swpvoticketgetresp) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to alb cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:ALB | Yes | string |
| uuid | body | alb cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ClusterNodeTicketResponse](#albvoclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into alb cluster

##### Description

add nodes into alb cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [albvo.ClusterAddNodeCBRequest](#albvoclusteraddnodecbrequest) |
| id | body | related ticket id | Yes | [albvo.ClusterAddNodeCBRequest](#albvoclusteraddnodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.ClusterNodeTicketResponse](#albvoclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/node/config/

#### POST
##### Summary

create hot update node ticket

##### Description

create hot update node ticket

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| is_overall | body | cluster's all nodes | No | boolean |
| uuid | body | cluster uuid | Yes | string |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| form_data | body | hot update ticket info | Yes | [albvo.HotUpdateTicketFormData](#albvohotupdateticketformdata) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.TicketResponse](#albvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/node/config/callback/

#### POST
##### Summary

hot update node ticket callback when gray stage or next

##### Description

batch hot update node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| baseRequest | body | nodes base information | Yes | [albvo.HotUpdateNodeBaseRequest](#albvohotupdatenodebaserequest) |
| operation_type | body | operation type,one of update,delete,add | Yes | string |
| target_config | body | target config info | Yes | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |
| rollback_config | body | rollback config info | Yes | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/node/config/reset/

#### POST
##### Summary

reset node

##### Description

set nodes HotUpdateConfig to nil

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | reset nodes ip address | Yes | string |
| total_nodes | body | number of reset nodes | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/node/config/rollback/

#### POST
##### Summary

rollback node when gray stage rejected

##### Description

batch rollback node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| stage | body | rollback stage | No | string |
| business_type | body | business type | Yes | string |
| ips | body | all node ip address | Yes | string |
| version | body | rollback config's version | Yes | string |
| total_nodes | body | total nodes number | Yes | string |
| gray_ips | body | gray rollback node ip address | Yes | string |
| total_gray_nodes | body | gray rollback nodes number | Yes | string |
| total_other_nodes | body | not rollback nodes number | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/node/retire/

#### POST
##### Summary

create remove node ticket

##### Description

create a ticket to remove nodes from alb cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | removed nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:ALB | Yes | string |
| uuid | body | alb cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.TicketResponse](#albvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /alb/v1/ticket/offline/

#### POST
##### Summary

check whether node can be offline

##### Description

check whether node can be offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | ip | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.TicketResponse](#albvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /anchor/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from Anchor Cluster Mgmt

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.ClusterListResponse](#anchorvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/cluster/subnets/

#### GET
##### Summary

fetch cluster subnet details

##### Description

fetch cluster subnet details

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.ClusterSubnetResponse](#anchorvoclustersubnetresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/cluster/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/cluster/uuid/{uuid}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.ClusterNodeResponse](#anchorvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/cluster/uuid/{uuid}/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.ClusterNodeResponse](#anchorvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /anchor/v1/node/

#### GET
##### Summary

fetch node lists

##### Description

fetch nodes

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.NodeListResponse](#anchorvonodelistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.NodeEventResponse](#anchorvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/node/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.NodeResponse](#anchorvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | anchor nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.NodeSpecResponse](#anchorvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | anchor nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.NodeResponse](#anchorvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/node/state/

#### GET
##### Summary

fetch node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.GetNodeResponse](#anchorvogetnoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node state info

##### Description

update node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.NodeTagVariableResponse](#anchorvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /anchor/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about anchor operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | anchor Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| type | body | ticket type, option:online offline prema | No | string |
| reason | body | ticket reason | No | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.TicketResponse](#anchorvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to anchor cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:Anchor | Yes | string |
| uuid | body | Anchor cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.TicketResponse](#anchorvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into anchor cluster

##### Description

add nodes into anchor cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [anchorvo.TicketAddCBRequest](#anchorvoticketaddcbrequest) |
| id | body | related ticket id | Yes | [anchorvo.TicketAddCBRequest](#anchorvoticketaddcbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.NodeResponse](#anchorvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/ticket/node/retire/

#### POST
##### Summary

create retire node ticket

##### Description

create a ticket to retire nodes from Anchor cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | retired nodes ip address | Yes | [ string ] |
| type | body | operation type, option:online offline ma retire | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.TicketResponse](#anchorvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /anchor/v1/ticket/node/retire/callback/

#### POST
##### Summary

retire nodes into anchor cluster

##### Description

retire nodes from anchor cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | body | related ticket id | Yes | [anchorvo.RetireNodeCBRequest](#anchorvoretirenodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.NodeResponse](#anchorvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /bot/v1/chat/

#### GET
##### Summary

fetch feedback for InfraBot via chat at seatalk

##### Description

auto-feedback according to the message

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| q | query | query message | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [middlewares.Response](#middlewaresresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /docs/bot/usage.md

#### GET
##### Summary

fetch usage for InfraBot via chat at seatalk

##### Description

how to chat with InfraBot

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | string |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /dns/v1/node/expire/

#### PATCH
##### Summary

clean-up expired nodes

##### Description

delete those expired nodes under Initialised or Running state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v1/node/ma/

#### PATCH
##### Summary

set node maintenance ticket

##### Description

set node maintenance ticket

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v1/node/state/{state}/

#### GET
##### Summary

fetch nodes by state

##### Description

fetch nodes by state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [sgwvo.StateNodesResponse](#sgwvostatenodesresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/

#### GET
##### Summary

fetch node lists

##### Description

fetch node details from AZMeta

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeListResponse](#dnsvonodelistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeEventResponse](#dnsvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/list/

#### GET
##### Summary

fetch node list

##### Description

fetch node list with details, like state, tasks etc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeInfosResponse](#dnsvonodeinfosresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/provision/

#### GET
##### Summary

fetch node provision config

##### Description

fetch node provision configs, including template, component and events

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeProvisionResponse](#dnsvonodeprovisionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/provision/abort/

#### PUT
##### Summary

abort node hot-update rollback

##### Description

just update node state to Running or Initialised

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/provision/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/provision/rollback/

#### PUT
##### Summary

trigger node hot-update rollback

##### Description

auto-gen node provision configs, then update node state to hot-update rollback

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | string |
| component | body | component name, options: shopee-dpdkdns-kernel dpdkdns-config-shopee shopee-dns-agent | Yes | string |
| version | body | rollback target version | Yes | string |
| ticket_id | body | swp ticket id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/provision/upgrade/

#### PUT
##### Summary

trigger node hot-update

##### Description

auto-gen node provision configs, then update node state to hot-update

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | string |
| component | body | component name, options: shopee-dpdkdns-kernel dpdkdns-config-shopee shopee-dns-agent | Yes | string |
| version | body | upgrade target version | Yes | string |
| ticket_id | body | swp ticket id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeSpecResponse](#dnsvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/state/

#### PUT
##### Summary

update node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeTagVariableResponse](#dnsvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /dns/v2/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from AZMeta

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.ClusterListResponse](#dnsvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/cluster/config/

#### OPTIONS
##### Summary

cluster config existence

##### Description

verify cluster config existence by cluster name

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/cluster/id/{id}/config/

#### GET
##### Summary

cluster config

##### Description

fetch cluster config by cluster id

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.ClusterConfigResponse](#dnsvoclusterconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/cluster/id/{id}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.ClusterNodeResponse](#dnsvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/cluster/id/{id}/node/precheck/

#### POST
##### Summary

check cluster nodes' condition before online

##### Description

check nodes' dpdkdns dns-agent status etc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | cluster uuid | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.ClusterNodePreCheckResponse](#dnsvoclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/cluster/id/{id}/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster id

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /dns/v2/component/

#### GET
##### Summary

get dns component list

##### Description

get dns component list

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.ComponentListResponse](#dnsvocomponentlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/component/version/

#### GET
##### Summary

fetch dns component versions

##### Description

fetch dns related components' version list

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| name | body | component name | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.ComponentVersionResponse](#dnsvocomponentversionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /dns/v2/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about DNS operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes ip address | Yes | [ string ] |
| cluster_id | body | cluster id | Yes | string |
| type | body | ticket type, option:online offline ma | No | string |
| reason | body | ticket reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.TicketResponse](#dnsvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to dns cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes ip address | Yes | [ string ] |
| cluster_id | body | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.TicketResponse](#dnsvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/ticket/node/callback/

#### POST
##### Summary

add nodes into cluster

##### Description

add nodes into cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | nodes and cluster info | Yes | [dnsvo.NodeTicketCBRequest](#dnsvonodeticketcbrequest) |
| id | body | related ticket id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/ticket/node/config/callback/

#### POST
##### Summary

hot update node ticket callback when gray stage or next

##### Description

batch hot update node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| baseRequest | body | nodes base information | Yes | [dnsvo.HotUpdateNodeRequest](#dnsvohotupdatenoderequest) |
| operation_type | body | operation type,one of update,delete,add | Yes | string |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| cluster_id | body | cluster id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/ticket/node/config/reset/

#### POST
##### Summary

reset node

##### Description

set nodes HotUpdateConfig to nil

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | reset nodes ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/ticket/node/config/rollback/

#### POST
##### Summary

rollback node when gray stage rejected

##### Description

batch rollback node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| baseRequest | body | nodes base information | Yes | [dnsvo.HotUpdateNodeRequest](#dnsvohotupdatenoderequest) |
| operation_type | body | operation type,one of update,delete,add | Yes | string |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| cluster_id | body | cluster id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.NodeResponse](#dnsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/ticket/node/deploy/

#### POST
##### Summary

create deploy node ticket

##### Description

create a ticket to deploy nodes into dns cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes ip address | Yes | [ string ] |
| cluster_id | body | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.TicketResponse](#dnsvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/ticket/node/hotupdate/

#### POST
##### Summary

create hot update node ticket

##### Description

create hot update node ticket

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| is_overall | body | cluster's all nodes | No | boolean |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| cluster_id | body | cluster id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.TicketResponse](#dnsvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /dns/v2/ticket/node/retire/

#### POST
##### Summary

create remove node ticket

##### Description

create a ticket to remove nodes from cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes ip address | Yes | [ string ] |
| cluster_id | body | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [dnsvo.TicketResponse](#dnsvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/ticket/node/config/callback/

#### POST
##### Summary

hot update node ticket callback when gray stage or next

##### Description

batch hot update node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| baseRequest | body | nodes base information | Yes | [natvo.HotUpdateNodeRequest](#natvohotupdatenoderequest) |
| operation_type | body | operation type,one of update,delete,add | Yes | string |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| cluster_id | body | cluster id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/ticket/node/config/callback/

#### POST
##### Summary

hot update node ticket callback when gray stage or next

##### Description

batch hot update node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| baseRequest | body | nodes base information | Yes | [nlbvo.HotUpdateNodeRequest](#nlbvohotupdatenoderequest) |
| operation_type | body | operation type,one of update,delete,add | Yes | string |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| cluster_id | body | cluster id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /egw/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from Anchor Cluster Mgmt

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.ClusterListResponse](#anchorvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/cluster/group/add/

#### PUT
##### Summary

add a new egw group into cluster

##### Description

create a new group name under the anchor cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| group | body | EGW group name | Yes | string |
| cluster | body | EGW cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/cluster/group/delete/

#### PUT
##### Summary

delete a egw group from the cluster

##### Description

delete a egw group under the anchor cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| group | body | EGW group name | Yes | string |
| cluster | body | EGW cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/cluster/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/cluster/uuid/{uuid}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.ClusterNodeResponse](#egwvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/cluster/uuid/{uuid}/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.ClusterNodeResponse](#egwvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /egw/v1/node/

#### GET
##### Summary

fetch node lists

##### Description

fetch nodes

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.NodeListResponse](#egwvonodelistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.NodeEventResponse](#egwvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/node/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.NodeResponse](#egwvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | egw nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.NodeSpecResponse](#egwvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | egw nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.NodeResponse](#egwvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/node/state/

#### GET
##### Summary

fetch node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.GetNodeResponse](#egwvogetnoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node state info

##### Description

update node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.NodeTagVariableResponse](#egwvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /egw/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about egw operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | egw Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| type | body | ticket type, option:online offline prema | No | string |
| reason | body | ticket reason | No | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.TicketResponse](#egwvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to egw cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:EGW | Yes | string |
| uuid | body | EGW cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.TicketResponse](#egwvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into egw cluster

##### Description

add nodes into egw cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [egwvo.TicketAddCBRequest](#egwvoticketaddcbrequest) |
| id | body | related ticket id | Yes | [egwvo.TicketAddCBRequest](#egwvoticketaddcbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.NodeResponse](#egwvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/ticket/node/retire/

#### POST
##### Summary

create retire node ticket

##### Description

create a ticket to retire nodes from EGW cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | retired nodes ip address | Yes | [ string ] |
| type | body | operation type, option:online offline ma retire | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.TicketResponse](#egwvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /egw/v1/ticket/node/retire/callback/

#### POST
##### Summary

retire nodes into egw cluster

##### Description

retire nodes from egw cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | body | related ticket id | Yes | [egwvo.RetireNodeCBRequest](#egwvoretirenodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [egwvo.NodeResponse](#egwvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /etcd/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from AZ Meta

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.ClusterListResponse](#etcdvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/alb/uuid/{uuid}/sync/

#### PUT
##### Summary

sync alb cluster's etcd endpoints into AZ meta

##### Description

sync alb cluster's etcd endpoints by cluster uuid into AZ meta

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.EtcdEndpoint](#etcdvoetcdendpoint) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/anchor/uuid/{uuid}/sync/

#### PUT
##### Summary

sync anchor cluster's etcd endpoints into AZ meta

##### Description

sync anchor cluster's etcd endpoints by cluster uuid into AZ meta

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.EtcdEndpoint](#etcdvoetcdendpoint) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/certs/

#### GET
##### Summary

fetch etcd cluster's certs

##### Description

fetch etcd cluster's certs

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | etcd cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.ClusterConfigResponse](#etcdvoclusterconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/config/

#### GET
##### Summary

fetch etcd cluster's config

##### Description

fetch etcd cluster's config

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | etcd cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.ClusterConfigResponse](#etcdvoclusterconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/config/default/

#### GET
##### Summary

fetch etcd default cluster's config

##### Description

fetch etcd default cluster's config

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | etcd cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.ClusterConfigResponse](#etcdvoclusterconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/id/{id}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.ClusterNodeResponse](#etcdvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/id/{id}/node/list/

#### GET
##### Summary

fetch cluster node list only

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.ClusterNodeResponse](#etcdvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/id/{id}/node/precheck/

#### POST
##### Summary

check cluster nodes' condition before online

##### Description

check nodes' iptables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | cluster id | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.ClusterNodePreCheckResponse](#etcdvoclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/meta/

#### GET
##### Summary

fetch etcd cluster's meta

##### Description

fetch etcd cluster's meta

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| rz | path | etcd cluster rz | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.ClusterMetaResponse](#etcdvoclustermetaresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/cluster/sync/

#### POST
##### Summary

sync misc cluster's etcd endpoints into AZ meta

##### Description

sync misc cluster's etcd endpoints by cluster uuid into AZ meta

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.EtcdEndpoint](#etcdvoetcdendpoint) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /etcd/v1/node/

#### GET
##### Summary

fetch node lists

##### Description

fetch nodes from AZ Meta

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeListResponse](#etcdvonodelistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeEventResponse](#etcdvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/node/expire/

#### PATCH
##### Summary

clean-up expired nodes

##### Description

delete those expired nodes under Initialised or Running state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeResponse](#etcdvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/node/ma/

#### PATCH
##### Summary

set node maintenance ticket

##### Description

set node maintenance ticket

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeResponse](#etcdvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/node/provision/

#### GET
##### Summary

fetch node provision config

##### Description

fetch node provision configs, including template, component and events

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | etcd nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeProvisionResponse](#etcdvonodeprovisionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/node/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeResponse](#etcdvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | etcd nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeSpecResponse](#etcdvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | etcd nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeResponse](#etcdvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/node/state/

#### GET
##### Summary

fetch node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.GetNodeResponse](#etcdvogetnoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node state info

##### Description

update node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/node/state/{state}/

#### GET
##### Summary

fetch nodes by state

##### Description

fetch nodes by state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [sgwvo.StateNodesResponse](#sgwvostatenodesresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeTagVariableResponse](#etcdvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /etcd/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about etcd operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | etcd Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| type | body | ticket type, option:online offline prema | No | string |
| reason | body | ticket reason | No | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.TicketResponse](#etcdvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/ticket/automa/

#### POST
##### Summary

auto MA

##### Description

auto MA

##### Responses

| Code | Description |
| ---- | ----------- |

### /etcd/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to etcd cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:ETCD | Yes | string |
| uuid | body | etcd cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.TicketResponse](#etcdvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into etcd cluster

##### Description

add nodes into etcd cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [etcdvo.TicketAddCBRequest](#etcdvoticketaddcbrequest) |
| id | body | related ticket id | Yes | [etcdvo.TicketAddCBRequest](#etcdvoticketaddcbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeResponse](#etcdvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/ticket/node/retire/

#### POST
##### Summary

create retire node ticket

##### Description

create a ticket to retire nodes from etcd cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | retired nodes ip address | Yes | [ string ] |
| type | body | operation type, option:online offline ma retire | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.TicketResponse](#etcdvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/ticket/node/retire/callback/

#### POST
##### Summary

retire nodes into etcd cluster

##### Description

retire nodes from etcd cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | body | related ticket id | Yes | [etcdvo.RetireNodeCBRequest](#etcdvoretirenodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeResponse](#etcdvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/ticket/refresh/

#### POST
##### Summary

create refresh cluster ticket

##### Description

create a ticket to update cluster meta and/or add nodes into etcd cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| string | body | cluster meta | Yes | string |
| type | body | k8s resource type, option:ETCD | Yes | string |
| uuid | body | etcd cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.TicketResponse](#etcdvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /etcd/v1/ticket/refresh/callback/

#### POST
##### Summary

refresh cluster nodes and meta

##### Description

update cluster meta and/or add nodes into cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | refresh cluster | Yes | [etcdvo.TicketRefreshCBRequest](#etcdvoticketrefreshcbrequest) |
| id | body | related ticket id | Yes | [etcdvo.TicketRefreshCBRequest](#etcdvoticketrefreshcbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [etcdvo.NodeResponse](#etcdvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /ext2/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from ext2-cluster mgmt

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.ClusterListResponse](#ext2voclusterlistresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/cluster/uuid/{uuid}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeInfosResponse](#ext2vonodeinfosresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/cluster/uuid/{uuid}/node/precheck/

#### POST
##### Summary

check cluster nodes' condition before online

##### Description

check nodes' server state, iptables, nf_conntrack etc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.ClusterNodePreCheckResponse](#ext2voclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /ext2/v1/component/

#### GET
##### Summary

get ext2 component list

##### Description

get ext2 component list

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.ComponentListResponse](#ext2vocomponentlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/component/version/

#### GET
##### Summary

fetch ext2 component versions

##### Description

fetch ext2 related components' version list

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| name | body | component name | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.ComponentVersionResponse](#ext2vocomponentversionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /ext2/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeEventResponse](#ext2vonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/node/expire/

#### PATCH
##### Summary

clean-up expired nodes

##### Description

delete those expired nodes under Initialised or Running state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeResponse](#ext2vonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/node/ma/

#### PATCH
##### Summary

set node maintenance ticket

##### Description

set node maintenance ticket

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeResponse](#ext2vonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/node/provision/

#### GET
##### Summary

fetch node provision config

##### Description

fetch node provision configs, including template, component and events

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeProvisionResponse](#ext2vonodeprovisionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeSpecResponse](#ext2vonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | ext2 nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeResponse](#ext2vonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/node/state/

#### PUT
##### Summary

update node state info

##### Description

update node state info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/node/state/{state}/

#### GET
##### Summary

fetch nodes by state

##### Description

fetch nodes by state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [sgwvo.StateNodesResponse](#sgwvostatenodesresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeTagVariableResponse](#ext2vonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /ext2/v1/ticket/node/

#### POST
##### Summary

add nodes into ext2 cluster

##### Description

add nodes into ext2 cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [ext2vo.ClusterNodeTicketRequest](#ext2voclusternodeticketrequest) |
| id | body | related ticket id | Yes | [ext2vo.ClusterNodeTicketRequest](#ext2voclusternodeticketrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.ClusterNodeTicketResponse](#ext2voclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into ext2 cluster

##### Description

add nodes into ext2 cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [ext2vo.ClusterAddNodeCBRequest](#ext2voclusteraddnodecbrequest) |
| id | body | related ticket id | Yes | [ext2vo.ClusterAddNodeCBRequest](#ext2voclusteraddnodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.ClusterNodeTicketResponse](#ext2voclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/ticket/node/config/

#### POST
##### Summary

create hot update node ticket

##### Description

create hot update node ticket

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| is_overall | body | cluster's all nodes | No | boolean |
| uuid | body | cluster uuid | Yes | string |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| form_data | body | hot update ticket info | Yes | [ext2vo.HotUpdateTicketFormData](#ext2vohotupdateticketformdata) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.TicketResponse](#ext2voticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/ticket/node/config/callback/

#### POST
##### Summary

hot update node ticket callback when gray stage or next

##### Description

batch hot update node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| baseRequest | body | nodes base information | Yes | [ext2vo.HotUpdateNodeBaseRequest](#ext2vohotupdatenodebaserequest) |
| operation_type | body | operation type,one of update,delete,add | Yes | string |
| target_config | body | target config info | Yes | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |
| rollback_config | body | rollback config info | Yes | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeResponse](#ext2vonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/ticket/node/config/reset/

#### POST
##### Summary

reset node

##### Description

set nodes HotUpdateConfig to nil

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | reset nodes ip address | Yes | string |
| total_nodes | body | number of reset nodes | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [albvo.NodeResponse](#albvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/ticket/node/config/rollback/

#### POST
##### Summary

rollback node when gray stage rejected

##### Description

batch rollback node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| stage | body | rollback stage | No | string |
| business_type | body | business type | Yes | string |
| ips | body | all node ip address | Yes | string |
| version | body | rollback config's version | Yes | string |
| total_nodes | body | total nodes number | Yes | string |
| gray_ips | body | gray rollback node ip address | Yes | string |
| total_gray_nodes | body | gray rollback nodes number | Yes | string |
| total_other_nodes | body | not rollback nodes number | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.NodeResponse](#ext2vonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /ext2/v1/ticket/node/retire/

#### POST
##### Summary

retire nodes in ext2 cluster

##### Description

retire nodes in ext2 cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | retire nodes and cluster info | Yes | [ext2vo.ClusterNodeTicketRequest](#ext2voclusternodeticketrequest) |
| id | body | related ticket id | Yes | [ext2vo.ClusterNodeTicketRequest](#ext2voclusternodeticketrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ext2vo.ClusterNodeTicketResponse](#ext2voclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /kls/api/version

#### GET
##### Summary

get kls api version

##### Description

get kls api version details

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | object |

---
### /kls/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from kls-cluster mgmt

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.ClusterListResponse](#klsvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /kls/v1/cluster/uuid/{uuid}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.NodeInfosResponse](#klsvonodeinfosresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /kls/v1/node/

#### GET
##### Summary

fetch node list

##### Description

fetch node list with details, like state, tasks etc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | kls nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.NodeInfosResponse](#klsvonodeinfosresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /kls/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about KLS operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | KLS Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| uuid | body | cluster uuid, included in the form_data | Yes | string |
| type | body | ticket type, option:online offline ma | No | string |
| reason | body | ticket reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.TicketResponse](#klsvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /kls/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to kls cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:KLS | Yes | string |
| uuid | body | kls cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.AddNodeTicketResponse](#klsvoaddnodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /kls/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into kls cluster

##### Description

add nodes into kls cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [klsvo.AddNodeCBRequest](#klsvoaddnodecbrequest) |
| id | body | related ticket id | Yes | [klsvo.AddNodeCBRequest](#klsvoaddnodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.NodeResponse](#klsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /kls/v1/ticket/node/hotupdate/

#### POST
##### Summary

create hot update node ticket

##### Description

create hot update node ticket

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| is_overall | body | cluster's all nodes | No | boolean |
| uuid | body | cluster uuid | Yes | string |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| form_data | body | hot update ticket info | Yes | [klsvo.HotUpdateTicketFormData](#klsvohotupdateticketformdata) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.HotUpdateTicketResponse](#klsvohotupdateticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /kls/v1/ticket/node/hotupdate/callback/

#### POST
##### Summary

hot update node ticket callback when gray stage or next

##### Description

batch hot update node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| baseRequest | body | nodes base information | Yes | [klsvo.HotUpdateNodeBaseRequest](#klsvohotupdatenodebaserequest) |
| operation_type | body | operation type,one of update,delete,add | Yes | string |
| target_config | body | target config info | Yes | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |
| rollback_config | body | rollback config info | Yes | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.NodeResponse](#klsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /kls/v1/ticket/node/hotupdate/reset/

#### POST
##### Summary

reset node

##### Description

set nodes HotUpdateConfig to nil

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | reset nodes ip address | Yes | string |
| total_nodes | body | number of reset nodes | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.NodeResponse](#klsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /kls/v1/ticket/node/hotupdate/rollback/

#### POST
##### Summary

rollback node when gray stage rejected

##### Description

batch rollback node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| stage | body | rollback stage | No | string |
| business_type | body | business type | Yes | string |
| ips | body | all node ip address | Yes | string |
| version | body | rollback config's version | Yes | string |
| total_nodes | body | total nodes number | Yes | string |
| gray_ips | body | gray rollback node ip address | Yes | string |
| total_gray_nodes | body | gray rollback nodes number | Yes | string |
| total_other_nodes | body | not rollback nodes number | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [klsvo.NodeResponse](#klsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /lcs/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from lcs-cluster mgmt

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.ClusterListResponse](#lcsvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/cluster/uuid/{uuid}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeInfosResponse](#lcsvonodeinfosresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/cluster/uuid/{uuid}/node/precheck/

#### POST
##### Summary

check cluster nodes' condition before online

##### Description

check nodes' server state, iptables, nf_conntrack etc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.ClusterNodePreCheckResponse](#lcsvoclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/cluster/uuid/{uuid}/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeResponse](#lcsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /lcs/v1/component/

#### GET
##### Summary

get lcs component list

##### Description

get lcs component list

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.ComponentListResponse](#lcsvocomponentlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/component/version/

#### GET
##### Summary

fetch lcs component versions

##### Description

fetch lcs related components' version list

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| name | body | component name | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.ComponentVersionResponse](#lcsvocomponentversionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /lcs/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeEventResponse](#lcsvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/expire/

#### PATCH
##### Summary

clean-up expired nodes

##### Description

delete those expired nodes under Initialised or Running state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeResponse](#lcsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/ma/

#### PATCH
##### Summary

set node maintenance ticket

##### Description

set node maintenance ticket

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeResponse](#lcsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/provision/

#### GET
##### Summary

fetch node provision config

##### Description

fetch node provision configs, including template, component and events

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeProvisionResponse](#lcsvonodeprovisionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/provision/abort/

#### PUT
##### Summary

abort node hot-update rollback

##### Description

just update node state to Running or Initialised

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeResponse](#lcsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/provision/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeResponse](#lcsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/provision/rollback/

#### PUT
##### Summary

trigger node hot-update rollback

##### Description

auto-gen node provision configs, then update node state to hot-update rollback

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | string |
| component | body | component name, options: lcs-metrics, sgw-agent, lcs-sd, mesos-nginx-lb, nginx-shopee | Yes | string |
| version | body | rollback target version | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |
| ticket_id | body | swp ticket id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeProvisionConfigResponse](#lcsvonodeprovisionconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/provision/upgrade/

#### GET
##### Summary

generate node provision config

##### Description

fetch node provision configs, including template, component

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| component | body | component name | Yes | string |
| version | body | upgrade target version | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeProvisionConfigResponse](#lcsvonodeprovisionconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

trigger node hot-update

##### Description

auto-gen node provision configs, then update node state to hot-update

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | string |
| component | body | component name, options: lcs-metrics, sgw-agent, lcs-sd, mesos-nginx-lb, nginx-shopee | Yes | string |
| version | body | upgrade target version | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |
| ticket_id | body | swp ticket id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeProvisionConfigResponse](#lcsvonodeprovisionconfigresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | lcs nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeSpecResponse](#lcsvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | lcs nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeResponse](#lcsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/state/

#### GET
##### Summary

fetch node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.GetNodeResponse](#lcsvogetnoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PATCH
##### Summary

reentrant node state info

##### Description

reentrant node state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/state/{state}/

#### GET
##### Summary

fetch nodes by state

##### Description

fetch nodes by state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [sgwvo.StateNodesResponse](#sgwvostatenodesresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeTagVariableResponse](#lcsvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /lcs/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about LCS operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | LCS Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| uuid | body | cluster uuid, included in the form_data | Yes | string |
| type | body | ticket type, option:online offline ma | No | string |
| reason | body | ticket reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.TicketResponse](#lcsvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to lcs cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:LCS | Yes | string |
| uuid | body | lcs cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.ClusterNodeTicketResponse](#lcsvoclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into lcs cluster

##### Description

add nodes into lcs cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [lcsvo.ClusterAddNodeCBRequest](#lcsvoclusteraddnodecbrequest) |
| id | body | related ticket id | Yes | [lcsvo.ClusterAddNodeCBRequest](#lcsvoclusteraddnodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.ClusterNodeTicketResponse](#lcsvoclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/ticket/node/config/

#### POST
##### Summary

create hot update node ticket

##### Description

create hot update node ticket

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| is_overall | body | cluster's all nodes | No | boolean |
| uuid | body | cluster uuid | Yes | string |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| form_data | body | hot update ticket info | Yes | [lcsvo.HotUpdateTicketFormData](#lcsvohotupdateticketformdata) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.TicketResponse](#lcsvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/ticket/node/config/callback/

#### POST
##### Summary

hot update node ticket callback when gray stage or next

##### Description

batch hot update node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| baseRequest | body | nodes base information | Yes | [lcsvo.HotUpdateNodeBaseRequest](#lcsvohotupdatenodebaserequest) |
| operation_type | body | operation type,one of update,delete,add | Yes | string |
| target_config | body | target config info | Yes | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |
| rollback_config | body | rollback config info | Yes | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeResponse](#lcsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/ticket/node/config/reset/

#### POST
##### Summary

reset node

##### Description

set nodes HotUpdateConfig to nil

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | reset nodes ip address | Yes | string |
| total_nodes | body | number of reset nodes | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeResponse](#lcsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/ticket/node/config/rollback/

#### POST
##### Summary

rollback node when gray stage rejected

##### Description

batch rollback node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| stage | body | rollback stage | No | string |
| business_type | body | business type | Yes | string |
| ips | body | all node ip address | Yes | string |
| version | body | rollback config's version | Yes | string |
| total_nodes | body | total nodes number | Yes | string |
| gray_ips | body | gray rollback node ip address | Yes | string |
| total_gray_nodes | body | gray rollback nodes number | Yes | string |
| total_other_nodes | body | not rollback nodes number | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.NodeResponse](#lcsvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /lcs/v1/ticket/node/retire/

#### POST
##### Summary

create remove node ticket

##### Description

create a ticket to remove nodes from cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | removed nodes ip address | Yes | [ string ] |
| uuid | body | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [lcsvo.TicketResponse](#lcsvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /nat/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from nat cluster mgmt

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.ClusterListResponse](#natvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/cluster/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/cluster/uuid/{uuid}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeInfosResponse](#natvonodeinfosresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/cluster/uuid/{uuid}/node/precheck/

#### POST
##### Summary

check cluster nodes' condition before online

##### Description

check nodes' server state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.ClusterNodePreCheckResponse](#natvoclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/cluster/uuid/{uuid}/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /nat/v1/component/

#### GET
##### Summary

get nat component list

##### Description

get nat component list

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.ComponentListResponse](#natvocomponentlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/component/version/

#### GET
##### Summary

fetch nat component versions

##### Description

fetch nat related components' version list

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| name | body | component name | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.ComponentVersionResponse](#natvocomponentversionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /nat/v1/node/config/

#### GET
##### Summary

fetch node configs

##### Description

fetch node configs by ticket info

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.FetchSwitchParamRsp](#natvofetchswitchparamrsp) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | nat node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeEventResponse](#natvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/expire/

#### PATCH
##### Summary

clean-up expired nodes

##### Description

delete those expired nodes under Initialised or Running state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/ma/

#### PATCH
##### Summary

set node maintenance ticket

##### Description

set node maintenance ticket

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/provision/

#### GET
##### Summary

fetch node provision config

##### Description

fetch node provision configs, including template, component and events

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nat nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeProvisionResponse](#natvonodeprovisionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/provision/abort/

#### PUT
##### Summary

abort node hot-update rollback

##### Description

just update node state to Running or Initialised

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/provision/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nat nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeSpecResponse](#natvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nat nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/state/

#### GET
##### Summary

fetch node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PATCH
##### Summary

reentrant node state info

##### Description

reentrant node state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/state/{state}/

#### GET
##### Summary

fetch nodes by state

##### Description

fetch nodes by state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [sgwvo.StateNodesResponse](#sgwvostatenodesresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nat nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeTagVariableResponse](#natvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /nat/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about NAT operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | NAT Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| uuid | body | cluster uuid, included in the form_data | Yes | string |
| type | body | ticket type, option:online offline ma | No | string |
| reason | body | ticket reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.TicketResponse](#natvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to nat cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:nat | Yes | string |
| uuid | body | nat cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.ClusterNodeTicketResponse](#natvoclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into nat cluster

##### Description

add nodes into nat cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [natvo.ClusterAddNodeCBRequest](#natvoclusteraddnodecbrequest) |
| id | body | related ticket id | Yes | [natvo.ClusterAddNodeCBRequest](#natvoclusteraddnodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.ClusterNodeTicketResponse](#natvoclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/ticket/node/config/

#### POST
##### Summary

create hot update node ticket

##### Description

create hot update node ticket

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| is_overall | body | cluster's all nodes | No | boolean |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| cluster_id | body | cluster id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.TicketResponse](#natvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/ticket/node/config/reset/

#### POST
##### Summary

reset node

##### Description

set nodes HotUpdateConfig to nil

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | reset nodes ip address | Yes | string |
| total_nodes | body | number of reset nodes | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/ticket/node/config/rollback/

#### POST
##### Summary

rollback node when gray stage rejected

##### Description

batch rollback node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| stage | body | rollback stage | No | string |
| business_type | body | business type | Yes | string |
| ips | body | all node ip address | Yes | string |
| version | body | rollback config's version | Yes | string |
| total_nodes | body | total nodes number | Yes | string |
| gray_ips | body | gray rollback node ip address | Yes | string |
| total_gray_nodes | body | gray rollback nodes number | Yes | string |
| total_other_nodes | body | not rollback nodes number | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.NodeResponse](#natvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/ticket/node/retire/

#### POST
##### Summary

create remove node ticket

##### Description

create a ticket to remove nodes from nat cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | removed nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:NAT | Yes | string |
| uuid | body | nat cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.TicketResponse](#natvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nat/v1/ticket/offline/

#### POST
##### Summary

check whether node can be offline

##### Description

check whether node can be offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | ip | Yes | string |
| reason | body | reason for offline | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [natvo.TicketResponse](#natvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /nlb/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from nlb cluster mgmt

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.ClusterListResponse](#nlbvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/cluster/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/cluster/uuid/{uuid}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeInfosResponse](#nlbvonodeinfosresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/cluster/uuid/{uuid}/node/expire/

#### PATCH
##### Summary

clean-up expired nodes

##### Description

delete those expired nodes under Initialised or Running state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/cluster/uuid/{uuid}/node/precheck/

#### POST
##### Summary

check cluster nodes' condition before online

##### Description

check nodes' server state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.ClusterNodePreCheckResponse](#nlbvoclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/cluster/uuid/{uuid}/node/precheck_offline/

#### POST
##### Summary

check cluster nodes' condition before offline

##### Description

check instances' advertise status etc

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.ClusterNodePreCheckResponse](#nlbvoclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/cluster/uuid/{uuid}/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /nlb/v1/component/

#### GET
##### Summary

get nlb component list

##### Description

get nlb component list

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.ComponentListResponse](#nlbvocomponentlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/component/version/

#### GET
##### Summary

fetch nlb component versions

##### Description

fetch nlb related components' version list

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| name | body | component name | Yes | string |
| env | body | environment,options: test, staging, stable, live | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.ComponentVersionResponse](#nlbvocomponentversionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /nlb/v1/node/config/

#### GET
##### Summary

fetch node configs

##### Description

fetch node configs by ticket info

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.FetchSwitchParamRsp](#nlbvofetchswitchparamrsp) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | nlb node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeEventResponse](#nlbvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/expire/

#### PATCH
##### Summary

clean-up expired nodes

##### Description

delete those expired nodes under Initialised or Running state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/ma/

#### PATCH
##### Summary

set node maintenance ticket

##### Description

set node maintenance ticket

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/provision/

#### GET
##### Summary

fetch node provision config

##### Description

fetch node provision configs, including template, component and events

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nlb nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeProvisionResponse](#nlbvonodeprovisionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/provision/abort/

#### PUT
##### Summary

abort node hot-update rollback

##### Description

just update node state to Running or Initialised

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/provision/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nlb nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeSpecResponse](#nlbvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nlb nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/state/

#### GET
##### Summary

fetch node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PATCH
##### Summary

reentrant node state info

##### Description

reentrant node state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/state/{state}/

#### GET
##### Summary

fetch nodes by state

##### Description

fetch nodes by state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [sgwvo.StateNodesResponse](#sgwvostatenodesresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nlb nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeTagVariableResponse](#nlbvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /nlb/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about NLB operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | NLB Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| uuid | body | cluster uuid, included in the form_data | Yes | string |
| type | body | ticket type, option:online offline ma | No | string |
| reason | body | ticket reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.TicketResponse](#nlbvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into nlb cluster

##### Description

add nodes into nlb cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [nlbvo.ClusterAddNodeCBRequest](#nlbvoclusteraddnodecbrequest) |
| id | body | related ticket id | Yes | [nlbvo.ClusterAddNodeCBRequest](#nlbvoclusteraddnodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.ClusterNodeTicketResponse](#nlbvoclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/ticket/node/config/

#### POST
##### Summary

create hot update node ticket

##### Description

create hot update node ticket

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| is_overall | body | cluster's all nodes | No | boolean |
| component | body | component name | Yes | string |
| target_version | body | update target version | Yes | string |
| rollback_version | body | rollback version | Yes | string |
| cluster_id | body | cluster id | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.TicketResponse](#nlbvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/ticket/node/config/reset/

#### POST
##### Summary

reset node

##### Description

set nodes HotUpdateConfig to nil

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | reset nodes ip address | Yes | string |
| total_nodes | body | number of reset nodes | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/ticket/node/config/rollback/

#### POST
##### Summary

rollback node when gray stage rejected

##### Description

batch rollback node component version

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| stage | body | rollback stage | No | string |
| business_type | body | business type | Yes | string |
| ips | body | all node ip address | Yes | string |
| version | body | rollback config's version | Yes | string |
| total_nodes | body | total nodes number | Yes | string |
| gray_ips | body | gray rollback node ip address | Yes | string |
| total_gray_nodes | body | gray rollback nodes number | Yes | string |
| total_other_nodes | body | not rollback nodes number | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/ticket/node/retire/

#### POST
##### Summary

create remove node ticket

##### Description

create a ticket to remove nodes from nlb cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | removed nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:NLB | Yes | string |
| uuid | body | nlb cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.TicketResponse](#nlbvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/ticket/node/retire/callback/

#### POST
##### Summary

remove nodes into nlb cluster

##### Description

remove nodes from nlb cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | body | related ticket id | Yes | [nlbvo.ClusterRemoveNodeCBRequest](#nlbvoclusterremovenodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.NodeResponse](#nlbvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /nlb/v1/ticket/offline/

#### POST
##### Summary

check whether node can be offline

##### Description

check whether node can be offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | ip | Yes | string |
| reason | body | reason for offline | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.TicketResponse](#nlbvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /nlb/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to nlb cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:nlb | Yes | string |
| uuid | body | nlb cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [nlbvo.ClusterNodeTicketResponse](#nlbvoclusternodeticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /openapi/v1/anchor/subnets/

#### GET
##### Summary

fetch anchor subnets

##### Description

fetch anchor subnets

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [openvo.AnchorClusterSubnetResponse](#openvoanchorclustersubnetresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /openapi/v1/etcd/meta/

#### GET
##### Summary

fetch etcd cluster meta

##### Description

their original infos store in AZMeta

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ [etcdvo.ClusterMeta](#etcdvoclustermeta) ] |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /openapi/v1/ext2/cluster/

#### GET
##### Summary

fetch Ext2 domains

##### Description

Ext2 domains for every cluster of AZ Segment

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ [openvo.Ext2ClusterInfo](#openvoext2clusterinfo) ] |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /openapi/v1/zk/cluster/

#### GET
##### Summary

fetch zookeeper clusters and nodes

##### Description

their original infos store in AZMeta

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ [openvo.ZKClusterInfo](#openvozkclusterinfo) ] |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /openapi/v1/zk/meta/

#### GET
##### Summary

fetch zookeeper cluster meta

##### Description

their original infos store in DNS internal domains

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [ [zkvo.ClusterMeta](#zkvoclustermeta) ] |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /sgwdr/v1/event/

#### GET
##### Summary

fetch events by token

##### Description

fetch events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| token | query | idempotency token | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [drcvo.EventResponse](#drcvoeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /sgwdr/v1/task/

#### GET
##### Summary

sgw dr controller task events

##### Description

tasks store in DB

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [drcvo.TaskEventsResponse](#drcvotaskeventsresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### POST
##### Summary

sgw dr controller task event handler

##### Description

webhook register in global controller

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [middlewares.Response](#middlewaresresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /sgwdr/v1/task/id/{id}/result/

#### GET
##### Summary

sgw dr controller task result

##### Description

result from adr

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | task id on switching | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [drcvo.TaskSpecResponse](#drcvotaskspecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /sgwdr/v1/task/scenario/{scenario}/mode/{mode}/

#### POST
##### Summary

sgw dr controller task event handler

##### Description

webhook register in global controller

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [middlewares.Response](#middlewaresresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /sgwdr/v1/task/state/

#### PUT
##### Summary

update sgw dr controller task state

##### Description

update sgw dr controller task state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| token | body | idempotency token | Yes | string |
| state | body | options: Switching | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /sgwdr/v1/task/token/{token}/

#### GET
##### Summary

sgw dr controller task event details

##### Description

details store in DB

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| token | path | idempotency token | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [drcvo.TaskEventResponse](#drcvotaskeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /sgwdr/v1/task/token/{token}/details/

#### GET
##### Summary

sgw dr controller task event spec

##### Description

details store in DB

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| token | path | idempotency token | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [drcvo.TaskDetailsResponse](#drcvotaskdetailsresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /sgwdr/v1/task/token/{token}/spec/

#### GET
##### Summary

sgw dr controller task event spec

##### Description

spec store in k8s

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| token | path | idempotency token | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [drcvo.TaskSpecResponse](#drcvotaskspecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /sgwdr/v1/task/token/{token}/status/

#### GET
##### Summary

sgw dr controller task event status

##### Description

status store in k8s

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| token | path | idempotency token | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [drcvo.TaskStateResponse](#drcvotaskstateresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /sgwdr/v1/health/

#### GET
##### Summary

sgw dr controller healthy handler

##### Description

webhook register in global controller

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [middlewares.Response](#middlewaresresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /sgwdr/v1/impact/

#### POST
##### Summary

sgw dr controller healthy handler

##### Description

webhook register in global controller

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [middlewares.Response](#middlewaresresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /vgw/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from Anchor Cluster Mgmt

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [anchorvo.ClusterListResponse](#anchorvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/cluster/group/add/

#### PUT
##### Summary

add a new vgw group into cluster

##### Description

create a new group name under the anchor cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| group | body | VGW group name | Yes | string |
| cluster | body | VGW cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/cluster/group/delete/

#### PUT
##### Summary

delete a vgw group from the cluster

##### Description

delete a vgw group under the anchor cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| group | body | VGW group name | Yes | string |
| cluster | body | VGW cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/cluster/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/cluster/uuid/{uuid}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.ClusterNodeResponse](#vgwvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/cluster/uuid/{uuid}/sync/

#### PUT
##### Summary

sync cluster's nodes into k8s runtime

##### Description

sync cluster node list by cluster uuid, only work at managed trigger

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.ClusterNodeResponse](#vgwvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /vgw/v1/node/

#### GET
##### Summary

fetch node lists

##### Description

fetch nodes

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.NodeListResponse](#vgwvonodelistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.NodeEventResponse](#vgwvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/node/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.NodeResponse](#vgwvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | vgw nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.NodeSpecResponse](#vgwvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | vgw nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.NodeResponse](#vgwvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/node/state/

#### GET
##### Summary

fetch node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.GetNodeResponse](#vgwvogetnoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node state info

##### Description

update node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.NodeTagVariableResponse](#vgwvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /vgw/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about vgw operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | vgw Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| type | body | ticket type, option:online offline prema | No | string |
| reason | body | ticket reason | No | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.TicketResponse](#vgwvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to vgw cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:VGW | Yes | string |
| uuid | body | VGW cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.TicketResponse](#vgwvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into vgw cluster

##### Description

add nodes into vgw cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [vgwvo.TicketAddCBRequest](#vgwvoticketaddcbrequest) |
| id | body | related ticket id | Yes | [vgwvo.TicketAddCBRequest](#vgwvoticketaddcbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.NodeResponse](#vgwvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/ticket/node/retire/

#### POST
##### Summary

create retire node ticket

##### Description

create a ticket to retire nodes from VGW cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | retired nodes ip address | Yes | [ string ] |
| type | body | operation type, option:online offline ma retire | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.TicketResponse](#vgwvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /vgw/v1/ticket/node/retire/callback/

#### POST
##### Summary

retire nodes into vgw cluster

##### Description

retire nodes from vgw cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | body | related ticket id | Yes | [vgwvo.RetireNodeCBRequest](#vgwvoretirenodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [vgwvo.NodeResponse](#vgwvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /viz/v1/path/

#### GET
##### Summary

sgw traffic trace path handler

##### Description

output sgw traffic trace path by mermaid format

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | string |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /zk/v1/cluster/

#### GET
##### Summary

fetch cluster lists

##### Description

fetch cluster details from AZ Meta

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.ClusterListResponse](#zkvoclusterlistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/cluster/id/{id}/node/

#### GET
##### Summary

fetch cluster details, includes node lists

##### Description

fetch cluster node list by cluster uuid

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | cluster id | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.ClusterNodeResponse](#zkvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/cluster/id/{id}/node/precheck/

#### POST
##### Summary

check cluster nodes' condition before online

##### Description

check nodes' iptables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | path | cluster id | Yes | string |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.ClusterNodePreCheckResponse](#zkvoclusternodeprecheckresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/cluster/meta/

#### GET
##### Summary

fetch zk cluster's meta

##### Description

fetch zk cluster's meta

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| rz | path | zk cluster rz | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.ClusterMetaResponse](#zkvoclustermetaresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/cluster/sync/

#### POST
##### Summary

sync existing cluster's zk endpoints

##### Description

sync existing cluster's zk endpoints by cluster uuid into AZ meta and k8s CR

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| uuid | path | cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.ClusterNodeResponse](#zkvoclusternoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /zk/v1/node/

#### GET
##### Summary

fetch node lists

##### Description

fetch nodes from AZ Meta

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeListResponse](#zkvonodelistresponse) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/node/event/

#### GET
##### Summary

fetch node events

##### Description

fetch node events by paging

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node's ip address | Yes | string |
| page | query | page number | Yes | integer |
| limit | query | expected how many records | Yes | integer |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeEventResponse](#zkvonodeeventresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/node/expire/

#### PATCH
##### Summary

clean-up expired nodes

##### Description

delete those expired nodes under Initialised or Running state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeResponse](#zkvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/node/ma/

#### PATCH
##### Summary

set node maintenance ticket

##### Description

set node maintenance ticket

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeResponse](#zkvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/node/provision/

#### GET
##### Summary

fetch node provision config

##### Description

fetch node provision configs, including template, component and events

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | etcd nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeProvisionResponse](#zkvonodeprovisionresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/node/recover/

#### PUT
##### Summary

recover node after MA, trigger recovery workflow

##### Description

update node state to PostChecking, they must be in MA

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | nodes' ip address list | Yes | [ string ] |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeResponse](#zkvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/node/spec/

#### GET
##### Summary

fetch node k8s runtime spec

##### Description

fetch node runtime state

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | zk nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeSpecResponse](#zkvonodespecresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node k8s runtime spec

##### Description

update node runtime spec

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | zk nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeResponse](#zkvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/node/state/

#### GET
##### Summary

fetch node state info

##### Description

fetch node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | query | node ip address | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.GetNodeResponse](#zkvogetnoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

#### PUT
##### Summary

update node state info

##### Description

update node state details

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ip | body | node ip address | Yes | string |
| state | body | node state,like spare,format etc. | Yes | string |
| reason | body | state reason | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse](#git_garena_com_shopee_devops_sgw-addon-operator_internal_corebaseresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/node/state/{state}/

#### GET
##### Summary

fetch nodes by state

##### Description

fetch nodes by state

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [sgwvo.StateNodesResponse](#sgwvostatenodesresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/node/variable/

#### GET
##### Summary

fetch node tag variables

##### Description

fetch node tag variables

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | query | nodes' ip address list | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeTagVariableResponse](#zkvonodetagvariableresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### /zk/v1/ticket/

#### POST
##### Summary

create SWP ticket

##### Description

create SWP ticket about zk operation, online/offline

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| state | body | zk Node state, options:PreRunning PreOffline PreMA | No | string |
| ips | body | related nodes ip address | Yes | [ string ] |
| type | body | ticket type, option:online offline prema | No | string |
| reason | body | ticket reason | No | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.TicketResponse](#zkvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/ticket/node/

#### POST
##### Summary

create add node ticket

##### Description

create a ticket to add nodes to zk cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | added nodes ip address | Yes | [ string ] |
| type | body | k8s resource type, option:ZK | Yes | string |
| uuid | body | zk cluster uuid | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.TicketResponse](#zkvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/ticket/node/callback/

#### POST
##### Summary

add nodes into zk cluster

##### Description

add nodes into zk cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| form_data | body | added nodes and cluster info | Yes | [zkvo.TicketAddCBRequest](#zkvoticketaddcbrequest) |
| id | body | related ticket id | Yes | [zkvo.TicketAddCBRequest](#zkvoticketaddcbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeResponse](#zkvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/ticket/node/retire/

#### POST
##### Summary

create retire node ticket

##### Description

create a ticket to retire nodes from zk cluster

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| ips | body | retired nodes ip address | Yes | [ string ] |
| type | body | operation type, option:online offline ma retire | Yes | string |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.TicketResponse](#zkvoticketresponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

### /zk/v1/ticket/node/retire/callback/

#### POST
##### Summary

retire nodes into zk cluster

##### Description

retire nodes from zk cluster by ticket info

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| id | body | related ticket id | Yes | [zkvo.RetireNodeCBRequest](#zkvoretirenodecbrequest) |

##### Responses

| Code | Description | Schema |
| ---- | ----------- | ------ |
| 200 | OK | [zkvo.NodeResponse](#zkvonoderesponse) |
| 400 | Bad Request | [middlewares.Error](#middlewareserror) |
| 500 | Internal Server Error | [middlewares.Error](#middlewareserror) |

---
### Models

#### albvo.Cluster

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_cluster_domains | [albvo.ClusterDomains](#albvoclusterdomains) |  | No |
| allocable_status | string |  | No |
| az | string |  | No |
| bound_alb_only | boolean |  | No |
| cluster_domain | string |  | No |
| cluster_lan_ips | [ string ] |  | No |
| cluster_private_wan_ips | [ string ] |  | No |
| cluster_type | string | NONSTD,STD-4,STD-16 | No |
| cluster_wan_ips | [ string ] |  | No |
| cpu_count | integer |  | No |
| created_at | integer |  | No |
| deploy_type | string |  | No |
| ecp_detail | [albvo.ECPDetail](#albvoecpdetail) |  | No |
| env | string |  | No |
| etcd_endpoints | [ string ] |  | No |
| etcd_timeout | integer |  | No |
| external2_detail | [albvo.Ext2Detail](#albvoext2detail) |  | No |
| id | integer |  | No |
| instances | [ [albvo.Instance](#albvoinstance) ] |  | No |
| is_default | boolean |  | No |
| is_ecp | boolean |  | No |
| is_legacy | boolean |  | No |
| is_service | boolean |  | No |
| labels | [ [albvo.Label](#albvolabel) ] |  | No |
| lan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |
| listener_count | integer |  | No |
| name | string |  | No |
| network_env | string | physical vpc | No |
| network_type | string | WAN LAN | No |
| node_etcd | string | etcd url like etcd://etcd-1:2379,etcd-2:2379/mesos-etcd | No |
| node_zk | string | zk url like zk://zookeeper-1:2181,zookeeper-2:2181/mesos-zk | No |
| nodes | [ [sgwvo.Node](#sgwvonode) ] |  | No |
| region | string |  | No |
| remark | string |  | No |
| resource_type | string |  | No |
| rz | string |  | No |
| score | integer |  | No |
| sdu | string |  | No |
| segment | string |  | No |
| subsystem | string | alb,lcs,ext2 | No |
| target_vpc_name | string |  | No |
| total_active_connections | integer |  | No |
| total_bandwidth | integer |  | No |
| total_qps | integer |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| used_bandwidth | integer |  | No |
| used_qps | integer |  | No |
| uuid | string |  | Yes |
| wan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |

#### albvo.ClusterAddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster | string |  | No |
| component | [albvo.NodeComponentVersion](#albvonodecomponentversion) |  | Yes |
| ips | [ string ] |  | Yes |
| uuid | string |  | Yes |

#### albvo.ClusterAddNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [albvo.ClusterAddNodeCBFormData](#albvoclusteraddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### albvo.ClusterDomains

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| lan_domain | string |  | No |
| wan_domain | string |  | No |

#### albvo.ClusterIP

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_uuid | string |  | No |
| create_at | integer |  | No |
| deleted_at | integer |  | No |
| id | integer |  | No |
| ip | string |  | No |
| update_at | integer |  | No |
| update_by | string |  | No |

#### albvo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [albvo.ClusterOverview](#albvoclusteroverview) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |

#### albvo.ClusterNodeNLBListenerResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"listeners"**: [ [nlbvo.ListenerTarget](#nlbvolistenertarget) ] } |  | No |
| message | string |  | No |

#### albvo.ClusterNodePreCheckResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"results"**: [ [sgwvo.NodeTaskResult](#sgwvonodetaskresult) ] } |  | No |
| message | string |  | No |

#### albvo.ClusterNodeTicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ], **"ticket"**: [swpvo.Ticket](#swpvoticket) } |  | No |
| message | string |  | No |

#### albvo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_cluster_domains | [albvo.ClusterDomains](#albvoclusterdomains) |  | No |
| allocable_status | string |  | No |
| az | string |  | No |
| bound_alb_only | boolean |  | No |
| cluster_domain | string |  | No |
| cluster_lan_ips | [ string ] |  | No |
| cluster_private_wan_ips | [ string ] |  | No |
| cluster_type | string | NONSTD,STD-4,STD-16 | No |
| cluster_wan_ips | [ string ] |  | No |
| combination | [albvo.Combination](#albvocombination) |  | No |
| cpu_count | integer |  | No |
| created_at | integer |  | No |
| deploy_type | string |  | No |
| ecp_detail | [albvo.ECPDetail](#albvoecpdetail) |  | No |
| env | string |  | No |
| etcd_endpoints | [ string ] |  | No |
| etcd_timeout | integer |  | No |
| external2_detail | [albvo.Ext2Detail](#albvoext2detail) |  | No |
| ha | string |  | No |
| ha_component | string |  | No |
| id | integer |  | No |
| instances | [ [albvo.Instance](#albvoinstance) ] |  | No |
| is_default | boolean |  | No |
| is_ecp | boolean |  | No |
| is_legacy | boolean |  | No |
| is_service | boolean |  | No |
| labels | [ [albvo.Label](#albvolabel) ] |  | No |
| lan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |
| listener_count | integer |  | No |
| managed | boolean |  | No |
| monitors | [albvo.Monitors](#albvomonitors) |  | No |
| name | string |  | No |
| network_env | string | physical vpc | No |
| network_type | string | WAN LAN | No |
| node_etcd | string | etcd url like etcd://etcd-1:2379,etcd-2:2379/mesos-etcd | No |
| node_zk | string | zk url like zk://zookeeper-1:2181,zookeeper-2:2181/mesos-zk | No |
| nodes | [ [sgwvo.Node](#sgwvonode) ] |  | No |
| ready_server_num | integer |  | No |
| region | string |  | No |
| remark | string |  | No |
| resource_type | string |  | No |
| rz | string |  | No |
| score | integer |  | No |
| sdu | string |  | No |
| segment | string |  | No |
| subsystem | string | alb,lcs,ext2 | No |
| target_vpc_name | string |  | No |
| total_active_connections | integer |  | No |
| total_bandwidth | integer |  | No |
| total_qps | integer |  | No |
| total_server_num | integer |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| used_bandwidth | integer |  | No |
| used_qps | integer |  | No |
| uuid | string |  | Yes |
| wan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |

#### albvo.Combination

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| env | string |  | No |
| region | string |  | No |
| service | string |  | No |
| type | string |  | No |

#### albvo.ComponentListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"components"**: [ [cmp.Component](#cmpcomponent) ] } |  | No |
| message | string |  | No |

#### albvo.ComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| created | integer |  | No |
| version | string |  | No |

#### albvo.ComponentVersionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"versions"**: [ [albvo.ComponentVersion](#albvocomponentversion) ] } |  | No |
| message | string |  | No |

#### albvo.ControlTrafficTaskResultsResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"details"**: [ [sgwvo.NodeTaskResult](#sgwvonodetaskresult) ] } |  | No |
| message | string |  | No |

#### albvo.ECPDetail

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| branch | string |  | No |
| cid | string |  | No |
| cluster | string |  | No |
| deployment_id | string |  | No |
| env | string |  | No |
| module | string |  | No |
| project_name | string |  | No |
| replicas | integer |  | No |
| service_name | string |  | No |
| version | string |  | No |

#### albvo.Ext2Detail

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_stage | string |  | No |
| env | string |  | No |
| nlb_param | [albvo.NLBParam](#albvonlbparam) |  | No |
| total_conn | integer |  | No |

#### albvo.GetNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [albvo.NodeState](#albvonodestate) |  | No |
| message | string |  | No |

#### albvo.GetStateTasksResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"tasks"**: [ string ] } |  | No |
| message | string |  | No |

#### albvo.GetStatesResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"states"**: [ [consts.State](#constsstate) ] } |  | No |
| message | string |  | No |

#### albvo.HotUpdateNodeBaseRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| business_type | string |  | Yes |
| gray_ips | [ string ] | first batch | Yes |
| ips | [ string ] | total IP list | Yes |
| other_ips | [ string ] | second batch | No |
| version | string |  | Yes |

#### albvo.HotUpdateTicketFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| gray_ips | [ string ] | gray_ips 可以用户传入，也可以通过计算得到 | No |
| gray_scale | integer | 如果gray_ips不是用户传入的， 那么就是必须填写的 | No |
| host | string |  | Yes |
| ips | [ string ] | must be the same ENV AZ | Yes |
| operation_description | string |  | Yes |
| operation_type | string | *Enum:* `"delete"`, `"update"`, `"add"` | Yes |
| other_ips | [ string ] | other_ips 通过计算得到 | No |

#### albvo.Instance

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| bandwidth | integer |  | No |
| cluster | [albvo.Cluster](#albvocluster) |  | No |
| created_at | integer |  | No |
| created_by | string |  | No |
| domains | [ string ] |  | No |
| env | string |  | No |
| instance_id | string |  | No |
| integrate_nlb | boolean |  | No |
| is_legacy | boolean |  | No |
| is_service | boolean |  | No |
| listener_count | integer |  | No |
| name | string |  | No |
| network_env | string |  | No |
| network_type | string |  | No |
| nlb_params | [ [albvo.NLBParam](#albvonlbparam) ] |  | No |
| peak_active_connections | integer |  | No |
| peak_qps | integer |  | No |
| region | string |  | No |
| remark | string |  | No |
| resource_type | string |  | No |
| segment | string |  | No |
| stage | string |  | No |
| status | string |  | No |
| subsystem | string |  | No |
| target_vpc_name | string |  | No |
| tree_node_id | string |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### albvo.Label

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| color | string |  | No |
| id | integer |  | No |
| label_name | string |  | No |
| remark | string |  | No |

#### albvo.Monitors

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_monitor | string |  | No |
| sc_monitor | string |  | No |
| total_bytes | string |  | No |
| total_connection | string |  | No |
| total_error | string |  | No |
| total_packet | string |  | No |
| vs_connection | string |  | No |

#### albvo.NLBParam

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_name | string |  | No |
| cluster_uuid | string |  | No |
| domain | string |  | No |
| instance_id | string |  | No |
| instance_name | string |  | No |
| network_type | string |  | No |
| vip | string |  | No |

#### albvo.NodeComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_metrics | string |  | No |
| alb_sd | string |  | No |
| alb_waf | string |  | No |
| mesos_nginx_lb | string |  | No |
| nginx | string |  | No |
| sgw_agent | string |  | No |

#### albvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### albvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_name | string |  | No |
| application | string |  | No |
| cluster_uuid | string |  | No |
| components | object |  | No |
| created_at | integer |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| function | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| sdu | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### albvo.NodeInfosResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [albvo.NodeInfo](#albvonodeinfo) ] } |  | No |
| message | string |  | No |

#### albvo.NodeProvisionConfigResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) |  | No |
| message | string |  | No |

#### albvo.NodeProvisionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) ] |  | No |
| message | string |  | No |

#### albvo.NodeRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| ips | [ string ] |  | Yes |

#### albvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### albvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.ALBSpec](#v1alpha1albspec) ] |  | No |
| message | string |  | No |

#### albvo.NodeState

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_name | string |  | No |
| application | string |  | No |
| function | string |  | No |
| ip | string |  | No |
| sdu | string |  | No |
| state | string |  | No |
| status | string |  | No |
| updated_at | string |  | No |

#### albvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### albvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### albvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [albvo.TicketData](#albvoticketdata) |  | No |
| message | string |  | No |

#### anchorvo.AZMeta

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az_meta_id | object |  | No |
| az_meta_name | object |  | No |

#### anchorvo.AddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_id | string |  | No |
| cluster_name | string |  | No |
| ips | [ string ] |  | Yes |
| role | string |  | No |
| state | string |  | No |
| type | string |  | Yes |

#### anchorvo.AnchorNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| ip | string |  | No |
| metadata | object |  | No |

#### anchorvo.ClusterBasic

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| az_full_name | string |  | No |
| az_meta | [anchorvo.AZMeta](#anchorvoazmeta) |  | No |
| bu | string |  | No |
| cluster_id | string |  | No |
| create_at | string |  | No |
| create_by | string |  | No |
| description | string |  | No |
| id | string |  | No |
| labels | object |  | No |
| name | string |  | No |
| update_at | string |  | No |

#### anchorvo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [anchorvo.ClusterOverview](#anchorvoclusteroverview) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |

#### anchorvo.ClusterNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [anchorvo.NodeInfo](#anchorvonodeinfo) ] } |  | No |
| message | string |  | No |

#### anchorvo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| anchor | [anchorvo.Node](#anchorvonode) |  | No |
| anchor_node | [ [anchorvo.AnchorNode](#anchorvoanchornode) ] |  | No |
| anchors | [ [anchorvo.Node](#anchorvonode) ] |  | No |
| basic | [anchorvo.ClusterBasic](#anchorvoclusterbasic) |  | No |
| components_status | [anchorvo.ComponentsStatus](#anchorvocomponentsstatus) |  | No |
| dry_run | boolean |  | No |
| egw_groups | [ string ] |  | No |
| egw_xdps | [ [vgwvo.Node](#vgwvonode) ] |  | No |
| egws | [ [vgwvo.Node](#vgwvonode) ] |  | No |
| etcd | [anchorvo.ETCD](#anchorvoetcd) |  | No |
| location | string |  | No |
| ready_server_num | integer |  | No |
| swp_id | string |  | No |
| toc_link | string |  | No |
| total_server_num | integer |  | No |
| type | string |  | No |
| vgw | [anchorvo.VGW](#anchorvovgw) |  | No |
| vgwp4s | [ [vgwvo.Node](#vgwvonode) ] |  | No |
| xgw_groups | [ string ] |  | No |
| xgws | [ [vgwvo.Node](#vgwvonode) ] |  | No |

#### anchorvo.ClusterSubnetResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"domain"**: string, **"subnets"**: [ [anchorvo.Subnet](#anchorvosubnet) ] } |  | No |
| message | string |  | No |

#### anchorvo.ComponentStatus

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| available_operation_names | [ string ] |  | No |
| component_status | string |  | No |
| gw_group | string |  | No |

#### anchorvo.ComponentsStatus

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| anchorStatus | [anchorvo.ComponentStatus](#anchorvocomponentstatus) |  | No |
| egwStatus | [ [anchorvo.ComponentStatus](#anchorvocomponentstatus) ] |  | No |
| egwXdpStatus | [ [anchorvo.ComponentStatus](#anchorvocomponentstatus) ] |  | No |
| vgwp4Status | [ [anchorvo.ComponentStatus](#anchorvocomponentstatus) ] |  | No |
| xgwStatus | [ [anchorvo.ComponentStatus](#anchorvocomponentstatus) ] |  | No |

#### anchorvo.ETCD

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| ca_cert | string |  | No |
| client_cert | string |  | No |
| client_key | string |  | No |
| nodes | [ string ] |  | No |
| primary | integer |  | No |

#### anchorvo.FormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| action | string |  | No |
| ips | [ string ] |  | No |
| nodes | [ [anchorvo.Node](#anchorvonode) ] |  | No |
| reason | string |  | No |
| updated_by | string |  | No |

#### anchorvo.FrpInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| node_ip | string |  | No |
| node_port | integer |  | No |
| proxy_ip | string |  | No |
| proxy_port | integer |  | No |

#### anchorvo.GetNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [anchorvo.NodeState](#anchorvonodestate) |  | No |
| message | string |  | No |

#### anchorvo.Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cert | string |  | No |
| domain | string |  | No |
| frps | object |  | No |
| grpc_port | integer |  | No |
| grpc_tls_port | integer |  | No |
| http_port | integer |  | No |
| https_port | integer |  | No |
| ip | string | Anchor Cluster Mgmt lumps all Anchor controllers into 1 single node need a new ip field to differentiate each node | No |
| nodes | [ string ] |  | No |
| pinger_host | string |  | No |
| services_address | object |  | No |
| token | string |  | No |

#### anchorvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### anchorvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cert | string |  | No |
| components | object |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| domain | string |  | No |
| frps | object |  | No |
| function | string |  | No |
| grpc_port | integer |  | No |
| grpc_tls_port | integer |  | No |
| http_port | integer |  | No |
| https_port | integer |  | No |
| ip | string | Anchor Cluster Mgmt lumps all Anchor controllers into 1 single node need a new ip field to differentiate each node | No |
| links | [ [tocvo.Nic](#tocvonic) ] |  | No |
| nodes | [ string ] |  | No |
| pinger_host | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| services_address | object |  | No |
| state | string |  | No |
| switches | [ [ndmpvo.Switch](#ndmpvoswitch) ] |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| token | string |  | No |

#### anchorvo.NodeListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [anchorvo.NodeInfo](#anchorvonodeinfo) ] } |  | No |
| message | string |  | No |

#### anchorvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### anchorvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.AnchorSpec](#v1alpha1anchorspec) ] |  | No |
| message | string |  | No |

#### anchorvo.NodeState

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_id | string |  | No |
| cluster_name | string |  | No |
| function | string |  | No |
| ip | string |  | No |
| state | string |  | No |
| status | string |  | No |
| updated_at | string |  | No |

#### anchorvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### anchorvo.RetireNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [anchorvo.FormData](#anchorvoformdata) |  | Yes |
| id | integer |  | Yes |

#### anchorvo.Subnet

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| availableCnt | integer |  | No |
| az | string |  | No |
| basic | [anchorvo.SubnetBasic](#anchorvosubnetbasic) |  | No |
| dnsNameservers | [ string ] |  | No |
| externalIpam | boolean |  | No |
| gateway | string |  | No |
| host | string |  | No |
| ipEnd | string |  | No |
| ipStart | string |  | No |
| sharedSubnet | boolean |  | No |
| subnetId | integer |  | No |
| subnetPool |  |  | No |
| type | string |  | No |

#### anchorvo.SubnetBasic

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| createAt | string |  | No |
| description | string |  | No |
| labels | object |  | No |
| name | string |  | No |
| subNamespace | string |  | No |
| updateAt | string |  | No |
| vpc | string |  | No |

#### anchorvo.TicketAddCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [anchorvo.AddNodeCBFormData](#anchorvoaddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### anchorvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### anchorvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [anchorvo.TicketData](#anchorvoticketdata) |  | No |
| message | string |  | No |

#### anchorvo.VGW

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| anchor_gateway | integer |  | No |
| anchor_gateway_exporter | integer |  | No |
| anchor_monitor | integer |  | No |
| nodes | [ string ] |  | No |

#### cmdbvo.BindServerRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| idc | string |  | No |
| ips | [ string ] |  | Yes |
| sdu | string |  | Yes |
| sdu_labels | object |  | No |
| service_id | integer |  | No |

#### cmdbvo.GetMonitorTagsResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"labels"**: , **"tag"**: [monitor.MonitorPort](#monitormonitorport) } |  | No |
| message | string |  | No |

#### cmp.Component

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| dependencies | [ [cmp.Component](#cmpcomponent) ] |  | No |
| dependent_images | [ [cmp.Component](#cmpcomponent) ] |  | No |
| dependent_non_live_templates | [ [tpl.Template](#tpltemplate) ] |  | No |
| dependent_private_templates | [ [tpl.Template](#tpltemplate) ] |  | No |
| dependent_templates | [ [tpl.Template](#tpltemplate) ] |  | No |
| image | string | only work when type=docker, it's a short name | No |
| name | string |  | No |
| service | string |  | No |
| type | string |  | No |
| verify_version | boolean | verify component version when add or hot-update | No |
| version | string | empty means latest | No |

#### consts.State

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| reason | string |  | No |
| status | string |  | No |

#### core.ItemError

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| ip | string |  | No |
| message | string |  | No |

#### defs.GetResp

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| assignee_team_id | string |  | No |
| assignee_user | string |  | No |
| bundle_meta | object |  | No |
| created_at | integer |  | No |
| creator | string |  | No |
| eta | integer |  | No |
| executable_transitions | [ [defs.SimplifiedTransition](#defssimplifiedtransition) ] |  | No |
| fields_to_show | object |  | No |
| form_data | object |  | No |
| id | integer |  | No |
| is_underway | boolean |  | No |
| json_schema | string |  | No |
| label | [ticket_defs.TicketLabel](#ticket_defsticketlabel) |  | No |
| name | string | SWP-1 SGW-10 | No |
| phase | string |  | No |
| phase_id | integer |  | No |
| phase_type | string |  | No |
| renderer_type | string |  | No |
| reviewers | [ string ] | simplified field for backward compatible | No |
| reviewers_with_role | [ [reviewer_defs.Reviewer](#reviewer_defsreviewer) ] |  | No |
| template_id | integer |  | No |
| title | string |  | No |
| ui_schema | string |  | No |
| updated_at | integer |  | No |
| viewable | boolean |  | No |
| watchers | [ string ] |  | No |
| workflow_id | integer |  | No |

#### defs.RBACPermission

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| module | string |  | No |
| object_type | string |  | No |
| operation | string |  | No |

#### defs.SAMPermission

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| env | string |  | No |
| object | string |  | No |
| operation | string |  | No |
| subsystem_id | integer |  | No |

#### defs.SimplifiedTransition

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| from_phase | string |  | No |
| on_behalf_roles | [ string ] | available for PIPELINE_REVIEWING phase | No |
| rbac_permissions | [ [defs.RBACPermission](#defsrbacpermission) ] |  | No |
| require_supplement_type | string |  | No |
| sam_permissions | [ [defs.SAMPermission](#defssampermission) ] |  | No |
| target | string |  | No |
| to_phase | string |  | No |
| transition_type | string |  | No |

#### dnsvo.ClusterConfigResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"config"**: [metavo.MetaVar](#metavometavar) } |  | No |
| message | string |  | No |

#### dnsvo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [dnsvo.ClusterOverview](#dnsvoclusteroverview) ] } |  | No |
| message | string |  | No |

#### dnsvo.ClusterNodePreCheckResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"results"**: [ [sgwvo.NodeTaskResult](#sgwvonodetaskresult) ] } |  | No |
| message | string |  | No |

#### dnsvo.ClusterNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [dnsvo.NodeInfo](#dnsvonodeinfo) ] } |  | No |
| message | string |  | No |

#### dnsvo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| availability | string |  | No |
| az | string |  | No |
| dns_agent_version | string |  | No |
| dpdkdns_kernel_version | string | default component versions | No |
| ecmp_vips | [ string ] |  | No |
| env | string |  | No |
| ha | string |  | No |
| ha_component | string |  | No |
| id | string |  | No |
| idc | string |  | No |
| labels | [ integer ] |  | No |
| name | string |  | No |
| node_infos | [ [metavo.Node](#metavonode) ] |  | No |
| nodes | [ string ] |  | No |
| ready_server_num | integer |  | No |
| remark | string |  | No |
| segment_name | string |  | No |
| status | string |  | No |
| total_server_num | integer |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| version | string |  | No |

#### dnsvo.ComponentListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"components"**: [ [cmp.Component](#cmpcomponent) ] } |  | No |
| message | string |  | No |

#### dnsvo.ComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| created | integer |  | No |
| version | string |  | No |

#### dnsvo.ComponentVersionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"versions"**: [ [dnsvo.ComponentVersion](#dnsvocomponentversion) ] } |  | No |
| message | string |  | No |

#### dnsvo.HotUpdateNodeRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| cluster_id | integer | cluster's id | Yes |
| cluster_name | string |  | No |
| component | string |  | Yes |
| gray_ips | [ string ] | first batch | No |
| gray_scale | integer |  | No |
| ips | [ string ] | total IP list | Yes |
| operation_description | string |  | Yes |
| operation_type | string | *Enum:* `"update"`, `"delete"`, `"add"` | Yes |
| other_ips | [ string ] | second batch | No |
| rollback_version | string |  | Yes |
| target_version | string |  | Yes |
| version | string |  | No |

#### dnsvo.Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| cluster_id | string |  | No |
| cluster_name | string |  | No |
| component_name | string |  | No |
| create_at | string |  | No |
| env | string |  | No |
| geo | string |  | No |
| id | string |  | No |
| idc | string |  | No |
| ip | string |  | No |
| ip_lan | string |  | No |
| metrics_port | integer |  | No |
| remarks | string |  | No |
| role | string |  | No |
| segment | string |  | No |
| state | string |  | No |
| status | string |  | No |
| toc_cluster | string |  | No |
| update_at | string |  | No |

#### dnsvo.NodeComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| dns_agent | string |  | Yes |
| dpdk_dns | string |  | No |
| dpdk_dns_config | string |  | Yes |

#### dnsvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### dnsvo.NodeFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_id | integer |  | Yes |
| cluster_name | string |  | Yes |
| component | [dnsvo.NodeComponentVersion](#dnsvonodecomponentversion) |  | No |
| component_txt | string |  | No |
| ips | [ string ] |  | Yes |
| ips_txt | string |  | No |
| reason | string |  | No |
| role | string | *Enum:* `"unbound"` | Yes |

#### dnsvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_info | [metavo.MetaVar](#metavometavar) |  | No |
| cluster_name | string |  | No |
| components | object |  | No |
| created_at | integer |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| function | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### dnsvo.NodeInfosResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [dnsvo.NodeInfo](#dnsvonodeinfo) ] } |  | No |
| message | string |  | No |

#### dnsvo.NodeListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [dnsvo.Node](#dnsvonode) ] } |  | No |
| message | string |  | No |

#### dnsvo.NodeProvisionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) ] |  | No |
| message | string |  | No |

#### dnsvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### dnsvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.DNSSpec](#v1alpha1dnsspec) ] |  | No |
| message | string |  | No |

#### dnsvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### dnsvo.NodeTicketCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [dnsvo.NodeFormData](#dnsvonodeformdata) |  | Yes |
| id | integer |  | Yes |

#### dnsvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### dnsvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [dnsvo.TicketData](#dnsvoticketdata) |  | No |
| message | string |  | No |

#### drcvo.EventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.DREventLog](#opsentdreventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### drcvo.TaskDetailsResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [v1alpha1.SGWDR](#v1alpha1sgwdr) |  | No |
| message | string |  | No |

#### drcvo.TaskEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [opsent.DREvent](#opsentdrevent) |  | No |
| message | string |  | No |

#### drcvo.TaskEventsResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [opsent.DREvent](#opsentdrevent) ] |  | No |
| message | string |  | No |

#### drcvo.TaskSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [v1alpha1.SGWDRSpec](#v1alpha1sgwdrspec) |  | No |
| message | string |  | No |

#### drcvo.TaskStateResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [v1alpha1.SGWDRStatus](#v1alpha1sgwdrstatus) |  | No |
| message | string |  | No |

#### egwvo.AddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_id | string |  | No |
| cluster_name | string |  | No |
| group_id | string |  | No |
| group_name | string |  | No |
| ips | [ string ] |  | Yes |
| role | string |  | No |
| state | string |  | No |
| type | string |  | Yes |

#### egwvo.ClusterNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [vgwvo.NodeInfo](#vgwvonodeinfo) ] } |  | No |
| message | string |  | No |

#### egwvo.FormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| action | string |  | No |
| ips | [ string ] |  | No |
| nodes | [ [vgwvo.Node](#vgwvonode) ] |  | No |
| reason | string |  | No |
| updated_by | string |  | No |

#### egwvo.GetNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [egwvo.NodeState](#egwvonodestate) |  | No |
| message | string |  | No |

#### egwvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### egwvo.NodeListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [vgwvo.NodeInfo](#vgwvonodeinfo) ] } |  | No |
| message | string |  | No |

#### egwvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### egwvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.EGWSpec](#v1alpha1egwspec) ] |  | No |
| message | string |  | No |

#### egwvo.NodeState

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_id | string |  | No |
| cluster_name | string |  | No |
| function | string |  | No |
| group_id | string |  | No |
| group_name | string |  | No |
| ip | string |  | No |
| state | string |  | No |
| status | string |  | No |
| updated_at | string |  | No |

#### egwvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### egwvo.RetireNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [egwvo.FormData](#egwvoformdata) |  | Yes |
| id | integer |  | Yes |

#### egwvo.TicketAddCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [egwvo.AddNodeCBFormData](#egwvoaddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### egwvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### egwvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [egwvo.TicketData](#egwvoticketdata) |  | No |
| message | string |  | No |

#### etcdvo.AddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_id | integer |  | No |
| cluster_name | string |  | No |
| etcd_version | string |  | No |
| ips | [ string ] |  | Yes |
| role | string |  | No |
| state | string |  | No |
| type | string |  | Yes |

#### etcdvo.Cert

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| common_name | string |  | No |
| name | string |  | No |
| organization_name | string |  | No |

#### etcdvo.ClusterConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| etcd_cert_dir | string |  | No |
| etcd_cert_roots | [ [etcdvo.Cert](#etcdvocert) ] |  | No |
| etcd_certs | [ [etcdvo.Cert](#etcdvocert) ] |  | No |
| etcd_client_port | integer |  | Yes |
| etcd_cluster_name | string |  | Yes |
| etcd_compaction_period | string |  | Yes |
| etcd_data_dir | string |  | Yes |
| etcd_hosts | object |  | Yes |
| etcd_listen_metrics | string |  | No |
| etcd_max_request_bytes | string |  | No |
| etcd_peer_port | integer |  | Yes |
| etcd_quota | string |  | Yes |
| etcd_tls | boolean |  | No |
| old_cluster_name | string |  | No |

#### etcdvo.ClusterConfigResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"config"**: [etcdvo.ClusterMetaConfig](#etcdvoclustermetaconfig) } |  | No |
| message | string |  | No |

#### etcdvo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [etcdvo.ClusterOverview](#etcdvoclusteroverview) ] } |  | No |
| message | string |  | No |

#### etcdvo.ClusterMeta

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| cluster_url | string |  | Yes |
| env | string |  | No |
| rz | string |  | No |
| segment | string |  | No |

#### etcdvo.ClusterMetaConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_config | [etcdvo.ClusterConfig](#etcdvoclusterconfig) |  | No |
| cluster_url | string |  | Yes |

#### etcdvo.ClusterMetaResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"metas"**: [ [etcdvo.ClusterMeta](#etcdvoclustermeta) ] } |  | No |
| message | string |  | No |

#### etcdvo.ClusterNodePreCheckResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"results"**: [ [sgwvo.NodeTaskResult](#sgwvonodetaskresult) ] } |  | No |
| message | string |  | No |

#### etcdvo.ClusterNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [etcdvo.NodeInfo](#etcdvonodeinfo) ] } |  | No |
| message | string |  | No |

#### etcdvo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| availability | string |  | No |
| az | string |  | No |
| component_name | string |  | No |
| deployment | string |  | No |
| environment | string |  | No |
| id | string |  | No |
| labels | [ integer ] |  | No |
| name | string |  | No |
| network_type | string | WAN LAN | No |
| node_infos | [ [metavo.Node](#metavonode) ] |  | No |
| nodes | [ string ] |  | No |
| ready_server_num | integer |  | No |
| remark | string |  | No |
| resourcezone | string |  | No |
| segment | string |  | No |
| status | string |  | No |
| total_server_num | integer |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| version | string |  | No |

#### etcdvo.EtcdEndpoint

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| host | string |  | No |
| port | integer |  | No |

#### etcdvo.FormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| action | string |  | No |
| ips | [ string ] |  | No |
| nodes | [ [etcdvo.Node](#etcdvonode) ] |  | No |
| reason | string |  | No |
| updated_by | string |  | No |

#### etcdvo.GetNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [etcdvo.NodeState](#etcdvonodestate) |  | No |
| message | string |  | No |

#### etcdvo.Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_name | string |  | No |
| created_at | integer |  | No |
| id | integer |  | No |
| ip | string |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### etcdvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### etcdvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| TLS | boolean |  | No |
| application | string |  | No |
| cluster_name | string |  | No |
| components | object |  | No |
| created_at | integer |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| etcdClientPort | integer |  | No |
| etcdPeerPort | integer |  | No |
| function | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| metrics_port | integer |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### etcdvo.NodeListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [metavo.Node](#metavonode) ] } |  | No |
| message | string |  | No |

#### etcdvo.NodeProvisionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) ] |  | No |
| message | string |  | No |

#### etcdvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### etcdvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.EtcdSpec](#v1alpha1etcdspec) ] |  | No |
| message | string |  | No |

#### etcdvo.NodeState

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_id | string |  | No |
| function | string |  | No |
| ip | string |  | No |
| state | string |  | No |
| status | string |  | No |
| updated_at | string |  | No |

#### etcdvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### etcdvo.RefreshCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_config | [sgwvo.ETCDClusterConfig](#sgwvoetcdclusterconfig) |  | No |
| cluster_id | integer |  | Yes |
| cluster_name | string |  | No |
| etcd_version | string |  | No |
| ips | [ string ] |  | No |
| meta | [sgwvo.ETCDClusterMetaData](#sgwvoetcdclustermetadata) |  | No |
| role | string | *Enum:* `"alb-etcd"`, `"spex-etcd"`, `"anchor-etcd"`, `"egw-etcd"`, `"egw_az-etcd"`, `"naming-etcd"` | Yes |
| state | string |  | No |
| type | string |  | Yes |

#### etcdvo.RetireNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [etcdvo.FormData](#etcdvoformdata) |  | Yes |
| id | integer |  | Yes |

#### etcdvo.TicketAddCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [etcdvo.AddNodeCBFormData](#etcdvoaddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### etcdvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### etcdvo.TicketRefreshCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [etcdvo.RefreshCBFormData](#etcdvorefreshcbformdata) |  | Yes |
| id | integer |  | Yes |

#### etcdvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [etcdvo.TicketData](#etcdvoticketdata) |  | No |
| message | string |  | No |

#### ext2vo.ClusterAddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster | string |  | No |
| component | [ext2vo.NodeComponentVersion](#ext2vonodecomponentversion) |  | Yes |
| ips | [ string ] |  | Yes |
| uuid | string |  | Yes |

#### ext2vo.ClusterAddNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [ext2vo.ClusterAddNodeCBFormData](#ext2voclusteraddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### ext2vo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [ext2vo.ClusterOverview](#ext2voclusteroverview) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |

#### ext2vo.ClusterNodePreCheckResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"results"**: [ [sgwvo.NodeTaskResult](#sgwvonodetaskresult) ] } |  | No |
| message | string |  | No |

#### ext2vo.ClusterNodeTicketRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| cluster | string |  | No |
| component | [ext2vo.NodeComponentVersion](#ext2vonodecomponentversion) |  | No |
| id | integer |  | No |
| ips | [ string ] |  | Yes |
| sdu | string |  | No |
| state | string |  | No |
| token | string |  | No |
| uuid | string | cluster uuid | Yes |

#### ext2vo.ClusterNodeTicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ], **"ticket"**: [swpvo.Ticket](#swpvoticket) } |  | No |
| message | string |  | No |

#### ext2vo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_cluster_domains | [albvo.ClusterDomains](#albvoclusterdomains) |  | No |
| allocable_status | string |  | No |
| az | string |  | No |
| bound_alb_only | boolean |  | No |
| cluster_domain | string |  | No |
| cluster_lan_ips | [ string ] |  | No |
| cluster_private_wan_ips | [ string ] |  | No |
| cluster_type | string | NONSTD,STD-4,STD-16 | No |
| cluster_wan_ips | [ string ] |  | No |
| combination | [albvo.Combination](#albvocombination) |  | No |
| cpu_count | integer |  | No |
| created_at | integer |  | No |
| deploy_type | string |  | No |
| ecp_detail | [albvo.ECPDetail](#albvoecpdetail) |  | No |
| env | string |  | No |
| etcd_endpoints | [ string ] |  | No |
| etcd_timeout | integer |  | No |
| external2_detail | [albvo.Ext2Detail](#albvoext2detail) |  | No |
| ha | string |  | No |
| ha_component | string |  | No |
| id | integer |  | No |
| instances | [ [albvo.Instance](#albvoinstance) ] |  | No |
| is_default | boolean |  | No |
| is_ecp | boolean |  | No |
| is_legacy | boolean |  | No |
| is_service | boolean |  | No |
| labels | [ [albvo.Label](#albvolabel) ] |  | No |
| lan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |
| listener_count | integer |  | No |
| managed | boolean |  | No |
| monitors | [albvo.Monitors](#albvomonitors) |  | No |
| name | string |  | No |
| network_env | string | physical vpc | No |
| network_type | string | WAN LAN | No |
| node_etcd | string | etcd url like etcd://etcd-1:2379,etcd-2:2379/mesos-etcd | No |
| node_zk | string | zk url like zk://zookeeper-1:2181,zookeeper-2:2181/mesos-zk | No |
| nodes | [ [sgwvo.Node](#sgwvonode) ] |  | No |
| ready_server_num | integer |  | No |
| region | string |  | No |
| remark | string |  | No |
| resource_type | string |  | No |
| rz | string |  | No |
| score | integer |  | No |
| sdu | string |  | No |
| segment | string |  | No |
| subsystem | string | alb,lcs,ext2 | No |
| target_vpc_name | string |  | No |
| total_active_connections | integer |  | No |
| total_bandwidth | integer |  | No |
| total_qps | integer |  | No |
| total_server_num | integer |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| used_bandwidth | integer |  | No |
| used_qps | integer |  | No |
| uuid | string |  | Yes |
| wan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |

#### ext2vo.ComponentListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"components"**: [ [cmp.Component](#cmpcomponent) ] } |  | No |
| message | string |  | No |

#### ext2vo.ComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| created | integer |  | No |
| version | string |  | No |

#### ext2vo.ComponentVersionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"versions"**: [ [ext2vo.ComponentVersion](#ext2vocomponentversion) ] } |  | No |
| message | string |  | No |

#### ext2vo.HotUpdateNodeBaseRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| business_type | string |  | Yes |
| gray_ips | [ string ] | first batch | Yes |
| ips | [ string ] | total IP list | Yes |
| other_ips | [ string ] | second batch | No |
| version | string |  | Yes |

#### ext2vo.HotUpdateTicketFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| gray_ips | [ string ] | gray_ips 可以用户传入，也可以通过计算得到 | No |
| gray_scale | integer | 如果gray_ips不是用户传入的， 那么就是必须填写的 | No |
| host | string |  | Yes |
| ips | [ string ] | must be the same ENV AZ | Yes |
| operation_description | string |  | Yes |
| operation_type | string | *Enum:* `"delete"`, `"update"`, `"add"` | Yes |
| other_ips | [ string ] | other_ips 通过计算得到 | No |

#### ext2vo.NodeComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| ext2_metrics | string |  | Yes |
| ext2_nginx | string |  | Yes |
| nginx | string |  | Yes |
| sgw_agent | string |  | Yes |

#### ext2vo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### ext2vo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_uuid | string |  | No |
| components | object |  | No |
| created_at | integer |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| ext2_name | string |  | No |
| function | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| sdu | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### ext2vo.NodeInfosResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [ext2vo.NodeInfo](#ext2vonodeinfo) ] } |  | No |
| message | string |  | No |

#### ext2vo.NodeProvisionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) ] |  | No |
| message | string |  | No |

#### ext2vo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### ext2vo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.Ext2Spec](#v1alpha1ext2spec) ] |  | No |
| message | string |  | No |

#### ext2vo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### ext2vo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### ext2vo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ext2vo.TicketData](#ext2voticketdata) |  | No |
| message | string |  | No |

#### git_garena_com_shopee_devops_sgw-addon-operator_internal_core.BaseResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| message | string |  | No |

#### gorm.DeletedAt

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| time | string |  | No |
| valid | boolean | Valid is true if Time is not NULL | No |

#### klsvo.AddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster | string |  | No |
| component | [klsvo.NodeComponentVersion](#klsvonodecomponentversion) |  | Yes |
| ips | [ string ] |  | Yes |
| uuid | string |  | Yes |

#### klsvo.AddNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [klsvo.AddNodeCBFormData](#klsvoaddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### klsvo.AddNodeTicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ], **"ticket"**: [swpvo.Ticket](#swpvoticket) } |  | No |
| message | string |  | No |

#### klsvo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [klsvo.ClusterOverview](#klsvoclusteroverview) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |

#### klsvo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_cluster_domains | [albvo.ClusterDomains](#albvoclusterdomains) |  | No |
| allocable_status | string |  | No |
| az | string |  | No |
| bound_alb_only | boolean |  | No |
| cluster_domain | string |  | No |
| cluster_lan_ips | [ string ] |  | No |
| cluster_private_wan_ips | [ string ] |  | No |
| cluster_type | string | NONSTD,STD-4,STD-16 | No |
| cluster_wan_ips | [ string ] |  | No |
| combination | [klsvo.Combination](#klsvocombination) |  | No |
| cpu_count | integer |  | No |
| created_at | integer |  | No |
| deploy_type | string |  | No |
| ecp_detail | [albvo.ECPDetail](#albvoecpdetail) |  | No |
| env | string |  | No |
| etcd_endpoints | [ string ] |  | No |
| etcd_timeout | integer |  | No |
| external2_detail | [albvo.Ext2Detail](#albvoext2detail) |  | No |
| ha | string |  | No |
| ha_component | string |  | No |
| id | integer |  | No |
| instances | [ [albvo.Instance](#albvoinstance) ] |  | No |
| is_default | boolean |  | No |
| is_ecp | boolean |  | No |
| is_legacy | boolean |  | No |
| is_service | boolean |  | No |
| labels | [ [albvo.Label](#albvolabel) ] |  | No |
| lan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |
| listener_count | integer |  | No |
| managed | boolean |  | No |
| monitors | [klsvo.Monitors](#klsvomonitors) |  | No |
| name | string |  | No |
| network_env | string | physical vpc | No |
| network_type | string | WAN LAN | No |
| node_etcd | string | etcd url like etcd://etcd-1:2379,etcd-2:2379/mesos-etcd | No |
| node_zk | string | zk url like zk://zookeeper-1:2181,zookeeper-2:2181/mesos-zk | No |
| nodes | [ [sgwvo.Node](#sgwvonode) ] |  | No |
| ready_server_num | integer |  | No |
| region | string |  | No |
| remark | string |  | No |
| resource_type | string |  | No |
| rz | string |  | No |
| score | integer |  | No |
| sdu | string |  | No |
| segment | string |  | No |
| subsystem | string | alb,lcs,ext2 | No |
| target_vpc_name | string |  | No |
| total_active_connections | integer |  | No |
| total_bandwidth | integer |  | No |
| total_qps | integer |  | No |
| total_server_num | integer |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| used_bandwidth | integer |  | No |
| used_qps | integer |  | No |
| uuid | string |  | Yes |
| wan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |

#### klsvo.Combination

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| env | string |  | No |
| region | string |  | No |
| service | string |  | No |
| type | string |  | No |

#### klsvo.HotUpdateNodeBaseRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| business_type | string |  | Yes |
| gray_ips | [ string ] | first batch | Yes |
| ips | [ string ] | total IP list | Yes |
| other_ips | [ string ] | second batch | No |
| version | string |  | Yes |

#### klsvo.HotUpdateTicketFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| gray_ips | [ string ] | gray_ips 可以用户传入，也可以通过计算得到 | No |
| gray_scale | integer | 如果gray_ips不是用户传入的， 那么就是必须填写的 | No |
| host | string |  | Yes |
| ips | [ string ] | must be the same ENV AZ | Yes |
| operation_description | string |  | Yes |
| operation_type | string | *Enum:* `"delete"`, `"update"`, `"add"` | Yes |
| other_ips | [ string ] | other_ips 通过计算得到 | No |

#### klsvo.HotUpdateTicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [klsvo.TicketData](#klsvoticketdata) |  | No |
| message | string |  | No |

#### klsvo.Monitors

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| kls_monitor | string |  | No |
| sc_monitor | string |  | No |
| total_bytes | string |  | No |
| total_connection | string |  | No |
| total_error | string |  | No |
| total_packet | string |  | No |
| vs_connection | string |  | No |

#### klsvo.NodeComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_metrics | string |  | No |
| nginx | string |  | No |
| sgw_agent | string |  | No |

#### klsvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_uuid | string |  | No |
| components | object |  | No |
| created_at | integer |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| function | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| kls_name | string |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| sdu | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### klsvo.NodeInfosResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [klsvo.NodeInfo](#klsvonodeinfo) ] } |  | No |
| message | string |  | No |

#### klsvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### klsvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### klsvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [klsvo.TicketData](#klsvoticketdata) |  | No |
| message | string |  | No |

#### lcsvo.ClusterAddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster | string |  | No |
| component | [lcsvo.NodeComponentVersion](#lcsvonodecomponentversion) |  | Yes |
| ips | [ string ] |  | Yes |
| uuid | string |  | Yes |

#### lcsvo.ClusterAddNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [lcsvo.ClusterAddNodeCBFormData](#lcsvoclusteraddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### lcsvo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [lcsvo.ClusterOverview](#lcsvoclusteroverview) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |

#### lcsvo.ClusterNodePreCheckResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"results"**: [ [sgwvo.NodeTaskResult](#sgwvonodetaskresult) ] } |  | No |
| message | string |  | No |

#### lcsvo.ClusterNodeTicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ], **"ticket"**: [swpvo.Ticket](#swpvoticket) } |  | No |
| message | string |  | No |

#### lcsvo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_cluster_domains | [albvo.ClusterDomains](#albvoclusterdomains) |  | No |
| allocable_status | string |  | No |
| az | string |  | No |
| bound_alb_only | boolean |  | No |
| cluster_domain | string |  | No |
| cluster_lan_ips | [ string ] |  | No |
| cluster_private_wan_ips | [ string ] |  | No |
| cluster_type | string | NONSTD,STD-4,STD-16 | No |
| cluster_wan_ips | [ string ] |  | No |
| combination | [albvo.Combination](#albvocombination) |  | No |
| cpu_count | integer |  | No |
| created_at | integer |  | No |
| deploy_type | string |  | No |
| ecp_detail | [albvo.ECPDetail](#albvoecpdetail) |  | No |
| env | string |  | No |
| etcd_endpoints | [ string ] |  | No |
| etcd_timeout | integer |  | No |
| external2_detail | [albvo.Ext2Detail](#albvoext2detail) |  | No |
| ha | string |  | No |
| ha_component | string |  | No |
| id | integer |  | No |
| instances | [ [albvo.Instance](#albvoinstance) ] |  | No |
| is_default | boolean |  | No |
| is_ecp | boolean |  | No |
| is_legacy | boolean |  | No |
| is_service | boolean |  | No |
| labels | [ [albvo.Label](#albvolabel) ] |  | No |
| lan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |
| listener_count | integer |  | No |
| managed | boolean |  | No |
| monitors | [albvo.Monitors](#albvomonitors) |  | No |
| name | string |  | No |
| network_env | string | physical vpc | No |
| network_type | string | WAN LAN | No |
| node_etcd | string | etcd url like etcd://etcd-1:2379,etcd-2:2379/mesos-etcd | No |
| node_zk | string | zk url like zk://zookeeper-1:2181,zookeeper-2:2181/mesos-zk | No |
| nodes | [ [sgwvo.Node](#sgwvonode) ] |  | No |
| ready_server_num | integer |  | No |
| region | string |  | No |
| remark | string |  | No |
| resource_type | string |  | No |
| rz | string |  | No |
| score | integer |  | No |
| sdu | string |  | No |
| segment | string |  | No |
| subsystem | string | alb,lcs,ext2 | No |
| target_vpc_name | string |  | No |
| total_active_connections | integer |  | No |
| total_bandwidth | integer |  | No |
| total_qps | integer |  | No |
| total_server_num | integer |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| used_bandwidth | integer |  | No |
| used_qps | integer |  | No |
| uuid | string |  | Yes |
| wan_ips | [ [albvo.ClusterIP](#albvoclusterip) ] |  | No |

#### lcsvo.ComponentListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"components"**: [ [cmp.Component](#cmpcomponent) ] } |  | No |
| message | string |  | No |

#### lcsvo.ComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| created | integer |  | No |
| version | string |  | No |

#### lcsvo.ComponentVersionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"versions"**: [ [lcsvo.ComponentVersion](#lcsvocomponentversion) ] } |  | No |
| message | string |  | No |

#### lcsvo.GetNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [lcsvo.NodeState](#lcsvonodestate) |  | No |
| message | string |  | No |

#### lcsvo.HotUpdateNodeBaseRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| business_type | string |  | Yes |
| gray_ips | [ string ] | first batch | Yes |
| ips | [ string ] | total IP list | Yes |
| other_ips | [ string ] | second batch | No |
| version | string |  | Yes |

#### lcsvo.HotUpdateTicketFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| gray_ips | [ string ] | gray_ips 可以用户传入，也可以通过计算得到 | No |
| gray_scale | integer | 如果gray_ips不是用户传入的， 那么就是必须填写的 | No |
| host | string |  | No |
| ips | [ string ] | must be the same ENV AZ | Yes |
| operation_description | string |  | Yes |
| operation_type | string | *Enum:* `"delete"`, `"update"`, `"add"` | Yes |
| other_ips | [ string ] | other_ips 通过计算得到 | No |

#### lcsvo.NodeComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_metrics | string |  | No |
| alb_sd | string |  | No |
| mesos_nginx_lb | string |  | No |
| nginx | string |  | No |
| sgw_agent | string |  | No |

#### lcsvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### lcsvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_uuid | string |  | No |
| components | object |  | No |
| created_at | integer |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| function | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| lcs_name | string |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| sdu | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### lcsvo.NodeInfosResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [lcsvo.NodeInfo](#lcsvonodeinfo) ] } |  | No |
| message | string |  | No |

#### lcsvo.NodeProvisionConfigResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) |  | No |
| message | string |  | No |

#### lcsvo.NodeProvisionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) ] |  | No |
| message | string |  | No |

#### lcsvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### lcsvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.LCSSpec](#v1alpha1lcsspec) ] |  | No |
| message | string |  | No |

#### lcsvo.NodeState

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_name | string |  | No |
| application | string |  | No |
| function | string |  | No |
| ip | string |  | No |
| sdu | string |  | No |
| state | string |  | No |
| status | string |  | No |
| updated_at | string |  | No |

#### lcsvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### lcsvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### lcsvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [lcsvo.TicketData](#lcsvoticketdata) |  | No |
| message | string |  | No |

#### metavo.MetaVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| metavo.MetaVar | object |  |  |

#### metavo.Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| cluster_id | string |  | No |
| cluster_name | string |  | No |
| component_name | string |  | No |
| create_at | string |  | No |
| env | string |  | No |
| id | string |  | No |
| idc | string |  | No |
| ip | string |  | No |
| ip_lan | string |  | No |
| metrics_port | integer |  | No |
| remarks | string |  | No |
| role | string |  | No |
| segment | string |  | No |
| state | string |  | No |
| status | string |  | No |
| toc_cluster | string |  | No |
| update_at | string |  | No |

#### middlewares.Error

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| message | string |  | No |
| traceback | string |  | No |
| type | [middlewares.ErrorType](#middlewareserrortype) |  | No |

#### middlewares.ErrorType

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| middlewares.ErrorType | string |  |  |

#### middlewares.Response

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| error |  |  | No |
| result |  |  | No |
| success | boolean |  | No |
| version | string |  | No |

#### monitor.MonitorPort

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| created_at | integer |  | No |
| creator | string |  | No |
| id | integer |  | No |
| identifier | string |  | No |
| metrics_path | string |  | No |
| metrics_scheme | string |  | No |
| modifier | string |  | No |
| scrape_interval | integer |  | No |
| service_id | integer |  | No |
| tags | [ [monitor.PortConfig](#monitorportconfig) ] |  | No |
| type | string |  | No |
| updated_at | integer |  | No |

#### monitor.PortConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| metrics_path | string |  | No |
| metrics_scheme | string |  | No |
| name | string |  | Yes |
| scrape_interval | integer |  | No |
| token | string |  | No |

#### natvo.ClusterAddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| add_node_param | [ [natvo.NATAddNodeParam](#natvonataddnodeparam) ] |  | No |
| cluster | string |  | No |
| component | [natvo.NodeComponentVersion](#natvonodecomponentversion) |  | No |
| ips | [ string ] |  | Yes |
| uuid | string |  | Yes |

#### natvo.ClusterAddNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [natvo.ClusterAddNodeCBFormData](#natvoclusteraddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### natvo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [natvo.ClusterOverview](#natvoclusteroverview) ] } |  | No |
| message | string |  | No |

#### natvo.ClusterNodePreCheckResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"results"**: [ [sgwvo.NodeTaskResult](#sgwvonodetaskresult) ] } |  | No |
| message | string |  | No |

#### natvo.ClusterNodeTicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ], **"ticket"**: [swpvo.Ticket](#swpvoticket) } |  | No |
| message | string |  | No |

#### natvo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| cluster_type | string |  | No |
| combination | [natvo.Combination](#natvocombination) |  | No |
| created_at | integer |  | No |
| env | string |  | No |
| gateway_ips | [ [natvo.GatewayIP](#natvogatewayip) ] |  | No |
| id | integer |  | No |
| name | string |  | No |
| network_env | string |  | No |
| network_type | string |  | No |
| nodes | [ [natvo.Node](#natvonode) ] |  | No |
| not_ready_server_num | integer |  | No |
| old_az | string |  | No |
| ready_server_num | integer |  | No |
| remark | string |  | No |
| require_bandwidth | integer |  | No |
| rz | string |  | No |
| sdu_name | string |  | No |
| segment | string |  | No |
| snat_ips | [ [natvo.SnatIP](#natvosnatip) ] |  | No |
| total_bandwidth | integer |  | No |
| total_server_num | integer |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| used_bandwidth | integer |  | No |
| uuid | string |  | No |

#### natvo.Combination

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| created_at | integer |  | No |
| env | string |  | No |
| id | integer |  | No |
| network_env | string |  | No |
| region | string |  | No |
| segment | string |  | No |
| service | string |  | No |
| standard_az | string |  | No |
| type | string |  | No |
| updated_at | integer |  | No |

#### natvo.ComponentListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"components"**: [ [cmp.Component](#cmpcomponent) ] } |  | No |
| message | string |  | No |

#### natvo.ComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| created | integer |  | No |
| version | string |  | No |

#### natvo.ComponentVersionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"versions"**: [ [natvo.ComponentVersion](#natvocomponentversion) ] } |  | No |
| message | string |  | No |

#### natvo.FetchSwitchParamRsp

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| message | string |  | No |
| node_param_list | [ [natvo.NATAddNodeParam](#natvonataddnodeparam) ] |  | No |
| peak_version | [natvo.NodeComponentVersion](#natvonodecomponentversion) |  | No |
| version_list | [natvo.SNATVersion](#natvosnatversion) |  | No |

#### natvo.GatewayIP

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_uuid | string |  | No |
| created_at | integer |  | No |
| id | integer |  | No |
| instance_id | string |  | No |
| ip | string |  | No |
| updated_at | integer |  | No |

#### natvo.HotUpdateNodeRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| business_type | string |  | Yes |
| gray_ips | [ string ] | first batch | Yes |
| ips | [ string ] | total IP list | Yes |
| operation_type | string | *Enum:* `"update"`, `"delete"`, `"add"` | Yes |
| other_ips | [ string ] | second batch | No |
| post_percentage | integer |  | No |
| rollback_config | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |  | Yes |
| target_config | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) |  | Yes |
| version | string |  | Yes |
| wait_for_bird_stopped | integer |  | No |

#### natvo.NATAddNodeParam

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| bond_env | string |  | No |
| cluster_type | string |  | No |
| config | [v1alpha1.MgmtNodeConfig](#v1alpha1mgmtnodeconfig) |  | No |
| ip | string |  | No |
| nic_keywords | string |  | No |

#### natvo.Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| TCPTimeout | integer |  | No |
| UDPTimeout | number |  | No |
| cluster_uuid | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| node_config | [natvo.NodeConfig](#natvonodeconfig) |  | No |
| node_status | string |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### natvo.NodeComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| dpvs | string |  | No |
| dpvs_metrics_snat | string |  | No |
| flow_log_agent | string |  | No |
| snat_agent | string |  | No |

#### natvo.NodeConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| check_interval | number |  | No |
| ctrl_poll_interval | integer |  | No |
| expected_lip_count | integer |  | No |
| lan_inner_ip | string |  | No |
| lan_local_as | integer |  | No |
| lan_mask | integer |  | No |
| lan_nic | string |  | No |
| lan_switch_as | integer |  | No |
| lan_switch_ip | string |  | No |
| node_status | string |  | No |
| rack | string |  | No |
| status_change_limit | integer |  | No |
| sync_session_dpdk_dst_ips | [ string ] |  | No |
| sync_session_dpdk_src_ip | string |  | No |
| sync_session_dst_ips | [ string ] |  | No |
| sync_session_src_ip | string |  | No |
| tcp_timeout | number |  | No |
| udp_timeout | number |  | No |
| wan_inner_ip | string |  | No |
| wan_local_as | integer |  | No |
| wan_mask | integer |  | No |
| wan_nic | string |  | No |
| wan_switch_as | integer |  | No |
| wan_switch_ip | string |  | No |

#### natvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### natvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| TCPTimeout | integer |  | No |
| UDPTimeout | number |  | No |
| application | string |  | No |
| cluster_uuid | string |  | No |
| components | object |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| function | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| lan_switches | [ [ndmpvo.Device](#ndmpvodevice) ] |  | No |
| links | [ [tocvo.Hardware](#tocvohardware) ] |  | No |
| nat_name | string |  | No |
| node_config | [natvo.NodeConfig](#natvonodeconfig) |  | No |
| node_status | string |  | No |
| sdu | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| wan_switches | [ [ndmpvo.Device](#ndmpvodevice) ] |  | No |

#### natvo.NodeInfosResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [natvo.NodeInfo](#natvonodeinfo) ] } |  | No |
| message | string |  | No |

#### natvo.NodeProvisionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) ] |  | No |
| message | string |  | No |

#### natvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### natvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.NATSpec](#v1alpha1natspec) ] |  | No |
| message | string |  | No |

#### natvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### natvo.SNATVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| dpvs_metrics_snat_version | [ string ] |  | No |
| dpvs_version | [ string ] |  | No |
| flow_log_agent_version | [ string ] |  | No |
| snat_agent_version | [ string ] |  | No |

#### natvo.SnatIP

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| allocation | string |  | No |
| cluster_uuid | string |  | No |
| created_at | integer |  | No |
| created_by | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| master_node | string |  | No |
| updated_at | integer |  | No |

#### natvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### natvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [natvo.TicketData](#natvoticketdata) |  | No |
| message | string |  | No |

#### ndmpvo.Device

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| area | string |  | No |
| asn | integer |  | No |
| az | string |  | No |
| business | string |  | No |
| created_at | string |  | No |
| deleted | integer |  | No |
| deleted_at | string |  | No |
| hall | string |  | No |
| hostname | string |  | No |
| id | integer |  | No |
| idc | string |  | No |
| idc_group | string |  | No |
| is_mlag | boolean |  | No |
| label | { **"cmdb_sdus"**: [ string ], **"cmdb_services"**: [ string ] } |  | No |
| local_asn | integer |  | No |
| location | string |  | No |
| management_ip | string |  | No |
| manufacture | string |  | No |
| mlag_peer | string |  | No |
| model | string |  | No |
| patch | string |  | No |
| platform | string |  | No |
| pod | string |  | No |
| rack | string |  | No |
| role | string |  | No |
| segment | string |  | No |
| service | string |  | No |
| sn | string |  | No |
| status | string |  | No |
| subnet | [ { **"NETWORK"**: string } ] |  | No |
| system_description | string |  | No |
| system_info | { **"HOSTNAME"**: string, **"MGMT_INTF"**: string, **"MGMT_IPADDR"**: string, **"MODEL"**: string, **"PRODUCT_VERSION"**: string, **"SERIAL_NUMBER"**: string, **"SOFTWARE"**: string, **"UPTIME"**: string, **"VRP_VERSION"**: string } |  | No |
| updated_at | string |  | No |
| vdom | string |  | No |
| version | string |  | No |

#### ndmpvo.Switch

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| area | string |  | No |
| asn | integer |  | No |
| az | string |  | No |
| business | string |  | No |
| created_at | string |  | No |
| deleted | integer |  | No |
| deleted_at | string |  | No |
| hall | string |  | No |
| hostname | string |  | No |
| id | integer |  | No |
| idc | string |  | No |
| idc_group | string |  | No |
| is_mlag | boolean |  | No |
| label | { **"cmdb_sdus"**: [ string ], **"cmdb_services"**: [ string ] } |  | No |
| local_asn | integer |  | No |
| location | string |  | No |
| management_ip | string |  | No |
| manufacture | string |  | No |
| mlag_peer | string |  | No |
| model | string |  | No |
| patch | string |  | No |
| physical_ip | string |  | No |
| platform | string |  | No |
| pod | string |  | No |
| rack | string |  | No |
| role | string |  | No |
| segment | string |  | No |
| service | string |  | No |
| sn | string |  | No |
| status | string |  | No |
| subnet | [ { **"NETWORK"**: string } ] |  | No |
| system_description | string |  | No |
| system_info | { **"HOSTNAME"**: string, **"MGMT_INTF"**: string, **"MGMT_IPADDR"**: string, **"MODEL"**: string, **"PRODUCT_VERSION"**: string, **"SERIAL_NUMBER"**: string, **"SOFTWARE"**: string, **"UPTIME"**: string, **"VRP_VERSION"**: string } |  | No |
| updated_at | string |  | No |
| vdom | string |  | No |
| version | string |  | No |

#### nlbvo.Allocation

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_uuid | string |  | No |
| created_at | integer |  | No |
| created_by | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| nlb | string |  | No |
| port | integer |  | No |
| protocol | string |  | No |
| updated_at | integer |  | No |
| vip_type | string |  | No |
| vni | integer |  | No |
| vpc_name | string |  | No |

#### nlbvo.ClusterAddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| add_node_param | [ [nlbvo.NLBAddNodeParam](#nlbvonlbaddnodeparam) ] |  | No |
| cluster | string |  | No |
| component | [nlbvo.NodeComponentVersion](#nlbvonodecomponentversion) |  | Yes |
| ips | [ string ] |  | Yes |
| uuid | string |  | Yes |

#### nlbvo.ClusterAddNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [nlbvo.ClusterAddNodeCBFormData](#nlbvoclusteraddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### nlbvo.ClusterBrief

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| id | integer |  | No |
| idc | string |  | No |
| name | string |  | No |
| network_type | string |  | No |
| remark | string |  | No |
| resource_type | string |  | No |
| type | string |  | No |
| uuid | string |  | No |
| vip_ranges | [ [nlbvo.VIPRange](#nlbvoviprange) ] |  | No |

#### nlbvo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [nlbvo.ClusterOverview](#nlbvoclusteroverview) ] } |  | No |
| message | string |  | No |

#### nlbvo.ClusterNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| add_node_param | [ [nlbvo.NLBAddNodeParam](#nlbvonlbaddnodeparam) ] |  | No |
| cluster | string |  | No |
| ips | [ string ] |  | Yes |
| uuid | string |  | Yes |

#### nlbvo.ClusterNodePreCheckResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"results"**: [ [sgwvo.NodeTaskResult](#sgwvonodetaskresult) ] } |  | No |
| message | string |  | No |

#### nlbvo.ClusterNodeTicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ], **"ticket"**: [swpvo.Ticket](#swpvoticket) } |  | No |
| message | string |  | No |

#### nlbvo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| allocations | [ [nlbvo.Allocation](#nlbvoallocation) ] |  | No |
| az | string |  | No |
| bound_alb_only | boolean |  | No |
| campaign_used_bandwidth | integer |  | No |
| cluster_bandwidth | integer |  | No |
| cluster_type | string |  | No |
| combination | [nlbvo.Combination](#nlbvocombination) |  | No |
| combination_id | integer |  | No |
| cpu_count | integer |  | No |
| created_at | integer |  | No |
| default_vips | [ [nlbvo.VIP](#nlbvovip) ] |  | No |
| enable_instance_qos | boolean |  | No |
| enable_qos_drop | boolean |  | No |
| env | string |  | No |
| id | integer |  | No |
| is_available | boolean |  | No |
| labels | [ [nlbvo.Label](#nlbvolabel) ] |  | No |
| lan_used_cluster_bandwidth | integer |  | No |
| monitors | [nlbvo.Monitor](#nlbvomonitor) |  | No |
| name | string |  | No |
| network_type | string |  | No |
| nic_qos_percentage | integer |  | No |
| node_bandwidth | integer |  | No |
| nodes | [ [nlbvo.Node](#nlbvonode) ] |  | No |
| ready_server_num | integer |  | No |
| remark | string |  | No |
| require_cluster_bandwidth | integer |  | No |
| require_lan_cluster_bandwidth | integer |  | No |
| require_wan_cluster_bandwidth | integer |  | No |
| resource_type | string |  | No |
| rest_cluster_bandwidth | integer |  | No |
| rz | string |  | No |
| sdu_name | string |  | No |
| segment | string |  | No |
| total_server_num | integer |  | No |
| traffic_type | string |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| used_cluster_bandwidth | integer |  | No |
| uuid | string |  | No |
| vips | [ [nlbvo.VIP](#nlbvovip) ] |  | No |
| wan_used_cluster_bandwidth | integer |  | No |

#### nlbvo.ClusterRemoveNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [nlbvo.ClusterNodeCBFormData](#nlbvoclusternodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### nlbvo.Combination

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| created_at | integer |  | No |
| env | string |  | No |
| id | integer |  | No |
| network_env | string |  | No |
| region | string |  | No |
| segment | string |  | No |
| service | string |  | No |
| standard_az | string |  | No |
| type | string |  | No |
| updated_at | integer |  | No |

#### nlbvo.ComponentListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"components"**: [ [cmp.Component](#cmpcomponent) ] } |  | No |
| message | string |  | No |

#### nlbvo.ComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| created | integer |  | No |
| version | string |  | No |

#### nlbvo.ComponentVersionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"versions"**: [ [nlbvo.ComponentVersion](#nlbvocomponentversion) ] } |  | No |
| message | string |  | No |

#### nlbvo.DomainBinding

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| bound_by | string |  | No |
| created_at | integer |  | No |
| dns_type | string |  | No |
| domain | string |  | No |
| id | integer |  | No |
| ticket | string |  | No |
| updated_at | integer |  | No |
| vip | string |  | No |

#### nlbvo.FetchSwitchParamRsp

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| message | string |  | No |
| node_param_list | [ [nlbvo.NLBAddNodeParam](#nlbvonlbaddnodeparam) ] |  | No |
| peak_version | [nlbvo.NodeComponentVersion](#nlbvonodecomponentversion) |  | No |
| version_list | [nlbvo.NLBVersionList](#nlbvonlbversionlist) |  | No |

#### nlbvo.HotUpdateNodeRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| business_type | string |  | Yes |
| check_mod | string |  | No |
| gray_ips | [ string ] | first batch | Yes |
| ips | [ string ] | total IP list | Yes |
| operation_type | string | *Enum:* `"update"`, `"delete"`, `"add"` | Yes |
| other_ips | [ string ] | second batch | No |
| post_percentage | integer |  | No |
| rollback_config | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) | mapping to SWP template JSON schema | Yes |
| target_config | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) | swpvo.HotUpdateSGWL7NodeTicketFormData | Yes |
| version | string |  | Yes |
| wait_for_bird_stopped | integer |  | No |

#### nlbvo.LIPBinding

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| inner_lips | [ string ] |  | No |
| master_node_ip | string |  | No |
| outer_lip | string |  | No |

#### nlbvo.Label

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_uuid | string |  | No |
| color | string |  | No |
| created_at | integer |  | No |
| id | integer |  | No |
| name | string |  | No |
| remark | string |  | No |
| updated_at | integer |  | No |

#### nlbvo.ListenerTarget

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cid | string |  | No |
| cluster | [nlbvo.ClusterBrief](#nlbvoclusterbrief) |  | No |
| env | string |  | No |
| forward_to_alb | boolean |  | No |
| id | integer |  | No |
| idc | string |  | No |
| is_locked | boolean |  | No |
| listener_type | string |  | No |
| lock_ticket | string |  | No |
| port | integer |  | No |
| protocol | string |  | No |
| remark | string |  | No |
| segment | string |  | No |
| standard_az | string |  | No |
| target_port | integer |  | No |
| target_type | string |  | No |
| uoa_trail | integer |  | No |
| vips | [ string ] |  | No |
| weight | integer |  | No |

#### nlbvo.Monitor

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| alb_monitor | string |  | No |
| total_bytes | string |  | No |
| total_connection | string |  | No |
| total_error | string |  | No |
| total_missed | string |  | No |
| total_packet | string |  | No |
| vs_connection | string |  | No |

#### nlbvo.NLBAddNodeParam

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| bond_env | string |  | No |
| check_mod | string |  | No |
| cid | string |  | No |
| cluster_type | string |  | No |
| config | [v1alpha1.MgmtNodeConfig](#v1alpha1mgmtnodeconfig) |  | No |
| ip | string |  | No |
| nic_keywords | string |  | No |

#### nlbvo.NLBVersionList

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| dpvs | [ string ] |  | No |
| dpvsMetrics | [ string ] |  | No |
| flowLogAgent | [ string ] |  | No |
| healthCheck | [ string ] |  | No |
| nlbSd | [ string ] |  | No |
| shopeeMesosDpvsFullnat | [ string ] |  | No |

#### nlbvo.Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| TCPTimeout | integer |  | No |
| UDPTimeout | number |  | No |
| check_interval | number |  | No |
| cluster_uuid | string |  | No |
| created_at | integer |  | No |
| ctrl_poll_interval | integer |  | No |
| expected_lip_count | integer |  | No |
| id | integer |  | No |
| ip | string |  | No |
| lan_inner_ip | string |  | No |
| lan_local_as | integer |  | No |
| lan_mask | integer |  | No |
| lan_nic | string |  | No |
| lan_switch_as | integer |  | No |
| lan_switch_ip | string |  | No |
| lips | string |  | No |
| node_config | [nlbvo.NodeConfig](#nlbvonodeconfig) |  | No |
| node_status | string |  | No |
| rack | string |  | No |
| status_change_limit | integer |  | No |
| tcp_timeout | number |  | No |
| udp_timeout | number |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| wan_inner_ip | string |  | No |
| wan_local_as | integer |  | No |
| wan_mask | integer |  | No |
| wan_nic | string |  | No |
| wan_switch_as | integer |  | No |
| wan_switch_ip | string |  | No |

#### nlbvo.NodeComponentVersion

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| dpvs | string |  | No |
| dpvs_fullnat | string |  | No |
| dpvs_metrics | string |  | No |
| flow_log_agent | string |  | No |
| health_check | string |  | No |
| nlb_sd | string |  | No |

#### nlbvo.NodeConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| check_interval | number |  | No |
| ctrl_poll_interval | integer |  | No |
| expected_lip_count | integer |  | No |
| lan_inner_ip | string |  | No |
| lan_local_as | integer |  | No |
| lan_mask | integer |  | No |
| lan_nic | string |  | No |
| lan_switch_as | integer |  | No |
| lan_switch_ip | string |  | No |
| rack | string |  | No |
| status_change_limit | integer |  | No |
| tcp_timeout | integer |  | No |
| udp_timeout | number |  | No |
| wan_inner_ip | string |  | No |
| wan_local_as | integer |  | No |
| wan_mask | integer |  | No |
| wan_nic | string |  | No |
| wan_switch_as | integer |  | No |
| wan_switch_ip | string |  | No |

#### nlbvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### nlbvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| TCPTimeout | integer |  | No |
| UDPTimeout | number |  | No |
| application | string |  | No |
| check_interval | number |  | No |
| cluster_uuid | string |  | No |
| components | object |  | No |
| created_at | integer |  | No |
| ctrl_poll_interval | integer |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| expected_lip_count | integer |  | No |
| function | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| lan_inner_ip | string |  | No |
| lan_local_as | integer |  | No |
| lan_mask | integer |  | No |
| lan_nic | string |  | No |
| lan_switch_as | integer |  | No |
| lan_switch_ip | string |  | No |
| lan_switches | [ [ndmpvo.Device](#ndmpvodevice) ] |  | No |
| links | [ [tocvo.Hardware](#tocvohardware) ] |  | No |
| lips | string |  | No |
| nlb_name | string |  | No |
| node_config | [nlbvo.NodeConfig](#nlbvonodeconfig) |  | No |
| node_status | string |  | No |
| rack | string |  | No |
| sdu | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| status_change_limit | integer |  | No |
| tcp_timeout | number |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| udp_timeout | number |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| wan_inner_ip | string |  | No |
| wan_local_as | integer |  | No |
| wan_mask | integer |  | No |
| wan_nic | string |  | No |
| wan_switch_as | integer |  | No |
| wan_switch_ip | string |  | No |
| wan_switches | [ [ndmpvo.Device](#ndmpvodevice) ] |  | No |

#### nlbvo.NodeInfosResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [nlbvo.NodeInfo](#nlbvonodeinfo) ] } |  | No |
| message | string |  | No |

#### nlbvo.NodeProvisionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) ] |  | No |
| message | string |  | No |

#### nlbvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### nlbvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.NLBSpec](#v1alpha1nlbspec) ] |  | No |
| message | string |  | No |

#### nlbvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### nlbvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### nlbvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [nlbvo.TicketData](#nlbvoticketdata) |  | No |
| message | string |  | No |

#### nlbvo.VIP

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| created_at | integer |  | No |
| domain_bindings | [ [nlbvo.DomainBinding](#nlbvodomainbinding) ] |  | No |
| id | integer |  | No |
| ip | string |  | No |
| is_default_vip | boolean |  | No |
| lip_bindings | [ [nlbvo.LIPBinding](#nlbvolipbinding) ] |  | No |
| type | string |  | No |
| updated_at | integer |  | No |
| usage | string | NORMAL==UNICAST, ANYCAST | No |

#### nlbvo.VIPRange

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_id | integer |  | No |
| id | integer |  | No |
| ip_range_end | string |  | No |
| ip_range_start | string |  | No |
| network_type | string |  | No |
| provider | string |  | No |

#### openvo.AnchorClusterSubnetResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"domain"**: string, **"subnets"**: [ [anchorvo.Subnet](#anchorvosubnet) ] } |  | No |
| message | string |  | No |

#### openvo.Ext2ClusterInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| cluster | string |  | No |
| domain | string |  | No |
| env | string |  | No |
| rz | string |  | No |
| segment | string |  | No |

#### openvo.NodeBrief

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| ip | string |  | No |
| port | integer |  | No |

#### openvo.ZKClusterInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster | string |  | No |
| env | string |  | No |
| nodes | [ [openvo.NodeBrief](#openvonodebrief) ] |  | No |
| rz | string |  | No |
| status | string |  | No |

#### opsent.DREvent

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| action | string |  | No |
| az_selections | string |  | No |
| controller_type | string |  | No |
| created_at | integer |  | No |
| deleted_at | [gorm.DeletedAt](#gormdeletedat) |  | No |
| details | [ integer ] |  | No |
| dry_run | boolean |  | No |
| failed_url | string |  | No |
| hall_selections | string |  | No |
| id | integer |  | No |
| idempotency_token | string |  | No |
| is_delete | boolean |  | No |
| physical_idc_selections | string |  | No |
| rz_selections | string |  | No |
| server_list | [ integer ] |  | No |
| success_url | string |  | No |
| updated_at | integer |  | No |

#### opsent.DREventLog

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| controller_type | string |  | No |
| created_at | integer |  | No |
| deleted_at | [gorm.DeletedAt](#gormdeletedat) |  | No |
| dry_run | boolean |  | No |
| event_type | string |  | No |
| id | integer |  | No |
| idempotency_token | string |  | No |
| message | string |  | No |
| reason | string |  | No |
| state | string |  | No |
| updated_at | integer |  | No |

#### opsent.EventLog

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| business_type | string |  | No |
| created_at | integer |  | No |
| deleted_at | [gorm.DeletedAt](#gormdeletedat) |  | No |
| event_type | string |  | No |
| id | integer |  | No |
| message | string |  | No |
| node_name | string |  | No |
| operate_user | string |  | No |
| reason | string |  | No |
| state | string |  | No |
| ticket_id | string |  | No |
| updated_at | integer |  | No |

#### reviewer_defs.Reviewer

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| email | string |  | No |
| operated | boolean |  | No |
| role | string |  | No |

#### sgwvo.ALBClusterConfigMeta

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | Yes |
| cluster_base_domain | string |  | No |
| ecmp_bgp_local_as | integer | default 65000 | No |
| ecmp_bgp_peer_as_mapping | object |  | Yes |
| ecmp_bgp_vips | [ string ] |  | No |
| ecmp_bgp_wan_vips | [ string ] |  | No |
| env | string |  | Yes |
| ha_instances | [ [sgwvo.HAInstance](#sgwvohainstance) ] |  | Yes |
| ha_wan_vips | [ string ] |  | No |
| idc_code | string |  | No |
| network_type | string | *Enum:* `"LAN"`, `"WAN"`, `"Mix"` | Yes |
| rz | string |  | Yes |
| segment | string |  | Yes |
| segment_code | string |  | No |

#### sgwvo.Cert

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| common_name | string |  | No |
| name | string |  | No |
| organization_name | string |  | No |

#### sgwvo.DetailInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| message | string |  | No |
| progress | integer |  | No |
| reason | string |  | No |
| tasks | [ [sgwvo.TaskResult](#sgwvotaskresult) ] |  | No |

#### sgwvo.ETCDClusterConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| etcd_cert_dir | string |  | No |
| etcd_cert_roots | [ [sgwvo.Cert](#sgwvocert) ] |  | No |
| etcd_certs | [ [sgwvo.Cert](#sgwvocert) ] |  | No |
| etcd_client_port | integer |  | Yes |
| etcd_cluster_name | string |  | Yes |
| etcd_compaction_period | string |  | Yes |
| etcd_data_dir | string |  | Yes |
| etcd_hosts | object |  | Yes |
| etcd_listen_metrics | string |  | No |
| etcd_max_request_bytes | string |  | No |
| etcd_peer_port | integer |  | Yes |
| etcd_quota | string |  | Yes |
| etcd_tls | boolean |  | No |
| old_cluster_name | string |  | No |

#### sgwvo.ETCDClusterMetaData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_url | string |  | Yes |

#### sgwvo.HAInstance

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| instances | [ [sgwvo.KeepalivedInstance](#sgwvokeepalivedinstance) ] |  | Yes |
| node | string | IP | Yes |

#### sgwvo.KeepalivedInstance

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| interface | string |  | Yes |
| name | string | must be unique in one HAInstance | Yes |
| priority | integer |  | No |
| state | string | *Enum:* `"MASTER"`, `"BACKUP"` | No |
| vip | string |  | Yes |
| virtual_router_id | integer |  | No |
| without_auth | boolean |  | No |

#### sgwvo.L7ClusterConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_base_domain | string |  | No |
| ecmp_bgp_local_as | integer | default 65000 | No |
| ecmp_bgp_peer_as_mapping | object |  | Yes |
| ecmp_bgp_vips | [ string ] |  | No |
| ecmp_bgp_wan_vips | [ string ] |  | No |
| ha_instances | [ [sgwvo.HAInstance](#sgwvohainstance) ] |  | Yes |
| ha_wan_vips | [ string ] |  | No |

#### sgwvo.Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_uuid | string |  | No |
| created_at | integer |  | No |
| id | integer |  | No |
| ip | string |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### sgwvo.NodeTaskResult

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| completed_at | integer |  | No |
| current_task | string | Task being executed when timeout occurred | No |
| ip | string |  | No |
| passed | boolean |  | No |
| started_at | integer |  | No |
| tasks | [ [sgwvo.TaskResult](#sgwvotaskresult) ] |  | No |
| timed_out | boolean |  | No |
| total_duration_seconds | number |  | No |

#### sgwvo.StateNodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| az | string |  | No |
| cluster_uuid | string |  | No |
| node_ip | string |  | No |
| operation | string |  | No |
| reason | string |  | No |
| rz | string |  | No |
| segment | string |  | No |
| state | string |  | No |
| ticket_id | integer |  | No |
| toc_state | string |  | No |

#### sgwvo.StateNodesResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| message | string |  | No |
| nodes | [ [sgwvo.StateNodeInfo](#sgwvostatenodeinfo) ] |  | No |

#### sgwvo.TaskResult

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| duration_seconds | number | Duration in seconds | No |
| reason | string |  | No |
| started_at | integer |  | No |
| success | boolean |  | No |
| task | string |  | No |
| updated_at | integer |  | No |

#### swpvo.Ticket

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| id | integer |  | No |
| name | string |  | No |
| template_id | integer |  | No |
| template_name | string |  | No |
| title | string |  | No |

#### swpvo.TicketGetResp

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"ticket"**: [defs.GetResp](#defsgetresp) } |  | No |
| message | string |  | No |

#### ticket_defs.Ticket

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | No |
| assignee_team_id | string |  | No |
| assignee_user | string |  | No |
| created_at | integer |  | No |
| creator | string |  | No |
| eta | integer |  | No |
| form_data | object |  | No |
| id | integer |  | No |
| is_underway | boolean |  | No |
| json_schema | string |  | No |
| label | [ticket_defs.TicketLabel](#ticket_defsticketlabel) |  | No |
| name | string | SWP-1 SGW-10 | No |
| phase | string |  | No |
| phase_id | integer |  | No |
| phase_type | string |  | No |
| renderer_type | string |  | No |
| reviewers | [ string ] | simplified field for backward compatible | No |
| reviewers_with_role | [ [reviewer_defs.Reviewer](#reviewer_defsreviewer) ] |  | No |
| template_id | integer |  | No |
| title | string |  | No |
| ui_schema | string |  | No |
| updated_at | integer |  | No |
| watchers | [ string ] |  | No |
| workflow_id | integer |  | No |

#### ticket_defs.TicketLabel

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| be_data | object |  | No |
| category | string |  | No |
| extra_data | object |  | No |
| fe_data | object |  | No |
| name | string |  | No |
| template_name | string |  | No |
| ticket_type | string |  | No |

#### toclib.Platform

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| id | integer |  | No |
| owners | [ string ] |  | No |
| platform_name | string |  | No |

#### toclib.Product

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| description | string |  | No |
| id | integer |  | No |
| name | string |  | No |

#### toclib.ProvisionNodeComponent

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| dependent_components | [ string ] |  | No |
| dependent_templates | [ string ] |  | No |
| disable_restart_service | boolean |  | No |
| disabled | boolean |  | No |
| ensure_service_inactive | boolean |  | No |
| err_msg | string |  | No |
| healthycheck_command | string |  | No |
| is_service | boolean |  | No |
| name | string |  | Yes |
| post_command | string |  | No |
| pre_command | string |  | No |
| remove_package_on_disabled | boolean |  | No |
| remove_paths_on_delete | [ string ] |  | No |
| script | string |  | No |
| service_args | [ string ] |  | No |
| service_name | string |  | No |
| service_options | [ string ] |  | No |
| stop_service_on_delete | boolean |  | No |
| type | string |  | Yes |
| version | string |  | Yes |

#### toclib.ProvisionNodeConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| components | [ [toclib.ProvisionNodeComponent](#toclibprovisionnodecomponent) ] |  | No |
| events | [ [toclib.ProvisionNodeEvent](#toclibprovisionnodeevent) ] |  | No |
| host_ip | string |  | Yes |
| templates | [ [toclib.ProvisionNodeTemplate](#toclibprovisionnodetemplate) ] |  | No |

#### toclib.ProvisionNodeEvent

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| message | string |  | Yes |
| reason | string |  | Yes |
| type | string |  | Yes |

#### toclib.ProvisionNodeTemplate

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| auto_create | boolean |  | No |
| auto_delete | boolean |  | No |
| block | string |  | No |
| block_comment | string |  | No |
| content | string |  | Yes |
| disabled | boolean |  | No |
| err_msg | string |  | No |
| group | string |  | No |
| owner | string |  | No |
| path | string |  | Yes |
| permission | integer |  | Yes |
| post_command | string |  | No |
| pre_command | string |  | No |

#### toclib.ServerBusiness

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| business | string |  | No |
| id | integer |  | No |
| server_id | integer |  | No |

#### toclib.ServerResourceNodes

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| bind_type | string |  | No |
| env | string |  | No |
| id | integer |  | No |
| node_name | string |  | No |
| node_template | string |  | No |
| node_template_path | string |  | No |
| node_type | string |  | No |
| server_id | integer |  | No |

#### toclib.ServerV3

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| businesses | [ [toclib.ServerBusiness](#toclibserverbusiness) ] |  | No |
| cluster | string |  | No |
| cpu_count | integer |  | No |
| cpu_model | string |  | No |
| hall | string |  | No |
| id | integer |  | No |
| idc | string |  | No |
| ip_lan | string |  | No |
| ip_lan_list | [ string ] |  | No |
| ip_wan | string |  | No |
| kernel | string |  | No |
| name | string |  | No |
| os | string |  | No |
| platform | [toclib.Platform](#toclibplatform) |  | No |
| power_status | string |  | No |
| product | [toclib.Product](#toclibproduct) |  | No |
| rack | string |  | No |
| resource_nodes | [ [toclib.ServerResourceNodes](#toclibserverresourcenodes) ] |  | No |
| segment | string |  | No |
| server_config | string |  | No |
| service_tag | string |  | No |
| services | [ [toclib.Service](#toclibservice) ] |  | No |
| state | string |  | No |
| tags | [ [toclib.Tag](#toclibtag) ] |  | No |
| uuid | string |  | No |

#### toclib.Service

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| id | integer |  | No |
| server_id | integer |  | No |
| service | string |  | No |
| service_id | integer |  | No |
| service_origin | string |  | No |

#### toclib.Tag

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| tag_category_key | string |  | No |
| value | string |  | No |

#### tocvo.ALBGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | Yes |
| cluster_store | string | for docker.json | No |
| ecmp_bgp_vips | [ string ] |  | No |
| ecmp_bgp_wan_vips | [ string ] |  | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| func_main | string |  | Yes |
| func_sub | string |  | Yes |
| http_proxy | string |  | No |
| https_proxy | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| iptable_rules | [ string ] |  | No |
| keepalived_instances | object |  | Yes |
| keepalived_wan_vips | [ string ] |  | No |
| registry_mirrors | string |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |

#### tocvo.AnchorGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| func_main | string |  | No |
| func_sub | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |

#### tocvo.DNSGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | Yes |
| bird_version | string |  | No |
| calico_bgp_peer_as_mapping | object |  | No |
| dns_agent | { **"space_api"**: { **"dump_info_url"**: string, **"get_by_ids_url"**: string } } |  | No |
| dns_agent_version | string |  | No |
| dns_bgp_remote_as | object |  | No |
| dns_ecmp_vips | [ string ] |  | No |
| dpdkdns_kernel_version | string |  | No |
| dpdkdns_version | string |  | No |
| ecmp_bgp_local_as | integer | default 65000 | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| forward_addr | string |  | No |
| forward_zones | [ [tocvo.ForwardZone](#tocvoforwardzone) ] |  | No |
| high_availability_mode | string | *Enum:* `"Std1"`, `"Std2"`, `"UnSpec"` | No |
| hosts | [ [tocvo.Host](#tocvohost) ] |  | No |
| http_proxy | string |  | No |
| https_proxy | string |  | No |
| idc | string |  | Yes |
| iptables | [ string ] |  | Yes |
| keepalived_interface | object |  | No |
| keepalived_vrrp | object |  | No |
| lan_switch_ip | object |  | No |
| port | integer |  | Yes |
| rdns_version | string |  | No |
| sdu | string |  | Yes |
| segment_code | string |  | No |
| segment_name | string |  | No |
| substitutions | [ [tocvo.Substitution](#tocvosubstitution) ] |  | No |

#### tocvo.EGWGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| func_main | string |  | No |
| func_sub | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |

#### tocvo.ETCDGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | Yes |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| func_main | string |  | Yes |
| func_sub | string |  | Yes |
| http_proxy | string |  | No |
| https_proxy | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| iptable_rules | [ string ] |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |

#### tocvo.Ext2GroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | Yes |
| calico_bgp_peer_as_mapping | object |  | Yes |
| cluster_store | string | for docker.json | No |
| ecmp_bgp_local_as | integer | default 65000 | No |
| ecmp_bgp_vips | [ string ] |  | No |
| ecmp_bgp_wan_vips | [ string ] |  | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| func_main | string |  | Yes |
| func_sub | string |  | Yes |
| http_proxy | string |  | No |
| https_proxy | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| iptable_rules | [ string ] |  | No |
| keepalived_instances | object |  | Yes |
| keepalived_wan_vips | [ string ] |  | No |
| registry_mirrors | string |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |

#### tocvo.ForwardZone

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| addrs | [ string ] |  | Yes |
| name | string |  | Yes |

#### tocvo.Hardware

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| capabilities | [ integer ] |  | No |
| capacity | integer |  | No |
| category | string |  | No |
| claimed | boolean |  | No |
| class | string |  | No |
| clock | integer |  | No |
| configuration | [ integer ] |  | No |
| description | string |  | No |
| handle | string |  | No |
| id | string |  | No |
| logicalname | string |  | No |
| product | string |  | No |
| serial | string |  | No |
| server_id | integer |  | No |
| service_tag | string |  | No |
| size | integer |  | No |
| slot | string |  | No |
| units | string |  | No |
| updated_at | integer |  | No |
| vendor | string |  | No |
| version | string |  | No |
| width | integer |  | No |

#### tocvo.Host

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| hostname | string |  | No |
| ip | string |  | No |

#### tocvo.KeepalivedInstance

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| interface | string |  | Yes |
| priority | integer |  | No |
| state | string | *Enum:* `"MASTER"`, `"BACKUP"` | No |
| vip | string |  | Yes |
| virtual_router_id | integer |  | No |
| without_auth | boolean |  | No |

#### tocvo.LCSGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | Yes |
| calico_bgp_peer_as_mapping | object |  | No |
| cluster_base_domain | string |  | No |
| cluster_store | string | for docker.json | No |
| ecmp_bgp_local_as | integer | default 65000 | No |
| ecmp_bgp_private_wan_vips | [ string ] |  | No |
| ecmp_bgp_vips | [ string ] |  | No |
| ecmp_bgp_wan_vips | [ string ] |  | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| etcd_proxy | string |  | No |
| func_main | string |  | Yes |
| func_sub | string |  | Yes |
| global | string |  | No |
| http_proxy | string |  | No |
| https_proxy | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| iptable_rules | [ string ] |  | No |
| keepalived_instances | object |  | Yes |
| keepliaved_private_wanvips | [ string ] |  | No |
| keepliaved_wanvips | [ string ] |  | No |
| registry_mirrors | string |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |

#### tocvo.NATGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | Yes |
| cluster_store | string | for docker.json | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| func_main | string |  | Yes |
| func_sub | string |  | Yes |
| http_proxy | string |  | No |
| https_proxy | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| registry_mirrors | string |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |

#### tocvo.NLBGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | Yes |
| cluster_store | string | for docker.json | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| func_main | string |  | Yes |
| func_sub | string |  | Yes |
| http_proxy | string |  | No |
| https_proxy | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| registry_mirrors | string |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |

#### tocvo.Nic

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| current_speed | string |  | No |
| logical_name | string |  | No |
| mac_address | string |  | No |
| manufacturer | string |  | No |
| max_speed | string |  | No |
| model | string |  | No |
| ports |  |  | No |
| state | string |  | No |

#### tocvo.Substitution

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| ip | string |  | Yes |
| replace | string |  | Yes |

#### tocvo.VGWGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| func_main | string |  | No |
| func_sub | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |

#### tocvo.VirtualInstance

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| interface | string |  | Yes |
| priority | object |  | Yes |
| state | object |  | Yes |
| vips | [ string ] |  | No |
| virtual_router_id | integer |  | No |

#### tocvo.ZKGroupVar

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | Yes |
| cluster_store | string | for docker.json | No |
| docker_port | integer | should make it default with 7070 | No |
| env | string | *Enum:* `"dev"`, `"test"`, `"staging"`, `"uat"`, `"stable"`, `"live"` | Yes |
| func_main | string |  | Yes |
| func_sub | string |  | Yes |
| hosts | [ string ] |  | Yes |
| http_proxy | string |  | No |
| https_proxy | string |  | No |
| idc | string |  | Yes |
| idc_code | string |  | No |
| iptable_rules | [ string ] |  | No |
| registry_mirrors | string |  | No |
| rz | string |  | No |
| segment_code | string |  | No |
| segment_name | string |  | No |
| zookeeper_dir | string |  | No |
| zookeeper_mem | string |  | No |
| zookeeper_nodes_num | integer |  | No |

#### tpl.Template

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| path | string |  | No |

#### v1.FieldsV1

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| v1.FieldsV1 | object |  |  |

#### v1.ManagedFieldsEntry

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| apiVersion | string | APIVersion defines the version of this resource that this field set applies to. The format is "group/version" just like the top-level APIVersion field. It is necessary to track the version of a field set because it cannot be automatically converted. | No |
| fieldsType | string | FieldsType is the discriminator for the different fields format and version. There is currently only one possible value: "FieldsV1" | No |
| fieldsV1 | [v1.FieldsV1](#v1fieldsv1) | FieldsV1 holds the first JSON version format as described in the "FieldsV1" type. +optional | No |
| manager | string | Manager is an identifier of the workflow managing these fields. | No |
| operation | [v1.ManagedFieldsOperationType](#v1managedfieldsoperationtype) | Operation is the type of operation which lead to this ManagedFieldsEntry being created. The only valid values for this field are 'Apply' and 'Update'. | No |
| subresource | string | Subresource is the name of the subresource used to update that object, or empty string if the object was updated through the main resource. The value of this field is used to distinguish between managers, even if they share the same name. For example, a status update will be distinct from a regular update using the same manager name. Note that the APIVersion field is not related to the Subresource field and it always corresponds to the version of the main resource. | No |
| time | string | Time is the timestamp of when the ManagedFields entry was added. The timestamp will also be updated if a field is added, the manager changes any of the owned fields value or removes a field. The timestamp does not update when a field is removed from the entry because another manager took it over. +optional | No |

#### v1.ManagedFieldsOperationType

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| v1.ManagedFieldsOperationType | string |  |  |

#### v1.ObjectMeta

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| annotations | object | Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: <http://kubernetes.io/docs/user-guide/annotations> +optional | No |
| creationTimestamp | string | CreationTimestamp is a timestamp representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations. Clients may not set this value. It is represented in RFC3339 form and is in UTC.  Populated by the system. Read-only. Null for lists. More info: <https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata> +optional | No |
| deletionGracePeriodSeconds | integer | Number of seconds allowed for this object to gracefully terminate before it will be removed from the system. Only set when deletionTimestamp is also set. May only be shortened. Read-only. +optional | No |
| deletionTimestamp | string | DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This field is set by the server when a graceful deletion is requested by the user, and is not directly settable by a client. The resource is expected to be deleted (no longer visible from resource lists, and not reachable by name) after the time in this field, once the finalizers list is empty. As long as the finalizers list contains items, deletion is blocked. Once the deletionTimestamp is set, this value may not be unset or be set further into the future, although it may be shortened or the resource may be deleted prior to this time. For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react by sending a graceful termination signal to the containers in the pod. After that 30 seconds, the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup, remove the pod from the API. In the presence of network partitions, this object may still exist after this timestamp, until an administrator or automated process can determine the resource is fully terminated. If not set, graceful deletion of the object has not been requested.  Populated by the system when a graceful deletion is requested. Read-only. More info: <https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata> +optional | No |
| finalizers | [ string ] | Must be empty before the object is deleted from the registry. Each entry is an identifier for the responsible component that will remove the entry from the list. If the deletionTimestamp of the object is non-nil, entries in this list can only be removed. Finalizers may be processed and removed in any order.  Order is NOT enforced because it introduces significant risk of stuck finalizers. finalizers is a shared field, any actor with permission can reorder it. If the finalizer list is processed in order, then this can lead to a situation in which the component responsible for the first finalizer in the list is waiting for a signal (field value, external system, or other) produced by a component responsible for a finalizer later in the list, resulting in a deadlock. Without enforced ordering finalizers are free to order amongst themselves and are not vulnerable to ordering changes in the list. +optional +patchStrategy=merge | No |
| generateName | string | GenerateName is an optional prefix, used by the server, to generate a unique name ONLY IF the Name field has not been provided. If this field is used, the name returned to the client will be different than the name passed. This value will also be combined with a unique suffix. The provided value has the same validation rules as the Name field, and may be truncated by the length of the suffix required to make the value unique on the server.  If this field is specified and the generated name exists, the server will return a 409.  Applied only if Name is not specified. More info: <https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency> +optional | No |
| generation | integer | A sequence number representing a specific generation of the desired state. Populated by the system. Read-only. +optional | No |
| labels | object | Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: <http://kubernetes.io/docs/user-guide/labels> +optional | No |
| managedFields | [ [v1.ManagedFieldsEntry](#v1managedfieldsentry) ] | ManagedFields maps workflow-id and version to the set of fields that are managed by that workflow. This is mostly for internal housekeeping, and users typically shouldn't need to set or understand this field. A workflow can be the user's name, a controller's name, or the name of a specific apply path like "ci-cd". The set of fields is always in the version that the workflow used when modifying the object.  +optional | No |
| name | string | Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: <http://kubernetes.io/docs/user-guide/identifiers#names> +optional | No |
| namespace | string | Namespace defines the space within which each name must be unique. An empty namespace is equivalent to the "default" namespace, but "default" is the canonical representation. Not all objects are required to be scoped to a namespace - the value of this field for those objects will be empty.  Must be a DNS_LABEL. Cannot be updated. More info: <http://kubernetes.io/docs/user-guide/namespaces> +optional | No |
| ownerReferences | [ [v1.OwnerReference](#v1ownerreference) ] | List of objects depended by this object. If ALL objects in the list have been deleted, this object will be garbage collected. If this object is managed by a controller, then an entry in this list will point to this controller, with the controller field set to true. There cannot be more than one managing controller. +optional +patchMergeKey=uid +patchStrategy=merge | No |
| resourceVersion | string | An opaque value that represents the internal version of this object that can be used by clients to determine when objects have changed. May be used for optimistic concurrency, change detection, and the watch operation on a resource or set of resources. Clients must treat these values as opaque and passed unmodified back to the server. They may only be valid for a particular resource or set of resources.  Populated by the system. Read-only. Value must be treated as opaque by clients and . More info: <https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency> +optional | No |
| selfLink | string | Deprecated: selfLink is a legacy read-only field that is no longer populated by the system. +optional | No |
| uid | string | UID is the unique in time and space value for this object. It is typically generated by the server on successful creation of a resource and is not allowed to change on PUT operations.  Populated by the system. Read-only. More info: <http://kubernetes.io/docs/user-guide/identifiers#uids> +optional | No |

#### v1.OwnerReference

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| apiVersion | string | API version of the referent. | No |
| blockOwnerDeletion | boolean | If true, AND if the owner has the "foregroundDeletion" finalizer, then the owner cannot be deleted from the key-value store until this reference is removed. See <https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion> for how the garbage collector interacts with this field and enforces the foreground deletion. Defaults to false. To set this field, a user needs "delete" permission of the owner, otherwise 422 (Unprocessable Entity) will be returned. +optional | No |
| controller | boolean | If true, this reference points to the managing controller. +optional | No |
| kind | string | Kind of the referent. More info: <https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds> | No |
| name | string | Name of the referent. More info: <http://kubernetes.io/docs/user-guide/identifiers#names> | No |
| uid | string | UID of the referent. More info: <http://kubernetes.io/docs/user-guide/identifiers#uids> | No |

#### v1alpha1.ALBNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| HotUpdateConfig | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) | +optional | No |
| albAgentImageTag | string |  | No |
| albName | string |  | No |
| albSdImageTag | string |  | No |
| azBusiness | string |  | No |
| clusterName | string | +optional | No |
| clusterUUID | string |  | No |
| componentMap | object | +optional | No |
| dockerVersion | string |  | No |
| driverVersion | string |  | No |
| env | string |  | No |
| hostName | string | +optional Attribute       string `json:"attribute"` | No |
| idc | string | equals to rz | No |
| lxcfsVersion | string |  | No |
| metricsImageTag | string |  | No |
| nginxLbImageTag | string |  | No |
| nginxVersion | string |  | No |
| options | object | +optional | No |
| scertmsVersion | string |  | No |
| sdu | string |  | No |
| segment | string |  | No |
| swpTicket | integer | +optional | No |
| templateMap | object | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |
| wafImageTag | string | TODO: to be removed | No |
| zone | string | equals to az | No |

#### v1alpha1.ALBSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| albNode | [v1alpha1.ALBNode](#v1alpha1albnode) | +optional | No |
| lanIP | string |  | No |
| type | string |  | No |

#### v1alpha1.AnchorNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| agentVersion | string | +optional | No |
| az | string |  | No |
| clusterName | string |  | No |
| clusterUUID | string |  | No |
| edsVersion | string | +optional | No |
| env | string |  | No |
| etcdBackupVersion | string | +optional | No |
| exporterVersion | string | +optional | No |
| operateRecord | [v1alpha1.AnchorOperate](#v1alpha1anchoroperate) | +optional | No |
| options | object | +optional | No |
| pingerVersion | string | +optional | No |
| releaseAPIVersion | string | +optional | No |
| rz | string |  | No |
| sdu | string | +optional | No |
| segment | string |  | No |
| statusAPIVersion | string | +optional | No |
| swpTicket | integer | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |

#### v1alpha1.AnchorOperate

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| name | string | +optional | No |
| ticketID | string | +optional | No |

#### v1alpha1.AnchorSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| anchorNode | [v1alpha1.AnchorNode](#v1alpha1anchornode) | +optional | No |
| lanIP | string |  | No |

#### v1alpha1.DNSNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| HotUpdateConfig | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) | +optional | No |
| az | string |  | No |
| azBusiness | string |  | No |
| byPassCheckVIP | boolean | +optional | No |
| clusterID | integer |  | No |
| clusterName | string |  | No |
| componentMap | object | +optional | No |
| dnsAgentVersion | string | +optional | No |
| dpdkDNSConfigVersion | string | +optional | No |
| dpdkDNSVersion | string | +optional | No |
| env | string |  | No |
| highAvailabilityMode | string | +optional | No |
| options | object | +optional | No |
| rz | string |  | No |
| sdu | string | +optional | No |
| segment | string |  | No |
| swpTicket | integer | +optional | No |
| templateMap | object | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |

#### v1alpha1.DNSSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| dnsNode | [v1alpha1.DNSNode](#v1alpha1dnsnode) | +optional | No |
| lanIP | string |  | No |
| type | string |  | No |

#### v1alpha1.EGWNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| agentVersion | string | +optional | No |
| az | string |  | No |
| clusterName | string |  | No |
| clusterUUID | string |  | No |
| env | string |  | No |
| exporterVersion | string | +optional | No |
| gatewayMonitorVersion | string | +optional | No |
| goBGPLANVersion | string | +optional | No |
| goBGPWANVersion | string | +optional | No |
| groupName | string | +optional | No |
| groupUUID | string | +optional | No |
| operateRecord | [v1alpha1.EGWOperate](#v1alpha1egwoperate) | +optional | No |
| options | object | +optional | No |
| pingerVersion | string | +optional | No |
| rz | string |  | No |
| sdu | string | +optional | No |
| segment | string |  | No |
| swpTicket | integer | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |

#### v1alpha1.EGWOperate

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| name | string | +optional | No |
| ticketID | string | +optional | No |

#### v1alpha1.EGWSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| egwNode | [v1alpha1.EGWNode](#v1alpha1egwnode) | +optional | No |
| lanIP | string |  | No |

#### v1alpha1.ETCDNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| cid | string | +optional | No |
| clusterID | string | +optional | No |
| clusterIPTables | string | +optional | No |
| clusterInfo | string | +optional | No |
| clusterName | string |  | No |
| env | string |  | No |
| etcdClientPort | integer |  | No |
| etcdPeerPort | integer |  | No |
| etcdVersion | string | +optional | No |
| idc | string | +optional | No |
| nodeID | string | +optional | No |
| operateRecord | [v1alpha1.EtcdOperate](#v1alpha1etcdoperate) | +optional | No |
| options | object | +optional | No |
| sdu | string | +optional | No |
| segment | string |  | No |
| swpTicket | integer | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |

#### v1alpha1.EtcdOperate

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| name | string | +optional | No |
| ticketID | string | +optional | No |

#### v1alpha1.EtcdSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| etcdNode | [v1alpha1.ETCDNode](#v1alpha1etcdnode) | +optional | No |
| lanIP | string |  | No |
| type | string |  | No |

#### v1alpha1.Ext2Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| HotUpdateConfig | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) | +optional | No |
| az | string |  | No |
| azBusiness | string |  | No |
| clusterName | string |  | No |
| clusterUUID | string |  | No |
| componentMap | object | +optional | No |
| env | string |  | No |
| ext2AgentImageTag | string |  | No |
| ext2ImageTag | string |  | No |
| ext2NginxVersion | string |  | No |
| metricsImageTag | string |  | No |
| options | object | +optional | No |
| rz | string |  | No |
| sdu | string | +optional Attribute       string `json:"attribute"` | No |
| segment | string |  | No |
| swpTicket | integer | +optional | No |
| templateMap | object | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |

#### v1alpha1.Ext2Spec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| ext2Node | [v1alpha1.Ext2Node](#v1alpha1ext2node) | +optional | No |
| lanIP | string |  | No |
| type | string |  | No |

#### v1alpha1.LCSNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| HotUpdateConfig | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) | +optional | No |
| albSdImageTag | string |  | No |
| az | string |  | No |
| azBusiness | string |  | No |
| clusterName | string |  | No |
| clusterUUID | string |  | No |
| componentMap | object | +optional | No |
| env | string |  | No |
| metricsImageTag | string |  | No |
| nginxLbImageTag | string |  | No |
| nginxVersion | string |  | No |
| options | object | +optional | No |
| rz | string |  | No |
| sdu | string | +optional Attribute       string `json:"attribute"` | No |
| segment | string |  | No |
| sgwAgentImageTag | string |  | No |
| swpTicket | integer | +optional | No |
| templateMap | object | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |

#### v1alpha1.LCSSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| lanIP | string |  | No |
| lcsNode | [v1alpha1.LCSNode](#v1alpha1lcsnode) | +optional | No |
| type | string |  | No |

#### v1alpha1.MgmtNodeConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| checkInterval | string |  | No |
| ctrlPollInterval | string |  | No |
| lanInnerIP | string |  | No |
| lanLocalAs | string |  | No |
| lanMask | string |  | No |
| lanNic | string |  | No |
| lanSwitchAs | string |  | No |
| lanSwitchIP | string |  | No |
| lipsAllocateType | string |  | No |
| lipsAmount | string |  | No |
| rack | string |  | No |
| statusChangeLimit | string |  | No |
| tcpTimeout | string |  | No |
| udpTimeout | string |  | No |
| wanInnerIP | string |  | No |
| wanLocalAs | string |  | No |
| wanMask | string |  | No |
| wanNic | string |  | No |
| wanSwitchAs | string |  | No |
| wanSwitchIP | string |  | No |

#### v1alpha1.NATHotUpdateConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| postPercentage | integer | +optional | No |
| waitForBirdStopped | integer | +optional | No |

#### v1alpha1.NATNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| HotUpdateConfig | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) | +optional | No |
| az | string |  | No |
| bondEnv | string | +optional | No |
| clsuterType | string | +optional | No |
| cluster | string |  | No |
| clusterUUID | string |  | No |
| componentMap | object | +optional | No |
| dpvsMetricsVersion | string | +optional | No |
| dpvsVersion | string | +optional | No |
| env | string |  | No |
| flowLogAgentVersion | string | +optional | No |
| hotUpdateConfig | [v1alpha1.NATHotUpdateConfig](#v1alpha1nathotupdateconfig) | +optional | No |
| nicKeywords | string | +optional | No |
| nodeConfig | [v1alpha1.MgmtNodeConfig](#v1alpha1mgmtnodeconfig) |  | No |
| options | object | +optional | No |
| rz | string |  | No |
| sdu | string | +optional | No |
| segment | string |  | No |
| snatAgentVersion | string | +optional | No |
| swpTicket | integer | +optional | No |
| templateMap | object | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |

#### v1alpha1.NATSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| lanIP | string |  | No |
| natNode | [v1alpha1.NATNode](#v1alpha1natnode) | +optional | No |
| type | string |  | No |

#### v1alpha1.NLBHotUpdateConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| postPercentage | integer | +optional | No |
| waitForBirdStopped | integer | +optional | No |

#### v1alpha1.NLBNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| HotUpdateConfig | [v1alpha1.TocexHotUpdateConfig](#v1alpha1tocexhotupdateconfig) | +optional | No |
| az | string |  | No |
| bondEnv | string | +optional | No |
| checkMod | string | +optional | No |
| cid | string | +optional | No |
| cluster | string |  | No |
| clusterType | string | +optional | No |
| clusterUUID | string |  | No |
| componentMap | object | +optional | No |
| dpvsFullNatVersion | string | +optional | No |
| dpvsMetricsVersion | string | +optional | No |
| dpvsVersion | string | +optional | No |
| env | string |  | No |
| flowLogAgentVersion | string | +optional | No |
| healthCheckVersion | string | +optional | No |
| hotUpdateConfig | [v1alpha1.NLBHotUpdateConfig](#v1alpha1nlbhotupdateconfig) | +optional | No |
| idc | string | equals to rz | No |
| nicKeywords | string | +optional | No |
| nlbSDImageTag | string | +optional | No |
| nodeConfig | [v1alpha1.MgmtNodeConfig](#v1alpha1mgmtnodeconfig) |  | No |
| options | object | +optional | No |
| sdu | string | +optional | No |
| segment | string |  | No |
| swpTicket | integer | +optional | No |
| templateMap | object | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |

#### v1alpha1.NLBSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| lanIP | string |  | No |
| nlbNode | [v1alpha1.NLBNode](#v1alpha1nlbnode) | +optional | No |
| type | string |  | No |

#### v1alpha1.SGWDR

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| apiVersion | string | APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: <https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources> +optional | No |
| kind | string | Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: <https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds> +optional | No |
| metadata | [v1.ObjectMeta](#v1objectmeta) |  | No |
| spec | [v1alpha1.SGWDRSpec](#v1alpha1sgwdrspec) |  | No |
| status | [v1alpha1.SGWDRStatus](#v1alpha1sgwdrstatus) |  | No |

#### v1alpha1.SGWDRSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| action | string |  | No |
| albClusters | object | +optional | No |
| albDomains | object | +optional | No |
| azs | [ string ] | +optional | No |
| drTask | [v1alpha1.SGWDRTaskSpec](#v1alpha1sgwdrtaskspec) | +optional | No |
| dryRun | boolean |  | No |
| ext2Clusters | object | +optional | No |
| ext2Domains | object | +optional | No |
| isALBClusterAffected | boolean |  | No |
| isAZAffected | boolean |  | No |
| isExt2ClusterAffected | boolean |  | No |
| isLCSClusterAffected | boolean |  | No |
| isManual | boolean | +optional | No |
| isNLBClusterAffected | boolean |  | No |
| isRZAffected | boolean |  | No |
| lcsClusters | object | +optional | No |
| lcsDomains | object | +optional | No |
| nlbClusters | object | +optional | No |
| nlbDomains | object | +optional | No |
| options | object | +optional | No |
| rzs | [ string ] | +optional | No |
| scenario | integer | +optional | No |
| type | string |  | No |

#### v1alpha1.SGWDRStatus

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| lastTransitionTime | string | LastTransitionTime is the timestamp of the last SGWDR controller transition +optional | No |
| operationTimestamp | string | OperationTimestamp is the server time when this SGWDR controller was started +optional | No |
| reason | string |  | No |
| state | string |  | No |

#### v1alpha1.SGWDRTask

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| adr_task_id | integer |  | No |
| completed | boolean |  | No |
| domains | [ string ] |  | No |
| rz | string |  | No |
| task_id | integer |  | No |

#### v1alpha1.SGWDRTaskSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| spec | [ [v1alpha1.SGWDRTask](#v1alpha1sgwdrtask) ] | +optional | No |
| status | [v1alpha1.SGWDRTaskStatus](#v1alpha1sgwdrtaskstatus) |  | No |

#### v1alpha1.SGWDRTaskStatus

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| reason | string |  | No |
| state | string | current state: pending, running, completed | No |
| task_id | integer | current task id | No |

#### v1alpha1.TocComponent

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| dependent_components | [ string ] | +optional | No |
| dependent_templates | [ string ] | +optional | No |
| disable_restart_service | boolean | +optional | No |
| err_msg | string | +optional | No |
| healthycheck_command | string | +optional | No |
| is_service | boolean | +optional | No |
| name | string |  | No |
| post_command | string | +optional | No |
| pre_command | string | +optional | No |
| script | string | +optional | No |
| service_args | [ string ] | +optional | No |
| service_name | string | +optional | No |
| service_options | [ string ] | +optional | No |
| stop_service_on_delete | boolean | +optional | No |
| type | string |  | No |
| version | string |  | No |

#### v1alpha1.TocTemplate

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| auto_create | boolean | +optional | No |
| auto_delete | boolean | +optional | No |
| content | string | +optional | No |
| dependent_components | [ string ] | +optional | No |
| dependent_templates | [ string ] | +optional | No |
| err_msg | string | +optional | No |
| group | string | +optional | No |
| owner | string | +optional | No |
| path | string |  | No |
| permission | integer | +optional | No |
| post_command | string | +optional | No |

#### v1alpha1.TocexHotUpdateConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| components | [ [v1alpha1.TocComponent](#v1alpha1toccomponent) ] | +optional | No |
| rollbackComponents | [ [v1alpha1.TocComponent](#v1alpha1toccomponent) ] | +optional | No |
| rollbackTemplates | [ [v1alpha1.TocTemplate](#v1alpha1toctemplate) ] | +optional | No |
| templates | [ [v1alpha1.TocTemplate](#v1alpha1toctemplate) ] | +optional | No |
| type | string | +optional | No |
| version | string | +optional | No |

#### v1alpha1.VGWNode

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| agentVersion | string | +optional | No |
| az | string |  | No |
| clusterName | string |  | No |
| clusterUUID | string |  | No |
| env | string |  | No |
| exporterVersion | string | +optional | No |
| gatewayMonitorVersion | string | +optional | No |
| goBGPLANVersion | string | +optional | No |
| goBGPWANVersion | string | +optional | No |
| groupName | string | +optional | No |
| groupUUID | string | +optional | No |
| operateRecord | [v1alpha1.VGWOperate](#v1alpha1vgwoperate) | +optional | No |
| options | object | +optional | No |
| pingerVersion | string | +optional | No |
| rz | string |  | No |
| sdu | string | +optional | No |
| segment | string |  | No |
| swpTicket | integer | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |

#### v1alpha1.VGWOperate

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| name | string | +optional | No |
| ticketID | string | +optional | No |

#### v1alpha1.VGWSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| lanIP | string |  | No |
| vgwNode | [v1alpha1.VGWNode](#v1alpha1vgwnode) | +optional | No |

#### v1alpha1.ZKOperate

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| name | string | +optional | No |
| ticketID | string | +optional | No |

#### v1alpha1.ZookeeperSpec

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string | +optional | No |
| cid | string | +optional | No |
| clusterID | string |  | No |
| clusterInfo | string | +optional | No |
| clusterIptables | string | +optional | No |
| clusterName | string |  | No |
| dockerVersion | string | +optional | No |
| env | string | +optional | No |
| idc | string | +optional | No |
| import | boolean | +optional | No |
| kernel | string | +optional | No |
| lanIP | string |  | No |
| nodeID | string | +optional | No |
| operateRecord | [v1alpha1.ZKOperate](#v1alpha1zkoperate) | +optional | No |
| options | object | +optional | No |
| productID | integer | +optional | No |
| sdu | string | +optional | No |
| segment | string | +optional | No |
| serverID | integer | +optional | No |
| serviceTag | string | +optional | No |
| swpTicket | integer | +optional | No |
| tocAttributes | object | +optional | No |
| tocCluster | string | +optional | No |
| zookeeperIndex | string | +optional | No |
| zookeeperMyid | string | +optional | No |
| zookeeperNodesNum | string | +optional | No |
| zookeeperTag | string | +optional | No |
| zookeeperType | string | +optional | No |
| zookeeperVersion | string | +optional | No |

#### vgwvo.AddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_id | string |  | No |
| cluster_name | string |  | No |
| group_id | string |  | No |
| group_name | string |  | No |
| ips | [ string ] |  | Yes |
| role | string |  | No |
| state | string |  | No |
| type | string |  | Yes |

#### vgwvo.ClusterNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [vgwvo.NodeInfo](#vgwvonodeinfo) ] } |  | No |
| message | string |  | No |

#### vgwvo.FormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| action | string |  | No |
| ips | [ string ] |  | No |
| nodes | [ [vgwvo.Node](#vgwvonode) ] |  | No |
| reason | string |  | No |
| updated_by | string |  | No |

#### vgwvo.GW

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| endpoint | string |  | No |
| exporters | { **"anchor-gateway"**: integer, **"anchor-gateway-exporter"**: integer, **"anchor-monitor"**: integer } |  | No |
| group | string |  | No |
| gw_group_configs | [ [vgwvo.KeyValuePair](#vgwvokeyvaluepair) ] |  | No |
| gw_node_configs | [ [vgwvo.GwNodeConfig](#vgwvogwnodeconfig) ] |  | No |
| lanCidrs | [ string ] |  | No |
| nodes | [ string ] |  | No |
| wanCidrs | [ string ] |  | No |

#### vgwvo.GetNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [vgwvo.NodeState](#vgwvonodestate) |  | No |
| message | string |  | No |

#### vgwvo.GwNodeConfig

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| configs | [ [vgwvo.KeyValuePair](#vgwvokeyvaluepair) ] |  | No |
| ip | string |  | No |

#### vgwvo.KeyValuePair

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| key | string |  | No |
| value | string |  | No |

#### vgwvo.Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| gw | [vgwvo.GW](#vgwvogw) |  | No |
| id | string |  | No |
| ip | string | Anchor Cluster Mgmt lumps all VGWs into 1 single node need a new ip field to differentiate each node | No |

#### vgwvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### vgwvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| components | object |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| function | string |  | No |
| group | string |  | No |
| gw | [vgwvo.GW](#vgwvogw) |  | No |
| id | string |  | No |
| ip | string | Anchor Cluster Mgmt lumps all VGWs into 1 single node need a new ip field to differentiate each node | No |
| links | [ [tocvo.Nic](#tocvonic) ] |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| switches | [ [ndmpvo.Switch](#ndmpvoswitch) ] |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |

#### vgwvo.NodeListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [vgwvo.NodeInfo](#vgwvonodeinfo) ] } |  | No |
| message | string |  | No |

#### vgwvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### vgwvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.VGWSpec](#v1alpha1vgwspec) ] |  | No |
| message | string |  | No |

#### vgwvo.NodeState

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_id | string |  | No |
| cluster_name | string |  | No |
| function | string |  | No |
| group_id | string |  | No |
| group_name | string |  | No |
| ip | string |  | No |
| state | string |  | No |
| status | string |  | No |
| updated_at | string |  | No |

#### vgwvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### vgwvo.RetireNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [vgwvo.FormData](#vgwvoformdata) |  | Yes |
| id | integer |  | Yes |

#### vgwvo.TicketAddCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [vgwvo.AddNodeCBFormData](#vgwvoaddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### vgwvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### vgwvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [vgwvo.TicketData](#vgwvoticketdata) |  | No |
| message | string |  | No |

#### zkvo.AddNodeCBFormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_id | integer |  | No |
| cluster_name | string |  | No |
| ips | [ string ] |  | Yes |
| role | string |  | No |
| state | string |  | No |
| type | string |  | Yes |

#### zkvo.ClusterListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"clusters"**: [ [zkvo.ClusterOverview](#zkvoclusteroverview) ] } |  | No |
| message | string |  | No |

#### zkvo.ClusterMeta

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| az | string |  | No |
| cluster_url | string |  | Yes |
| env | string |  | No |
| hosts | [ string ] |  | No |
| rz | string |  | No |
| segment | string |  | No |

#### zkvo.ClusterMetaResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"metas"**: [ [zkvo.ClusterMeta](#zkvoclustermeta) ] } |  | No |
| message | string |  | No |

#### zkvo.ClusterNodePreCheckResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"results"**: [ [sgwvo.NodeTaskResult](#sgwvonodetaskresult) ] } |  | No |
| message | string |  | No |

#### zkvo.ClusterNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [zkvo.NodeInfo](#zkvonodeinfo) ] } |  | No |
| message | string |  | No |

#### zkvo.ClusterOverview

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| availability | string |  | No |
| az | string |  | No |
| component_name | string |  | No |
| deployment | string |  | No |
| environment | string |  | No |
| id | string |  | No |
| labels | [ integer ] |  | No |
| name | string |  | No |
| node_infos | [ [metavo.Node](#metavonode) ] |  | No |
| nodes | [ string ] |  | No |
| ready_server_num | integer |  | No |
| remark | string |  | No |
| resourcezone | string |  | No |
| segment | string |  | No |
| status | string |  | No |
| total_server_num | integer |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |
| version | string |  | No |

#### zkvo.FormData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| action | string |  | No |
| ips | [ string ] |  | No |
| nodes | [ [zkvo.Node](#zkvonode) ] |  | No |
| reason | string |  | No |
| updated_by | string |  | No |

#### zkvo.GetNodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [zkvo.NodeState](#zkvonodestate) |  | No |
| message | string |  | No |

#### zkvo.Node

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| cluster_name | string |  | No |
| created_at | integer |  | No |
| id | integer |  | No |
| ip | string |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### zkvo.NodeEventResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"events"**: [ [opsent.EventLog](#opsenteventlog) ] } |  | No |
| message | string |  | No |
| size | integer |  | No |
| total | integer |  | No |

#### zkvo.NodeInfo

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_name | string |  | No |
| created_at | integer |  | No |
| details | [sgwvo.DetailInfo](#sgwvodetailinfo) |  | No |
| function | string |  | No |
| id | integer |  | No |
| ip | string |  | No |
| node_status | string |  | No |
| remark | string |  | No |
| server | [toclib.ServerV3](#toclibserverv3) |  | No |
| state | string |  | No |
| ticket | [ticket_defs.Ticket](#ticket_defsticket) |  | No |
| updated_at | integer |  | No |
| updated_by | string |  | No |

#### zkvo.NodeListResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"nodes"**: [ [metavo.Node](#metavonode) ] } |  | No |
| message | string |  | No |

#### zkvo.NodeProvisionResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [toclib.ProvisionNodeConfig](#toclibprovisionnodeconfig) ] |  | No |
| message | string |  | No |

#### zkvo.NodeResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | { **"errors"**: [ [core.ItemError](#coreitemerror) ] } |  | No |
| message | string |  | No |

#### zkvo.NodeSpecResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [ [v1alpha1.ZookeeperSpec](#v1alpha1zookeeperspec) ] |  | No |
| message | string |  | No |

#### zkvo.NodeState

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| application | string |  | No |
| cluster_id | string |  | No |
| function | string |  | No |
| ip | string |  | No |
| state | string |  | No |
| status | string |  | No |
| updated_at | string |  | No |

#### zkvo.NodeTagVariableResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | object |  | No |
| message | string |  | No |

#### zkvo.RetireNodeCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [zkvo.FormData](#zkvoformdata) |  | Yes |
| id | integer |  | Yes |

#### zkvo.TicketAddCBRequest

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| applicant_user | string |  | Yes |
| form_data | [zkvo.AddNodeCBFormData](#zkvoaddnodecbformdata) |  | Yes |
| id | integer |  | Yes |

#### zkvo.TicketData

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| errors | [ [core.ItemError](#coreitemerror) ] |  | No |
| ticket | [swpvo.Ticket](#swpvoticket) |  | No |

#### zkvo.TicketResponse

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| code | integer |  | No |
| data | [zkvo.TicketData](#zkvoticketdata) |  | No |
| message | string |  | No |
