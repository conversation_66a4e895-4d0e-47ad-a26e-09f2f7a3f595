# how to integrate with k8s

## under local development

When dev locally, it integrates with k8s outside, like specifying `kubeconfig` or `master`.

So application will initialize with k8s config which integrate `client-go`, like

```go
	switch application {
	case consts.ALB:
		p.app = &alb.Application{
			KubeConfig: kubeconfig,
			Quit:       p.quit,
		}
	}
```

and then initialization `kubeConfig, err := clientcmd.BuildConfigFromFlags("", kubeconfig)`

## under online ECP runtime

When application deploy into ECP, it integrates with k8s inside, like specifying environment variables `KUBERNETES_SERVICE_HOST` and `KUBERNETES_SERVICE_PORT`.

So application will initialize with k8s config which discovers through `client-go` `restclient.config`, it's that

```go clientcmd.BuildConfigFromFlags()
    kubeconfig, err := restclient.InClusterConfig()
    if err == nil {
    	return kubeconfig, nil
    }
```

```go restclient.InClusterConfig() 
    const (
        tokenFile  = "/var/run/secrets/kubernetes.io/serviceaccount/token"
        rootCAFile = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
	)
	host, port := os.Getenv("KUBERNETES_SERVICE_HOST"), os.Getenv("KUBERNETES_SERVICE_PORT")
	if len(host) == 0 || len(port) == 0 {
		return nil, ErrNotInCluster
	}

	token, err := ioutil.ReadFile(tokenFile)
	if err != nil {
		return nil, err
	}
```

To make this, we need to specify `enable_service_account=true` at `deploy.json`, then raise a ticket for ECP SRE dod that make sure account done.

Ticket like [SATOS-92631](https://jira.shopee.io/browse/SATOS-92631). If account ok, the can find related k8s configs from environment inside container, like

```shell
KUBERNETES_SERVICE_HOST=***********
KUBERNETES_SERVICE_PORT=443
KUBERNETES_SERVICE_PORT_HTTPS=443
KUBERNETES_PORT=tcp://***********:443
KUBERNETES_PORT_443_TCP=tcp://***********:443
KUBERNETES_PORT_443_TCP_PROTO=tcp
KUBERNETES_PORT_443_TCP_ADDR=***********
KUBERNETES_PORT_443_TCP_PORT=443
```