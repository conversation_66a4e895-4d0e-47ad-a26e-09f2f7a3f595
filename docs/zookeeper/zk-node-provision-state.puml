@startuml
title Zookeeper Node Provision State

Spare: 1.AddAZMetaNodeTask

Format: 1.BindServersToTreeTask

Provision: 1.UpdateHostsTask
Provision: 2.UpdateIPTablesTask
Provision: 3.ProvisionTask
Provision: 4.SendSeeEventTask

PostProvision: 1.RemoveOldMetricsTask
PostProvision: 2.DeleteOldMetricsProvisionTask
PostProvision: 3.MetricsProvisionTask
PostProvision: 4.CheckMetricsProvisionTask

Initialising: 1.CheckNodeProvisionTask
Initialising: 2.AddInternalDNSTask

Postcheck: 1.CheckIPTablesTask
Postcheck: 2.CheckHostsTask
Postcheck: 3.CheckZkImageTask

PreMA: 1.CheckMATicketTask
PreMA: 2.StopZKTask
PreMA: 3.UpdateNodeServerStateTask
PreMA: 4.RaiseNodeMATicketTask

Initialised:

Running:

PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.CheckZKHostsTask
PreRunning: 3.StartZKTask
PreRunning: 4.CheckZKHealthyTask

PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.StopZKTask

PreRetire: 1.CheckRetireTicketTask

Retiring: 1.RemoveZKTask
Retiring: 2.DeleteProvisionTask
Retiring: 3.RemoveAZMetaNodeTask
Retiring: 4.UnbindTreeNodeTask

Sunset: 1.DeleteZKCRTask

[*] -> Spare

Spare --> Format: RunSuccessfully

Format --> Provision: RunSuccessfully

Provision --> Initialising: RunSuccessfully

Initialising --> Postcheck: RunSuccessfully

Provision --> Provision: RunFailed

Initialising --> Initialising: RunFailed

Initialised --> PreRunning: Raise Online Ticket

Postcheck --> Initialised: RunSuccessfully

Postcheck --> Postcheck: RunFailed

PreOffline --> Initialised: RunSuccessfully

PreOffline --> PreOffline: RunFailed

PreRunning --> PreRunning: RunFailed

PreRunning --> Running: RunSuccessfully

Running --> PreOffline: Raise Offline Ticket

Running --> PreMA: Raise MA Ticket

Running --> PostProvision: API

PostProvision --> Running: RunSuccessfully

PostProvision --> PostProvision: RunFailed

PreMA --> Running: Rejected or cancelled

PreMA --> PreMA: RunFailed

PreMA --> MA: Approved

MA --> MA: Wait for Repair

MA --> Postcheck: Recover

Initialised --> PreRetire: Raise Retire Ticket

PreRetire --> PreRetire: WaitForApproval or RunFailed

PreRetire --> Retiring: Approved

Retiring --> Retiring: RunFailed

Retiring --> Sunset: RunSuccessfully

Sunset --> Sunset: RunFailed

Sunset --> [*]: RunSuccessfully
@enduml
