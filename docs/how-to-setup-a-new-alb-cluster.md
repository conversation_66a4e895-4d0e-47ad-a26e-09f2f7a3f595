# how to setup a new ALB cluster

## create a cluster on `alb-cluster-management`

workspace: https://space.shopee.io/console/ops/sgw/alb/clusters

1. redirect to [`alb-cluster-mgmt`](https://space.shopee.io/sgw/cluster/alb/create) via `OpsPlatform->ALB->Cluster Management`.
2. ensure the basic info ok, like `AZ` `Segment` `Name` etc.
3. deployment config only the `Specification`.
4. advance config ignore them, will fulfill automatically.
5. in general, we need to bind a specified SDU on the cluster via seatalk, like

    - bind SDU `@infrabot alb c cfg --sdu=alb-xx-env-az -c=alb.xx.az.env`

    - check SDU `@infrabot alb c info -c=alb.xx.az.env`

    - results with `SDU` column `@infrabot alb c ls --az=xx --more`

6. pick the target `cluster` after creation with `Refresh`.

---
**note**

when it's a Private AZ cluster, we must label with `SeaMoney`, so that can be verified properly at next stage.

better label with dedicated `BU` like `Credit` `ShopeePay` `Insurance` etc.

---

## node application

follow this guide [Server Application on OpsPlatform](https://confluence.shopee.io/x/tgi_Tw).

**important**

Please ensure `OS Reload` and `AZ Common Init V2` be done.

---

## for general cluster

### 1. ensure node's tags and assign business

TOC ServerCMDB https://toc.shopee.io/cmdb/server

1.1 check its `Tags` must contain `environment` `application:alb` `function:nginx` `idc`.

1.2 check its `Product Line` must contain `engineering_infra.infra_products.sgw`.

### 2. ensure node's tag variables

2.1 `env` must be an option of `dev` `test` `uat` `staging` `stable` `live`

2.2 `idc` `az` must exist

2.3 `func_main` must be `alb` and `func_sub` must be `nginx`

2.4 `zookeeper_nodes_num` must be greater than `3`

2.5 `iptable_rules` ensure allow internal sources, must allow `protocol udp and dport 443` when enable `HTTP/3`

**note**

Priority bind servers into ServerGroup `alb-live`
which bound with SecurityGroup `alb-ingress-default` via [NPM](https://space.shopee.io/ndre/npm).

As a result, we can do no iptable rules by tag vars.

```yaml
iptable_rules:
  - '-A INPUT -s 10.0.0.0/8 -p tcp -m multiport --dports 9100:9400 -j ACCEPT'
  - '-A INPUT -s 10.0.0.0/8 -p tcp -m multiport --dports 80,443 -j ACCEPT'
  - '-A INPUT -s 10.0.0.0/8 -p udp -m udp --dport 443 -j ACCEPT'
```

## for private cluster

TOC ServerCMDB https://toc.seamoney.io/cmdb/server

### 1. ensure node's tags

1.1 check its `Tags` must contain `environment` `application:alb` `function:nginx` `az` `segment` `Network Zone`
`HighAvailabilityMode`.

HighAvailabilityMode Options follow [Shopee ALB High Availability Mode](https://confluence.shopee.io/x/sxf3Vg)

### 2. ensure node's tag variables

2.1 `env` must be an option of `dev` `test` `uat` `staging` `stable` `live`.

2.2 `idc` `az` must exist.

2.3 `func_main` must be `alb` and `func_sub` must be `nginx`.

2.4 `zookeeper_nodes_num` must be greater than `3`.

2.5 `http_proxy` `https_proxy`, cannot access internet directly, we must set it like `http://ap-sg-1-private-g-ea4e381cc3cc96d7c6fce550171a91b0.squid.sgw.shopee.io:10701`.

2.6 `segment_name` `segment_code` must exist, will check their precision, please ensure `segment_code` = md5sum(`segment_name`).

2.7 `iptable_rules` ensure allow internal sources, like

```yaml
iptable_rules:
  - '-A INPUT -s 10.0.0.0/8 -p tcp -m multiport --dports 9100:9400 -j ACCEPT'
  - '-A INPUT -s 10.0.0.0/8 -p tcp -m multiport --dports 80,443 -j ACCEPT'
  - '-A INPUT -s 10.0.0.0/8 -p udp -m udp --dport 443 -j ACCEPT'
```

if HAM is NonStd1(supported by keepalived), iptables rules need additional as

```yaml
  - '-A INPUT -p vrrp -j ACCEPT'
  - '-A INPUT -p igmp -j ACCEPT'
  - '-A INPUT -d **********/32 -j ACCEPT'
```

2.8 `calico_bgp_peer_as_mapping` must exist when choose HAM above `NonStd-2` which rely on `ecmp-bgp`.

  - 2.9.1 if empty, it'll auto-retrieve, according to default route gateway, details [here](auto-retrieve-node-bgp-peer.md).

2.9 `ecmp_bgp_vips` must exist with LAN VIP when choose HAM `NonStd-3` `NonStd-4`.

  - apply LAN VIP via [IPAM](https://space.shopee.io/ndre/ipam/v2/apply-subnet)

2.10 `ecmp_bgp_wan_vips` must exist with WAN VIP when choose HAM `NonStd-2` `NonStd-3`.

  - apply WAN VIP via [IPAM](https://space.shopee.io/ndre/ipam/v2/apply-subnet)
  - apply WAN(-LAN) VIP firewall policy via [Firewall Request](https://space.shopee.io/utility/swp/ticket_creation?template=%28infra%29_firewall_rules_requests)

2.11 `keepalived_instances` should exist if ham is NonStd1, demo as

```yaml
keepalived_instances:
    VI_1:
        interface: bond0
        state: MASTER
        virtual_router_id: 23
        priority: 100
        vip: ************
```

## join nodes into cluster

1. choose target cluster like `alb.inbound.ap-sg-1-private-g.SPM-BR`.
2. click button `Add Node` and input candidate nodes' IP list, submit it.
3. it'll do check node's `tags` on TOC while submit.
4. it'll return one SWP ticket like [swp-440042](https://space.shopee.io/utility/swp/detail/440042)
5. it'll install all dependent components and templates behind ticket's approval and join in target cluster automatically.

## nodes sequence diagram

### add nodes into cluster

- [add-nodes-into-alb-cluster.puml](alb/add-nodes-into-alb-cluster.puml)

```text
                                                                     Submit Add ALB Nodes Request  

                             ,---.              ,-------.                      ,-------------.                   ,----------.          ,---.          ,---.  
                             |Bob|              |OpsPlat|                      |ALBController|                   |ALBCluster|          |TOC|          |SWP|  
                             `-+-'              `---+---'                      `------+------'                   `----+-----'          `-+-'          `-+-'  
  ,-------------------------!. |1 Submit Add Nodes  |                                 |                               |                  |              |  
  |Pick a target ALB Cluster|_\|------------------->|                                 |                               |                  |              |  
  `---------------------------'|                    |                                 |                               |                  |              |  
                               |                    |  2 Request Raise a ticket of    |                               |                  |              |  
                               |                    |   `Add SGW L7 Nodes`            |                               |                  |              |  
                               |                    |-------------------------------->|                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 |     3 Fetch Cluster Meta      |                  |              |  
                               |                    |                                 |------------------------------->                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 |              4                |                  |              |  
                               |                    |                                 |<- - - - - - - - - - - - - - - -                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |           ,-----------------!.  ----.                           |                  |              |  
                               |                    |           |Managed by NonECP|_\     | 5 Verify Managed          |                  |              |  
                               |                    |           `-------------------' <---'                           |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |       6 Alert on failure        |                               |                  |              |  
                               |                    |<- - - - - - - - - - - - - - - - |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               | 7 Show Alert Tips  |                                 |                               |                  |              |  
                               |<- - - - - - - - - -|                                 |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 |         8 Fetch Nodes         |                  |              |  
                               |                    |                                 |------------------------------->                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 |              9                |                  |              |  
                               |                    |                                 |<- - - - - - - - - - - - - - - -                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 ----.                           |                  |              |  
                               |                    |                                     | 10 Verify Nodes Existed   |                  |              |  
                               |                    |                                 <---'                           |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |      11 Alert on existed        |                               |                  |              |  
                               |                    |<- - - - - - - - - - - - - - - - |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |12 Show Alert Tips  |                                 |                               |                  |              |  
                               |<- - - - - - - - - -|                                 |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 ----.                           |                  |              |  
                               |                    |                                     | 13 Verify Cluster SDU     |                  |              |  
                               |                    |                                 <---'                           |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |     14 Alert on no binding      |                               |                  |              |  
                               |                    |<- - - - - - - - - - - - - - - - |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |15 Show Alert Tips  |                                 |                               |                  |              |  
                               |<- - - - - - - - - -|                                 |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 |                16 Fetch Servers                  |              |  
                               |                    |                                 |------------------------------------------------->|              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 |                       17      |                  |              |  
                               |                    |                                 |<- - - - - - - - - - - - - - - - - - - - - - - - -|              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 ----.                                              |              |  
                               |                    |                                     | 18 Verify Servers BizAssign                  |              |  
                               |                    |                                 <---'                                              |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |19 Alert on no assign at general |                               |                  |              |  
                               |                    |<- - - - - - - - - - - - - - - - |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |20 Show Alert Tips  |                                 |                               |                  |              |  
                               |<- - - - - - - - - -|                                 |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 |                             ,----------------------------------------!.
                               |                    |                                 ----.                         |general: `environment` `application:alb`|_\
                               |                    |                                     | 21 Verify Servers Tags  | `function:nginx` `idc`                   |
                               |                    |                                 <---'                         |private:`environment` `application:alb`   |
                               |                    |                                 |                             | `function:nginx` `az` `segment`          |
                               |                    |                                 |                             | `Network Zone` `HighAvailabilityMode`    |
                               |                    |                                 |                             `------------------------------------------'
                               |                    |    22 Alert on illegal tag      |                               |                  |              |  
                               |                    |<- - - - - - - - - - - - - - - - |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |23 Show Alert Tips  |                                 |                               |                  |              |  
                               |<- - - - - - - - - -|                                 |                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 ----.                           |                  |              |  
                               |                    |                                     | 24 Fetch Component Version|                  |              |  
                               |                    |                                 <---'                           |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 |                        25 Create Ticket          |              |  
                               |                    |                                 |---------------------------------------------------------------->|  
                               |                    |                                 |                               |                  |              |  
                               |                    |                                 |                              26                  |              |  
                               |                    |                                 |<- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - |  
                               |                    |                                 |                               |                  |              |  
                               |                    |     27 Response SWP Ticket      |                               |                  |              |  
                               |                    |<--------------------------------|                               |                  |              |  
                               |                    |                                 |                               |                  |              |  
                               |28 Show Ticket Tips |                                 |                               |                  |              |  
                               |<-------------------|                                 |                               |                  |              |  
                             ,-+-.              ,---+---.                      ,------+------.                   ,----+-----.          ,-+-.          ,-+-.  
                             |Bob|              |OpsPlat|                      |ALBController|                   |ALBCluster|          |TOC|          |SWP|  
                             `---'              `-------'                      `-------------'                   `----------'          `---'          `---'  

```

### join nodes into cluster

- [join-nodes-into-alb-cluster.puml](alb/join-nodes-into-alb-cluster.puml)

```text
                                                                  Join ALB Nodes Request  

     ,---.                 ,---.                    ,-------------.                  ,----------.          ,---.          ,---.  
     |Bob|                 |SWP|                    |ALBController|                  |ALBCluster|          |TOC|          |K8S|  
     `-+-'                 `-+-'                    `------+------'                  `----+-----'          `-+-'          `-+-'  
       |1 Approval Add Nodes |                             |                              |                  |              |  
       |-------------------->|                             |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |         2 Approve           |                              |                  |              |  
       |                     |---------------------------->|                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |     3 Fetch Cluster Meta     |                  |              |  
       |                     |                             |------------------------------>                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |              4               |                  |              |  
       |                     |                             |<- - - - - - - - - - - - - - -                   |              |  
       |                     |                             |                              |                  |              |  
       |                     |       ,-----------------!.  ----.                          |                  |              |  
       |                     |       |Managed by NonECP|_\     | 5 Verify Managed         |                  |              |  
       |                     |       `-------------------' <---'                          |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |     6 Alert on failure      |                              |                  |              |  
       |                     |<- - - - - - - - - - - - - - |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       | 7 Show Alert Tips   |                             |                              |                  |              |  
       |<- - - - - - - - - - |                             |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |    8 Fetch Ticket Meta      |                              |                  |              |  
       |                     |<----------------------------|                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |             9               |                              |                  |              |  
       |                     | - - - - - - - - - - - - - ->|                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             ----.                          |                  |              |  
       |                     |                                 | 10 Verify Ticket         |                  |              |  
       |                     |                             <---'                          |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |    11 Alert on failure      |                              |                  |              |  
       |                     |<- - - - - - - - - - - - - - |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       | 12 Show Alert Tips  |                             |                              |                  |              |  
       |<- - - - - - - - - - |                             |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |    13 Fetch Cluster Meta     |                  |              |  
       |                     |                             |------------------------------>                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |             14               |                  |              |  
       |                     |                             |<- - - - - - - - - - - - - - -                   |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             ----.                          |                  |              |  
       |                     |                                 | 15 Verify Cluster SDU    |                  |              |  
       |                     |                             <---'                          |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |   16 Alert on no binding    |                              |                  |              |  
       |                     |<- - - - - - - - - - - - - - |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       | 17 Show Alert Tips  |                             |                              |                  |              |  
       |<- - - - - - - - - - |                             |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |   ,---------------------!.  ----.                          |                  |              |  
       |                     |   |Verify ETCD Endpoint |_\     | 18 Verify Cluster Meta   |                  |              |  
       |                     |   |Connectivity on Nodes  | <---'                          |                  |              |  
       |                     |   `-----------------------' |                              |                  |              |  
       |                     |  19 Alert on unconnected    |                              |                  |              |  
       |                     |<- - - - - - - - - - - - - - |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       | 20 Show Alert Tips  |                             |                              |                  |              |  
       |<- - - - - - - - - - |                             |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |             21 Fetch Servers Tags               |              |  
       |                     |                             |------------------------------------------------>|              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |                      22      |                  |              |  
       |                     |                             |<- - - - - - - - - - - - - - - - - - - - - - - - |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |                             ,----------------------------------------!.  
       |                     |                             ----.                         |general: `environment` `application:alb`|_\  
       |                     |                                 | 23 Verify Servers Tags  | `function:nginx` `idc`                   |  
       |                     |                             <---'                         |private:`environment` `application:alb`   |  
       |                     |                             |                             | `function:nginx` `az` `segment`          |  
       |                     |                             |                             | `Network Zone` `HighAvailabilityMode`    |  
       |                     |                             |                             `------------------------------------------'  
       |                     |  24 Alert on illegal tag    |                              |                  |              |  
       |                     |<- - - - - - - - - - - - - - |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       | 25 Show Alert Tips  |                             |                              |                  |              |  
       |<- - - - - - - - - - |                             |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |         26 Fetch Server Tag Variables           |              |  
       |                     |                             |------------------------------------------------>|              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |                      27      |                  |              |  
       |                     |                             |<- - - - - - - - - - - - - - - - - - - - - - - - |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             ----.                          ,--------------------------------!.  
       |                     |                                 | 28 Verify Tag Variables  |common: `func_main` `func_sub`  |_\  
       |                     |                             <---'                          | `etcd_clusters` `alb_provision`  |  
       |                     |                             |                              | `etcd|zk Connectivity on node`   |  
       |                     |                             |                              `----------------------------------'  
       |                     |                             ----.                             ,--------------------------------------!.  
       |                     |                                 | 29 Verify HA Tag Variables  |`VIP` on HAM, BGP Peer on HAM, WAN VIP|_\  
       |                     |                             <---'                             |HTTP Tunnel                             |  
       |                     |                             |                              |  `----------------------------------------'  
       |                     |30 Alert on illegal variable |                              |                  |              |  
       |                     |<- - - - - - - - - - - - - - |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       | 31 Show Alert Tips  |                             |                              |                  |              |  
       |<- - - - - - - - - - |                             |                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |                     |                             |                    32 Add Nodes into Queue      |              |  ,------------------!.
       |                     |                             |--------------------------------------------------------------->|  |node state `Spare`|_\
       |                     |                             |                              |                  |              |  `--------------------'
       |                     |                             |                              33                 |              |  
       |                     |                             |<- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -|  
       |                     |                             |                              |                  |              |  
       |                     |         34 Success          |                              |                  |              |  
       |                     |<----------------------------|                              |                  |              |  
       |                     |                             |                              |                  |              |  
       |      35 Done        |                             |                              |                  |              |  
       |<--------------------|                             |                              |                  |              |  
     ,-+-.                 ,-+-.                    ,------+------.                  ,----+-----.          ,-+-.          ,-+-.  
     |Bob|                 |SWP|                    |ALBController|                  |ALBCluster|          |TOC|          |K8S|  
     `---'                 `---'                    `-------------'                  `----------'          `---'          `---'  

```
