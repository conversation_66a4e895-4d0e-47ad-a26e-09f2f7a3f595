@startuml

title DNS Node Provision State

Spare: 1.AddAZMetaNodeTask

Buffer:

PreDeploy: 1.CheckDeployTicketTask

Format: 1.RegisterNodeIntoSDU

PreProvision: 1.InstallDpdkDNSConfigTask
PreProvision: 2.CheckDpdkDNSConfigTask

Provision: 1.ProvisionTask

Initialising: 1.CheckNodeProvisionTask

PostCheck: 1.CheckUnboundReadyTask

state Initialised{

    InitialisedHotUpdate: 1.TriggerComponentUpdateTask
    InitialisedHotUpdate: 2.CheckComponentUpdateTask
    InitialisedHotUpdate: 3.RestartDpdkDNSTask

    InitialisedRollback: 1.TriggerComponentRollbackTask
    InitialisedRollback: 2.CheckComponentRollbackTask
    InitialisedRollback: 3.CleanComponentRollbackTask

    [*] -> Initial

   Initial --> InitialisedHotUpdate: RaiseHotUpdateTicket
   InitialisedHotUpdate -> InitialisedHotUpdate: RunFailed
   InitialisedHotUpdate --> Initial: RunSuccessfully

   Initial --> InitialisedRollback: RaiseRollbackTicket
   InitialisedRollback -> InitialisedRollback: RunFailed
   InitialisedRollback --> Initial: RunSuccessfully
}
note left of Initialised: Initial done and Wait for Approval of `Online`

PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.StartBird(Keepalived)Task
PreRunning: 3.CheckBird(Keepalived)ReadyTask
PreRunning: 4.CheckVIPConnectivityTask
PreRunning: 5.SendSeeEventTask

state Running{
    RunningHotUpdate: 1.TriggerComponentUpdateTask
    RunningHotUpdate: 2.CheckComponentUpdateTask

    RunningRollback: 1.TriggerComponentRollbackTask
    RunningRollback: 2.CheckComponentRollbackTask
    RunningRollback: 3.CleanComponentRollbackTask

    [*] -> Run

    Run --> RunningHotUpdate: RaiseHotUpdateTicket
    RunningHotUpdate -> RunningHotUpdate: RunFailed
    RunningHotUpdate --> Run: RunSuccessfully

    Run --> RunningRollback: RaiseRollbackTicket
    RunningRollback -> RunningRollback: RunFailed
    RunningRollback --> Run: RunSuccessfully
}
note right of Running: Node in Serving Traffic

PreMA: 1.CheckMATicketTask
PreMA: 2.StopBirdTask
PreMA: 3.UpdateNodeServerStateTask
PreMA: 4.RaiseNodeMATicketTask

PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.StopBirdTask

PreRetire: 1.CheckRetireTicketTask

Retiring: 1.DeleteProvisionTask
Retiring: 2.UnRegisterNodeTask
Retiring: 3.MoveNodeMAPoolTask

PreSunset: 1.CheckRetireTicketTask

Sunset: 1.RemoveAZMetaNodeTask
Sunset: 2.DeleteDNSCRTask

[*] -> Spare

Spare --> Buffer: Approved

Buffer --> Buffer: No Requirements

Buffer --> PreDeploy: Raise Deploy Ticket
PreDeploy --> PreDeploy: RunFailed
PreDeploy --> Buffer: Rejected or Canceled

Buffer --> PreSunset: Raise Sunset Ticket

PreDeploy --> Format: RunSuccessfully
Format --> Format: RunFailed

Format --> PreProvision: RunSuccessfully
PreProvision --> PreProvision: RunFailed

PreProvision --> Provision: RunSuccessfully

Provision --> Initialising: RunSuccessfully

Initialising --> PostCheck: RunSuccessfully

Provision --> Provision: RunFailed

Initialising --> Initialising: RunFailed

Initialised --> PreRunning: Raise Online Ticket

PostCheck --> Initialised: RunSuccessfully

PostCheck --> PostCheck: RunFailed

PreOffline --> Initialised: RunSuccessfully

PreOffline --> PreOffline: RunFailed
PreOffline --> Running: Rejected or Canceled

PreRunning --> PreRunning: RunFailed
PreRunning --> Initialised: Rejected or Canceled

PreRunning --> Running: RunSuccessfully

Running --> PreOffline: Raise Offline Ticket

Running --> PreMA: Raise MA Ticket

PreMA --> Running: Rejected or Canceled

PreMA --> PreMA: RunFailed

PreMA --> MA: Approved

MA --> MA: Wait for Repair

MA --> PostCheck: Recover

Initialised --> PreRetire: Raise Retire Ticket

PreRetire --> PreRetire: WaitForApproval or RunFailed
PreRetire --> Initialised: Rejected or Canceled

PreRetire --> Retiring: Approved

Retiring --> Retiring: RunFailed

Retiring --> Sunset: RunSuccessfully

PreSunset --> PreSunset: WaitForApproval or RunFailed
PreSunset --> Buffer: Rejected or Canceled

PreSunset --> Sunset: Approved

Sunset --> Sunset: RunFailed

Sunset --> [*]: RunSuccessfully

@enduml
