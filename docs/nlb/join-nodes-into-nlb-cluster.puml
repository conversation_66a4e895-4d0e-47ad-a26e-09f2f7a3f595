@startuml

title Join NLB Nodes Request

participant Bob
autonumber

Bob -> NLBCluster: Create Cluster
note right: Bind SDU

Bob -> SWP: Approval Add Nodes
SWP -> NLBController: Approve

NLBController -> NLBCluster: Fetch Cluster Meta
return

NLBController --> SWP: <PERSON><PERSON> on failure
SWP --[#red]> Bob: Show Alert Tips

NLBController -> SWP: Fetch Ticket Meta
return

NLBController -> NLBController: Verify Ticket
NLBController --> SWP: Alert on failure
SWP --[#red]> Bob: Show Alert Tips

NLBController -> NLBCluster: Fetch Cluster Meta
return

NLBController -> NLBController: Verify Cluster SDU
NLBController --> SWP: Alert on no binding
SWP --[#red]> Bob: Show Alert Tips

NLBController -> NLBController: Verify Cluster Meta
note left: Verify zookeeper\n Connectivity on Nodes
NLBController --> SWP: Alert on unconnected
SWP --[#red]> Bob: Show Alert Tips

NLBController -> NDMP: Verify Switch
note left: Verify switch existence
NLBController --> SWP: <PERSON><PERSON> on invalid switch
SWP --[#red]> Bob: Show Alert Tips

NLBController -> TOC: Fetch Servers Tags
return

NLBController -> NLBController: Verify Servers Tags
note right: general: `environment` `application:nlb`\n `function:dpvs` `idc`
NLBController --> SWP: Alert on illegal tag
SWP --[#red]> Bob: Show Alert Tips

NLBController -> TOC: Fetch Server Tag Variables
return

NLBController -> NLBController: Verify Tag Variables
note right: common: `func_main` `func_sub`\n `zk Connectivity on node`

NLBController -> K8S: Add Nodes into Queue
note right: node state `Spare`
return

NLBController -> SWP: Success
SWP -> Bob: Done

@enduml
