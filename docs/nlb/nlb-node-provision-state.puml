@startuml

title NLB Node Provision State

'https://confluence.shopee.io/x/jK2vdg
Spare: 1.CheckConfigFileTask
Spare: 2.AddNodeIntoClusterTask

Format:
Format: 1.BindServerTreeMainNodeTask

Provision: 1.ProvisionTask

Initialising: 1.CheckNodeProvisionStatusTask
Initialising: 2.InstallMlnxofedTaskTask

PostInitialising: 1.CheckNetplanTask
PostInitialising: 1.RebootServerTask

PreInitialised: 1.StartServiceTask
PreInitialised: 2.CheckServiceTask
PreInitialised: 3.EnableServiceTask
PreInitialised: 4.InstallHealthCheckTask
PreInitialised: 5.CheckHealthCheckServiceTask
PreInitialised: 6.CheckInnerIPTask

note left of PreInitialised: Service: dpvs dpvs-fullnat

state Initialised{
    [*] -> Initial

   Initial --> InitialisedHotUpdate: RaiseHotUpdateTicket
   InitialisedHotUpdate -> InitialisedHotUpdate: RunFailed
   InitialisedHotUpdate --> Initial: RunSuccessfully

   Initial --> InitialisedRollback: RaiseRollbackTicket
   InitialisedRollback -> InitialisedRollback: RunFailed
   InitialisedRollback --> Initial: RunSuccessfully
}
note left of Initialised: Initial done and Wait for Approval of `Online`

InitialisedHotUpdate: 1.HotUpdateProvisionTask
InitialisedHotUpdate: 2.CheckHotUpdateProvisionStatusTask

InitialisedRollback: 1.RollbackProvisionTask
InitialisedRollback: 2.CheckRollbackProvisionStatusTask

PreRunning: 1.CheckOnlineTicketTask
PreRunning: 2.CheckNodeMgmtStatusTask
PreRunning: 3.UpdateNodeServerStateTask
PreRunning: 4.SendSeeEventTask

state Running{
    [*] -> Run

    Run --> RunningHotUpdate: RaiseHotUpdateTicket
    RunningHotUpdate -> RunningHotUpdate: RunFailed
    RunningHotUpdate --> Run: RunSuccessfully

    Run --> RunningRollback: RaiseRollbackTicket
    RunningRollback -> RunningRollback: RunFailed
    RunningRollback --> Run: RunSuccessfully
}
note right of Running: Node in Serving Traffic

RunningHotUpdate: 1.HotUpdateProvisionTask
RunningHotUpdate: 2.CheckHotUpdateProvisionStatusTask
RunningHotUpdate: 3.CheckHotUpdateStatusTask
RunningHotUpdate: 4.(dpvs-metrics)RestartNodeExporterTask
RunningHotUpdate: 4.(flowlog-agent)AddLogRotateCrontab
RunningHotUpdate: 4.(nlb-sd)PostCheckNlbSdTask
RunningHotUpdate: 4.(dpvs-fullnat)PostCheckDpvsMesosStatusTask
RunningHotUpdate: 5.(dpvs-fullnat)PostCheckDpvsSvcLinesTask
RunningHotUpdate:
RunningHotUpdate: (dpvs)UpdateDpvsPrecheckTask
RunningHotUpdate: (dpvs)RecordDpvsServiceLinesTask
RunningHotUpdate: (dpvs)RecordDpvsConnTask
RunningHotUpdate: (dpvs)CheckDpvsMesosStatusTask
RunningHotUpdate: (dpvs)UpgradeDpvsStopServiceTask
RunningHotUpdate: (dpvs)CheckTrafficTask
RunningHotUpdate: (dpvs)TouchUpgradeFileTask
RunningHotUpdate: (dpvs)UnreadyNodeTask
RunningHotUpdate: (dpvs)CheckNodeMgmtStatusTask
RunningHotUpdate: (dpvs)StopDpvsFullnatTask
RunningHotUpdate: (dpvs)StopBirdTask
RunningHotUpdate: (dpvs)CheckTrafficTask
RunningHotUpdate: (dpvs)UninstallDpvsTask
RunningHotUpdate: (dpvs)HotUpdateConfigTask
RunningHotUpdate: (dpvs)HotUpdateTask
RunningHotUpdate: (dpvs)CheckHotUpdateStatusTask
RunningHotUpdate: (dpvs)InstallMlnxofedTask
RunningHotUpdate: (dpvs)StartDpvsServiceTask
RunningHotUpdate: (dpvs)CheckStartDpvsResultTask
RunningHotUpdate: (dpvs)RecordDpvsServiceLinesTask
RunningHotUpdate: (dpvs)StartDpvsFullnatServiceTask
RunningHotUpdate: (dpvs)CheckDpvsFullnatServiceTask
RunningHotUpdate: (dpvs)PostCheckDpvsSvcLinesTask
RunningHotUpdate: (dpvs)ConnSyncModeTask
RunningHotUpdate: (dpvs)CheckConnSyncTask
RunningHotUpdate: (dpvs)StartHealthCheckServiceTask
RunningHotUpdate: (dpvs)RemoveUpgradeFileTask
RunningHotUpdate: (dpvs)ReadyNodeTask


RunningRollback: 1.RollbackProvisionTask
RunningRollback: 2.CheckRollbackProvisionStatusTask

PreOffline: 1.CheckOfflineTicketTask
PreOffline: 2.CheckNodeMgmtStatusTask

PreMA: 1.CheckOfflineTicketTask
PreMA: 2.CheckNodeMgmtStatusTask
PreMA: 3.UpdateNodeServerStateTask

MA: 1.WaitForRepairManually

PreRetiring: 1.CheckRetiringTicketTask

Retiring: 1.StopHealthCheckTask
Retiring: 2.CheckTrafficStatusTask
Retiring: 3.StopNodeServiceTask
Retiring: 4.DeleteProvisionTask
Retiring: 5.RemoveNodeTask
Retiring: 6.MoveNodeMAPoolTask
Retiring: 7.UnbindServersFromTreeTask

Sunset: 1.DeleteNLBCRTask

[*] -> Spare
Spare -> Spare: RunFailed

Spare --> Format: RunSuccessfully

Format -> Format: RunFailed
Format --> Provision: RunSuccessfully

Provision -> Provision: RunFailed
Provision --> Initialising: RunSuccessfully

Initialising -> Initialising: RunFailed
Initialising --> PostInitialising: RunSuccessfully

PostInitialising --> PostInitialising: RunFailed
PostInitialising --> PreInitialised: RunSuccessfully

PreInitialised --> PreInitialised: RunFailed
PreInitialised --> PostCheck: RunSuccessfully

PostCheck -> PostCheck: RunFailed
PostCheck --> Initialised: RunSuccessfully

Initialised -> Initialised: HotUpdateType

Initialised --> PreRunning: RaiseOnlineTicket

PreRunning -> PreRunning: WaitForApproval or RunFailed
PreRunning -> Running: Approved

Running -> Running: HotUpdateType

Running --> PreOffline: RaiseOfflineTicket
Running --> PreMA: RaiseMATicket

PreMA --> Running: Rejected or Canceled
PreMA --> PreMA: RunFailed
PreMA --> MA: Approved

MA --> MA: WaitForRepair
MA --> PostCheck: Recover

PreOffline -> PreOffline: WaitForApproval or RunFailed
PreOffline --> Initialised: Approved

Initialised --> PreRetiring: RaiseRetiringTicket

PreRetiring -> PreRetiring: WaitForApproval or RunFailed
PreRetiring --> Retiring: Approved

Retiring -> Retiring: RunFailed
Retiring --> Sunset: RunSuccessfully

Sunset -> Sunset: RunFailed
Sunset --> [*]: RunSuccessfully

@enduml
