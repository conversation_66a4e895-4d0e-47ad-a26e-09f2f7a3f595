# how to manage ALB cluster step by step

## enable `alb-controller` managed

those clusters under VPC network aren't managed. the others are managed.

### check alb cluster bound to sdu

those clusters in general AZ must be bound to sdu, check them like:

```shell
sgwctl alb c info --cluster alb.xx.live
```

if unbound, then bind it as:

```shell
sgwctl alb c cfg --sdu alb-live-xx --cluster alb.xx.live
```

### trigger alb cluster sync

trigger synchronization, like:

```shell
curl -X PUT http://localhost:8080/alb/v1/cluster/uuid/60ff0efb-b9f2-5a01-88fe-b78279a4db94/sync/
```
