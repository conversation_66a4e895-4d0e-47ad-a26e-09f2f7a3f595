# how to sync ALB etcd clusters into AZ meta

## background

AZ meta store all products' meta data, then ops related system can depend on it.

## ALB cluster

ALB clusters deploy in every AZ, and most of AZ there's multi clusters, but only one etcd cluster in one AZ.

## synchronize

1. dump ALB clusters
2. foreach cluster with AZ and upsert etcd cluster into AZ meta

  2.1 check whether existed etcd cluster by AZ
  2.2 if non-existed, create cluster by AZ; if existed then skip
  2.3 fetch etcd nodes' IP list via cluster's node(tag vars), upsert into AZ meta
