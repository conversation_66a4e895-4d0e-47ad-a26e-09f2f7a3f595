# ALB Nginx Reload Functionality

This document describes the ALB nginx reload functionality that allows authorized users to trigger nginx reload operations on ALB cluster nodes.

## Overview

The ALB reload functionality provides a secure REST API for business departments to reload nginx configurations on ALB nodes belonging to their Business Unit (BU). It includes user authentication, BU-based authorization, rate limiting, and comprehensive audit logging.

## Features

### 1. User Authentication & Authorization
- Integration with existing authentication system
- User-to-BU mapping configuration
- Only authorized users can perform reload operations
- Users can only reload nginx on nodes belonging to their BU

### 2. Rate Limiting
- Prevents abuse with configurable rate limits
- Default: 1 reload operation per BU every 5 minutes
- In-memory rate limiting for the initial version

### 3. Audit Logging
- Comprehensive logging of all reload operations
- Records timestamp, user, BU, target nodes, and results
- Local file-based logging with rotation support
- JSON format for easy parsing and analysis

### 4. Security
- Input validation and sanitization
- Command injection prevention
- Only predefined safe nginx commands allowed
- Proper error handling and status codes

### 5. Nginx Reload Execution
- Uses tocex to execute `systemctl reload nginx` on remote nodes
- Fixed command for security (no custom commands allowed)
- Configurable timeout settings
- Detailed result reporting per node
- Graceful error handling for partial failures

### 6. Middleware-based Authorization
- ALBReloadAuthMiddleware: Validates user authentication and BU mapping
- ALBClusterAccessMiddleware: Validates cluster access and request format
- Centralized permission control for all reload operations

## API Endpoints

### POST /alb/v1/reload
Triggers a nginx reload operation on ALB cluster nodes.

**Request Body:**
```json
{
  "cluster_id": "550e8400-e29b-41d4-a716-446655440000",
  "reason": "Updated SSL certificates"
}
```

**Response:**
```json
{
  "code": 200,
  "message": "OK",
  "data": {
    "request_id": "123e4567-e89b-12d3-a456-426614174000",
    "status": "success",
    "message": "Successfully reloaded nginx on 3 nodes",
    "cluster_id": "550e8400-e29b-41d4-a716-446655440000",
    "bu": "payments",
    "target_nodes": ["10.0.1.10", "10.0.1.11", "10.0.1.12"],
    "start_time": "2025-01-08T10:30:00Z",
    "end_time": "2025-01-08T10:30:15Z"
  }
}
```

### GET /alb/v1/reload/config
Returns the current reload configuration for the authenticated user.

### GET /alb/v1/reload/history
Lists recent reload operations (placeholder for future implementation).

### GET /alb/v1/reload/status/{request_id}
Gets the status of a specific reload operation (placeholder for future implementation).

## Configuration

### Example Configuration
See `configs/alb-reload-config-example.yaml` for a complete configuration example.

### Key Configuration Sections

#### User-BU Mapping
```yaml
alb:
  reload:
    user_bu_map:
      - email: "<EMAIL>"
        bu: "payments"
      - email: "<EMAIL>"
        bu: "checkout"
```

#### Rate Limiting
```yaml
alb:
  reload:
    rate_limit:
      window_minutes: 5
      max_requests: 1
```

#### Audit Logging
```yaml
alb:
  reload:
    audit_log:
      enable: true
      file_path: "/var/log/sgw-addon-operator/alb-reload-audit.log"
      max_size_mb: 100
      max_age_days: 30
```

#### Nginx Command
```yaml
alb:
  reload:
    nginx_command:
      # Command is fixed to "systemctl reload nginx" for security
      command: "systemctl reload nginx"  # This setting is ignored
      timeout_seconds: 30
```

## Error Codes

| Code | Error | Description |
|------|-------|-------------|
| 400 | ERR_INVALID_REQUEST | Invalid request format or parameters |
| 401 | ERR_UNAUTHORIZED | User authentication required |
| 403 | ERR_FORBIDDEN | User not authorized for ALB reload operations |
| 429 | ERR_RATE_LIMITED | Rate limit exceeded for the user's BU |
| 400 | ERR_INVALID_CLUSTER | Invalid or non-existent cluster ID |
| 500 | ERR_INTERNAL_SERVER | Internal server error |
| 500 | ERR_RELOAD_FAILED | Nginx reload operation failed |

## Security Considerations

### Input Validation
- Cluster ID must be a valid UUID
- Reason field limited to 500 characters
- XSS prevention in reason field
- Email validation for requesting user

### Command Security
- Fixed to use only `systemctl reload nginx` command
- No custom commands allowed for maximum security
- Command injection prevention through fixed command
- No configuration-based command changes permitted

### Access Control
- User must be authenticated
- User must be mapped to a BU
- User can only access nodes in their BU
- Rate limiting prevents abuse

## Deployment

1. **Configuration**: Update the ALB configuration file with user-BU mappings and other settings
2. **Enable Feature**: Set `alb.reload.enable` to `true`
3. **Log Directory**: Ensure the audit log directory exists and is writable
4. **Restart Service**: Restart the sgw-addon-operator service
5. **Test**: Verify functionality with a test reload operation

## Monitoring

### Audit Logs
- Location: `/var/log/sgw-addon-operator/alb-reload-audit.log`
- Format: JSON lines
- Fields: timestamp, request_id, user_email, bu, cluster_id, target_nodes, status, error_msg, reason, duration_ms

### Metrics
- Monitor reload success/failure rates
- Track rate limiting hits
- Monitor operation latency
- Alert on repeated failures

## Future Enhancements

1. **Database Storage**: Replace in-memory rate limiting with persistent storage
2. **History API**: Implement full history and status tracking APIs
3. **Rollback Support**: Add nginx configuration rollback functionality
4. **Advanced BU Filtering**: Implement sophisticated node-to-BU mapping
5. **Webhook Notifications**: Add webhook support for reload events
6. **Batch Operations**: Support reloading multiple clusters in one request

## Troubleshooting

### Common Issues

1. **Rate Limited**: Wait for the rate limit window to expire
2. **Unauthorized**: Ensure user is mapped to a BU in configuration
3. **Cluster Not Found**: Verify cluster ID is correct and accessible
4. **Command Failed**: Check nginx configuration syntax and node connectivity
5. **Audit Log Issues**: Verify log directory permissions and disk space

### Debug Information
- Check application logs for detailed error messages
- Review audit logs for operation history
- Verify tocex connectivity to target nodes
- Validate nginx configuration before reload
