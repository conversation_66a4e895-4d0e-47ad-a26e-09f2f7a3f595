# SGWCTL ALB Commands

`sgwctl alb` provides ALB (Application Load Balancer) management commands.

## Overview

```bash
sgwctl alb [command] [subcommand] [options]
```

## Command Structure

### `cluster` - Cluster Management

ALB cluster management commands.

```bash
sgwctl alb cluster [subcommand] [options]
```

#### `list` - List Clusters

List all ALB clusters with support for multiple output formats and filtering options.

##### Basic Usage

```bash
# Default JSON format output for all clusters
sgwctl alb cluster list

# Use filters (table format output)
sgwctl alb cluster list --env test
sgwctl alb cluster list --region sg --az ap-sg-1-general-y
```

##### Go Template Formatting

Use `--template` parameter for custom output formatting:

```bash
# Output only cluster UUIDs
sgwctl alb cluster list --template '{{.UUID}}'

# Output cluster name and UUID
sgwctl alb cluster list --template '{{.Name}}: {{.UUID}}'

# Output detailed information
sgwctl alb cluster list --template 'Name: {{.Name}}, Region: {{.Region}}, Env: {{.Env}}, Nodes: {{.NodesCount}}'
```

##### Available Template Fields

| Field | Type | Description |
|-------|------|-------------|
| `{{.UUID}}` | string | Cluster UUID |
| `{{.Name}}` | string | Cluster name |
| `{{.Env}}` | string | Environment (test, staging, live, etc.) |
| `{{.Region}}` | string | Region |
| `{{.AZ}}` | string | Availability Zone |
| `{{.Segment}}` | string | Network segment |
| `{{.SDU}}` | string | SDU |
| `{{.NetworkType}}` | string | Network type |
| `{{.NetworkEnv}}` | string | Network environment |
| `{{.ClusterType}}` | string | Cluster type |
| `{{.NodesCount}}` | int | Number of nodes |
| `{{.ReadyNodes}}` | int | Number of ready nodes |
| `{{.IsLegacy}}` | bool | Whether it's a legacy cluster |
| `{{.Labels}}` | []string | List of labels |

##### Options

| Parameter | Description |
|-----------|-------------|
| `--template value` | Format output using Go template |
| `--region value` | Filter by region |
| `--az value` | Filter by availability zone |
| `--env value` | Filter by environment (test,staging,stable,live,liveish) |
| `--label value` | Filter by label |
| `--legacy` | Show legacy clusters |
| `--standard, --std` | Show standard clusters |
| `--more, --detail` | Show more cluster information |
| `--render value, --format value` | Output format (table,csv,html,markdown, default: "table") |
| `--all, --show-all` | Show all |

##### Examples

```bash
# List all clusters in test environment
sgwctl alb cluster list --env test

# List clusters in Singapore region, show only UUIDs
sgwctl alb cluster list --region sg --template '{{.UUID}}'

# List clusters with "SeaMoney" label
sgwctl alb cluster list --label SeaMoney

# Output detailed information in CSV format
sgwctl alb cluster list --env live --more --format csv

# Output cluster name and ready node count
sgwctl alb cluster list --template '{{.Name}}: {{.ReadyNodes}}/{{.NodesCount}} nodes ready'
```

#### `sync` - Sync Cluster Nodes

Synchronize all nodes of a specified cluster into k8s runtime.

##### Usage

```bash
sgwctl alb cluster sync --cluster-uuid <uuid>
# or use shorthand
sgwctl alb cluster sync --uuid <uuid>
```

##### Options

| Parameter | Description |
|-----------|-------------|
| `--cluster-uuid value, --uuid value` | Cluster UUID (required) |

##### Examples

```bash
# Sync all nodes of the specified cluster
sgwctl alb cluster sync --cluster-uuid 48756da0-262c-5105-877b-55482d41b5f4

# Use shorthand parameter
sgwctl alb cluster sync --uuid 48756da0-262c-5105-877b-55482d41b5f4
```

##### How It Works

1. Retrieve cluster details by cluster UUID
2. Extract all node IP addresses
3. Process node IPs in batches of 30
4. Call `PUT /alb/v1/cluster/uuid/{uuid}/sync/` API
5. Display progress for each batch and final results

#### `sync-spec` - Sync Node Specifications

Synchronize node specification information for all nodes in a specified cluster.

##### Usage

```bash
sgwctl alb cluster sync-spec --cluster-uuid <uuid>
# or use shorthand
sgwctl alb cluster sync-spec --uuid <uuid>
```

##### Options

| Parameter | Description |
|-----------|-------------|
| `--cluster-uuid value, --uuid value` | Cluster UUID (required) |

##### Examples

```bash
# Sync node specifications for the specified cluster
sgwctl alb cluster sync-spec --cluster-uuid 48756da0-262c-5105-877b-55482d41b5f4

# Use shorthand parameter
sgwctl alb cluster sync-spec --uuid 48756da0-262c-5105-877b-55482d41b5f4
```

##### How It Works

1. Retrieve cluster details by cluster UUID
2. Extract all node IP addresses
3. Process node IPs in batches of 30
4. Call `PUT /alb/v1/node/spec/` API
5. Display progress for each batch and final results

#### `vip` - VIP Operations

Virtual IP related management operations.

```bash
sgwctl alb cluster vip [subcommand] [options]
```

Refer to specific subcommand help for detailed VIP operations.

## Global Options

All ALB commands support the following global options:

| Option | Description |
|--------|-------------|
| `--help, -h` | Show help information |

## Use Cases

### 1. Cluster Maintenance

```bash
# 1. View all production clusters
sgwctl alb cluster list --env live

# 2. Check status of specific clusters
sgwctl alb cluster list --template '{{.Name}}: {{.ReadyNodes}}/{{.NodesCount}} ready' --env live

# 3. Sync cluster nodes
sgwctl alb cluster sync --uuid <cluster-uuid>

# 4. Sync node specifications
sgwctl alb cluster sync-spec --uuid <cluster-uuid>
```

### 2. Cluster Monitoring

```bash
# View cluster distribution across environments
sgwctl alb cluster list --template '{{.Env}}: {{.Name}} ({{.Region}})' | sort

# Check legacy clusters
sgwctl alb cluster list --legacy --template '{{.Name}} - Legacy: {{.IsLegacy}}'

# View node count statistics
sgwctl alb cluster list --template '{{.Name}}: {{.NodesCount}} nodes'
```

### 3. Automation Scripts

```bash
#!/bin/bash
# Batch sync all clusters in test environment

# Get UUIDs of all test environment clusters
CLUSTER_UUIDS=$(sgwctl alb cluster list --env test --template '{{.UUID}}')

# Iterate and sync each cluster
for uuid in $CLUSTER_UUIDS; do
    echo "Syncing cluster: $uuid"
    sgwctl alb cluster sync --uuid "$uuid"
    sgwctl alb cluster sync-spec --uuid "$uuid"
done
```

## Error Handling

### Common Errors and Solutions

1. **Cluster UUID not found**
   ```
   Error: fetch_cluster_failed: cluster not found
   ```
   Solution: Verify the UUID is correct, or use `list` command to view available clusters.

2. **Cluster has no nodes**
   ```
   Cluster <uuid> has no nodes to sync
   ```
   This is a normal message indicating the cluster currently has no configured nodes.

3. **Network or permission error**
   ```
   Error: fetch_opsbot_token_failed: unauthorized
   ```
   Solution: Check authentication configuration and network connectivity.

4. **Batch processing failure**
   ```
   Error: sync_cluster_batch_1_failed: response error
   ```
   Solution: Check backend API status, may need to retry.

## Important Notes

1. **Production Operations**: `sync` and `sync-spec` commands call actual backend APIs. Use with caution in production environments.

2. **Batch Size**: To avoid oversized requests, the system automatically processes nodes in batches of 30.

3. **Template Syntax**: Field names in Go templates are case-sensitive. Ensure correct field names are used.

4. **Filter Combinations**: Multiple filter conditions can be used simultaneously, e.g., `--env test --region sg`.

5. **Output Formats**:
   - Default JSON format when no filter conditions are used
   - Default table format when filter conditions are used
   - Custom format when using `--template`

## Related Documentation

- [ALB Cluster Management Guide](../manage-alb-cluster-guideline.md)
- [OPS Platform Operations Guide](../ops/README.md)