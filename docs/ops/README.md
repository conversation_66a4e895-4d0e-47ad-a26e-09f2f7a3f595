# SGW Operations Commands

## Overview

The `sgwctl ops` command provides comprehensive tools for ops platform related operations, including ALB management, server pool operations, ticket status tracking, and TOC server management. These commands help automate and monitor infrastructure operations across different business lines.

## Command Structure

```
sgwctl ops
├── alb              # ALB operations
│   ├── version      # Show ALB API version
│   ├── cluster      # Cluster operations
│   │   └── sync     # Sync nodes into k8s runtime
│   └── node-state   # Check ALB node state in cluster
├── server-pool      # Server pool operations
├── ticket-status    # SWP ticket status operations
└── toc-server       # TOC server operations
```

## Commands

### sgwctl ops alb

ALB operations including node state checking, version info, and cluster management.

#### sgwctl ops alb node-state

Check ALB node state in cluster for OS upgrade readiness.

##### Syntax
```shell
sgwctl ops alb node-state --ip <node_ip> [flags]
```

##### Description
This command performs comprehensive readiness checks for ALB nodes before OS upgrades. It validates node health, connectivity, service status, and deployment state.

##### Examples

```shell
# Check ALB node state
sgwctl ops alb node-state --ip ********

# Get JSON output
sgwctl ops alb node-state --ip ******** --format json

# Enable verbose logging
sgwctl ops alb node-state --ip ******** --verbose
```

##### Flags
- `--ip` - Target ALB node IP address (required)
- `--format` - Output format (json for raw JSON output)
- `--verbose` - Enable verbose logging

##### Output
Returns detailed information including:
- Node state and ALB name
- SDU and application details
- Function and ticket information
- Deployment status

#### sgwctl ops alb version

Show ALB API version information.

##### Syntax
```shell
sgwctl ops alb version
```

#### sgwctl ops alb cluster sync

Sync nodes into k8s runtime.

##### Syntax
```shell
sgwctl ops alb cluster sync --uuid <cluster_uuid>
```

##### Flags
- `--uuid` - Cluster UUID (required)

### sgwctl ops server-pool

Check server information in OpsPlatform server pool.

#### Syntax
```shell
sgwctl ops server-pool --ip <server_ip> [flags]
```

#### Description
Retrieves comprehensive server information from the OpsPlatform server pool, including hardware state, OS details, and deployment configuration.

#### Examples

```shell
# Check server pool info
sgwctl ops server-pool --ip *************

# Get JSON output
sgwctl ops server-pool --ip ************* --format json

# Enable verbose logging
sgwctl ops server-pool --ip ************* --verbose
```

#### Flags
- `--ip` - Target server IP address (required)
- `--format` - Output format (json for raw JSON output)
- `--verbose` - Enable verbose logging

#### Output
Returns server details including:
- Server state and TOC state
- Hardware and OS information
- Application and function details
- AZ, IDC, and environment info

### sgwctl ops ticket-status

Check SWP ticket status by ticket ID.

#### Syntax
```shell
sgwctl ops ticket-status --id <ticket_id> [flags]
```

#### Description
Retrieves detailed SWP ticket information including status, phases, and assignee details for tracking OS upgrade operations.

#### Examples

```shell
# Check ticket status
sgwctl ops ticket-status --id 12345

# Get JSON output
sgwctl ops ticket-status --id 12345 --format json

# Enable verbose logging
sgwctl ops ticket-status --id 12345 --verbose
```

#### Flags
- `--id` - SWP ticket ID (required)
- `--format` - Output format (json for raw JSON output)
- `--verbose` - Enable verbose logging

#### Output
Returns ticket information including:
- Ticket ID and title
- Phase and phase type
- Applicant and assignee details
- Creation and update timestamps

### sgwctl ops toc-server

Check TOC server information by IP address.

#### Syntax
```shell
sgwctl ops toc-server --ip <server_ip> [flags]
```

#### Description
Retrieves comprehensive TOC server information including hardware details, platform configuration, and deployment status.

#### Examples

```shell
# Check TOC server info
sgwctl ops toc-server --ip ***********

# Get JSON output
sgwctl ops toc-server --ip *********** --format json

# Enable verbose logging
sgwctl ops toc-server --ip *********** --verbose
```

#### Flags
- `--ip` - Target server IP address (required)
- `--format` - Output format (json for raw JSON output)
- `--verbose` - Enable verbose logging

#### Output
Returns comprehensive server details including:
- Server identification and network info
- Hardware specifications
- Platform and product information
- Tags and configuration details

## Workflow Integration

### Pre-upgrade Checks
1. Run `sgwctl ops alb node-state` to verify ALB node readiness
2. Use `sgwctl ops ticket-status` to ensure proper ticket coordination
3. Check server information with `sgwctl ops server-pool` and `sgwctl ops toc-server`

### During Operations
- Monitor progress through ticket status commands
- Coordinate with server pool management for capacity planning
- Use TOC server commands for hardware state verification

### Post-operation Validation
- Re-run node state checks to verify successful operations
- Update ticket status to reflect completion
- Validate server pool and TOC server states

## Related Documentation

- [ALB Cluster Management](../manage-alb-cluster-guideline.md) - ALB-specific operations
- [Development Guide](../../how-to-develop.md) - Development setup and testing
- [API Documentation](../swagger.md) - REST API reference

## Troubleshooting

### Common Issues

**Node connectivity issues**
- Verify network connectivity to target node
- Check SSH access and credentials
- Ensure node is reachable from the operator environment

**API failures**
- Review individual command output for specific errors
- Use `--format json` for detailed error information
- Check logs for additional diagnostic information

**Ticket integration problems**
- Verify ticket system connectivity
- Check authentication credentials
- Ensure proper ticket workflow configuration

**Server information retrieval issues**  
- Confirm server IP addresses are correct
- Verify access permissions to OpsPlatform and TOC systems
- Check network connectivity to backend services

For additional support, consult the main project documentation or contact the development team.
