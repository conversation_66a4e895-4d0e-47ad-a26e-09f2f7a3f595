# hot-update nginx binary

## Solution 1: via APT

1. set target version via toc provision with `apt` type

    ```go
    package albprov

    func (n *NodeProvision) NginxComponent(version string) *toclib.ProvisionNodeComponent {
        return &toclib.ProvisionNodeComponent{
            IsService:           true,
            StopServiceOnDelete: true,
            Name:                cmp.NginxComponent.Name,
            ServiceName:         cmp.NginxComponent.Service,
            Type:                cmp.NginxComponent.Type,
            Version:             version,
            DependentTemplates:  n.dependentTemplatePaths(cmp.NginxComponent),
            DependentComponents: n.dependentComponents(cmp.NginxComponent),
            PreCommand:          "",
            PostCommand:         "",
            HealthycheckCommand: "kill -s 0 $(cat /var/run/nginx.pid)",
        }
    }
    ```

2. `PreCommand` backup binary shared library and related files

    ```shell
   # backup old bin
    day=$(date "+%Y%m%d")
    mv $NGINX_BIN_PATH/nginx $NGINX_BIN_PATH/nginx_bak."$day"

   # backup shared library
   cp -av /usr/local/ssl/lib/libcrypto.so.1.1{,~}
   cp -av /usr/local/ssl/lib/libssl.so.1.1{,~}
   cp -av /usr/share/nginx/modules/ngx_http_qatzip_filter_module.so{,~}
   cp -av /usr/share/nginx/modules/ngx_ssl_engine_qat_module.so{,~}

   # backup conf files
   cp -av /etc/nginx/nginx.conf{,."$day"}
   cp -av /etc/nginx/mime.types{,."$day"}
   cp -av /etc/nginx/ssl/dhparams.pem{,."$day"}
   cp -av /etc/nginx/geoip/GeoIP.dat{,."$day"}
   cp -av /etc/nginx/geoip/GeoLiteCity.dat{,."$day"}
    ```

3. `PostCommand` restore backup conf files to cover files from package, then execute `upgrade.sh` which is similar to
[ngx_upgrade.sh](https://git.garena.com/shopee/devops/shopee-ops/-/blob/master/ansible/roles/albcommon/files/nginx/ngx_upgrade.sh)
then restart `sgw-agent` ensure nginx version consistency.

   1. if `upgrade.sh` failed then restore shared library and nginx binary

    ```shell
    log "upgrade failed"

    if [ -f "$CRYPTO_BAK" ]
    then
        log "recover $CRYPTO_BAK"
        mv $CRYPTO_BAK $CRYPTO  
    fi

    if [ -f "$SSL_BAK" ]
    then
        log "recover $SSL_BAK"
        mv $SSL_BAK $SSL  
    fi

    if [ -f "$QATZIP_BAK" ]
    then
        log "recover $QATZIP_BAK"
        mv $QATZIP_BAK $QATZIP  
    fi  

    if [ -f "$SSL_ENGINE_BAK" ]
    then
        log "recover $SSL_ENGINE_BAK"
        mv $SSL_ENGINE_BAK $SSL_ENGINE  
    fi

    log "recover old nginx"
    mv $NGINX_BIN_PATH/nginx_bak."$day" $NGINX_BIN_PATH/nginx
    exit 1
    ```

### Advantage

1. the same way with initialization on `nginx-shopee` via `apt` through `toc` `provision` [tunnel](https://confluence.shopee.io/x/Ba1ZKw).
2. `toc` ensure the target version on package, cannot update version manually, if change then will be reverted to
predefined version.
3. package's version the same to binary's version.

### Disadvantage

1. cannot update package version manually
2. need double-check related config files, like `nginx.conf` `mime.types` `geo.dat`, they'll be updated by
`mesos-nginx-lb` or `sgw-agent`.

## Solution 2: via Script

1. define `update-nginx-binary` script component

    ```go
   package cmp

   var UpdateNginxComponent = Component{
		Name:          "update-nginx-binary",
		Type:          ComponentTypeScript,
		Service:       "nginx",
		VerifyVersion: true,
		Dependencies: []*Component{
			&GeoIPBinComponent, &LinuxEthernetComponent,
			&LinuxToolsComponent, &DriverManagerComponent, &FlameGraphComponent,
		},
		DependentTemplates: tpl.ALBCoreTemplates,
    }

   func (n *NodeProvision) NginxComponent(version string) *toclib.ProvisionNodeComponent {
        return &toclib.ProvisionNodeComponent{
            Name:                cmp.UpdateNginxComponent.Name,
            Type:                cmp.UpdateNginxComponent.Type,
            Version:             version,
            Script:              updateNginxBinaryScript,
            DependentTemplates:  n.dependentTemplatePaths(cmp.UpdateNginxComponent),
            DependentComponents: n.dependentComponents(cmp.UpdateNginxComponent),
            PreCommand:          "",
            PostCommand:         "",
            HealthycheckCommand: "kill -s 0 $(cat /var/run/nginx.pid)",
        }
    }
    ```

2. `updateNginxBinaryScript` similar to [ngx_upgrade.sh](https://git.garena.com/shopee/devops/shopee-ops/-/blob/master/ansible/roles/albcommon/files/nginx/ngx_upgrade.sh)

3. `PreCommand` fetch nginx package and backup shared library

    ```shell
    mkdir -p /tmp/ngx_upgrade /usr/share/nginx/modules /usr/local/ssl/lib
    rm -fr /tmp/ngx_upgrade/*
    chmod a+x /data

   # fetch target version package
   wget https://deb.shopee.io/ubuntu/pool/main/n/nginx-shopee/nginx-shopee-"${version}".deb -o /tmp/ngx_upgrade/
   chmod 0644 /tmp/ngx_upgrade/nginx-shopee-"${version}".deb

   dpkg -X /tmp/ngx_upgrade/nginx-shopee-"${version}".deb /tmp/ngx_upgrade/

   # backup shared library
   mv -v -b /tmp/ngx_upgrade/usr/share/nginx/modules/* /usr/share/nginx/modules
   mv -v -b /tmp/ngx_upgrade/usr/local/ssl/lib/* /usr/local/ssl/lib/
    ```

4. `PostCommand` restart `sgw-agent` ensure nginx version consistency.

### Advantage

1. most of the operations reuse `update-nginx-bianry.yml` playbook, works fine at daily practice.
2. no need actions on conf files

### Disadvantage

1. a new component definition
2. update nginx-shopee version at `CRD` after update successfully
3. different with initialization, fetch version not the same between `apt` and `nginx`
